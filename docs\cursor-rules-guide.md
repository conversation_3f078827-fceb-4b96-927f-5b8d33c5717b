# Cursor Rules 使用指南

## 📖 概述

本项目使用 Cursor AI 编辑器的规则系统来标准化开发流程，确保代码质量和一致性。所有规则文件存储在 `.cursor/rules/` 目录下，采用 Markdown 格式（.mdc 扩展名）。

## 📁 目录结构

```
.cursor/
├── rules/                          # Cursor Rules 目录
│   ├── rules-index.json           # 规则索引配置文件
│   ├── code-standards.mdc         # 代码标准规范 [自动应用]
│   ├── design-style.mdc           # 设计风格指南 [自动应用]
│   ├── database-structure.mdc     # 数据库结构规范 [自动应用]
│   ├── device-adaptation.mdc      # 设备适配规范 [自动应用]
│   ├── error-handling.mdc         # 错误处理规范 [自动应用]
│   ├── iconfont-guide.mdc         # 图标字体使用指南 [自动应用]
│   ├── performance-optimization.mdc # 性能优化规范 [自动应用]
│   ├── project-structure.mdc      # 项目结构指南 [手动应用]
│   ├── miniprogram-development.mdc # 小程序开发规范 [手动应用]
│   ├── mathematical-content-formatting.mdc # 数学内容格式化 [手动应用]
│   └── ...其他规则文件
```

## 🔧 规则分类

### 自动应用规则（alwaysApply: true）

这些规则在每次 AI 对话时都会自动生效，确保基础开发标准：

- **code-standards.mdc** - 编码规范、组件开发、图标使用
- **design-style.mdc** - 色彩系统、字体规范、布局设计
- **database-structure.mdc** - 数据库设计和SQL语法
- **device-adaptation.mdc** - 响应式设计和多设备兼容
- **error-handling.mdc** - 异常处理和错误提示
- **iconfont-guide.mdc** - 图标管理和SVG使用
- **performance-optimization.mdc** - 性能优化和内存管理

### 手动应用规则（alwaysApply: false）

这些规则在特定开发场景下手动引用：

- **project-structure.mdc** - 项目结构和文件组织
- **miniprogram-development.mdc** - 微信小程序特定开发规范
- **mathematical-content-formatting.mdc** - 数学公式和KaTeX语法
- **page-navigation.mdc** - 页面路由和导航管理
- **package-structure.mdc** - 分包策略和模块划分

## 🎯 使用方法

### 1. 自动应用

标记为 `alwaysApply: true` 的规则会自动在每次 AI 交互中生效，无需手动操作。

### 2. 手动触发

对于手动应用的规则，可以通过以下方式触发：

```
// 在对话中提及规则文件名
请参考 project-structure.mdc 的规范

// 或者通过描述字段触发
如何组织项目文件结构？
```

### 3. 特定文件类型规则

某些规则可能只对特定文件类型生效，通过 `globs` 字段配置：

```yaml
---
globs: *.js,*.ts,*.wxml
---
```

## 📝 规则文件格式

每个规则文件采用 Markdown 格式，开头包含 YAML frontmatter：

```markdown
---
description: 规则描述
globs: 适用文件类型（可选）
alwaysApply: true/false
---
# 规则标题

规则内容...
```

### Frontmatter 字段说明

- **description**: 规则的简短描述，用于手动触发时的识别
- **globs**: 适用的文件类型（如 `*.js,*.ts`），为空表示适用所有文件
- **alwaysApply**: 是否自动应用到每次对话

## 🛠️ 管理和维护

### 1. 添加新规则

1. 在 `.cursor/rules/` 目录下创建新的 `.mdc` 文件
2. 添加合适的 frontmatter 配置
3. 编写规则内容
4. 更新 `rules-index.json` 配置文件

### 2. 修改现有规则

1. 直接编辑对应的 `.mdc` 文件
2. 如需修改应用方式，调整 frontmatter 配置
3. 更新 `rules-index.json` 中的相关信息

### 3. 规则优先级

- 自动应用规则 > 手动应用规则
- 较新的规则会覆盖较旧的相似规则
- 具体文件类型规则 > 通用规则

## 🔍 规则索引

详细的规则列表和描述请参考：[`.cursor/rules-index.json`](../.cursor/rules-index.json)

## 📊 统计信息

- 总规则数量：17个
- 自动应用规则：7个
- 手动应用规则：10个
- 总大小：约120KB

## 🚀 最佳实践

### 对于开发者

1. **熟悉自动应用规则**：了解项目的基础开发标准
2. **合理使用手动规则**：在特定场景下引用相关规则
3. **保持规则一致性**：新增功能时遵循现有规则
4. **及时更新规则**：根据项目发展调整规则内容

### 对于项目管理者

1. **定期审查规则**：确保规则与项目需求保持一致
2. **控制规则数量**：避免规则过多导致冲突或混乱
3. **培训团队成员**：确保团队了解并正确使用规则系统
4. **版本控制**：将规则文件纳入版本控制系统

## 📞 支持

如有关于 Cursor Rules 的问题或建议，请：

1. 查看本文档和规则索引文件
2. 与项目维护者联系
3. 在项目讨论区提出问题

---

*最后更新：2024-12-19* 