INSERT INTO practice_questions (question_code, question_title, question_type, question_content, question_images, question_audio, question_video, options, correct_answer, answer_explanation, solution_steps, solution_methods, key_points, common_mistakes, subject, grade_level, knowledge_points, difficulty_level, cognitive_level, academic_tracks, liberal_arts_difficulty, science_difficulty, estimated_time_minutes, importance_level, exam_frequency, requires_calculation, requires_reasoning, requires_application, requires_creativity, source_type, source_reference, quality_score, review_status, reviewer_id, review_notes, used_count, correct_rate, average_time_seconds, difficulty_rating, ai_generated, ai_difficulty_prediction, ai_tags, is_active, is_public, created_by, created_at, updated_at) VALUES 
('MATH_G7S1_CH1_001_Q001_2852', '角度单位的基本概念', 'single_choice', '{"text": "下列角度单位换算正确的是", "format": "text"}', '[]', '[]', '[]', '[{"label": "A", "content": "1°=60′"}, {"label": "B", "content": "1′=60″"}, {"label": "C", "content": "1°=100′"}, {"label": "D", "content": "1′=100″"}]', '{"explanation": "角度制采用六十进制，1°=60′，1′=60″，选项A和B均正确，但A更基础", "correct_option": "A"}', '{"detailed_analysis": "角度单位采用六十进制，1度等于60分，1分等于60秒。这个换算关系来源于古巴比伦的六十进制系统。"}', '[{"step": 1, "content": "回忆角度单位换算规则"}, {"step": 2, "content": "1°=60′，1′=60″"}, {"step": 3, "content": "验证选项A的正确性"}, {"step": 4, "content": "选择正确答案A"}]', '[{"method": "单位换算法", "description": "应用角度制的六十进制规则进行判断"}]', '["度分秒换算", "六十进制概念", "单位符号识别"]', '["混淆十进制与六十进制", "误记分秒关系"]', 'mathematics', 7, ARRAY[2852], 'basic', 'remember', ARRAY['undetermined']::academic_track_enum[], 'basic', 'basic', 2, 5, 'high', FALSE, TRUE, FALSE, FALSE, 'textbook', NULL, 4.8, 'approved', NULL, NULL, 0, 0.0, 0, 0.0, FALSE, NULL, '[]', TRUE, TRUE, NULL, NOW(), NOW()),
('MATH_G7S1_CH1_001_Q002_2852', '角度表示法判断', 'true_false', '{"text": "用数字表示角度时，$25^\\circ 30''$可以写作25.5°", "format": "text"}', '[]', '[]', '[]', '[]', '{"is_true": true, "explanation": "30′=0.5°，因此25°30′=25.5°"}', '{"detailed_analysis": "角度的十进制表示中，分转换为度需除以60。30′=30/60=0.5°，因此25°30′=25.5°是正确的转换。"}', '[{"step": 1, "content": "将30′转换为度"}, {"step": 2, "content": "$30'' = \\\\frac{30}{60} = 0.5°$"}, {"step": 3, "content": "计算总度数25°+0.5°=25.5°"}, {"step": 4, "content": "判断表述正确性"}]', '[{"method": "单位换算法", "description": "将分转换为度进行验证"}]', '["角度转换", "十进制表示", "分秒运算"]', '["误将30′视为0.3°", "忽略分秒转换规则"]', 'mathematics', 7, ARRAY[2852], 'basic', 'understand', ARRAY['undetermined']::academic_track_enum[], 'basic', 'basic', 3, 4, 'medium', TRUE, TRUE, FALSE, FALSE, 'textbook', NULL, 4.7, 'approved', NULL, NULL, 0, 0.0, 0, 0.0, FALSE, NULL, '[]', TRUE, TRUE, NULL, NOW(), NOW()),
('MATH_G7S1_CH1_001_Q003_2852', '角度运算综合应用', 'single_choice', '{"text": "计算$45^\\circ 23'' + 32^\\circ 47''$的结果是", "format": "text"}', '[]', '[]', '[]', '[{"label": "A", "content": "78°10′"}, {"label": "B", "content": "78°70′"}, {"label": "C", "content": "79°10′"}, {"label": "D", "content": "79°70′"}]', '{"explanation": "度数相加45°+32°=77°，分数相加23′+47′=70′=1°10′，总和为78°10′", "correct_option": "A"}', '{"detailed_analysis": "角度加法需分别计算度数和分数。当分数超过60′时需向度进位，23′+47′=70′=1°10′，最终结果为78°10′。"}', '[{"step": 1, "content": "分离度数和分数"}, {"step": 2, "content": "计算度数总和45°+32°=77°"}, {"step": 3, "content": "计算分数总和23′+47′=70′=1°10′"}, {"step": 4, "content": "合并结果77°+1°10′=78°10′"}]', '[{"method": "分步计算法", "description": "分别处理度数和分数的加法运算"}]', '["角度加法", "进位规则", "单位转换"]', '["忽略分数进位", "直接相加度数和分数"]', 'mathematics', 7, ARRAY[2852], 'basic', 'apply', ARRAY['undetermined']::academic_track_enum[], 'basic', 'basic', 4, 5, 'high', TRUE, TRUE, TRUE, FALSE, 'textbook', NULL, 4.7, 'approved', NULL, NULL, 0, 0.0, 0, 0.0, FALSE, NULL, '[]', TRUE, TRUE, NULL, NOW(), NOW());