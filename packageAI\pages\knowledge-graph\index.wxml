<view class="container knowledge-graph-container">
  <!-- 顶部导航栏 -->
  <nav-bar 
    title="知识图谱" 
    fixed="{{true}}" 
    showBack="{{true}}" 
    bgColor="#ffffff"
    textColor="#333333"
    bind:back="goBack"> 
  </nav-bar>
  
  <!-- 固定内容区域 -->
  <view class="fixed-content">
    <!-- 学段选择 -->
    <view class="stage-select">
      <view class="stage-item {{selectedStage === 'primary' ? 'active' : ''}}" 
            bindtap="onStageChange" data-stage="primary">小学</view>
      <view class="stage-item {{selectedStage === 'junior' ? 'active' : ''}}" 
            bindtap="onStageChange" data-stage="junior">初中</view>
      <view class="stage-item {{selectedStage === 'senior' ? 'active' : ''}}" 
            bindtap="onStageChange" data-stage="senior">高中</view>
    </view>
    
    <!-- 年级选择（小学和初中） -->
    <scroll-view wx:if="{{selectedStage !== 'senior'}}" scroll-x enhanced show-scrollbar="{{false}}" class="grade-scroll">
      <view class="grade-list">
        <block wx:for="{{grades}}" wx:key="*this">
          <view class="grade-item {{selectedGrade === item ? 'active' : ''}}" 
                bindtap="onGradeChange" 
                data-grade="{{item}}">
            {{GRADE_LEVEL_MAP[item]}}
          </view>
        </block>
      </view>
    </scroll-view>
    
    <!-- 高中教材选择 -->
    <view wx:if="{{selectedStage === 'senior'}}" class="textbook-select-container">
      <!-- 教材类型选择 -->
      <scroll-view scroll-x enhanced show-scrollbar="{{false}}" class="grade-scroll">
        <view class="grade-list">
          <block wx:for="{{HIGH_SCHOOL_TEXTBOOKS}}" wx:key="{{index}}" wx:for-item="textbook" wx:for-index="typeKey">
            <view class="grade-item {{selectedTextbookType === typeKey ? 'active' : ''}}" 
                  bindtap="onTextbookTypeChange" 
                  data-type="{{typeKey}}">
              {{textbook.name}}
            </view>
          </block>
        </view>
      </scroll-view>
      
      <!-- 册次选择 -->
      <scroll-view scroll-x enhanced show-scrollbar="{{false}}" class="grade-scroll">
        <view class="grade-list">
          <block wx:for="{{HIGH_SCHOOL_TEXTBOOKS[selectedTextbookType].books}}" wx:key="nodeCodePrefix" wx:for-item="book">
            <view class="grade-item {{selectedBook.nodeCodePrefix === book.nodeCodePrefix ? 'active' : ''}}" 
                  bindtap="onBookSelect" 
                  data-index="{{index}}">
              {{book.name}}
            </view>
          </block>
        </view>
      </scroll-view>
    </view>
    

    
    <!-- 搜索框 -->
    <view class="search-container">
      <input class="search-input" 
             type="text"
             placeholder="搜索知识点..." 
             value="{{searchKeyword}}"
             bindinput="onSearch"
             confirm-type="search"
             placeholder-style="color: #999; font-size: 26rpx;" />
      <view class="search-icon">🔍</view>
    </view>
    
    <!-- 掌握状态筛选 -->
    <view class="mastery-filter-container">
      <scroll-view scroll-x enhanced show-scrollbar="{{false}}" class="mastery-filter-scroll">
        <view class="mastery-filter-list">
          <view class="mastery-filter-item {{selectedMasteryStatus === 'all' ? 'active' : ''}}" 
                bindtap="onMasteryStatusChange" 
                data-status="all">
            全部
          </view>
          <view class="mastery-filter-item {{selectedMasteryStatus === 'not_started' ? 'active' : ''}}" 
                bindtap="onMasteryStatusChange" 
                data-status="not_started">
            未开始
          </view>
          <view class="mastery-filter-item {{selectedMasteryStatus === 'weak' ? 'active' : ''}}" 
                bindtap="onMasteryStatusChange" 
                data-status="weak">
            薄弱
          </view>
          <view class="mastery-filter-item {{selectedMasteryStatus === 'learning' ? 'active' : ''}}" 
                bindtap="onMasteryStatusChange" 
                data-status="learning">
            学习中
          </view>
          <view class="mastery-filter-item {{selectedMasteryStatus === 'mastered' ? 'active' : ''}}" 
                bindtap="onMasteryStatusChange" 
                data-status="mastered">
            已掌握
          </view>
        </view>
      </scroll-view>
    </view>
  </view>
  
  <!-- 可滚动内容区域 -->
  <scroll-view scroll-y="true" class="scrollable-content" 
               scroll-top="{{scrollTop}}"
               refresher-enabled="{{true}}" 
               refresher-threshold="80" 
               bindrefresherrefresh="onRefresh"
               bindscroll="onScroll">
    <!-- 知识点区域 -->
    <view class="knowledge-section">
      <!-- 加载状态 -->
      <block wx:if="{{loading || isLoading}}">
        <view class="loading-container">
          <view class="loading-spinner"></view>
          <view class="loading-text">知识点加载中...</view>
        </view>
      </block>
      
      <!-- 知识点列表（按章节分组） -->
      <block wx:elif="{{chaptersData && chaptersData.length > 0}}">
        <view class="chapters-container">
          <block wx:for="{{chaptersData}}" wx:key="chapterNumber" wx:for-item="chapter">
            <!-- 章节标题 -->
            <view class="chapter-header {{chapter.isSpecialChapter ? 'special-chapter' : ''}}">
              <view class="chapter-title">
                <view class="chapter-name">{{chapter.displayTitle}}</view>
              </view>
              <view class="chapter-count">{{chapter.nodes.length}}个知识点</view>
            </view>
            
            <!-- 该章节的知识点 -->
            <view class="knowledge-grid-container">
              <view class="knowledge-grid">
                <block wx:for="{{chapter.nodes}}" wx:key="id" wx:for-item="node">
                  <view class="knowledge-card" bindtap="onNodeClick" data-node-id="{{node.id}}">
                    <!-- 小节号 -->
                    <view class="section-badge" wx:if="{{node.sectionNumber}}">{{node.sectionNumber}}</view>
                    
                    <!-- 右上角标签容器 -->
                    <view class="card-badges">
                      <!-- 掌握状态标签 -->
                      <view class="mastery-status {{MASTERY_STATUS_COLORS[node.masteryStatus]}}">
                        {{MASTERY_STATUS_TEXT[node.masteryStatus]}}
                      </view>
                      
                      <!-- 难度标签 -->
                      <view class="difficulty-tag difficulty-{{node.difficulty || 'intermediate'}}">
                        {{DIFFICULTY_LEVEL_TEXT[node.difficulty] || '中等'}}
                      </view>
                    </view>
                    
                    <!-- 卡片主体 -->
                    <view class="card-content">
                      <!-- 知识点名称 -->
                      <view class="card-name">{{node.name}}</view>
                      
                      <!-- 信息行 -->
                      <view class="card-info-row">
                        <!-- 重要程度 -->
                        <view class="importance-level" wx:if="{{node.importanceLevel}}">
                          重要度: {{node.importanceLevel}}/5
                        </view>
                        
                        <!-- 考试频率 -->
                        <view class="exam-frequency" wx:if="{{node.examFrequency}}">
                          考频: {{node.examFrequency === 'high' ? '高' : node.examFrequency === 'medium' ? '中' : '低'}}
                        </view>
                        
                        <!-- 掌握百分比 -->
                        <view class="mastery-percentage" wx:if="{{node.masteryPercentage > 0}}">
                          掌握: {{node.masteryPercentage}}%
                        </view>
                      </view>
                      
                      <!-- 能力要求标签 -->
                      <view class="ability-tags" wx:if="{{node.requiresMemorization || node.requiresUnderstanding || node.requiresApplication}}">
                        <text class="ability-tag" wx:if="{{node.requiresMemorization}}">记忆</text>
                        <text class="ability-tag" wx:if="{{node.requiresUnderstanding}}">理解</text>
                        <text class="ability-tag" wx:if="{{node.requiresApplication}}">应用</text>
                        <text class="ability-tag" wx:if="{{node.requiresAnalysis}}">分析</text>
                      </view>
                    </view>
                  </view>
                </block>
              </view>
            </view>
          </block>
        </view>
      </block>
      
      <!-- 空状态提示 -->
      <block wx:else>
        <view class="empty-state">
          <view class="empty-image">📚</view>
          <view class="empty-text">
            <view class="empty-title">暂无知识点数据</view>
            <view class="empty-desc">尝试选择其他年级或学科查看</view>
          </view>
          <view class="empty-action">
            <button class="action-button" bindtap="onRefresh">重新加载</button>
          </view>
        </view>
      </block>
    </view>
  </scroll-view>
 
  

  <!-- 遮罩层 -->
  <view class="mask-layer {{showDetail ? 'visible' : ''}}" 
        bindtap="closeDetail"></view>

  <!-- 知识点详情面板 -->
  <view class="detail-panel {{showDetail ? 'visible' : ''}}" 
        catchtap="stopPropagation" 
        catchtouchmove="preventTouchMove">
    <view class="detail-header">
      <view class="detail-title-container">
        <view class="detail-title">{{selectedNode.name || '知识点详情'}}</view>
        <view class="difficulty-tag difficulty-{{selectedNode.difficulty || 'intermediate'}}" wx:if="{{selectedNode.difficulty}}">
          {{DIFFICULTY_LEVEL_TEXT[selectedNode.difficulty] || '中等'}}
        </view>
      </view>
      <view class="detail-close" bindtap="closeDetail">✕</view>
    </view>
    
    <!-- 开始学习按钮区域 -->
    <view class="action-section">
      <button class="start-learn-button" bindtap="startLearning">
        <view class="button-icon">📚</view>
        <view class="button-text">开始学习</view>
      </button>
    </view>

    <scroll-view scroll-y="true" class="detail-content">
      <!-- 基本信息 -->
      <view class="detail-section">
        <view class="section-title">基本信息</view>
        <view class="info-grid">
          <view class="info-item">
            <view class="info-label">学科</view>
            <view class="info-value">{{SUBJECT_TEXT[selectedNode.subject] || selectedNode.subject}}</view>
          </view>
          <view class="info-item">
            <view class="info-label">年级</view>
            <view class="info-value">{{GRADE_LEVEL_MAP[selectedNode.gradeLevel] || selectedNode.gradeLevel}}</view>
          </view>
          <view class="info-item" wx:if="{{selectedNode.chapterTitle}}">
            <view class="info-label">章节</view>
            <view class="info-value">{{selectedNode.chapterTitle}}</view>
          </view>
          <view class="info-item" wx:if="{{selectedNode.sectionTitle}}">
            <view class="info-label">小节</view>
            <view class="info-value">{{selectedNode.sectionTitle}}</view>
          </view>
          <view class="info-item" wx:if="{{selectedNode.importanceLevel}}">
            <view class="info-label">重要程度</view>
            <view class="info-value">{{selectedNode.importanceLevel}}/5</view>
          </view>
          <view class="info-item" wx:if="{{selectedNode.estimatedTimeMinutes}}">
            <view class="info-label">预计学时</view>
            <view class="info-value">{{selectedNode.estimatedTimeMinutes}}分钟</view>
          </view>
        </view>
      </view>
      
      <!-- 掌握状态信息 -->
      <view class="detail-section">
        <view class="section-title">掌握状态</view>
        <view class="info-grid">
          <view class="info-item">
            <view class="info-label">当前状态</view>
            <view class="info-value status-value {{MASTERY_STATUS_COLORS[selectedNode.masteryStatus]}}">
              {{MASTERY_STATUS_TEXT[selectedNode.masteryStatus]}}
            </view>
          </view>
          <view class="info-item" wx:if="{{selectedNode.masteryPercentage > 0}}">
            <view class="info-label">掌握程度</view>
            <view class="info-value">{{selectedNode.masteryPercentage}}%</view>
          </view>
          <view class="info-item" wx:if="{{selectedNode.studyTimeMinutes > 0}}">
            <view class="info-label">学习时长</view>
            <view class="info-value">{{selectedNode.studyTimeMinutes}}分钟</view>
          </view>
          <view class="info-item" wx:if="{{selectedNode.practiceCount > 0}}">
            <view class="info-label">练习次数</view>
            <view class="info-value">{{selectedNode.practiceCount}}次</view>
          </view>
          <view class="info-item" wx:if="{{selectedNode.correctCount > 0 && selectedNode.practiceCount > 0}}">
            <view class="info-label">正确率</view>
            <view class="info-value">{{Math.round(selectedNode.correctCount / selectedNode.practiceCount * 100)}}%</view>
          </view>
          <view class="info-item" wx:if="{{selectedNode.lastStudyTime}}">
            <view class="info-label">最近学习</view>
            <view class="info-value">{{selectedNode.lastStudyTime}}</view>
          </view>
        </view>
      </view>
      
      <!-- 知识点描述 -->
      <view class="detail-section" wx:if="{{nodeContent && nodeContent.description}}">
        <view class="section-title">知识点描述</view>
        <view class="desc-content">{{nodeContent.description}}</view>
      </view>
      
      <!-- 详细解释 -->
      <view class="detail-section" wx:if="{{nodeContent && nodeContent.detailed_explanation}}">
        <view class="section-title">详细解释</view>
        <view class="desc-content">{{nodeContent.detailed_explanation}}</view>
      </view>
      
      <!-- 重点内容 -->
      <view class="detail-section" wx:if="{{nodeContent && nodeContent.key_points && nodeContent.key_points.length > 0}}">
        <view class="section-title">重点内容</view>
        <view class="key-points">
          <block wx:for="{{nodeContent.key_points}}" wx:key="*this">
            <view class="key-point-item">• {{item}}</view>
          </block>
        </view>
      </view>
      
      <!-- 记忆技巧 -->
      <view class="detail-section" wx:if="{{selectedNode.memoryTips}}">
        <view class="section-title">记忆技巧</view>
        <view class="desc-content">{{selectedNode.memoryTips}}</view>
      </view>
      
      <!-- 常见误区 -->
      <view class="detail-section" wx:if="{{selectedNode.commonMisconceptions && selectedNode.commonMisconceptions.length > 0}}">
        <view class="section-title">常见误区</view>
        <view class="misconceptions">
          <block wx:for="{{selectedNode.commonMisconceptions}}" wx:key="*this">
            <view class="misconception-item">⚠️ {{item}}</view>
          </block>
        </view>
      </view>
      
      <!-- 能力要求 -->
      <view class="detail-section">
        <view class="section-title">能力要求</view>
        <view class="ability-requirements">
          <view class="ability-tag {{selectedNode.requiresMemorization ? 'active' : ''}}">记忆</view>
          <view class="ability-tag {{selectedNode.requiresUnderstanding ? 'active' : ''}}">理解</view>
          <view class="ability-tag {{selectedNode.requiresApplication ? 'active' : ''}}">应用</view>
          <view class="ability-tag {{selectedNode.requiresAnalysis ? 'active' : ''}}">分析</view>
          <view class="ability-tag {{selectedNode.requiresSynthesis ? 'active' : ''}}">综合</view>
          <view class="ability-tag {{selectedNode.requiresEvaluation ? 'active' : ''}}">评价</view>
        </view>
      </view>
      
      <!-- 前置知识点 -->
      <view class="detail-section" wx:if="{{loadingRelations || (prerequisites && prerequisites.length > 0)}}">
        <view class="section-title">前置知识点</view>
        <block wx:if="{{loadingRelations}}">
          <view class="relation-loading">
            <view class="loading-spinner-small"></view>
            <view class="loading-text-small">正在加载相关知识点...</view>
          </view>
        </block>
        <view class="relation-list" wx:else>
          <view class="relation-item" 
                wx:for="{{prerequisites}}" 
                wx:key="id"
                bindtap="onRelatedNodeClick" 
                data-node-id="{{item.id}}">
            <view class="relation-name">{{item.name}}</view>
            <view class="relation-info">
              <text class="grade-text" wx:if="{{item.gradeLevel}}">{{GRADE_LEVEL_MAP[item.gradeLevel]}}</text>
              <text class="strength-text">关联度: {{item.strength >= 0.8 ? '强' : item.strength >= 0.5 ? '中' : '弱'}}</text>
            </view>
            <view class="relation-arrow">→</view>
          </view>
        </view>
      </view>
      
      <!-- 后续知识点 -->
      <view class="detail-section" wx:if="{{loadingRelations || (successors && successors.length > 0)}}">
        <view class="section-title">后续知识点</view>
        <block wx:if="{{loadingRelations}}">
          <view class="relation-loading">
            <view class="loading-spinner-small"></view>
            <view class="loading-text-small">正在加载相关知识点...</view>
          </view>
        </block>
        <view class="relation-list" wx:else>
          <view class="relation-item" 
                wx:for="{{successors}}" 
                wx:key="id"
                bindtap="onRelatedNodeClick" 
                data-node-id="{{item.id}}">
            <view class="relation-name">{{item.name}}</view>
            <view class="relation-info">
              <text class="grade-text" wx:if="{{item.gradeLevel}}">{{GRADE_LEVEL_MAP[item.gradeLevel]}}</text>
              <text class="strength-text">关联度: {{item.strength >= 0.8 ? '强' : item.strength >= 0.5 ? '中' : '弱'}}</text>
            </view>
            <view class="relation-arrow">→</view>
          </view>
        </view>
      </view>
      
      <!-- 相关知识点 -->
      <view class="detail-section" wx:if="{{loadingRelations || (related && related.length > 0)}}">
        <view class="section-title">相关知识点</view>
        <block wx:if="{{loadingRelations}}">
          <view class="relation-loading">
            <view class="loading-spinner-small"></view>
            <view class="loading-text-small">正在加载相关知识点...</view>
          </view>
        </block>
        <view class="relation-list" wx:else>
          <view class="relation-item" 
                wx:for="{{related}}" 
                wx:key="id"
                bindtap="onRelatedNodeClick" 
                data-node-id="{{item.id}}">
            <view class="relation-name">{{item.name}}</view>
            <view class="relation-info">
              <text class="grade-text" wx:if="{{item.gradeLevel}}">{{GRADE_LEVEL_MAP[item.gradeLevel]}}</text>
              <text class="relation-type">{{item.relationshipTypeText}}</text>
            </view>
            <view class="relation-arrow">→</view>
          </view>
        </view>
      </view>
      
      <!-- 应用知识点 -->
      <view class="detail-section" wx:if="{{loadingRelations || (applications && applications.length > 0)}}">
        <view class="section-title">应用知识点</view>
        <block wx:if="{{loadingRelations}}">
          <view class="relation-loading">
            <view class="loading-spinner-small"></view>
            <view class="loading-text-small">正在加载相关知识点...</view>
          </view>
        </block>
        <view class="relation-list" wx:else>
          <view class="relation-item" 
                wx:for="{{applications}}" 
                wx:key="id"
                bindtap="onRelatedNodeClick" 
                data-node-id="{{item.id}}">
            <view class="relation-name">{{item.name}}</view>
            <view class="relation-info">
              <text class="grade-text" wx:if="{{item.gradeLevel}}">{{GRADE_LEVEL_MAP[item.gradeLevel]}}</text>
            </view>
            <view class="relation-arrow">→</view>
          </view>
        </view>
      </view>
      
      <!-- 其他关系 -->
      <view class="detail-section" wx:if="{{loadingRelations || (others && others.length > 0)}}">
        <view class="section-title">其他关系</view>
        <block wx:if="{{loadingRelations}}">
          <view class="relation-loading">
            <view class="loading-spinner-small"></view>
            <view class="loading-text-small">正在加载相关知识点...</view>
          </view>
        </block>
        <view class="relation-list" wx:else>
          <view class="relation-item" 
                wx:for="{{others}}" 
                wx:key="id"
                bindtap="onRelatedNodeClick" 
                data-node-id="{{item.id}}">
            <view class="relation-name">{{item.name}}</view>
            <view class="relation-info">
              <text class="grade-text" wx:if="{{item.gradeLevel}}">{{GRADE_LEVEL_MAP[item.gradeLevel]}}</text>
              <text class="relation-type">{{item.relationshipTypeText}}</text>
            </view>
            <view class="relation-arrow">→</view>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 回到顶部按钮 -->
  <view class="back-to-top-btn {{showBackToTop ? 'show' : ''}}" 
        bindtap="scrollToTop">
    <view class="back-to-top-icon"></view>
  </view>
</view> 