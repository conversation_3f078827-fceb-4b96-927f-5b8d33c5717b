<wxs src="./graph-utils.wxs" module="graphUtils" />

<view class="container device-container {{ isIPad ? 'ipad-mode' : '' }} {{ isIphoneX ? 'iphonex-mode' : '' }}">
  <!-- 导航栏 -->
  <view class="nav-header" style="padding-top: {{statusBarHeight}}px;">
    <view class="nav-back" bindtap="goBack">
      <view class="icon icon-back icon-sm"></view>
    </view>
    <view class="nav-title">数学知识图谱</view>
  </view>
  
  <!-- 固定内容区域 -->
  <view class="fixed-content" style="margin-top: {{navBarHeight ? 0 : 10}}px;">
    <!-- 学段选择 -->
    <view class="stage-select">
      <block wx:for="{{['primary', 'junior', 'senior']}}" wx:key="*this" wx:for-item="stage">
        <view class="stage-item {{selectedStage === stage ? 'active' : ''}}" 
              bindtap="onStageChange" data-stage="{{stage}}">
          {{stage === 'primary' ? '小学' : stage === 'junior' ? '初中' : '高中'}}
        </view>
      </block>
    </view>
    
    <!-- 年级选择 -->
    <scroll-view scroll-x enhanced show-scrollbar="{{false}}" class="grade-scroll">
      <view class="grade-list">
        <block wx:for="{{grades}}" wx:key="*this">
          <view class="grade-item {{selectedGrade === item ? 'active' : ''}}" 
                bindtap="onGradeChange" 
                data-grade="{{item}}" 
                data-level="{{GRADE_LEVEL_MAP[item]}}">
            {{item}}
            <text class="grade-level">({{GRADE_LEVEL_MAP[item]}})</text>
          </view>
        </block>
      </view>
    </scroll-view>
    
    <!-- 知识点统计 -->
    <view class="status-panel">
      <view class="status-item {{activeStatus === 'mastered' ? 'active' : ''}}" bindtap="onStatusFilter" data-status="mastered">
        <view class="status-label mastered-color">已掌握</view>
        <view class="status-count mastered-color">{{masteredCount}}</view>
      </view>
      <view class="status-item {{activeStatus === 'learning' ? 'active' : ''}}" bindtap="onStatusFilter" data-status="learning">
        <view class="status-label learning-color">学习中</view>
        <view class="status-count learning-color">{{learningCount}}</view>
      </view>
      <view class="status-item {{activeStatus === 'weak' ? 'active' : ''}}" bindtap="onStatusFilter" data-status="weak">
        <view class="status-label weak-color">薄弱</view>
        <view class="status-count weak-color">{{weakCount}}</view>
      </view>
      <view class="status-item {{activeStatus === 'not-mastered' ? 'active' : ''}}" bindtap="onStatusFilter" data-status="not-mastered">
        <view class="status-label not-mastered-color">未掌握</view>
        <view class="status-count not-mastered-color">{{notMasteredCount}}</view>
      </view>
      <view class="status-item {{activeStatus === 'not-started' ? 'active' : ''}}" bindtap="onStatusFilter" data-status="not-started">
        <view class="status-label not-started-color">未开始</view>
        <view class="status-count not-started-color">{{notStartedCount}}</view>
      </view>
    </view>
    
    <!-- 标题栏 -->
    <view class="section-header">
      <view class="header-title">
        <view class="title-icon">
          <view class="icon icon-knowledge icon-sm"></view>
        </view>
        <text>{{(activeStatus === STATUS_MASTERED ? '已掌握知识点' : (activeStatus === STATUS_LEARNING ? '学习中知识点' : (activeStatus === STATUS_WEAK ? '薄弱知识点' : (activeStatus === STATUS_NOT_MASTERED ? '未掌握知识点' : (activeStatus === STATUS_NOT_STARTED ? '未开始知识点' : '全部知识点')))))}}</text>
      </view>
      <view class="header-actions">
        <view class="sort-btn" bindtap="onToggleSort">
          <view class="icon icon-sort icon-sm"></view>
          <text>{{sortOrderText}}</text>
        </view>
      </view>
    </view>
  </view>
  
  <!-- 可滚动内容区域 -->
  <scroll-view scroll-y="true" class="scrollable-content" 
               refresher-enabled="{{true}}" 
               refresher-threshold="80" 
               refresher-default-style="black" 
               refresher-background="#f4f4f4" 
               bindrefresherrefresh="onRefresh">
    <!-- 知识点区域 -->
    <view class="knowledge-section">
      <!-- 加载状态 -->
      <block wx:if="{{loading || isLoading}}">
        <view class="loading-container">
          <view class="loading-spinner"></view>
          <view class="loading-text">知识点加载中...</view>
        </view>
      </block>
      
      <!-- 知识点列表（按章节分组） -->
      <block wx:elif="{{chaptersData && chaptersData.length > 0}}">
        <view class="chapters-container">
          <block wx:for="{{chaptersData}}" wx:key="chapterNumber" wx:for-item="chapter">
            <!-- 章节标题 -->
            <view class="chapter-header {{chapter.isSpecialChapter ? 'special-chapter' : ''}}">
              <view class="chapter-title">
                <view class="chapter-name full-title">{{chapter.displayTitle}}</view>
              </view>
              <view class="chapter-count">{{chapter.nodes.length}}个知识点</view>
            </view>
            
            <!-- 该章节的知识点 -->
            <view class="knowledge-grid-container">
              <view class="knowledge-grid">
                <block wx:for="{{chapter.nodes}}" wx:key="id" wx:for-item="node">
                  <view class="knowledge-card" bindtap="onNodeClick" data-node-id="{{node.id}}">
                    <!-- 状态指示器 -->
                    <view class="card-status-indicator status-{{node.status}}"></view>
                    
                    <!-- 小节号（左上角显示） -->
                    <view class="section-badge" wx:if="{{node.section}}">{{node.section}}</view>
                    
                    <!-- 卡片主体 -->
                    <view class="card-content">
                      <!-- 知识点名称 -->
                      <view class="card-name">{{node.name}}</view>
                      
                      <!-- 难度标签（移至右上角） -->
                      <view class="difficulty-tag difficulty-{{node.difficulty || 'medium'}} difficulty-corner">
                        {{(node.difficulty === DIFFICULTY_EASY ? '简单' : (node.difficulty === DIFFICULTY_MEDIUM ? '中等' : (node.difficulty === DIFFICULTY_HARD ? '困难' : (node.difficulty === DIFFICULTY_EXPERT ? '挑战' : '中等'))))}}
                      </view>
                      
                      <!-- 信息行 -->
                      <view class="card-info-row">
                        <!-- 状态标签 -->
                        <view class="card-status status-{{node.status}}-text">
                          {{(node.status === STATUS_MASTERED ? '已掌握' : (node.status === STATUS_LEARNING ? '学习中' : (node.status === STATUS_WEAK ? '薄弱' : (node.status === STATUS_NOT_MASTERED ? '未掌握' : '未开始'))))}}
                        </view>
                      </view>
                      
                      <!-- 学习操作按钮 -->
                      <view class="card-actions">
                        <view class="action-btn {{(node.status === STATUS_MASTERED ? 'btn-mastered' : (node.status === STATUS_LEARNING ? 'btn-learning' : (node.status === STATUS_WEAK ? 'btn-weak' : (node.status === STATUS_NOT_MASTERED ? 'btn-not-mastered' : 'btn-not-started'))))}} full-width" 
                          catchtap="onStartLearningFromCard" 
                          data-node-id="{{node.id}}">
                          <text>{{(node.status === STATUS_MASTERED ? '巩固学习' : (node.status === STATUS_LEARNING ? '继续学习' : (node.status === STATUS_WEAK ? '加强学习' : (node.status === STATUS_NOT_MASTERED ? '重点攻坚' : '开始学习'))))}}</text>
                        </view>
                      </view>
                    </view>
                  </view>
                </block>
              </view>
            </view>
          </block>
        </view>
      </block>
      
      <!-- 空状态提示 -->
      <block wx:else>
        <view class="empty-state">
          <view class="empty-image">
            <view class="icon icon-empty-knowledge icon-xl"></view>
          </view>
          <view class="empty-text">
            <view class="empty-title">暂无{{activeStatus === STATUS_MASTERED ? '已掌握' : 
                                          activeStatus === STATUS_LEARNING ? '学习中' : 
                                          activeStatus === STATUS_WEAK ? '薄弱' : 
                                          activeStatus === STATUS_NOT_MASTERED ? '未掌握' : 
                                          activeStatus === STATUS_NOT_STARTED ? '未开始' : ''}}知识点</view>
            <view class="empty-desc">尝试选择其他状态或年级查看更多知识点</view>
          </view>
          <view class="empty-action">
            <button class="action-button" bindtap="resetFilters">重置筛选</button>
          </view>
        </view>
      </block>
    </view>
  </scroll-view>
 
  <!-- 遮罩层 -->
  <view class="mask-layer {{showDetail ? 'visible' : ''}}" 
        bindtap="closeDetail" 
        catchtouchmove="preventTouchMove"></view>

  <!-- 知识点详情面板 -->
  <view class="detail-panel {{showDetail ? 'visible' : ''}} {{selectedNode ? 'theme-' + selectedNode.status : ''}}" 
        catchtap="stopPropagation" 
        catchtouchmove="preventTouchMove">
    <view class="detail-header">
      <view class="detail-title-container">
        <view class="detail-title">{{selectedNode.name || '知识点详情'}}</view>
        <view class="difficulty-tag difficulty-{{selectedNode.difficulty || 'medium'}}" wx:if="{{selectedNode.difficulty}}">
          {{(selectedNode.difficulty === DIFFICULTY_EASY ? '简单' : (selectedNode.difficulty === DIFFICULTY_MEDIUM ? '中等' : (selectedNode.difficulty === DIFFICULTY_HARD ? '困难' : (selectedNode.difficulty === DIFFICULTY_EXPERT ? '挑战' : '中等'))))}}
        </view>
      </view>
      <view class="detail-close" bindtap="closeDetail">
        <view class="icon icon-close icon-sm"></view>
      </view>
    </view>
    
    <view class="detail-content">
      <!-- 调试信息 (开发阶段显示) -->
      <view class="debug-info" style="background: #f0f0f0; padding: 10px; margin: 10px 0; font-size: 12px; border-radius: 4px;">
        <text>调试信息:\n</text>
        <text>节点ID: {{selectedNode.id || '无'}}\n</text>
        <text>节点名称: {{selectedNode.name || '无'}}\n</text>
        <text>状态: {{selectedNode.status || '无'}}\n</text>
        <text>难度: {{selectedNode.difficulty || '无'}}\n</text>
        <text>描述: {{selectedNode.description || '无'}}\n</text>
        <text>关系数据: {{selectedNode.relations ? '已加载' : '未加载'}}\n</text>
        <text wx:if="{{selectedNode.relations}}">
          前置: {{selectedNode.relations.prerequisites.length || 0}}个
          后续: {{selectedNode.relations.progressions.length || 0}}个
          相关: {{selectedNode.relations.correlations.length || 0}}个
          应用: {{selectedNode.relations.applications.length || 0}}个
          其他: {{selectedNode.relations.others.length || 0}}个
        </text>
      </view>
      
      <!-- 知识点描述 -->
      <view class="detail-desc-section" wx:if="{{selectedNode.description}}">
        <view class="section-title">
          <text>知识点描述</text>
        </view>
        <view class="desc-content">{{ selectedNode.description || '暂无描述' }}</view>
      </view>
      
      <!-- 关系展示区：所有关系类型 -->
      <block wx:for="{{relationSections}}" wx:key="type" wx:for-item="section">
        <view class="relation-section" wx:if="{{selectedNode.relations[section.key].length > 0}}">
          <view class="relation-title {{section.type}}-title">
            <text>{{section.title}}</text>
          </view>
          <view class="relation-list">
            <view class="relation-item {{section.type}}-item {{item.isExternalNode ? 'external-node' : ''}}" 
                  wx:for="{{selectedNode.relations[section.key]}}" 
                  wx:key="id"
                  bindtap="onRelatedPointClick" 
                  data-point-id="{{item.id}}" 
                  data-point-name="{{item.name}}">
              <text class="relation-name">{{item.name}}</text>
              <view class="relation-info">
                <!--text class="relation-strength strength-{{item.strength}}">关联度:{{item.strength >= 0.9 ? '强' : item.strength >= 0.7 ? '中' : '弱'}}</text-->
                <text class="grade-text" wx:if="{{item.isExternalNode && item.gradeText}}">{{item.gradeText}} <text wx:if="{{item.level}}">({{item.level}})</text></text>
                <text class="status-text status-{{item.status}}-text" wx:if="{{item.status}}">
                  {{(item.status === STATUS_MASTERED ? '已掌握' : (item.status === STATUS_LEARNING ? '学习中' : (item.status === STATUS_WEAK ? '薄弱' : (item.status === STATUS_NOT_MASTERED ? '未掌握' : '未开始'))))}}
                </text>
                <!-- 显示年级信息（仅对跨年级节点） -->
              </view>
              <view class="icon icon-arrow-right"></view>
            </view>
          </view>
        </view>
      </block>
      
      <!-- 其他类型的关系 -->
      <block wx:if="{{selectedNode.relations.others && selectedNode.relations.others.length > 0}}">
        <view class="relation-section">
          <view class="relation-title others-title">
            <text>其他关系类型</text>
          </view>
          <view class="relation-list">
            <view class="relation-item others-item {{item.isExternalNode ? 'external-node' : ''}}" 
                  wx:for="{{selectedNode.relations.others}}" 
                  wx:key="id"
                  bindtap="onRelatedPointClick" 
                  data-point-id="{{item.id}}" 
                  data-point-name="{{item.name}}">
              <text class="relation-name">
                <text class="relation-type">{{item.relationTypeText || '相关'}}</text>: {{item.name}}
              </text>
              <view class="relation-info">
                <!-- text class="relation-strength strength-{{item.strength}}">关联度:{{item.strength >= 0.9 ? '强' : item.strength >= 0.7 ? '中' : '弱'}}</text-->
                <text class="grade-text" wx:if="{{item.isExternalNode && item.gradeText}}">{{item.gradeText}} <text wx:if="{{item.level}}">({{item.level}})</text></text>
                <text class="status-text status-{{item.status}}-text" wx:if="{{item.status}}">
                  {{(item.status === STATUS_MASTERED ? '已掌握' : (item.status === STATUS_LEARNING ? '学习中' : (item.status === STATUS_WEAK ? '薄弱' : (item.status === STATUS_NOT_MASTERED ? '未掌握' : '未开始'))))}}
                </text>
                <!-- 显示年级信息（仅对跨年级节点） -->
              </view>
              <view class="icon icon-arrow-right"></view>
            </view>
          </view>
        </view>
      </block>
    </view>
    
    <view class="detail-actions">
      <button class="action-button learn-button {{(selectedNode.status === STATUS_MASTERED ? 'btn-mastered' : (selectedNode.status === STATUS_LEARNING ? 'btn-learning' : (selectedNode.status === STATUS_WEAK ? 'btn-weak' : (selectedNode.status === STATUS_NOT_MASTERED ? 'btn-not-mastered' : 'btn-not-started'))))}}" 
        bindtap="onStartLearning">
        {{(selectedNode.status === STATUS_MASTERED ? '巩固学习' : (selectedNode.status === STATUS_LEARNING ? '继续学习' : (selectedNode.status === STATUS_WEAK ? '加强学习' : (selectedNode.status === STATUS_NOT_MASTERED ? '重点攻坚' : '开始学习'))))}}
      </button>
    </view>
  </view>
</view> 