<!-- 知识点详情页 - 重新设计 -->
<view class="page-container device-container" style="--safe-bottom: {{safeAreaBottom}}px;">
  
  <!-- 导航栏 - 与知识图谱页面一致 -->
  <view class="simple-navbar" style="padding-top: {{statusBarHeight}}px; height: {{navBarHeight + statusBarHeight}}px;">
    <view class="navbar-content" style="height: {{navBarHeight}}px;">
      <view class="back-btn" bindtap="onBack">
        <view class="icon icon-back icon-sm"></view>
      </view>
      <view class="nav-title">知识点详情</view>
      <view class="nav-actions">
        <view class="nav-btn" bindtap="toggleCollection">
          <view class="icon {{isCollected ? 'icon-heart-filled' : 'icon-heart'}} icon-sm"></view>
        </view>
      </view>
    </view>
  </view>
  
  <!-- 加载状态 -->
  <view wx:if="{{isLoading}}" class="loading-container">
    <view class="loading-content">
      <view class="loading-spinner"></view>
      <text class="loading-text">正在加载知识点内容...</text>
    </view>
  </view>
  
  <!-- 错误状态 -->
  <view wx:elif="{{isError}}" class="error-container">
    <view class="error-content">
      <view class="error-icon">⚠️</view>
      <text class="error-text">知识点加载失败</text>
      <view class="error-retry" bindtap="reloadData">重新加载</view>
    </view>
  </view>
  
  <!-- 主要内容区域 -->
  <view wx:elif="{{knowledgePoint}}" class="main-content">
    
    <!-- 知识点标题卡片 -->
    <view class="title-card">
      <view class="knowledge-title">
        {{knowledgePoint.node_name || knowledgePoint.title}}
        <text class="grade-badge">{{knowledgePoint.grade_level}}年级</text>
      </view>
      <view class="knowledge-desc">{{knowledgePoint.description}}</view>
      
      <!-- 知识掌握进度 - 仅在登录时显示 -->
      <view wx:if="{{isLoggedIn && masteryInfo}}" class="mastery-progress">
        <view class="progress-header">
          <text class="progress-label">知识掌握程度</text>
          <text class="progress-percentage">{{masteryInfo.mastery_percentage || 0}}%</text>
        </view>
        <view class="progress-bar">
          <view class="progress-fill" style="width: {{masteryInfo.mastery_percentage || 0}}%;"></view>
        </view>
        <view class="progress-details">
          <view class="progress-item {{masteryInfo.basic_concept_mastery ? 'mastered' : 'not-mastered'}}">
            <text class="progress-icon">{{masteryInfo.basic_concept_mastery ? '✓' : '✗'}}</text>
            <text class="progress-text">理论掌握</text>
          </view>
          <view class="progress-item {{masteryInfo.calculation_skill_mastery ? 'mastered' : 'learning'}}">
            <text class="progress-icon">{{masteryInfo.calculation_skill_mastery ? '✓' : '◐'}}</text>
            <text class="progress-text">计算应用</text>
          </view>
          <view class="progress-item {{masteryInfo.application_ability_mastery ? 'mastered' : 'not-mastered'}}">
            <text class="progress-icon">{{masteryInfo.application_ability_mastery ? '✓' : '✗'}}</text>
            <text class="progress-text">实际应用</text>
          </view>
        </view>
        <view class="progress-prediction">预计完全掌握还需{{masteryInfo.predicted_mastery_days || 0}}天</view>
      </view>
      
      <!-- 未登录提示 -->
      <view wx:if="{{!isLoggedIn}}" class="login-prompt">
        <view class="login-prompt-content">
          <text class="prompt-icon">👤</text>
          <text class="prompt-text">登录后追踪个人掌握进度</text>
          <view class="prompt-button" bindtap="goToLogin">立即登录</view>
        </view>
      </view>
      
      <!-- 快速信息 -->
      <view class="quick-info">
        <view class="info-item">
          <view class="info-label">应用广度</view>
          <view class="info-value">
            <text class="importance-stars">{{knowledgePoint.importance_stars || '☆☆☆☆☆'}}</text>
          </view>
        </view>
        <view class="info-item">
          <view class="info-label">学习时长</view>
          <view class="info-value">{{knowledgePoint.estimated_time_minutes || 30}}分钟</view>
        </view>
        <view class="info-item">
          <view class="info-label">考试频次</view>
          <view class="info-value exam-frequency">{{knowledgePoint.exam_frequency_display || '重要考点'}}</view>
        </view>
      </view>
    </view>
    
    <!-- AI助手快捷入口 -->
    <view class="ai-assistant">
      <view class="assistant-title">
        <view class="icon icon-ai-assistant icon-lg"></view>
        <text>AI智能助手</text>
      </view>
      
      <!-- 核心学习功能 -->
      <view class="assistant-actions">
        <view class="action-item" bindtap="startQuickExplanation">
          <view class="action-icon">
            <view class="icon icon-volume icon-md"></view>
          </view>
          <view class="action-text">快速精讲</view>
        </view>
        
        <view class="action-item" bindtap="startAIPractice">
          <view class="action-icon">
            <view class="icon icon-robot icon-md"></view>
          </view>
          <view class="action-text">智能训练</view>
        </view>
        
        <view class="action-item" bindtap="viewWeakPoints">
          <view class="action-icon">
            <view class="icon icon-weak-point icon-md"></view>
          </view>
          <view class="action-text">薄弱分析</view>
        </view>
        
        <view class="action-item" bindtap="startSyncHomework">
          <view class="action-icon">
            <view class="icon icon-homework icon-md"></view>
          </view>
          <view class="action-text">配套练习</view>
        </view>
      </view>
      
      <!-- 学习进度提示 - 仅在登录时显示 -->
      <view wx:if="{{isLoggedIn && masteryInfo}}" class="learning-status">
        <text class="status-text">当前掌握度 {{masteryInfo.mastery_percentage || 0}}% | 预计{{masteryInfo.predicted_mastery_days || 0}}天后完全掌握</text>
      </view>
      
      <!-- 未登录状态下的通用提示 -->
      <view wx:if="{{!isLoggedIn}}" class="learning-status">
        <text class="status-text">登录后获得个性化学习建议和进度追踪</text>
      </view>
    </view>
    
    <!-- 内容标签页 -->
    <view class="content-tabs">
      <view class="tab-header">
        <view wx:for="{{['核心要点', '深度解析', '实战应用', '拓展提升']}}" wx:key="*this"
              class="tab-item {{activeTab === item ? 'active' : ''}}"
              bindtap="switchTab" data-tab="{{item}}">
          {{item}}
        </view>
      </view>
      
      <view class="tab-content">
        <!-- 核心要点 -->
        <view wx:if="{{activeTab === '核心要点'}}" class="content-section">
          <view class="quick-master">
            <view class="core-points">
              <view class="section-title">
                <view class="title-icon">⚡</view>
                <text>核心要点</text>
              </view>
              <view class="point-item" wx:for="{{knowledgePoint.content.sections || []}}" wx:key="title">
                <view class="point-marker">{{index + 1}}</view>
                <view class="point-content">
                  <view class="point-title">{{item.title}}</view>
                  <view class="point-text">{{item.text}}</view>
                </view>
              </view>
            </view>
            
            <view class="memory-tips">
              <view class="section-title">
                <view class="title-icon">💡</view>
                <text>记忆要诀</text>
              </view>
              <view class="tip-card">
                <text class="tip-text">{{knowledgePoint.memory_tips || '理解核心概念，结合实际应用，多做练习巩固'}}</text>
              </view>
            </view>
          </view>
        </view>
        
        <!-- 深度解析 -->
        <view wx:elif="{{activeTab === '深度解析'}}" class="content-section">
          <view class="deep-understanding">
            <view class="section-title">
              <view class="title-icon">🔍</view>
              <text>深度解析</text>
            </view>
            <view class="principle-content">
              <text class="principle-text">{{knowledgePoint.detailed_explanation || '正在加载详细解析内容...'}}</text>
            </view>
            
            <!-- 常见误区展示 -->
            <view wx:if="{{knowledgePoint.common_misconceptions && knowledgePoint.common_misconceptions.length > 0}}" class="common-misconceptions">
              <view class="section-title">
                <view class="title-icon">⚠️</view>
                <text>常见误区</text>
              </view>
              <view class="misconception-item" wx:for="{{knowledgePoint.common_misconceptions}}" wx:key="*this">
                <text class="misconception-text">• {{item}}</text>
              </view>
            </view>
          </view>
        </view>
        
        <!-- 实战应用 -->
        <view wx:elif="{{activeTab === '实战应用'}}" class="content-section">
          <view class="practice-section">
            <view class="practice-header">
              <view class="practice-title">分层实战训练</view>
              <view class="practice-subtitle">AI根据掌握情况智能推荐</view>
            </view>
            
            <view class="practice-levels">
              <view class="level-item" wx:for="{{['基础理解', '应用提升', '综合拓展']}}" wx:key="*this">
                <view class="level-header">
                  <view class="level-title">{{item}}</view>
                  <view class="level-count">{{index === 0 ? '6' : index === 1 ? '8' : '4'}}题</view>
                </view>
                <view class="level-description">
                  {{index === 0 ? '掌握基本概念和简单计算' : index === 1 ? '提升实际应用和分析能力' : '挑战综合性复杂问题'}}
                </view>
                <view class="level-button" bindtap="startLevelPractice" data-level="{{index}}">
                  开始训练
                </view>
              </view>
            </view>
          </view>
        </view>
        
        <!-- 拓展提升 -->
        <view wx:elif="{{activeTab === '拓展提升'}}" class="content-section">
          <view class="extension-section">
            
            <!-- 学习技巧 -->
            <view wx:if="{{knowledgePoint.learningTips && knowledgePoint.learningTips.length > 0}}" class="learning-tips-section">
              <view class="section-title">
                <view class="title-icon">💡</view>
                <text>学习技巧</text>
              </view>
              <view class="tip-item" wx:for="{{knowledgePoint.learningTips}}" wx:key="index">
                <view class="tip-marker">{{index + 1}}</view>
                <view class="tip-content">
                  <view class="tip-title" wx:if="{{item.title}}">{{item.title}}</view>
                  <view class="tip-description">{{item.description || item}}</view>
                </view>
              </view>
            </view>
            
            <!-- 常见错误 -->
            <view wx:if="{{knowledgePoint.commonMistakes && knowledgePoint.commonMistakes.length > 0}}" class="common-mistakes-section">
              <view class="section-title">
                <view class="title-icon">⚠️</view>
                <text>常见错误</text>
              </view>
              <view class="mistake-item" wx:for="{{knowledgePoint.commonMistakes}}" wx:key="index">
                <view class="mistake-marker">❌</view>
                <view class="mistake-content">
                  <view class="mistake-title" wx:if="{{item.error}}">{{item.error}}</view>
                  <view class="mistake-reason" wx:if="{{item.reason}}">原因：{{item.reason}}</view>
                  <view class="mistake-correction" wx:if="{{item.correction}}">正确做法：{{item.correction}}</view>
                  <view wx:if="{{item.isString}}" class="mistake-text">{{item.text || item}}</view>
                </view>
              </view>
            </view>
            
            <!-- 多媒体资源 -->
            <view wx:if="{{hasMultimediaResources}}" class="multimedia-resources">
              <view class="section-title">
                <view class="title-icon">📱</view>
                <text>多媒体资源</text>
              </view>
              
              <!-- 视频资源 -->
              <view wx:if="{{knowledgePoint.videoUrls && knowledgePoint.videoUrls.length > 0}}" class="resource-group">
                <view class="resource-subtitle">📹 视频解析</view>
                <view class="resource-list">
                  <view class="resource-item" wx:for="{{knowledgePoint.videoUrls}}" wx:key="index">
                    <view class="resource-icon">🎥</view>
                    <view class="resource-info">
                      <view class="resource-title">{{item.title || '视频解析' + (index + 1)}}</view>
                      <view class="resource-desc" wx:if="{{item.description}}">{{item.description}}</view>
                    </view>
                    <view class="resource-action" bindtap="playVideo" data-url="{{item.url || item}}">观看</view>
                  </view>
                </view>
              </view>
              
              <!-- 动画演示 -->
              <view wx:if="{{knowledgePoint.animationUrls && knowledgePoint.animationUrls.length > 0}}" class="resource-group">
                <view class="resource-subtitle">🎬 动画演示</view>
                <view class="resource-list">
                  <view class="resource-item" wx:for="{{knowledgePoint.animationUrls}}" wx:key="index">
                    <view class="resource-icon">🎭</view>
                    <view class="resource-info">
                      <view class="resource-title">{{item.title || '动画演示' + (index + 1)}}</view>
                      <view class="resource-desc" wx:if="{{item.description}}">{{item.description}}</view>
                    </view>
                    <view class="resource-action" bindtap="playAnimation" data-url="{{item.url || item}}">演示</view>
                  </view>
                </view>
              </view>
              
              <!-- 音频资源 -->
              <view wx:if="{{knowledgePoint.audioUrls && knowledgePoint.audioUrls.length > 0}}" class="resource-group">
                <view class="resource-subtitle">🎵 音频解析</view>
                <view class="resource-list">
                  <view class="resource-item" wx:for="{{knowledgePoint.audioUrls}}" wx:key="index">
                    <view class="resource-icon">🎤</view>
                    <view class="resource-info">
                      <view class="resource-title">{{item.title || '音频解析' + (index + 1)}}</view>
                      <view class="resource-desc" wx:if="{{item.description}}">{{item.description}}</view>
                    </view>
                    <view class="resource-action" bindtap="playAudio" data-url="{{item.url || item}}">播放</view>
                  </view>
                </view>
              </view>
            </view>
            
            <!-- 参考资料 -->
            <view wx:if="{{knowledgePoint.referenceMaterials && knowledgePoint.referenceMaterials.length > 0}}" class="reference-materials">
              <view class="section-title">
                <view class="title-icon">📚</view>
                <text>参考资料</text>
              </view>
              <view class="reference-list">
                <view class="reference-item" wx:for="{{knowledgePoint.referenceMaterials}}" wx:key="index">
                  <view class="reference-icon">📖</view>
                  <view class="reference-content">
                    <view class="reference-title">{{item.title || '参考资料' + (index + 1)}}</view>
                    <view class="reference-author" wx:if="{{item.author}}">作者：{{item.author}}</view>
                    <view class="reference-desc" wx:if="{{item.description}}">{{item.description}}</view>
                    <view class="reference-link" wx:if="{{item.url}}" bindtap="openReference" data-url="{{item.url}}">查看详情</view>
                  </view>
                </view>
              </view>
            </view>
            
            <!-- 练习资源 -->
            <view wx:if="{{knowledgePoint.practiceResources && knowledgePoint.practiceResources.length > 0}}" class="practice-resources">
              <view class="section-title">
                <view class="title-icon">📝</view>
                <text>练习资源</text>
              </view>
              <view class="practice-list">
                <view class="practice-item" wx:for="{{knowledgePoint.practiceResources}}" wx:key="index">
                  <view class="practice-info">
                    <view class="practice-title">{{item.title || '练习题集' + (index + 1)}}</view>
                    <view class="practice-difficulty" wx:if="{{item.difficulty}}">难度：{{item.difficulty}}</view>
                    <view class="practice-count" wx:if="{{item.count}}">题目数量：{{item.count}}题</view>
                  </view>
                  <view class="practice-action" bindtap="startPractice" data-resource="{{item}}">开始练习</view>
                </view>
              </view>
            </view>
            
            <!-- 文理科差异化内容 -->
            <view wx:if="{{hasTrackSpecificContent}}" class="track-specific-content">
              <view class="section-title">
                <view class="title-icon">🎯</view>
                <text>学科应用</text>
              </view>
              
              <!-- 文科内容 -->
              <view wx:if="{{knowledgePoint.liberalArtsContent && knowledgePoint.liberalArtsContent.applications}}" class="track-content">
                <view class="track-subtitle">📖 文科应用</view>
                <view class="track-applications">
                  <view class="application-item" wx:for="{{knowledgePoint.liberalArtsContent.applications}}" wx:key="index">
                    <view class="app-title">{{item.subject || '文科应用'}}</view>
                    <view class="app-desc">{{item.description || item}}</view>
                  </view>
                </view>
              </view>
              
              <!-- 理科内容 -->
              <view wx:if="{{knowledgePoint.scienceContent && knowledgePoint.scienceContent.applications}}" class="track-content">
                <view class="track-subtitle">🔬 理科应用</view>
                <view class="track-applications">
                  <view class="application-item" wx:for="{{knowledgePoint.scienceContent.applications}}" wx:key="index">
                    <view class="app-title">{{item.subject || '理科应用'}}</view>
                    <view class="app-desc">{{item.description || item}}</view>
                  </view>
                </view>
              </view>
            </view>
            
            <!-- 默认内容（当数据库内容为空时） -->
            <view wx:if="{{!hasExtensionContent}}" class="default-extension">
              <view class="section-title">
                <view class="title-icon">🌟</view>
                <text>知识拓展</text>
              </view>
              <view class="default-content">
                <text class="default-text">该知识点的拓展内容正在完善中，敬请期待更多实用资源。</text>
              </view>
            </view>
            
          </view>
        </view>
      </view>
    </view>
    
    <!-- 知识关联 -->
    <view class="knowledge-relation">
      <view class="relation-header">
        <view class="relation-title">
          <view class="icon icon-network icon-sm"></view>
          <text class="relation-title-text">知识关联网络</text>
        </view>
        <view wx:if="{{isLoggedIn && masteryInfo}}" class="overall-progress">
          <text class="progress-text">整体掌握度 {{masteryInfo.mastery_percentage || 0}}%</text>
          <view class="mini-progress-bar">
            <view class="mini-progress-fill" style="width: {{masteryInfo.mastery_percentage || 0}}%;"></view>
          </view>
        </view>
      </view>
      
      <!-- 智能推荐区域 - 仅在登录时显示 -->
      <view wx:if="{{isLoggedIn}}" class="smart-recommendations">
        <view class="recommendation-header">
          <view class="rec-icon-wrapper">
            <view class="icon icon-ai icon-sm"></view>
          </view>
          <text class="recommendation-title-text">AI学习建议</text>
        </view>
        <view class="recommendation-cards">
          <view class="rec-card priority-high" wx:for="{{aiRecommendations || []}}" wx:key="id">
            <view class="rec-header">
              <view class="rec-title">{{item.title}}</view>
            </view>
            <view class="rec-subtitle">{{item.subtitle}}</view>
            <view class="rec-desc">{{item.description}}</view>
          </view>
          
          <!-- 默认推荐 -->
          <view wx:if="{{!aiRecommendations || aiRecommendations.length === 0}}" class="rec-card priority-high">
            <view class="rec-header">
              <view class="rec-title">个性化学习路径</view>
            </view>
            <view class="rec-subtitle">基于当前掌握情况</view>
            <view class="rec-desc">建议先完成基础概念理解，再进行实际应用练习</view>
          </view>
        </view>
      </view>
      
      <!-- 知识关联Tab导航 -->
      <view class="knowledge-tabs">
        <view class="knowledge-tab-header">
          <view wx:for="{{['前置知识', '后续知识', '相关知识', '拓展知识']}}" wx:key="*this"
                class="knowledge-tab-item {{knowledgeActiveTab === item ? 'active' : ''}}"
                bindtap="switchKnowledgeTab" data-tab="{{item}}">
            <view class="knowledge-tab-title">{{item}}</view> 
          </view>
        </view>
        
        <view class="knowledge-tab-content-wrapper">
          <!-- 前置知识 -->
          <view wx:if="{{knowledgeActiveTab === '前置知识'}}" class="knowledge-tab-content">
            <!-- 登录用户显示掌握状态统计 -->
            <view wx:if="{{isLoggedIn}}" class="tab-summary">
              <view class="summary-item">
                <text class="summary-label">掌握状况</text>
                <text class="summary-value success">良好</text>
              </view>
              <view class="summary-item">
                <text class="summary-label">学习建议</text>
                <text class="summary-value">定期复习巩固</text>
              </view>
            </view>
            
            <!-- 未登录用户显示通用提示 -->
            <view wx:else class="tab-summary">
              <view class="summary-item">
                <text class="summary-label">前置知识</text>
                <text class="summary-value info">{{prerequisites.length || 0}}个</text>
              </view>
              <view class="summary-item">
                <text class="summary-label">建议</text>
                <text class="summary-value">按顺序学习</text>
              </view>
            </view>
            
            <view class="knowledge-items">
              <view class="knowledge-item" wx:for="{{prerequisites}}" wx:key="id">
                <view class="knowledge-content" bind:tap="toggleKnowledgeDetail" data-id="{{item.id}}" data-type="prerequisite">
                  <view class="knowledge-main">
                    <view class="knowledge-name">{{item.name}}</view>
                    <view class="knowledge-tags">
                      <text class="tag tag-grade">{{item.grade}}年级</text>
                      <text class="tag tag-chapter">{{item.chapter}}</text>
                    </view>
                  </view>
                  <view class="knowledge-stats">
                    <!-- 登录用户显示掌握百分比 -->
                    <view wx:if="{{isLoggedIn}}" class="progress-circle">
                      <text class="progress-number">{{item.masteryRate}}%</text>
                    </view>
                    <!-- 未登录用户显示通用图标 -->
                    <view wx:else class="progress-circle" style="border-color: #e5e7eb;">
                      <text class="progress-number" style="color: #9ca3af; font-size: 16rpx;">未登录</text>
                    </view>
                  </view>
                </view>
                
                <view class="knowledge-detail" wx:if="{{item.showDetail}}">
                  <view class="detail-content">
                    <view class="detail-section">
                      <text class="detail-label">学习建议：</text>
                      <text class="detail-suggestion">{{item.suggestion}}</text>
                    </view>
                    
                    <view class="detail-section" wx:if="{{item.keyPoints}}">
                      <text class="detail-label">关键要点：</text>
                      <view class="key-points">
                        <text class="key-point-item" wx:for="{{item.keyPoints}}" wx:key="*this">• {{item}}</text>
                      </view>
                    </view>
                    
                    <view class="detail-section" wx:if="{{item.masteryTips}}">
                      <text class="detail-label">掌握技巧：</text>
                      <text class="mastery-tips">{{item.masteryTips}}</text>
                    </view>
                    
                    <view class="detail-meta">
                      <view class="meta-item">
                        <text class="meta-label">预计时长</text>
                        <text class="meta-value">{{item.learningTime}}</text>
                      </view>
                      <view class="meta-item" wx:if="{{item.practiceType}}">
                        <text class="meta-label">练习类型</text>
                        <text class="meta-value">{{item.practiceType}}</text>
                      </view>
                    </view>
                  </view>
                  
                  <view class="detail-actions">
                    <button class="action-btn primary" 
                            bind:tap="startLearning" 
                            data-knowledge-id="{{item.id}}" 
                            data-knowledge-code="{{item.code}}"
                            data-knowledge-name="{{item.name}}">立即学习</button>
                    <button class="action-btn secondary" 
                            bind:tap="addToReview" 
                            data-knowledge-id="{{item.id}}" 
                            data-knowledge-code="{{item.code}}"
                            data-knowledge-name="{{item.name}}">加入复习</button>
                  </view>
                </view>
              </view>
            </view>
          </view>
          
          <!-- 后续知识 -->
          <view wx:elif="{{knowledgeActiveTab === '后续知识'}}" class="knowledge-tab-content">
            <!-- 登录用户显示掌握状态统计 -->
            <view wx:if="{{isLoggedIn}}" class="tab-summary">
              <view class="summary-item">
                <text class="summary-label">准备状况</text>
                <text class="summary-value warning">待提升</text>
              </view>
              <view class="summary-item">
                <text class="summary-label">学习建议</text>
                <text class="summary-value">先打好基础，再进阶学习</text>
              </view>
            </view>
            
            <!-- 未登录用户显示通用提示 -->
            <view wx:else class="tab-summary">
              <view class="summary-item">
                <text class="summary-label">后续知识</text>
                <text class="summary-value info">{{subsequent.length || 0}}个</text>
              </view>
              <view class="summary-item">
                <text class="summary-label">建议</text>
                <text class="summary-value">循序渐进</text>
              </view>
            </view>
            
            <view class="knowledge-items">
              <view class="knowledge-item" wx:for="{{subsequent}}" wx:key="id">
                <view class="knowledge-content" bind:tap="toggleKnowledgeDetail" data-id="{{item.id}}" data-type="subsequent">
                  <view class="knowledge-main">
                    <view class="knowledge-name">{{item.name}}</view>
                    <view class="knowledge-tags">
                      <text class="tag tag-grade">{{item.grade}}年级</text>
                      <text class="tag tag-chapter">{{item.chapter}}</text>
                    </view>
                  </view>
                  <view class="knowledge-stats">
                    <!-- 登录用户显示掌握百分比 -->
                    <view wx:if="{{isLoggedIn}}" class="progress-circle">
                      <text class="progress-number">{{item.masteryRate}}%</text>
                    </view>
                    <!-- 未登录用户显示通用图标 -->
                    <view wx:else class="progress-circle" style="border-color: #e5e7eb;">
                      <text class="progress-number" style="color: #9ca3af; font-size: 16rpx;">未登录</text>
                    </view>
                  </view>
                </view>
                
                <view class="knowledge-detail" wx:if="{{item.showDetail}}">
                  <view class="detail-content">
                    <view class="detail-section">
                      <text class="detail-label">学习建议：</text>
                      <text class="detail-suggestion">{{item.suggestion}}</text>
                    </view>
                    
                    <view class="detail-section" wx:if="{{item.keyPoints}}">
                      <text class="detail-label">关键要点：</text>
                      <view class="key-points">
                        <text class="key-point-item" wx:for="{{item.keyPoints}}" wx:key="*this">• {{item}}</text>
                      </view>
                    </view>
                    
                    <view class="detail-section" wx:if="{{item.masteryTips}}">
                      <text class="detail-label">掌握技巧：</text>
                      <text class="mastery-tips">{{item.masteryTips}}</text>
                    </view>
                    
                    <view class="detail-meta">
                      <view class="meta-item">
                        <text class="meta-label">预计时长</text>
                        <text class="meta-value">{{item.learningTime}}</text>
                      </view>
                      <view class="meta-item" wx:if="{{item.practiceType}}">
                        <text class="meta-label">练习类型</text>
                        <text class="meta-value">{{item.practiceType}}</text>
                      </view>
                    </view>
                  </view>
                  
                  <view class="detail-actions">
                    <button class="action-btn primary" 
                            bind:tap="startLearning" 
                            data-knowledge-id="{{item.id}}" 
                            data-knowledge-code="{{item.code}}"
                            data-knowledge-name="{{item.name}}">开始学习</button>
                    <button class="action-btn secondary" 
                            bind:tap="addToReview" 
                            data-knowledge-id="{{item.id}}" 
                            data-knowledge-code="{{item.code}}"
                            data-knowledge-name="{{item.name}}">添加计划</button>
                  </view>
                </view>
              </view>
            </view>
          </view>
          
          <!-- 相关知识 -->
          <view wx:elif="{{knowledgeActiveTab === '相关知识'}}" class="knowledge-tab-content">
            <!-- 登录用户显示掌握状态统计 -->
            <view wx:if="{{isLoggedIn}}" class="tab-summary">
              <view class="summary-item">
                <text class="summary-label">掌握状况</text>
                <text class="summary-value warning">部分掌握</text>
              </view>
              <view class="summary-item">
                <text class="summary-label">学习建议</text>
                <text class="summary-value">可对比学习，建立知识网络</text>
              </view>
            </view>
            
            <!-- 未登录用户显示通用提示 -->
            <view wx:else class="tab-summary">
              <view class="summary-item">
                <text class="summary-label">相关知识</text>
                <text class="summary-value info">{{related.length || 0}}个</text>
              </view>
              <view class="summary-item">
                <text class="summary-label">建议</text>
                <text class="summary-value">对比学习</text>
              </view>
            </view>
            
            <view class="knowledge-items">
              <view class="knowledge-item" wx:for="{{related}}" wx:key="id">
                <view class="knowledge-content" bind:tap="toggleKnowledgeDetail" data-id="{{item.id}}" data-type="related">
                  <view class="knowledge-main">
                    <view class="knowledge-name">{{item.name}}</view>
                    <view class="knowledge-tags">
                      <text class="tag tag-grade">{{item.grade}}年级</text>
                      <text class="tag tag-chapter">{{item.chapter}}</text>
                    </view>
                  </view>
                  <view class="knowledge-stats">
                    <!-- 登录用户显示掌握百分比 -->
                    <view wx:if="{{isLoggedIn}}" class="progress-circle">
                      <text class="progress-number">{{item.masteryRate}}%</text>
                    </view>
                    <!-- 未登录用户显示通用图标 -->
                    <view wx:else class="progress-circle" style="border-color: #e5e7eb;">
                      <text class="progress-number" style="color: #9ca3af; font-size: 16rpx;">未登录</text>
                    </view>
                  </view>
                </view>
                
                <view class="knowledge-detail" wx:if="{{item.showDetail}}">
                  <view class="detail-content">
                    <view class="detail-section">
                      <text class="detail-label">学习建议：</text>
                      <text class="detail-suggestion">{{item.suggestion}}</text>
                    </view>
                    
                    <view class="detail-section" wx:if="{{item.keyPoints}}">
                      <text class="detail-label">关键要点：</text>
                      <view class="key-points">
                        <text class="key-point-item" wx:for="{{item.keyPoints}}" wx:key="*this">• {{item}}</text>
                      </view>
                    </view>
                    
                    <view class="detail-section" wx:if="{{item.masteryTips}}">
                      <text class="detail-label">掌握技巧：</text>
                      <text class="mastery-tips">{{item.masteryTips}}</text>
                    </view>
                    
                    <view class="detail-meta">
                      <view class="meta-item">
                        <text class="meta-label">预计时长</text>
                        <text class="meta-value">{{item.learningTime}}</text>
                      </view>
                      <view class="meta-item" wx:if="{{item.practiceType}}">
                        <text class="meta-label">练习类型</text>
                        <text class="meta-value">{{item.practiceType}}</text>
                      </view>
                    </view>
                  </view>
                  
                  <view class="detail-actions">
                    <button class="action-btn primary" 
                            bind:tap="startLearning" 
                            data-knowledge-id="{{item.id}}" 
                            data-knowledge-code="{{item.code}}"
                            data-knowledge-name="{{item.name}}">对比学习</button>
                    <button class="action-btn secondary" 
                            bind:tap="addToReview" 
                            data-knowledge-id="{{item.id}}" 
                            data-knowledge-code="{{item.code}}"
                            data-knowledge-name="{{item.name}}">加入复习</button>
                  </view>
                </view>
              </view>
            </view>
          </view>
          
          <!-- 拓展知识 -->
          <view wx:elif="{{knowledgeActiveTab === '拓展知识'}}" class="knowledge-tab-content">
            <!-- 登录用户显示掌握状态统计 -->
            <view wx:if="{{isLoggedIn}}" class="tab-summary">
              <view class="summary-item">
                <text class="summary-label">挑战程度</text>
                <text class="summary-value danger">较高</text>
              </view>
              <view class="summary-item">
                <text class="summary-label">学习建议</text>
                <text class="summary-value">基础扎实后再挑战拓展内容</text>
              </view>
            </view>
            
            <!-- 未登录用户显示通用提示 -->
            <view wx:else class="tab-summary">
              <view class="summary-item">
                <text class="summary-label">拓展知识</text>
                <text class="summary-value info">{{extended.length || 0}}个</text>
              </view>
              <view class="summary-item">
                <text class="summary-label">建议</text>
                <text class="summary-value">挑战性学习</text>
              </view>
            </view>
            
            <view class="knowledge-items">
              <view class="knowledge-item" wx:for="{{extended}}" wx:key="id">
                <view class="knowledge-content" bind:tap="toggleKnowledgeDetail" data-id="{{item.id}}" data-type="extended">
                  <view class="knowledge-main">
                    <view class="knowledge-name">{{item.name}}</view>
                    <view class="knowledge-tags">
                      <text class="tag tag-grade">{{item.grade}}年级</text>
                      <text class="tag tag-chapter">{{item.chapter}}</text>
                    </view>
                  </view>
                  <view class="knowledge-stats">
                    <!-- 登录用户显示掌握百分比 -->
                    <view wx:if="{{isLoggedIn}}" class="progress-circle">
                      <text class="progress-number">{{item.masteryRate}}%</text>
                    </view>
                    <!-- 未登录用户显示通用图标 -->
                    <view wx:else class="progress-circle" style="border-color: #e5e7eb;">
                      <text class="progress-number" style="color: #9ca3af; font-size: 16rpx;">未登录</text>
                    </view>
                  </view>
                </view>
                
                <view class="knowledge-detail" wx:if="{{item.showDetail}}">
                  <view class="detail-content">
                    <view class="detail-section">
                      <text class="detail-label">学习建议：</text>
                      <text class="detail-suggestion">{{item.suggestion}}</text>
                    </view>
                    
                    <view class="detail-section" wx:if="{{item.keyPoints}}">
                      <text class="detail-label">关键要点：</text>
                      <view class="key-points">
                        <text class="key-point-item" wx:for="{{item.keyPoints}}" wx:key="*this">• {{item}}</text>
                      </view>
                    </view>
                    
                    <view class="detail-section" wx:if="{{item.masteryTips}}">
                      <text class="detail-label">掌握技巧：</text>
                      <text class="mastery-tips">{{item.masteryTips}}</text>
                    </view>
                    
                    <view class="detail-meta">
                      <view class="meta-item">
                        <text class="meta-label">预计时长</text>
                        <text class="meta-value">{{item.learningTime}}</text>
                      </view>
                      <view class="meta-item" wx:if="{{item.practiceType}}">
                        <text class="meta-label">练习类型</text>
                        <text class="meta-value">{{item.practiceType}}</text>
                      </view>
                    </view>
                  </view>
                  
                  <view class="detail-actions">
                    <button class="action-btn primary" 
                            bind:tap="startLearning" 
                            data-knowledge-id="{{item.id}}" 
                            data-knowledge-code="{{item.code}}"
                            data-knowledge-name="{{item.name}}">挑战学习</button>
                    <button class="action-btn secondary" 
                            bind:tap="addToReview" 
                            data-knowledge-id="{{item.id}}" 
                            data-knowledge-code="{{item.code}}"
                            data-knowledge-name="{{item.name}}">设为目标</button>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
    
  </view>
  
  <!-- 底部安全区域 -->
  <view class="safe-bottom" style="height: {{safeAreaBottom}}px;"></view>
  
  <!-- 语音播放提示 -->
  <view wx:if="{{voicePlaying}}" class="voice-tip">
    <view class="voice-animation"></view>
    <text>{{voiceText}}</text>
  </view>
  
</view> 