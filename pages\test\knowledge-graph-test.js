Page({
  data: {
    testResults: [],
    loading: false,
    studentId: 12345 // 测试用学生ID
  },

  onLoad() {
    console.log('知识图谱云函数测试页面加载');
  },

  /**
   * 测试获取知识点列表
   */
  async testGetKnowledgeNodes() {
    this.setData({ loading: true });
    try {
      const result = await wx.cloud.callFunction({
        name: 'knowledge-graph-query',
        data: {
          action: 'getKnowledgeNodes',
          params: {
            subject: 'mathematics',
            gradeLevel: 7,
            limit: 10
          }
        }
      });
      
      this.addTestResult('获取知识点列表', result.result);
    } catch (error) {
      this.addTestResult('获取知识点列表', { error: error.message });
    }
    this.setData({ loading: false });
  },

  /**
   * 测试搜索知识点
   */
  async testSearchKnowledge() {
    this.setData({ loading: true });
    try {
      const result = await wx.cloud.callFunction({
        name: 'knowledge-graph-query',
        data: {
          action: 'searchKnowledge',
          params: {
            keyword: '一元二次方程',
            subject: 'mathematics',
            limit: 5
          }
        }
      });
      
      this.addTestResult('搜索知识点', result.result);
    } catch (error) {
      this.addTestResult('搜索知识点', { error: error.message });
    }
    this.setData({ loading: false });
  },

  /**
   * 测试获取年级知识点
   */
  async testGetKnowledgeByGrade() {
    this.setData({ loading: true });
    try {
      const result = await wx.cloud.callFunction({
        name: 'knowledge-graph-query',
        data: {
          action: 'getKnowledgeByGrade',
          params: {
            gradeLevel: 8,
            subject: 'mathematics',
            semester: 'first'
          }
        }
      });
      
      this.addTestResult('按年级获取知识点', result.result);
    } catch (error) {
      this.addTestResult('按年级获取知识点', { error: error.message });
    }
    this.setData({ loading: false });
  },

  /**
   * 测试获取学生掌握状态
   */
  async testGetStudentMastery() {
    this.setData({ loading: true });
    try {
      const result = await wx.cloud.callFunction({
        name: 'knowledge-graph-query',
        data: {
          action: 'getStudentMastery',
          params: {
            studentId: this.data.studentId,
            masteryStatus: 'learning'
          }
        }
      });
      
      this.addTestResult('获取学生掌握状态', result.result);
    } catch (error) {
      this.addTestResult('获取学生掌握状态', { error: error.message });
    }
    this.setData({ loading: false });
  },

  /**
   * 测试更新掌握状态
   */
  async testUpdateMasteryStatus() {
    this.setData({ loading: true });
    try {
      const result = await wx.cloud.callFunction({
        name: 'knowledge-graph-query',
        data: {
          action: 'updateMasteryStatus',
          params: {
            studentId: this.data.studentId,
            nodeId: 1001, // 假设的知识点ID
            masteryStatus: 'learning',
            masteryPercentage: 75,
            studyTimeMinutes: 30,
            isCorrect: true
          }
        }
      });
      
      this.addTestResult('更新掌握状态', result.result);
    } catch (error) {
      this.addTestResult('更新掌握状态', { error: error.message });
    }
    this.setData({ loading: false });
  },

  /**
   * 测试获取知识点关系
   */
  async testGetKnowledgeRelationships() {
    this.setData({ loading: true });
    try {
      const result = await wx.cloud.callFunction({
        name: 'knowledge-graph-query',
        data: {
          action: 'getKnowledgeRelationships',
          params: {
            nodeId: 1001, // 假设的知识点ID
            relationshipType: 'prerequisite'
          }
        }
      });
      
      this.addTestResult('获取知识点关系', result.result);
    } catch (error) {
      this.addTestResult('获取知识点关系', { error: error.message });
    }
    this.setData({ loading: false });
  },

  /**
   * 测试获取前置知识点
   */
  async testGetPrerequisites() {
    this.setData({ loading: true });
    try {
      const result = await wx.cloud.callFunction({
        name: 'knowledge-graph-query',
        data: {
          action: 'getPrerequisites',
          params: {
            nodeId: 1001 // 假设的知识点ID
          }
        }
      });
      
      this.addTestResult('获取前置知识点', result.result);
    } catch (error) {
      this.addTestResult('获取前置知识点', { error: error.message });
    }
    this.setData({ loading: false });
  },

  /**
   * 测试获取学习路径
   */
  async testGetLearningPath() {
    this.setData({ loading: true });
    try {
      const result = await wx.cloud.callFunction({
        name: 'knowledge-graph-query',
        data: {
          action: 'getLearningPath',
          params: {
            academicTrack: 'science',
            gradeLevel: 9,
            subject: 'mathematics'
          }
        }
      });
      
      this.addTestResult('获取学习路径', result.result);
    } catch (error) {
      this.addTestResult('获取学习路径', { error: error.message });
    }
    this.setData({ loading: false });
  },

  /**
   * 测试获取个性化推荐
   */
  async testGetRecommendations() {
    this.setData({ loading: true });
    try {
      const result = await wx.cloud.callFunction({
        name: 'knowledge-graph-query',
        data: {
          action: 'getRecommendations',
          params: {
            studentId: this.data.studentId,
            academicTrack: 'science',
            limit: 5
          }
        }
      });
      
      this.addTestResult('获取个性化推荐', result.result);
    } catch (error) {
      this.addTestResult('获取个性化推荐', { error: error.message });
    }
    this.setData({ loading: false });
  },

  /**
   * 测试获取知识点详细内容
   */
  async testGetKnowledgeContent() {
    this.setData({ loading: true });
    try {
      const result = await wx.cloud.callFunction({
        name: 'knowledge-graph-query',
        data: {
          action: 'getKnowledgeContent',
          params: {
            nodeId: 1001 // 假设的知识点ID
          }
        }
      });
      
      this.addTestResult('获取知识点详细内容', result.result);
    } catch (error) {
      this.addTestResult('获取知识点详细内容', { error: error.message });
    }
    this.setData({ loading: false });
  },

  /**
   * 运行全部测试
   */
  async runAllTests() {
    const tests = [
      'testGetKnowledgeNodes',
      'testSearchKnowledge', 
      'testGetKnowledgeByGrade',
      'testGetStudentMastery',
      'testUpdateMasteryStatus',
      'testGetKnowledgeRelationships',
      'testGetPrerequisites',
      'testGetLearningPath',
      'testGetRecommendations',
      'testGetKnowledgeContent'
    ];

    this.setData({ testResults: [] });
    
    for (const testName of tests) {
      await this[testName]();
      await this.sleep(1000); // 间隔1秒
    }
    
    wx.showToast({
      title: '所有测试完成',
      icon: 'success'
    });
  },

  /**
   * 清空测试结果
   */
  clearResults() {
    this.setData({ testResults: [] });
  },

  /**
   * 添加测试结果
   */
  addTestResult(testName, result) {
    const testResults = this.data.testResults;
    testResults.unshift({
      id: Date.now(),
      testName,
      result,
      timestamp: new Date().toLocaleString(),
      success: result.success !== false
    });
    this.setData({ testResults });
  },

  /**
   * 查看详细结果
   */
  viewDetail(e) {
    const { index } = e.currentTarget.dataset;
    const result = this.data.testResults[index];
    
    wx.showModal({
      title: result.testName,
      content: JSON.stringify(result.result, null, 2),
      showCancel: false,
      confirmText: '确定'
    });
  },

  /**
   * 辅助函数：延时
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}); 