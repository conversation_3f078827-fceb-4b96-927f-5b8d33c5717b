.circular-progress {
  position: relative;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.progress-ring-bg {
  position: absolute;
  top: 0;
  left: 0;
  border-radius: 50%;
}

.progress-ring {
  position: absolute;
  top: 0;
  left: 0;
  border-radius: 50%;
  transition: all 0.6s ease-out;
  transform: rotate(-90deg);
}

.progress-mask {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border-radius: 50%;
  background-color: #fff;
  z-index: 1;
}

.progress-text {
  position: relative;
  z-index: 2;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.progress-value {
  font-weight: bold;
  margin-bottom: 4rpx;
  line-height: 1;
}

.progress-label {
  font-size: 24rpx;
  color: #999;
  line-height: 1;
}

.circular-progress-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
}

.progress-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.progress-text-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 2;
}

.progress-value {
  font-weight: bold;
  line-height: 1.2;
}

.progress-label {
  font-weight: normal;
  opacity: 0.8;
  margin-top: 4rpx;
  line-height: 1;
} 