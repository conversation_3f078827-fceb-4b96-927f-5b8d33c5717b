.toast-container {
  position: fixed;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  z-index: 9999;
  opacity: 0;
}

.toast-top {
  top: 120rpx;
}

.toast-center {
  top: 50%;
  transform: translateY(-50%);
}

.toast-bottom {
  bottom: 120rpx;
}

.toast-content {
  max-width: 70%;
  padding: 20rpx 30rpx;
  background-color: rgba(0, 0, 0, 0.7);
  color: #fff;
  border-radius: 10rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.toast-icon {
  margin-bottom: 10rpx;
}

.toast-icon .icon {
  font-size: 48rpx;
  color: #fff;
}

.toast-text {
  font-size: 28rpx;
  line-height: 1.4;
  text-align: center;
} 