from insert_v2 import main
import json

# 测试SQL注入
test_json = '''[{
    "question_code": "TEST'; DROP TABLE test; --",
    "question_title": "恶意标题'; DELETE FROM users; --",
    "question_type": "single_choice",
    "subject": "mathematics",
    "grade_level": 7
}]'''

print("测试SQL注入防护...")
result = main(test_json)

if "insert_sql" in result:
    sql = result["insert_sql"]
    print("生成的SQL前200字符:")
    print(sql[:200])
    print("\n是否包含危险SQL:", "DROP TABLE" in sql or "DELETE FROM" in sql)
    print("是否正确转义:", "''" in sql)
else:
    print("SQL生成失败:", result) 