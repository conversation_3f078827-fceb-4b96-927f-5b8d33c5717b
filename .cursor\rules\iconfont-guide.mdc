---
description: 
globs: 
alwaysApply: true
---
# Iconfont图标使用规范

## 基本原则

- 使用iconfont开源图标库，不创建自定义图标文件
- 统一使用CSS背景图方式实现图标
- 遵循命名和大小规范，保持视觉一致性
- 图标必须有清晰的语义，增强用户体验

## 引入方式

在页面或组件的WXSS文件中通过以下方式引入图标样式：
```css
@import "../../styles/icons.wxss";
```
注意引入路径需要根据实际文件层级调整

## 使用方法

```html
<!-- 基础使用 -->
<view class="icon icon-home"></view>

<!-- 配合尺寸类 -->
<view class="icon icon-home icon-md"></view>

<!-- 配合颜色类 -->
<view class="icon icon-home icon-primary"></view>

<!-- 带无障碍标签的图标 -->
<view class="icon icon-home" aria-label="首页"></view>

<!-- 纯装饰性图标 -->
<view class="icon icon-decoration" aria-hidden="true"></view>
```

## 图标命名规范

- 基础类：`.icon` - 所有图标的基础类
- 具体图标：`.icon-{name}` - 例如：
  - `.icon-home` - 首页图标
  - `.icon-user` - 用户图标
  - `.icon-math` - 数学图标
  - `.icon-formula` - 公式图标

## 图标尺寸类

- `.icon-xs`: 24rpx - 用于辅助提示、标签等小型场景
- `.icon-sm`: 32rpx - 用于列表项、按钮内的图标
- `.icon-md`: 40rpx - 用于主导航、功能入口
- `.icon-lg`: 48rpx - 用于强调性图标、特殊功能按钮
- `.icon-xl`: 64rpx - 用于页面级图标、空状态提示

## 图标颜色类

- `.icon-primary`: 主色 #3E7BFA - 用于主要功能、积极操作
- `.icon-info`: 信息色 #10AEFF - 用于信息提示、引导
- `.icon-success`: 成功色 #09BB07 - 用于成功状态、完成提示
- `.icon-warning`: 警告色 #FFBE00 - 用于警告信息、需要注意
- `.icon-danger`: 危险色 #E64340 - 用于错误状态、危险操作

## 场景使用规范

### 导航场景
- 主导航图标使用 `.icon-md` 配合 `.icon-primary`
- 次级导航使用 `.icon-sm` 配合默认颜色
- 返回按钮统一使用 `.icon-back.icon-sm`

### 功能按钮
- 主要功能按钮使用 `.icon-md` 配合 `.icon-primary`
- 辅助功能使用 `.icon-sm` 配合对应语义色
- 禁用状态增加 `.icon-disabled` 类

### 列表场景
- 列表项图标统一使用 `.icon-sm`
- 右侧箭头统一使用 `.icon-arrow-right.icon-xs`
- 状态图标使用对应语义色

### 表单场景
- 输入框图标使用 `.icon-sm`
- 必填项标记使用 `.icon-required.icon-xs.icon-danger`
- 验证状态图标使用对应语义色

## 无障碍支持

为了支持无障碍访问，在使用图标时：
```html
<!-- 添加aria-label属性提供图标含义 -->
<view class="icon icon-home" aria-label="返回首页"></view>

<!-- 纯装饰性图标添加aria-hidden属性 -->
<view class="icon icon-decoration" aria-hidden="true"></view>
```

## 添加新图标流程

1. 在[iconfont.cn](mdc:https:/www.iconfont.cn)选择并下载SVG格式图标
2. 压缩和优化SVG代码
3. 将SVG转换为URL编码格式
4. 在[styles/icons.wxss](mdc:styles/icons.wxss)中添加新的图标类
5. 在相应页面中使用新图标

## 注意事项

- SVG代码必须URL编码后才能在CSS中使用
- 使用currentColor支持继承父元素颜色
- 图标尺寸通过尺寸类统一控制
- 确保SVG的viewBox属性设置正确
- 避免在一个元素上同时使用多个尺寸或颜色类
