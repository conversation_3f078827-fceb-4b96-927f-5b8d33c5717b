Component({
  /**
   * 组件的属性列表
   */
  properties: {
    knowledgeData: {
      type: Object,
      value: {}
    },
    showActions: {
      type: Boolean,
      value: true
    },
    expandable: {
      type: Boolean,
      value: true
    },
    initialExpanded: {
      type: Boolean,
      value: false
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    isExpanded: false,
    isCollected: false
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      // 初始展开状态设置
      this.setData({
        isExpanded: this.properties.initialExpanded
      });
      
      // 收藏状态初始化（实际项目中可能需要从缓存或服务端获取）
      if (this.properties.knowledgeData.isCollected) {
        this.setData({
          isCollected: true
        });
      }
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 展开/收起卡片
    toggleExpand() {
      if (!this.properties.expandable) return;
      
      this.setData({
        isExpanded: !this.data.isExpanded
      });
      
      this.triggerEvent('toggle', {
        isExpanded: this.data.isExpanded,
        knowledgeId: this.properties.knowledgeData.id
      });
    },
    
    // 收藏/取消收藏
    toggleCollect() {
      this.setData({
        isCollected: !this.data.isCollected
      });
      
      this.triggerEvent('collect', {
        isCollected: this.data.isCollected,
        knowledgeId: this.properties.knowledgeData.id
      });
    },
    
    // 点击查看详情
    onDetail() {
      this.triggerEvent('detail', {
        knowledgeId: this.properties.knowledgeData.id
      });
    },
    
    // 点击练习按钮
    onPractice() {
      this.triggerEvent('practice', {
        knowledgeId: this.properties.knowledgeData.id
      });
    },
    
    // 点击分享按钮
    onShare() {
      this.triggerEvent('share', {
        knowledgeId: this.properties.knowledgeData.id,
        title: this.properties.knowledgeData.title
      });
    },
    
    // 点击相关知识点
    onRelatedKnowledge(e) {
      const { id } = e.currentTarget.dataset;
      this.triggerEvent('related', {
        knowledgeId: id
      });
    },
    
    // 点击标签
    onLabelTap(e) {
      const { label } = e.currentTarget.dataset;
      this.triggerEvent('label', {
        label: label
      });
    },
    
    // 阻止事件冒泡
    preventBubble() {
      // 仅用于阻止事件冒泡
    }
  }
}); 