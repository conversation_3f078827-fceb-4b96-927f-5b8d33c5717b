// 智能诊断报告生成器 - 优化版 v3.0.0
// 增强了缓存、错误处理、性能监控和模板优化功能

/**
 * 优化版智能诊断报告生成器
 * 新增功能：
 * - 智能缓存系统
 * - 增强错误处理
 * - 性能监控
 * - 模板预编译
 * - 并行处理
 * - 内容验证
 */
class ReportGenerator {
  constructor() {
    // 优化版配置
    this.config = {
      enableCache: true,
      cacheTimeout: 10 * 60 * 1000, // 10分钟缓存
      maxCacheSize: 50,
      enableMetrics: true,
      enableValidation: true,
      enableParallelProcessing: true,
      maxRetries: 2,
      performanceThreshold: 3000 // 3秒性能阈值
    };

    // 缓存系统
    this.cache = new Map();
    
    // 性能指标
    this.metrics = {
      totalReports: 0,
      successfulReports: 0,
      failedReports: 0,
      avgGenerationTime: 0,
      cacheHitRate: 0,
      lastResetTime: Date.now()
    };

    // 预编译模板
    this.reportTemplates = this.initializeTemplates();
    
    // 验证规则
    this.validationRules = this.initializeValidationRules();
  }

  /**
   * 初始化报告模板
   * @returns {Object} 预编译的报告模板
   */
  initializeTemplates() {
    return {
      comprehensive: {
        sections: ['studentProfile', 'executiveSummary', 'detailedAnalysis', 'recommendations', 'visualizationData'],
        required: ['studentId', 'timestamp'],
        optional: ['parentGuidance', 'progressTracking']
      },
      quick: {
        sections: ['studentProfile', 'executiveSummary', 'recommendations'],
        required: ['studentId'],
        optional: []
      },
      detailed: {
        sections: ['studentProfile', 'executiveSummary', 'detailedAnalysis', 'recommendations', 'visualizationData', 'parentGuidance', 'progressTracking'],
        required: ['studentId', 'timestamp', 'analysisResults'],
        optional: []
      }
    };
  }

  /**
   * 初始化验证规则
   * @returns {Object} 数据验证规则
   */
  initializeValidationRules() {
    return {
      studentId: {
        required: true,
        type: 'string',
        minLength: 1
      },
      timestamp: {
        required: true,
        type: 'string',
        validator: (value) => !isNaN(Date.parse(value))
      },
      analysisResults: {
        required: false,
        type: 'object'
      }
    };
  }

  /**
   * 生成综合诊断报告 - 优化版
   * @param {Object} diagnosisData - 诊断数据
   * @param {Object} options - 生成选项
   * @returns {Object} 格式化的诊断报告
   */
  async generateComprehensiveReport(diagnosisData, options = {}) {
    const startTime = Date.now();
    const reportId = this.generateReportId();
    
    try {
      console.log(`[${reportId}] 开始生成综合诊断报告...`);
      
      // 确保timestamp存在
      if (!diagnosisData.timestamp) {
        diagnosisData.timestamp = new Date().toISOString();
      }
      
      // 输入验证
      this.validateDiagnosisData(diagnosisData);
      
      // 检查缓存
      const cacheKey = this.generateCacheKey(diagnosisData, options);
      if (this.config.enableCache && this.cache.has(cacheKey)) {
        const cachedReport = this.cache.get(cacheKey);
        if (Date.now() - cachedReport.timestamp < this.config.cacheTimeout) {
          console.log(`[${reportId}] 返回缓存的报告`);
          this.updateMetrics('cache_hit', Date.now() - startTime);
          return cachedReport.data;
        }
      }

      // 并行生成报告各部分
      const reportSections = await this.generateReportSectionsParallel(diagnosisData, reportId);
      
      // 组装完整报告
    const report = {
        reportId,
      studentId: diagnosisData.studentId,
        generatedAt: diagnosisData.timestamp || new Date().toISOString(),
        reportType: options.type || 'comprehensive',
        version: '3.0.0',
        generationTime: Date.now() - startTime,
        
        // 报告各部分
        ...reportSections,
        
        // 元数据
        metadata: {
          dataQuality: this.assessDataQuality(diagnosisData),
          completeness: this.calculateCompleteness(reportSections),
          confidence: this.calculateReportConfidence(diagnosisData),
          processingTime: Date.now() - startTime
        }
      };

      // 验证报告完整性
      if (this.config.enableValidation) {
        this.validateReport(report);
      }

      // 缓存报告
      if (this.config.enableCache) {
        this.setCache(cacheKey, report);
      }

      // 更新指标
      this.updateMetrics('success', Date.now() - startTime);
      
      console.log(`[${reportId}] 综合诊断报告生成完成，用时: ${Date.now() - startTime}ms`);
      return report;
      
    } catch (error) {
      this.updateMetrics('error', Date.now() - startTime);
      console.error(`[${reportId}] 报告生成失败:`, error);
      throw new Error(`报告生成失败: ${error.message}`);
    }
  }

  /**
   * 并行生成报告各部分 - 新增
   * @param {Object} diagnosisData - 诊断数据
   * @param {string} reportId - 报告ID
   * @returns {Object} 报告各部分
   */
  async generateReportSectionsParallel(diagnosisData, reportId) {
    if (!this.config.enableParallelProcessing) {
      return this.generateReportSectionsSequential(diagnosisData);
    }

    try {
      console.log(`[${reportId}] 并行生成报告各部分...`);
      
      const sectionPromises = [
        this.executeWithRetry(() => this.generateStudentProfile(diagnosisData), '学生概况'),
        this.executeWithRetry(() => this.generateExecutiveSummary(diagnosisData), '执行摘要'),
        this.executeWithRetry(() => this.generateDetailedAnalysis(diagnosisData), '详细分析'),
        this.executeWithRetry(() => this.generateRecommendations(diagnosisData), '推荐方案'),
        this.executeWithRetry(() => this.generateVisualizationData(diagnosisData), '可视化数据')
      ];

      const [
        studentProfile,
        executiveSummary,
        detailedAnalysis,
        recommendations,
        visualizationData
      ] = await Promise.all(sectionPromises);

      // 生成额外部分（非关键）
      const [parentGuidance, progressTracking] = await Promise.all([
        Promise.resolve(this.generateParentGuidanceSection(diagnosisData)).catch(error => {
          console.warn(`[${reportId}] 家长指导部分生成失败:`, error.message);
          return this.getDefaultParentGuidance();
        }),
        Promise.resolve(this.generateProgressTrackingPlan(diagnosisData)).catch(error => {
          console.warn(`[${reportId}] 进度跟踪部分生成失败:`, error.message);
          return this.getDefaultProgressTracking();
        })
      ]);

      return {
        studentProfile,
        executiveSummary,
        detailedAnalysis,
        recommendations,
        visualizationData,
        parentGuidance,
        progressTracking
      };
    } catch (error) {
      console.warn(`[${reportId}] 并行生成失败，回退到顺序生成:`, error.message);
      return this.generateReportSectionsSequential(diagnosisData);
    }
  }

  /**
   * 顺序生成报告各部分 - 新增
   * @param {Object} diagnosisData - 诊断数据
   * @returns {Object} 报告各部分
   */
  async generateReportSectionsSequential(diagnosisData) {
    return {
      studentProfile: this.generateStudentProfile(diagnosisData),
      executiveSummary: this.generateExecutiveSummary(diagnosisData),
      detailedAnalysis: this.generateDetailedAnalysis(diagnosisData),
      recommendations: this.generateRecommendations(diagnosisData),
      visualizationData: this.generateVisualizationData(diagnosisData),
      parentGuidance: this.generateParentGuidanceSection(diagnosisData),
      progressTracking: this.generateProgressTrackingPlan(diagnosisData)
    };
  }

  /**
   * 重试执行 - 新增
   * @param {Function} operation - 操作函数
   * @param {string} operationName - 操作名称
   * @returns {Promise} 执行结果
   */
  async executeWithRetry(operation, operationName) {
    let lastError;
    
    for (let attempt = 1; attempt <= this.config.maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error;
        console.warn(`${operationName} 第${attempt}次尝试失败:`, error.message);
        
        if (attempt < this.config.maxRetries) {
          await this.delay(500 * attempt); // 递增延迟
        }
      }
    }
    
    throw lastError;
  }

  /**
   * 延迟函数 - 新增
   * @param {number} ms - 延迟毫秒数
   * @returns {Promise} 延迟Promise
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 验证诊断数据 - 新增
   * @param {Object} diagnosisData - 诊断数据
   */
  validateDiagnosisData(diagnosisData) {
    if (!diagnosisData || typeof diagnosisData !== 'object') {
      throw new Error('诊断数据必须是一个有效的对象');
    }

    for (const [field, rule] of Object.entries(this.validationRules)) {
      if (rule.required && !(field in diagnosisData)) {
        throw new Error(`缺少必要字段: ${field}`);
      }

      if (field in diagnosisData) {
        const value = diagnosisData[field];
        
        if (rule.type && typeof value !== rule.type) {
          throw new Error(`字段 ${field} 类型错误，期望 ${rule.type}，实际 ${typeof value}`);
        }

        if (rule.minLength && value.length < rule.minLength) {
          throw new Error(`字段 ${field} 长度不足，最小长度 ${rule.minLength}`);
        }

        if (rule.validator && !rule.validator(value)) {
          throw new Error(`字段 ${field} 验证失败`);
        }
      }
    }
  }

  /**
   * 验证报告完整性 - 新增
   * @param {Object} report - 生成的报告
   */
  validateReport(report) {
    const requiredSections = ['reportId', 'studentId', 'generatedAt', 'studentProfile'];
    
    for (const section of requiredSections) {
      if (!(section in report)) {
        throw new Error(`报告缺少必要部分: ${section}`);
      }
    }

    if (!report.reportId || typeof report.reportId !== 'string') {
      throw new Error('报告ID无效');
    }

    if (report.generationTime && report.generationTime > this.config.performanceThreshold) {
      console.warn(`报告生成时间过长: ${report.generationTime}ms`);
    }
  }

  /**
   * 生成缓存键 - 新增
   * @param {Object} diagnosisData - 诊断数据
   * @param {Object} options - 选项
   * @returns {string} 缓存键
   */
  generateCacheKey(diagnosisData, options) {
    const keyData = {
      studentId: diagnosisData.studentId,
      timestamp: diagnosisData.timestamp,
      type: options.type || 'comprehensive',
      analysisHash: this.hashAnalysisData(diagnosisData.analysisResults)
    };
    
    return `report_${JSON.stringify(keyData)}`.replace(/[^\w]/g, '_');
  }

  /**
   * 哈希分析数据 - 新增
   * @param {Object} analysisData - 分析数据
   * @returns {string} 哈希值
   */
  hashAnalysisData(analysisData) {
    if (!analysisData) return 'no_analysis';
    
    const dataString = JSON.stringify(analysisData);
    let hash = 0;
    for (let i = 0; i < dataString.length; i++) {
      const char = dataString.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return hash.toString(36);
  }

  /**
   * 设置缓存 - 新增
   * @param {string} key - 缓存键
   * @param {Object} value - 缓存值
   */
  setCache(key, value) {
    // 清理过期缓存
    if (this.cache.size >= this.config.maxCacheSize) {
      this.cleanExpiredCache();
    }

    this.cache.set(key, {
      data: value,
      timestamp: Date.now()
    });
  }

  /**
   * 清理过期缓存 - 新增
   */
  cleanExpiredCache() {
    const now = Date.now();
    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > this.config.cacheTimeout) {
        this.cache.delete(key);
      }
    }

    // 如果还是太大，删除最老的条目
    if (this.cache.size >= this.config.maxCacheSize) {
      const entries = Array.from(this.cache.entries());
      const sortedEntries = entries.sort((a, b) => a[1].timestamp - b[1].timestamp);
      const toDelete = sortedEntries.slice(0, this.cache.size - this.config.maxCacheSize + 1);
      toDelete.forEach(([key]) => this.cache.delete(key));
    }
  }

  /**
   * 更新性能指标 - 新增
   * @param {string} operation - 操作类型
   * @param {number} duration - 执行时间
   */
  updateMetrics(operation, duration) {
    if (!this.config.enableMetrics) return;

    this.metrics.totalReports++;

    switch (operation) {
      case 'success':
        this.metrics.successfulReports++;
        break;
      case 'error':
        this.metrics.failedReports++;
        break;
      case 'cache_hit':
        // 缓存命中在其他地方处理
        break;
    }

    // 更新平均生成时间
    this.metrics.avgGenerationTime = 
      (this.metrics.avgGenerationTime + duration) / 2;

    // 计算缓存命中率
    const cacheHits = this.metrics.totalReports - this.metrics.successfulReports - this.metrics.failedReports;
    this.metrics.cacheHitRate = cacheHits / this.metrics.totalReports;
  }

  /**
   * 评估数据质量 - 新增
   * @param {Object} diagnosisData - 诊断数据
   * @returns {Object} 数据质量评估
   */
  assessDataQuality(diagnosisData) {
    let score = 1.0;
    const issues = [];

    if (!diagnosisData.analysisResults) {
      score -= 0.4;
      issues.push('缺少分析结果');
    }

    if (!diagnosisData.studentId) {
      score -= 0.3;
      issues.push('缺少学生ID');
    }

    if (!diagnosisData.timestamp) {
      score -= 0.2;
      issues.push('缺少时间戳');
    }

    return {
      score: Math.max(score, 0.1),
      level: score > 0.8 ? 'high' : score > 0.5 ? 'medium' : 'low',
      issues: issues
    };
  }

  /**
   * 计算完整性 - 新增
   * @param {Object} reportSections - 报告各部分
   * @returns {number} 完整性分数
   */
  calculateCompleteness(reportSections) {
    const totalSections = 7; // 总共7个主要部分
    const completedSections = Object.keys(reportSections).length;
    return completedSections / totalSections;
  }

  /**
   * 计算报告置信度 - 新增
   * @param {Object} diagnosisData - 诊断数据
   * @returns {number} 置信度分数
   */
  calculateReportConfidence(diagnosisData) {
    const dataQuality = this.assessDataQuality(diagnosisData);
    const hasAnalysisResults = diagnosisData.analysisResults ? 0.5 : 0;
    const hasTimestamp = diagnosisData.timestamp ? 0.3 : 0;
    const hasStudentId = diagnosisData.studentId ? 0.2 : 0;

    return Math.min(1.0, dataQuality.score * 0.5 + hasAnalysisResults + hasTimestamp + hasStudentId);
  }

  /**
   * 生成报告ID
   * @returns {string} 唯一报告ID
   */
  generateReportId() {
    const timestamp = Date.now().toString(36);
    const randomStr = Math.random().toString(36).substr(2, 5);
    return `DIAG_${timestamp}_${randomStr}`.toUpperCase();
  }

  /**
   * 生成学生概况
   * @param {Object} diagnosisData - 诊断数据
   * @returns {Object} 学生概况
   */
  generateStudentProfile(diagnosisData) {
    const analysisResults = diagnosisData.analysisResults || {};
    const knowledgeAnalysis = analysisResults.weaknessAnalysis || {};
    
    return {
      studentId: diagnosisData.studentId,
      gradeLevel: diagnosisData.gradeLevel || '未知',
      diagnosisDate: diagnosisData.timestamp || new Date().toISOString(),
      overallPerformance: {
        masteryLevel: knowledgeAnalysis.overallWeaknessLevel || 'medium',
        overallScore: knowledgeAnalysis.weaknessPoints ? 
          Math.round((1 - knowledgeAnalysis.weaknessPoints.length / 10) * 100) : 75,
        rank: this.calculatePerformanceRank(0.75),
        readinessForAdvancement: knowledgeAnalysis.weaknessPoints ? 
          knowledgeAnalysis.weaknessPoints.length < 3 : true
      }
    };
  }

  /**
   * 生成执行摘要
   * @param {Object} diagnosisData - 诊断数据
   * @returns {Object} 执行摘要
   */
  generateExecutiveSummary(diagnosisData) {
    const analysisResults = diagnosisData.analysisResults || {};
    const weaknessAnalysis = analysisResults.weaknessAnalysis || {};
    const aiAnalysis = analysisResults.aiEnhancedAnalysis || {};

    return {
      overallAssessment: this.generateOverallAssessment(weaknessAnalysis),
      keyFindings: {
        strengths: this.identifyKeyStrengths(aiAnalysis),
        challenges: this.identifyKeyChallenges(weaknessAnalysis),
        opportunities: this.identifyLearningOpportunities(diagnosisData)
      },
      criticalActions: this.generateCriticalActions(weaknessAnalysis),
      expectedOutcomes: this.generateExpectedOutcomes(diagnosisData)
    };
  }

  /**
   * 生成详细分析
   * @param {Object} diagnosisData - 诊断数据
   * @returns {Object} 详细分析
   */
  generateDetailedAnalysis(diagnosisData) {
    const analysisResults = diagnosisData.analysisResults || {};
    
    return {
      knowledgeMastery: this.formatKnowledgeAnalysis(analysisResults.weaknessAnalysis),
      cognitiveDevelopment: this.formatCognitiveAnalysis(analysisResults.aiEnhancedAnalysis),
      coreCompetencies: this.formatCompetencyAnalysis(analysisResults.aiEnhancedAnalysis),
      weaknessIdentification: this.formatWeaknessAnalysis(analysisResults.weaknessAnalysis)
    };
  }

  /**
   * 生成推荐方案
   * @param {Object} diagnosisData - 诊断数据
   * @returns {Object} 推荐方案
   */
  generateRecommendations(diagnosisData) {
    const analysisResults = diagnosisData.analysisResults || {};
    
    return {
      learningPath: this.formatLearningPath(analysisResults.pathRecommendation),
      personalizedStrategies: this.formatPersonalizedAdvice(analysisResults),
      actionPlan: this.generateActionPlan(diagnosisData)
    };
  }

  // ==================== 格式化方法 ====================

  /**
   * 格式化知识掌握分析
   * @param {Object} weaknessAnalysis - 薄弱点分析数据
   * @returns {Object} 格式化的知识分析
   */
  formatKnowledgeAnalysis(weaknessAnalysis) {
    const safeWeaknessAnalysis = weaknessAnalysis || {};
    const weaknessPoints = safeWeaknessAnalysis.weaknessPoints || [];
    
    return {
      summary: {
        totalKnowledgePoints: 20, // 假设总共20个知识点
        overallMasteryScore: Math.round((1 - weaknessPoints.length / 20) * 100),
        masteryLevel: this.translateMasteryLevel(safeWeaknessAnalysis.overallWeaknessLevel || 'medium'),
        strengthsCount: Math.max(0, 20 - weaknessPoints.length),
        weaknessesCount: weaknessPoints.length
      },
      strengthAreas: this.formatStrengthAreas(safeWeaknessAnalysis),
      weaknessAreas: this.formatWeaknessAreas(safeWeaknessAnalysis),
      masteryDistribution: this.calculateMasteryDistribution(safeWeaknessAnalysis),
      improvementPotential: this.calculateImprovementPotential(safeWeaknessAnalysis)
    };
  }

  /**
   * 格式化认知发展分析
   * @param {Object} aiAnalysis - AI分析数据
   * @returns {Object} 格式化的认知分析
   */
  formatCognitiveAnalysis(aiAnalysis) {
    const safeAiAnalysis = aiAnalysis || {};
    
    return {
      developmentStage: {
        current: this.translateDevelopmentStage('concrete_operational'),
        gradeLevel: 6,
        readinessForNext: true
      },
      cognitiveCapabilities: {
        abstractThinking: {
          score: 75,
          level: this.determineCapabilityLevel(0.75),
          description: this.getAbstractThinkingDescription(0.75)
        },
        logicalReasoning: {
          score: 80,
          level: this.determineCapabilityLevel(0.80),
          description: this.getLogicalReasoningDescription(0.80)
        },
        spatialVisualization: {
          score: 70,
          level: this.determineCapabilityLevel(0.70),
          description: this.getSpatialVisualizationDescription(0.70)
        },
        problemSolving: {
          score: 78,
          level: this.determineCapabilityLevel(0.78),
          description: this.getProblemSolvingDescription(0.78)
        }
      },
      developmentRecommendations: [
        '加强抽象思维练习',
        '增加空间想象训练',
        '培养逻辑推理能力'
      ]
    };
  }

  /**
   * 格式化核心素养分析
   * @param {Object} aiAnalysis - AI分析数据
   * @returns {Object} 格式化的素养分析
   */
  formatCompetencyAnalysis(aiAnalysis) {
    const competencies = {
      mathematical_thinking: {
        name: '数学思维',
        score: 75,
        level: 'developing',
        description: '数学思维能力正在发展中，需要继续强化'
      },
      problem_solving: {
        name: '问题解决',
        score: 80,
        level: 'proficient',
        description: '问题解决能力较强，能够独立解决大部分问题'
      },
      mathematical_communication: {
        name: '数学交流',
        score: 70,
        level: 'developing',
        description: '数学表达能力有待提升，需要加强数学语言的运用'
      }
    };

    const formattedCompetencies = {};
    for (const [key, competency] of Object.entries(competencies)) {
        formattedCompetencies[key] = {
        name: competency.name,
        score: competency.score,
        level: this.translateCompetencyLevel(competency.level),
        description: competency.description,
        priority: this.calculateCompetencyPriority(competency.score / 100),
        improvement: {
          target: Math.min(100, competency.score + 15),
          strategies: [`提升${competency.name}的具体策略`],
          timeline: '2-3个月'
        }
      };
    }
    
    return {
      overview: this.generateCompetencyOverview(competencies),
      detailedAnalysis: formattedCompetencies,
      developmentPlan: this.generateCompetencyDevelopmentPlan(competencies)
    };
  }

  /**
   * 格式化薄弱点分析
   * @param {Object} weaknessAnalysis - 薄弱点分析数据
   * @returns {Object} 格式化的薄弱点分析
   */
  formatWeaknessAnalysis(weaknessAnalysis) {
    const safeWeaknessAnalysis = weaknessAnalysis || {};
    const weaknessPoints = safeWeaknessAnalysis.weaknessPoints || [];
    
    return {
      summary: {
        totalWeaknesses: weaknessPoints.length,
        severityDistribution: this.calculateSeverityDistribution(weaknessPoints),
        overallWeaknessLevel: safeWeaknessAnalysis.overallWeaknessLevel || 'medium'
      },
      detailedWeaknesses: weaknessPoints.map(weakness => ({
        nodeId: weakness.nodeId,
        knowledgePoint: weakness.nodeId.replace('e6n', '知识点'),
        currentScore: weakness.currentScore || 0,
        severity: weakness.severity || 'medium',
        description: this.getWeaknessDescription(weakness),
        impact: this.assessWeaknessImpact(weakness),
        remediationStrategy: this.generateRemediationStrategy(weakness)
      })),
      prerequisiteGaps: this.identifyPrerequisiteGaps(weaknessPoints),
      rootCauseAnalysis: this.performRootCauseAnalysis(weaknessPoints),
      prioritizedActions: this.prioritizeWeaknessActions(weaknessPoints)
    };
  }

  /**
   * 格式化学习路径
   * @param {Object} pathRecommendation - 路径推荐数据
   * @returns {Object} 格式化的学习路径
   */
  formatLearningPath(pathRecommendation) {
    const safePath = pathRecommendation || {};
    
    return {
      overview: {
        totalSteps: safePath.learningPath?.steps?.length || 5,
        estimatedDuration: safePath.estimatedDuration || '4-6周',
        difficulty: safePath.difficulty || 'moderate',
        completion: 0
      },
      detailedPath: this.generateDetailedLearningSteps(safePath),
      milestones: this.generateMilestones(safePath),
      checkpoints: this.generateCheckpoints(safePath),
      adaptiveOptions: {
        canAccelerate: true,
        canSupplement: true,
        personalizationLevel: 'high'
      }
    };
  }

  /**
   * 格式化个性化建议
   * @param {Object} analysisResults - 分析结果
   * @returns {Object} 格式化的个性化建议
   */
  formatPersonalizedAdvice(analysisResults) {
    return {
      studyStrategies: [
        {
          category: '学习方法',
          recommendations: [
            '采用可视化学习方法，使用图表和模型帮助理解',
            '建立知识点之间的连接，形成知识网络',
            '定期复习，巩固已学知识'
          ]
        },
        {
          category: '练习策略',
          recommendations: [
            '从基础题开始，逐步提高难度',
            '重点练习薄弱知识点相关题目',
            '保持每日练习的习惯'
          ]
        }
      ],
      motivationTechniques: [
        '设置小目标，增加成就感',
        '记录学习进步，可视化成长',
        '适当奖励，保持学习兴趣'
      ],
      resourceRecommendations: {
        books: this.recommendBooks(analysisResults),
        apps: this.recommendApps(analysisResults),
        activities: this.recommendFamilyActivities(analysisResults)
      }
    };
  }

  /**
   * 生成行动计划
   * @param {Object} diagnosisData - 诊断数据
   * @returns {Object} 行动计划
   */
  generateActionPlan(diagnosisData) {
    const plan = {
      immediate: {
        timeframe: '1-2周',
        priorities: [],
        goals: [],
        actionItems: []
      },
      shortTerm: {
        timeframe: '1-2个月',
        priorities: [],
        goals: [],
        actionItems: []
      },
      longTerm: {
        timeframe: '3-6个月',
        priorities: [],
        goals: [],
        actionItems: []
      }
    };

    // 填充计划内容
    this.populateActionPlan(plan, diagnosisData);
    
    return plan;
  }

  /**
   * 生成可视化数据
   * @param {Object} diagnosisData - 诊断数据
   * @returns {Object} 可视化数据
   */
  generateVisualizationData(diagnosisData) {
    return {
      masteryChart: this.generateMasteryChartData(diagnosisData),
      cognitiveRadar: this.generateCognitiveRadarData(diagnosisData),
      competencyDashboard: this.generateCompetencyDashboardData(diagnosisData),
      progressTimeline: this.generateTimelineData(diagnosisData),
      learningHeatmap: this.generateProgressionHeatmapData(diagnosisData)
    };
  }

  /**
   * 生成家长指导部分
   * @param {Object} diagnosisData - 诊断数据
   * @returns {Object} 家长指导
   */
  generateParentGuidanceSection(diagnosisData) {
    return {
      understandingReport: {
        keyPoints: this.generateParentUnderstandingPoints(diagnosisData),
        commonConcerns: this.generateCommonConcerns(diagnosisData),
        interpretation: '报告解读指南'
      },
      supportStrategies: {
        dailySupport: this.generateDailySupportAdvice(diagnosisData),
        homeworkGuidance: this.generateHomeworkGuidance(diagnosisData),
        motivationTechniques: this.generateParentMotivationTechniques(diagnosisData)
      },
      communicationTips: {
        withTeacher: this.generateTeacherCommunicationTopics(diagnosisData),
        withChild: this.generateParentQuestions(diagnosisData)
      },
      resources: {
        books: this.recommendBooks(diagnosisData),
        apps: this.recommendApps(diagnosisData),
        activities: this.recommendFamilyActivities(diagnosisData)
      }
    };
  }

  /**
   * 生成进度跟踪计划
   * @param {Object} diagnosisData - 诊断数据
   * @returns {Object} 进度跟踪计划
   */
  generateProgressTrackingPlan(diagnosisData) {
    return {
      trackingMetrics: {
        keyIndicators: this.generateKeyMetrics(diagnosisData),
        assessmentMethods: this.generateAssessmentMethods(diagnosisData),
        frequency: 'weekly'
      },
      reportingSchedule: this.generateReportingSchedule(diagnosisData),
      adjustmentTriggers: this.generateAdjustmentTriggers(diagnosisData),
      successMilestones: [
        '基础知识点掌握达到80%',
        '薄弱点数量减少50%',
        '学习兴趣和信心提升'
      ]
    };
  }

  // ==================== 默认数据方法 ====================

  /**
   * 获取默认家长指导
   * @returns {Object} 默认家长指导内容
   */
  getDefaultParentGuidance() {
    return {
      understandingReport: {
        keyPoints: ['定期关注孩子学习进展', '保持与老师的沟通'],
        commonConcerns: ['如何帮助孩子提高数学成绩'],
        interpretation: '基础报告解读'
      },
      supportStrategies: {
        dailySupport: ['营造良好学习环境', '督促完成作业'],
        homeworkGuidance: ['检查作业完成情况', '适当提供帮助'],
        motivationTechniques: ['鼓励孩子', '适当奖励']
      }
    };
  }

  /**
   * 获取默认进度跟踪
   * @returns {Object} 默认进度跟踪内容
   */
  getDefaultProgressTracking() {
    return {
      trackingMetrics: {
        keyIndicators: ['学习时间', '作业完成率', '测试成绩'],
        assessmentMethods: ['定期测试', '作业评估'],
        frequency: 'weekly'
      },
      reportingSchedule: { weekly: '每周进度报告', monthly: '月度总结' }
    };
  }

  // ==================== 辅助方法 ====================

  calculatePerformanceRank(score) {
    if (score >= 0.9) return '优秀';
    if (score >= 0.8) return '良好';
    if (score >= 0.7) return '中等';
    return '需要提升';
  }

  translateMasteryLevel(level) {
    const translations = {
      'high': '掌握良好',
      'medium': '掌握一般',
      'low': '需要加强',
      'unknown': '待评估'
    };
    return translations[level] || '待评估';
  }

  translateDevelopmentStage(stage) {
    const translations = {
      'concrete_operational': '具体运算阶段',
      'formal_operational': '形式运算阶段',
      'unknown': '待评估'
    };
    return translations[stage] || '待评估';
  }

  determineCapabilityLevel(score) {
    if (score >= 0.8) return '熟练';
    if (score >= 0.6) return '发展中';
    return '需要强化';
  }

  translateCompetencyLevel(level) {
    const translations = {
      'proficient': '熟练',
      'developing': '发展中',
      'beginning': '起步',
      'advanced': '高级'
    };
    return translations[level] || '发展中';
  }

  // ==================== 简化实现的辅助方法 ====================

  generateOverallAssessment(analysis) { return '学生数学能力总体表现良好，在某些领域需要加强练习。'; }
  identifyKeyStrengths(analysis) { return ['计算能力较强', '基础概念掌握良好']; }
  identifyKeyChallenges(analysis) { return ['应用题理解需要提升', '解题步骤需要规范']; }
  identifyLearningOpportunities(data) { return ['可以尝试更有挑战性的题目', '培养数学思维能力']; }
  generateCriticalActions(analysis) { return ['加强薄弱知识点练习', '提高解题准确率']; }
  generateExpectedOutcomes(data) { return ['预期2-3个月内数学成绩有明显提升']; }
  formatStrengthAreas(analysis) { return [{ area: '基础运算', score: 85 }]; }
  formatWeaknessAreas(analysis) { return [{ area: '应用题', score: 65 }]; }
  calculateMasteryDistribution(analysis) { return { excellent: 30, good: 40, average: 20, poor: 10 }; }
  calculateImprovementPotential(analysis) { return { high: 2, medium: 3, low: 1 }; }
  getAbstractThinkingDescription(score) { return '抽象思维能力处于年级平均水平'; }
  getLogicalReasoningDescription(score) { return '逻辑推理能力较强'; }
  getSpatialVisualizationDescription(score) { return '空间想象能力需要加强'; }
  getProblemSolvingDescription(score) { return '问题解决能力良好'; }
  generateCompetencyOverview(analysis) { return { summary: '核心素养发展均衡' }; }
  generateCompetencyDevelopmentPlan(analysis) { return { plan: '制定个性化素养发展计划' }; }
  calculateCompetencyPriority(score) { return score < 0.6 ? 'high' : score < 0.8 ? 'medium' : 'low'; }
  getWeaknessDescription(weakness) { return `${weakness.nodeId} 知识点掌握不够牢固`; }
  assessWeaknessImpact(weakness) { return 'medium'; }
  generateRemediationStrategy(weakness) { return '针对性练习和复习'; }
  calculateSeverityDistribution(weaknesses) { return { high: 2, medium: 3, low: 1 }; }
  identifyPrerequisiteGaps(weaknesses) { return []; }
  performRootCauseAnalysis(weaknesses) { return { primaryCause: '基础知识不够扎实' }; }
  prioritizeWeaknessActions(weaknesses) { return []; }
  generateDetailedLearningSteps(path) { return []; }
  generateMilestones(data) { return ['第一阶段完成', '第二阶段完成']; }
  generateCheckpoints(data) { return ['每周检查', '每月评估']; }
  populateActionPlan(plan, data) { 
    plan.immediate.actionItems = ['复习基础概念', '完成练习题'];
    plan.shortTerm.actionItems = ['提高解题速度', '加强应用能力'];
    plan.longTerm.actionItems = ['培养数学思维', '提升综合能力'];
  }
  generateMasteryChartData(data) { return { labels: [], data: [] }; }
  generateCognitiveRadarData(data) { return { axes: [], values: [] }; }
  generateCompetencyDashboardData(data) { return { competencies: [] }; }
  generateTimelineData(data) { return { timeline: [] }; }
  generateProgressionHeatmapData(data) { return { heatmap: [] }; }
  generateParentUnderstandingPoints(data) { return ['理解孩子的学习特点', '支持孩子的学习需求']; }
  generateCommonConcerns(data) { return ['如何提高孩子学习兴趣', '如何帮助孩子克服学习困难']; }
  generateDailySupportAdvice(data) { return ['创造安静的学习环境', '定期检查学习进度']; }
  generateHomeworkGuidance(data) { return ['督促按时完成作业', '检查作业质量']; }
  generateParentMotivationTechniques(data) { return ['适当表扬鼓励', '设置学习目标和奖励']; }
  generateTeacherCommunicationTopics(data) { return ['讨论孩子的学习进展', '了解课堂表现']; }
  generateParentQuestions(data) { return ['孩子的学习兴趣如何?', '在家如何更好地辅导?']; }
  recommendBooks(data) { return ['《小学数学思维训练》', '《趣味数学故事》']; }
  recommendApps(data) { return ['数学练习APP', '智能学习助手']; }
  recommendFamilyActivities(data) { return ['数学游戏', '生活中的数学应用']; }
  generateKeyMetrics(data) { return ['学习时间', '完成率', '准确率']; }
  generateAssessmentMethods(data) { return ['定期测试', '作业评价', '课堂表现']; }
  generateReportingSchedule(data) { return { weekly: '每周报告', monthly: '月度总结' }; }
  generateAdjustmentTriggers(data) { return ['成绩显著下降', '学习兴趣明显降低']; }

  /**
   * 获取系统指标 - 新增
   * @returns {Object} 系统性能指标
   */
  getMetrics() {
    return {
      ...this.metrics,
      cacheSize: this.cache.size,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * 清理缓存 - 新增
   * @returns {Object} 清理结果
   */
  clearCache() {
    const cacheSize = this.cache.size;
    this.cache.clear();
    
    return {
      success: true,
      clearedEntries: cacheSize,
      timestamp: new Date().toISOString()
    };
  }
}

module.exports = ReportGenerator; 