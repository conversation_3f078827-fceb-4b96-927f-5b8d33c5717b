---
description: 
globs: 
alwaysApply: false
---
# 数学学习路径规范

本项目实现了系统化的K12数学学习路径，确保学生能够循序渐进地掌握数学知识。

## 知识点体系

- 基于中国义务教育数学课程标准，分为小学和初中两大阶段
- 每个年级的知识点按教材章节组织
- 知识点之间建立前后依赖关系，形成完整的知识图谱

## 学习路径设计

- **基础路径**：按教材章节顺序学习
- **能力路径**：按数学能力维度（计算能力、空间想象、推理能力等）组织学习内容
- **专题路径**：围绕特定专题（如几何、方程、函数等）深入学习
- **薄弱点强化路径**：基于学生薄弱环节自动生成的个性化学习路径

## 难度分级

- 基础题（覆盖教材基本要求）
- 提高题（适度拓展，培养应用能力）
- 挑战题（较高难度，发展思维能力）
- 奥数题（高难度思维拓展）

## 内容形式

- **讲解内容**：知识点概念解释、例题讲解、重点难点分析
- **练习内容**：基础巩固题、能力提升题、综合应用题
- **测试内容**：单元测试、阶段性评估、模拟考试

## 实现参考

- 知识点数据模型：[models/knowledge-point.js](mdc:models/knowledge-point.js)
- 学习路径配置：[data/learning-paths.js](mdc:data/learning-paths.js)
- 学生进度模型：[models/student-model.js](mdc:models/student-model.js)
- 路径推荐算法：[utils/path-recommendation.js](mdc:utils/path-recommendation.js)
