/* 返回顶部按钮样式 */
.back-to-top {
  position: fixed;
  right: 30rpx;
  bottom: -80rpx;
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
  z-index: 99;
  opacity: 0;
  transition: all 0.3s ease;
}

.back-to-top-show {
  opacity: 1;
  transform: translateY(0);
}

/* 添加按下效果 */
.back-to-top:active {
  background-color: rgba(255, 255, 255, 0.7);
  transform: scale(0.95);
}

/* 向上箭头样式 */
.arrow-up {
  width: 20rpx;
  height: 20rpx;
  border-top: 6rpx solid #3E7BFA;
  border-right: 6rpx solid #3E7BFA;
  transform: rotate(-45deg);
  margin-top: 10rpx;
} 