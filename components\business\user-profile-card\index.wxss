/* 导入公共样式和图标 */
@import "/styles/common.wxss";
@import "/styles/icons.wxss";

/* 顶部卡片区域 */
.profile-header {
  padding: 20rpx 20rpx;
  margin-top: 6rpx;
  margin-bottom: 10rpx;
}

/* 用户信息卡片基础样式 */
.profile-card {
  width: 100%;
  background-color: #fff;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

/* 访客卡片样式 - 全新设计 */
.profile-card--guest {
  position: relative;
  display: flex;
  flex-direction: column;
  padding: 0;
}

/* 顶部横幅 */
.login-banner {
  background: linear-gradient(135deg, #4776E6, #8E54E9);
  padding: 30rpx 40rpx;
  color: #fff;
  position: relative;
  z-index: 1;
}

.login-banner::after {
  content: "";
  position: absolute;
  right: 0;
  bottom: 0;
  width: 160rpx;
  height: 160rpx;
  background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M95 5C50 5 5 50 5 95' stroke='rgba(255,255,255,0.2)' stroke-width='10' stroke-linecap='round'/%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  opacity: 0.5;
  z-index: 0;
}

.login-title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
  position: relative;
  z-index: 1;
}

.login-description {
  font-size: 24rpx;
  opacity: 0.9;
  position: relative;
  z-index: 1;
}

/* 登录内容区域 */
.login-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx 32rpx;
  position: relative;
}

.login-avatar {
  margin-bottom: 24rpx;
}

.avatar-circle {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  background-color: #f0f2f5;
  display: flex;
  justify-content: center;
  align-items: center;
}

.avatar-circle .icon {
  color: #8E54E9;
}

.login-message {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 32rpx;
}

.login-button {
  width: 60%;
  height: 80rpx;
  border-radius: 40rpx;
  background: linear-gradient(135deg, #4776E6, #8E54E9);
  color: #fff;
  font-size: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  border: none;
  padding: 0;
}

.login-button::after {
  display: none;
}

.login-button .icon {
  margin-left: 6rpx;
}

/* 用户卡片样式 */
.profile-card--user {
  padding: 0;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  overflow: hidden;
  background: linear-gradient(135deg, #4776E6, #8E54E9);
  position: relative;
  color: #ffffff;
}

.profile-card__main {
  padding: 24rpx 30rpx;
  display: flex;
  align-items: center;
  position: relative;
}

.profile-card__avatar-container {
  position: relative;
}

.profile-card__avatar {
  width: 104rpx;
  height: 104rpx;
  border-radius: 52rpx;
  background: rgba(255, 255, 255, 0.25);
  color: #fff;
  font-size: 48rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.15);
  transition: none;
  border: 2rpx solid rgba(255, 255, 255, 0.5);
}

.profile-card__avatar:active {
  transform: none;
}

.profile-card__info {
  flex: 1;
  margin-left: 24rpx;
  overflow: hidden;
}

/* 用户信息头部区域 */
.profile-card__header {
  margin-bottom: 10rpx;
}

/* 用户名和等级行 */
.profile-card__name-row {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.profile-card__name {
  font-size: 36rpx;
  font-weight: bold;
  color: #ffffff;
}

/* 等级和进度条样式 */
.profile-card__level-row {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.profile-card__level-tag {
  display: inline-block;
  padding: 4rpx 12rpx;
  background-color: rgba(255, 255, 255, 0.25);
  color: white;
  border-radius: 12rpx;
  font-size: 22rpx;
  font-weight: bold;
  margin-right: 12rpx;
  transition: none;
  position: relative;
  overflow: hidden;
}

.profile-card__level-tag:active {
  transform: none;
  background-color: rgba(255, 255, 255, 0.25);
}

.profile-card__level-tag::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 120%;
  height: 120%;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  transform: translate(-50%, -50%) scale(0);
  opacity: 0;
  transition: none;
}

.profile-card__level-tag:active::after {
  transform: translate(-50%, -50%) scale(0);
  opacity: 0;
}

.profile-card__level-progress {
  flex: 1;
  height: 10rpx;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 5rpx;
  overflow: hidden;
  max-width: 200rpx;
  cursor: pointer;
  transition: none;
}

.profile-card__level-progress:active {
  background-color: rgba(255, 255, 255, 0.2);
  transform: none;
}

.profile-card__level-upgrade {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.9);
  margin-left: 16rpx;
  padding: 4rpx 10rpx;
  border-radius: 10rpx;
  background-color: rgba(255, 255, 255, 0.15);
  position: relative;
  overflow: hidden;
  transition: all 0.2s ease;
  animation: upgradeGlow 2s infinite;
}

.profile-card__level-upgrade:active {
  background-color: rgba(255, 255, 255, 0.3);
  transform: scale(0.95);
}

@keyframes upgradeGlow {
  0% {
    box-shadow: 0 0 2rpx rgba(255, 255, 255, 0.5);
  }
  50% {
    box-shadow: 0 0 8rpx rgba(255, 255, 255, 0.8);
  }
  100% {
    box-shadow: 0 0 2rpx rgba(255, 255, 255, 0.5);
  }
}

.profile-card__level-progress-bar {
  height: 100%;
  background-color: rgba(255, 255, 255, 0.6);
  transition: none;
}

.profile-card__level-progress-bar::after {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(to right, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.8) 50%, rgba(255, 255, 255, 0) 100%);
  animation: none;
}

.profile-card__level-progress-bar--wrong {
  background-color: rgba(255, 200, 200, 0.7);
}

.profile-card__level-progress-bar--time {
  background-color: rgba(200, 255, 200, 0.7);
}

.profile-card__level-progress-bar--medal {
  background-color: rgba(255, 240, 150, 0.7);
}

/* 学校和年级信息样式 */
.profile-card__school-grade-row {
  display: flex;
  align-items: center;
}

.profile-card__school,
.profile-card__grade {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.85);
}

.profile-card__school {
  margin-right: 24rpx;
}

.profile-card__school .icon,
.profile-card__grade .icon {
  margin-right: 8rpx;
  color: rgba(255, 255, 255, 0.85);
}

/* 用户等级容器 */
.profile-card__level-container {
  display: flex;
  flex-direction: column;
  min-width: 80rpx;
}

.profile-card__level {
  font-size: 22rpx;
  background-color: #3E7BFA;
  color: #ffffff;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-weight: bold;
  text-align: center;
  margin-bottom: 6rpx;
  align-self: flex-start;
  position: relative;
  overflow: hidden;
  box-shadow: 0 2rpx 6rpx rgba(62, 123, 250, 0.3);
}

/* 勋章行 */
.profile-card__medals-row {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  margin-bottom: 4rpx;
  position: relative;
}

/* 单个勋章样式 */
.medal-item {
  position: relative;
  margin-right: 12rpx;
  margin-bottom: 4rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36rpx;
  height: 36rpx;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.8);
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
  transition: none;
}

.medal-item-hover {
  transform: scale(1.2);
  background-color: rgba(255, 255, 255, 0.95);
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.2);
}

.medal-item .icon {
  font-size: 24rpx;
}

/* 勋章提示文本 */
.medal-tooltip {
  position: absolute;
  bottom: -38rpx;
  left: 50%;
  transform: translateX(-50%) scale(0);
  background-color: rgba(0, 0, 0, 0.7);
  color: #fff;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  white-space: nowrap;
  transition: none;
  opacity: 0;
  pointer-events: none;
  z-index: 10;
}

.medal-item:active .medal-tooltip {
  transform: translateX(-50%) scale(1);
  opacity: 0.9;
}

/* 勋章信息提示 */
.medal-info-tip {
  display: flex;
  align-items: center;
  margin-left: 8rpx;
  background-color: rgba(62, 123, 250, 0.1);
  padding: 4rpx 8rpx;
  border-radius: 10rpx;
  animation: none;
}

.medal-info-tip .icon {
  color: #3E7BFA;
  margin-right: 4rpx;
}

.medal-info-tip text {
  font-size: 20rpx;
  color: #3E7BFA;
  white-space: nowrap;
}

.profile-card__details {
  display: flex;
  flex-direction: column;
}

.profile-card__detail {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #999;
  margin-top: 6rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.profile-card__detail .icon {
  margin-right: 8rpx;
  color: #8E54E9;
}

.profile-card__edit {
  position: absolute;
  top: 24rpx;
  right: 24rpx;
  display: flex;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.25);
  color: #ffffff;
  font-size: 24rpx;
  padding: 6rpx 16rpx;
  border-radius: 28rpx;
  transition: none;
  z-index: 5;
}

.profile-card__edit:active {
  background-color: rgba(255, 255, 255, 0.25);
  transform: none;
}

.profile-card__edit .icon {
  margin-right: 4rpx;
  color: #ffffff;
}

/* 学习数据统计 */
.profile-stats {
  display: flex;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  padding: 20rpx 10rpx;
  background-color: rgba(255, 255, 255, 0.1);
}

.profile-stats__item {
  flex: 1;
  text-align: center;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.profile-stats__item:not(:last-child)::after {
  content: "";
  position: absolute;
  right: 0;
  top: 20%;
  height: 60%;
  width: 1rpx;
  background-color: rgba(255, 255, 255, 0.2);
}

/* 勋章数可点击样式 */
.profile-stats__item[bindtap] {
  position: relative;
  overflow: hidden;
}

.profile-stats__item[bindtap]:active {
  opacity: 0.9;
}

.profile-stats__item[bindtap]::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.1);
  opacity: 0;
}

.profile-stats__item[bindtap]:active::before {
  opacity: 1;
}

.profile-stats__value {
  font-size: 44rpx;
  font-weight: bold;
  color: #ffffff;
  line-height: 1.2;
  margin-bottom: 8rpx;
  letter-spacing: -1rpx;
}

.profile-stats__label {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 8rpx;
}

/* 统计项的进度条 */
.profile-stats__progress {
  width: 80rpx;
  height: 4rpx;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 2rpx;
  overflow: hidden;
  margin-top: 6rpx;
}

.profile-stats__progress-bar {
  height: 100%;
  background-color: rgba(255, 255, 255, 0.6);
  transition: none;
}

.profile-stats__progress-bar--wrong {
  background-color: rgba(255, 200, 200, 0.7);
}

.profile-stats__progress-bar--time {
  background-color: rgba(200, 255, 200, 0.7);
}

.profile-stats__progress-bar--medal {
  background-color: rgba(255, 240, 150, 0.7);
}

/* 小屏幕适配 */
@media screen and (max-height: 700px) {
  .profile-header {
    padding: 16rpx 20rpx;
    margin-top: 5rpx;
  }
  
  .login-banner {
    padding: 24rpx 32rpx;
  }
  
  .login-content {
    padding: 32rpx 24rpx;
  }
  
  .avatar-circle {
    width: 100rpx;
    height: 100rpx;
  }
  
  .login-button {
    height: 72rpx;
    font-size: 28rpx;
  }
  
  .profile-card__avatar {
    width: 80rpx;
    height: 80rpx;
    font-size: 36rpx;
  }
  
  .profile-card__main {
    padding: 20rpx;
  }
  
  .profile-card__name {
    font-size: 30rpx;
    margin-bottom: 8rpx;
  }
  
  .profile-card__detail {
    font-size: 22rpx;
    margin-top: 4rpx;
  }
  
  .profile-stats {
    padding: 16rpx 0;
  }
  
  .profile-stats__value {
    font-size: 36rpx;
    margin-bottom: 4rpx;
  }
  
  .profile-stats__label {
    font-size: 22rpx;
  }
}

/* iPad适配 */
.profile-header--ipad .profile-card {
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.08);
  border-radius: 24rpx;
}

.profile-header--ipad .login-banner {
  padding: 44rpx 48rpx;
}

.profile-header--ipad .login-title {
  font-size: 42rpx;
}

.profile-header--ipad .login-description {
  font-size: 28rpx;
}

.profile-header--ipad .login-content {
  padding: 50rpx 40rpx;
}

.profile-header--ipad .avatar-circle {
  width: 140rpx;
  height: 140rpx;
}

.profile-header--ipad .login-message {
  font-size: 32rpx;
  margin-bottom: 40rpx;
}

.profile-header--ipad .login-button {
  width: 50%;
  height: 88rpx;
  font-size: 32rpx;
}

.profile-header--ipad .profile-card__avatar {
  width: 120rpx;
  height: 120rpx;
  font-size: 56rpx;
}

.profile-header--ipad .profile-card__name {
  font-size: 36rpx;
  max-width: 280rpx;
}

.profile-header--ipad .profile-card__level {
  font-size: 24rpx;
  padding: 4rpx 14rpx;
}

.profile-header--ipad .profile-card__detail {
  font-size: 26rpx;
  margin-top: 8rpx;
}

.profile-header--ipad .profile-stats__value {
  font-size: 46rpx;
}

.profile-header--ipad .profile-stats__label {
  font-size: 26rpx;
}

.profile-header--ipad .medal-item {
  width: 44rpx;
  height: 44rpx;
  margin-right: 16rpx;
}

.profile-header--ipad .medal-item .icon {
  font-size: 28rpx;
}

.profile-header--ipad .medal-tooltip {
  font-size: 22rpx;
  padding: 6rpx 10rpx;
  bottom: -42rpx;
}

.profile-header--ipad .medal-info-tip text {
  font-size: 22rpx;
}

/* 勋章展示区样式 */
.medals-container {
  border-top: 1px solid rgba(230, 230, 240, 0.5);
  padding: 16rpx 20rpx;
  overflow: hidden;
  height: 160rpx;
  box-sizing: border-box;
  background-color: #ffffff;
}

.medals-scroll-view {
  white-space: nowrap;
  width: 100%;
  height: 140rpx;
  overflow: hidden;
}

.medals-content {
  display: flex;
  height: 140rpx;
  width: fit-content;
}

.medal-scroll-item {
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  margin-right: 24rpx;
  padding: 8rpx 12rpx;
  width: 120rpx;
  height: 140rpx;
  position: relative;
  box-sizing: border-box;
  flex-shrink: 0;
}

.medal-scroll-item:last-child {
  margin-right: 12rpx;
}

.medal-icon-container {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: #f8f9fd;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  position: relative;
  transition: none;
}

.medal-icon-container .icon {
  width: 50rpx;
  height: 50rpx;
}

.medal-name {
  font-size: 22rpx;
  color: #666;
  text-align: center;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  line-height: 1.2;
  height: 26rpx;
}

/* 已解锁勋章样式 */
.medal-unlocked .medal-icon-container {
  background: linear-gradient(135deg, #f7f9ff, #e8eeff);
  border: 1px solid #e0e6ff;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1);
}

.medal-unlocked .medal-icon-container .icon {
  color: #7B6CFF;
}

.medal-unlocked .medal-name {
  color: #333;
  font-weight: 500;
}

/* 未解锁勋章样式 */
.medal-locked .medal-icon-container {
  background-color: #f0f0f0;
  border: 1px solid #eaeaea;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
}

.medal-locked .medal-icon-container::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 50%;
}

.medal-locked .medal-icon-container .icon {
  opacity: 0.4;
  filter: grayscale(1);
}

.medal-locked .medal-name {
  color: #999;
  font-weight: normal;
}

/* 更多按钮样式 */
.medal-more-item {
  padding: 8rpx 12rpx;
}

.medal-more-icon {
  background: linear-gradient(135deg, #f2f4ff, #eaeeff);
  border: 1px dashed #c9d4ff;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 自定义更多图标 - 圆形背景 */
.more-icon-circle {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  background-color: #7B6CFF;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 6rpx rgba(123, 108, 255, 0.3);
}

/* 加号横线 */
.more-icon-plus {
  width: 20rpx;
  height: 4rpx;
  background-color: white;
  border-radius: 2rpx;
  position: relative;
}

/* 加号竖线 */
.more-icon-plus::after {
  content: "";
  position: absolute;
  top: -8rpx;
  left: 8rpx;
  width: 4rpx;
  height: 20rpx;
  background-color: white;
  border-radius: 2rpx;
}

/* 点击效果 */
.medal-scroll-item:active .medal-icon-container {
  transform: none;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

/* iPad适配 */
.profile-header--ipad .medals-container {
  padding: 20rpx 24rpx;
}

.profile-header--ipad .medal-scroll-item {
  width: 140rpx;
  margin-right: 32rpx;
}

.profile-header--ipad .medal-icon-container {
  width: 92rpx;
  height: 92rpx;
}

.profile-header--ipad .medal-icon-container .icon {
  width: 60rpx;
  height: 60rpx;
}

.profile-header--ipad .medal-name {
  font-size: 24rpx;
} 