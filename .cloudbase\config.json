{"envId": "cloud1-0g3pg16f43f63333", "region": "ap-guangzhou", "functionRoot": "./cloudfunctions", "functions": [{"name": "knowledge-graph-query", "config": {"timeout": 60, "memorySize": 512, "runtime": "Nodejs16.13"}}, {"name": "database-query", "config": {"timeout": 60, "memorySize": 512, "runtime": "Nodejs16.13"}}, {"name": "intelligent-diagnosis", "config": {"timeout": 60, "memorySize": 512, "runtime": "Nodejs16.13"}}, {"name": "login", "config": {"timeout": 60, "memorySize": 512, "runtime": "Nodejs16.13"}}, {"name": "user-knowledge-status", "config": {"timeout": 60, "memorySize": 512, "runtime": "Nodejs16.13"}}]}