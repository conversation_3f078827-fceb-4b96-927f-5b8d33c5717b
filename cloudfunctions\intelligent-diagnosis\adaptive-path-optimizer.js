// 自适应学习路径优化器
const { RELATION_TYPES, RELATION_STRENGTH } = require('./graph-data-types');

class AdaptivePathOptimizer {
  constructor(knowledgeGraph) {
    this.knowledgeGraph = knowledgeGraph;
    this.learningAnalytics = new Map();
    this.optimizationHistory = [];
  }

  /**
   * 自适应路径优化
   * @param {Object} currentPath - 当前学习路径
   * @param {Object} learningProgress - 学习进度数据
   * @param {Object} performanceMetrics - 性能指标
   * @param {Object} studentProfile - 学生档案
   * @returns {Object} 优化后的路径
   */
  async optimizePath(currentPath, learningProgress, performanceMetrics, studentProfile) {
    console.log('开始自适应路径优化...');

    // 1. 分析当前学习状态
    const currentState = this.analyzeCurrentLearningState(learningProgress, performanceMetrics);
    
    // 2. 识别优化机会
    const optimizationOpportunities = this.identifyOptimizationOpportunities(
      currentPath, currentState, studentProfile
    );
    
    // 3. 计算路径调整策略
    const adjustmentStrategy = this.calculateAdjustmentStrategy(
      optimizationOpportunities, currentState
    );
    
    // 4. 执行路径优化
    const optimizedPath = await this.executePathOptimization(
      currentPath, adjustmentStrategy, studentProfile
    );
    
    // 5. 验证优化效果
    const optimizationValidation = this.validateOptimization(
      currentPath, optimizedPath, currentState
    );
    
    // 6. 记录优化历史
    this.recordOptimizationHistory({
      timestamp: new Date().toISOString(),
      originalPath: currentPath,
      optimizedPath: optimizedPath,
      strategy: adjustmentStrategy,
      validation: optimizationValidation
    });

    console.log('自适应路径优化完成');
    
    return {
      optimizedPath,
      adjustmentStrategy,
      optimizationMetrics: optimizationValidation,
      recommendations: this.generateOptimizationRecommendations(adjustmentStrategy)
    };
  }

  /**
   * 分析当前学习状态
   * @param {Object} learningProgress - 学习进度
   * @param {Object} performanceMetrics - 性能指标
   * @returns {Object} 当前学习状态分析
   */
  analyzeCurrentLearningState(learningProgress, performanceMetrics) {
    const currentState = {
      overallProgress: 0,
      learningVelocity: 0,
      masteryConsistency: 0,
      difficultyAdaptation: 0,
      engagementLevel: 0,
      strugglingAreas: [],
      acceleratedAreas: [],
      stagnantAreas: [],
      timeUtilization: 0,
      learningPatterns: {}
    };

    // 计算整体进度
    currentState.overallProgress = this.calculateOverallProgress(learningProgress);
    
    // 计算学习速度
    currentState.learningVelocity = this.calculateLearningVelocity(learningProgress);
    
    // 评估掌握一致性
    currentState.masteryConsistency = this.evaluateMasteryConsistency(performanceMetrics);
    
    // 分析难度适应性
    currentState.difficultyAdaptation = this.analyzeDifficultyAdaptation(learningProgress);
    
    // 评估参与度
    currentState.engagementLevel = this.assessEngagementLevel(learningProgress);
    
    // 识别问题区域
    currentState.strugglingAreas = this.identifyStrugglingAreas(performanceMetrics);
    currentState.acceleratedAreas = this.identifyAcceleratedAreas(performanceMetrics);
    currentState.stagnantAreas = this.identifyStagnantAreas(learningProgress);
    
    // 计算时间利用率
    currentState.timeUtilization = this.calculateTimeUtilization(learningProgress);
    
    // 分析学习模式
    currentState.learningPatterns = this.analyzeLearningPatterns(learningProgress);

    return currentState;
  }

  /**
   * 识别优化机会
   * @param {Object} currentPath - 当前路径
   * @param {Object} currentState - 当前状态
   * @param {Object} studentProfile - 学生档案
   * @returns {Array} 优化机会列表
   */
  identifyOptimizationOpportunities(currentPath, currentState, studentProfile) {
    const opportunities = [];

    // 1. 进度优化机会
    if (currentState.overallProgress < 0.6) {
      opportunities.push({
        type: 'pace_adjustment',
        priority: 'high',
        description: '学习进度偏慢，需要调整学习节奏',
        recommendedAction: 'increase_support',
        targetImprovement: 0.2
      });
    }

    // 2. 难度调整机会
    if (currentState.difficultyAdaptation < 0.5) {
      opportunities.push({
        type: 'difficulty_optimization',
        priority: 'high',
        description: '难度适应性不佳，需要重新评估内容难度',
        recommendedAction: 'adjust_difficulty_sequence',
        targetImprovement: 0.3
      });
    }

    // 3. 薄弱区域强化机会
    if (currentState.strugglingAreas.length > 0) {
      opportunities.push({
        type: 'weakness_reinforcement',
        priority: 'critical',
        description: '发现薄弱区域，需要针对性强化',
        recommendedAction: 'add_remediation_content',
        targetAreas: currentState.strugglingAreas,
        targetImprovement: 0.4
      });
    }

    // 4. 优势区域加速机会
    if (currentState.acceleratedAreas.length > 0) {
      opportunities.push({
        type: 'strength_acceleration',
        priority: 'medium',
        description: '发现优势区域，可以适当加速学习',
        recommendedAction: 'accelerate_progression',
        targetAreas: currentState.acceleratedAreas,
        targetImprovement: 0.2
      });
    }

    // 5. 停滞区域激活机会
    if (currentState.stagnantAreas.length > 0) {
      opportunities.push({
        type: 'stagnation_resolution',
        priority: 'high',
        description: '发现学习停滞区域，需要改变学习策略',
        recommendedAction: 'change_learning_strategy',
        targetAreas: currentState.stagnantAreas,
        targetImprovement: 0.3
      });
    }

    // 6. 参与度提升机会
    if (currentState.engagementLevel < 0.7) {
      opportunities.push({
        type: 'engagement_enhancement',
        priority: 'medium',
        description: '学习参与度偏低，需要增加趣味性和互动性',
        recommendedAction: 'add_interactive_elements',
        targetImprovement: 0.25
      });
    }

    // 7. 时间效率优化机会
    if (currentState.timeUtilization < 0.8) {
      opportunities.push({
        type: 'time_optimization',
        priority: 'medium',
        description: '时间利用率偏低，需要优化学习时间分配',
        recommendedAction: 'optimize_time_allocation',
        targetImprovement: 0.2
      });
    }

    // 按优先级排序
    return opportunities.sort((a, b) => {
      const priorityOrder = { 'critical': 3, 'high': 2, 'medium': 1, 'low': 0 };
      return priorityOrder[b.priority] - priorityOrder[a.priority];
    });
  }

  /**
   * 计算调整策略
   * @param {Array} opportunities - 优化机会
   * @param {Object} currentState - 当前状态
   * @returns {Object} 调整策略
   */
  calculateAdjustmentStrategy(opportunities, currentState) {
    const strategy = {
      priorityAdjustments: [],
      sequenceModifications: [],
      contentEnhancements: [],
      difficultyAdjustments: [],
      timeAllocations: [],
      supportMechanisms: []
    };

    opportunities.forEach(opportunity => {
      switch (opportunity.type) {
        case 'pace_adjustment':
          strategy.priorityAdjustments.push(this.createPaceAdjustment(opportunity));
          break;
          
        case 'difficulty_optimization':
          strategy.difficultyAdjustments.push(this.createDifficultyAdjustment(opportunity));
          break;
          
        case 'weakness_reinforcement':
          strategy.contentEnhancements.push(this.createWeaknessReinforcement(opportunity));
          strategy.supportMechanisms.push(this.createSupportMechanism(opportunity));
          break;
          
        case 'strength_acceleration':
          strategy.sequenceModifications.push(this.createAcceleration(opportunity));
          break;
          
        case 'stagnation_resolution':
          strategy.sequenceModifications.push(this.createStagnationResolution(opportunity));
          strategy.contentEnhancements.push(this.createAlternativeApproach(opportunity));
          break;
          
        case 'engagement_enhancement':
          strategy.contentEnhancements.push(this.createEngagementEnhancement(opportunity));
          break;
          
        case 'time_optimization':
          strategy.timeAllocations.push(this.createTimeOptimization(opportunity));
          break;
      }
    });

    return strategy;
  }

  /**
   * 执行路径优化
   * @param {Object} currentPath - 当前路径
   * @param {Object} adjustmentStrategy - 调整策略
   * @param {Object} studentProfile - 学生档案
   * @returns {Object} 优化后的路径
   */
  async executePathOptimization(currentPath, adjustmentStrategy, studentProfile) {
    console.log('执行路径优化调整...');

    let optimizedPath = JSON.parse(JSON.stringify(currentPath)); // 深拷贝

    // 1. 执行优先级调整
    optimizedPath = this.applyPriorityAdjustments(optimizedPath, adjustmentStrategy.priorityAdjustments);
    
    // 2. 执行序列修改
    optimizedPath = this.applySequenceModifications(optimizedPath, adjustmentStrategy.sequenceModifications);
    
    // 3. 执行内容增强
    optimizedPath = this.applyContentEnhancements(optimizedPath, adjustmentStrategy.contentEnhancements);
    
    // 4. 执行难度调整
    optimizedPath = this.applyDifficultyAdjustments(optimizedPath, adjustmentStrategy.difficultyAdjustments);
    
    // 5. 执行时间分配调整
    optimizedPath = this.applyTimeAllocations(optimizedPath, adjustmentStrategy.timeAllocations);
    
    // 6. 应用支持机制
    optimizedPath = this.applySupportMechanisms(optimizedPath, adjustmentStrategy.supportMechanisms);
    
    // 7. 重新计算路径元数据
    optimizedPath.metadata = this.recalculatePathMetadata(optimizedPath);
    
    // 8. 添加优化标记
    optimizedPath.optimizationApplied = {
      timestamp: new Date().toISOString(),
      strategy: adjustmentStrategy,
      version: this.generateOptimizationVersion()
    };

    return optimizedPath;
  }

  /**
   * 验证优化效果
   * @param {Object} originalPath - 原始路径
   * @param {Object} optimizedPath - 优化后路径
   * @param {Object} currentState - 当前状态
   * @returns {Object} 验证结果
   */
  validateOptimization(originalPath, optimizedPath, currentState) {
    const validation = {
      improvementMetrics: {},
      riskAssessment: {},
      validationScore: 0,
      recommendations: []
    };

    // 计算改进指标
    validation.improvementMetrics = {
      pathEfficiency: this.calculatePathEfficiency(optimizedPath) - this.calculatePathEfficiency(originalPath),
      contentBalance: this.calculateContentBalance(optimizedPath) - this.calculateContentBalance(originalPath),
      adaptabilityScore: this.calculateAdaptabilityScore(optimizedPath) - this.calculateAdaptabilityScore(originalPath),
      engagementPotential: this.calculateEngagementPotential(optimizedPath) - this.calculateEngagementPotential(originalPath)
    };

    // 评估风险
    validation.riskAssessment = {
      overloadRisk: this.assessOverloadRisk(optimizedPath),
      gapRisk: this.assessGapRisk(optimizedPath),
      paceRisk: this.assessPaceRisk(optimizedPath, currentState),
      motivationRisk: this.assessMotivationRisk(optimizedPath)
    };

    // 计算验证分数
    validation.validationScore = this.calculateValidationScore(validation.improvementMetrics, validation.riskAssessment);

    // 生成建议
    validation.recommendations = this.generateValidationRecommendations(validation);

    return validation;
  }

  // 状态分析方法
  calculateOverallProgress(learningProgress) {
    const completed = Object.values(learningProgress.knowledgePoints || {})
      .filter(point => point.masteryScore >= 0.8).length;
    const total = Object.keys(learningProgress.knowledgePoints || {}).length;
    return total > 0 ? completed / total : 0;
  }

  calculateLearningVelocity(learningProgress) {
    const recentProgress = this.getRecentProgressData(learningProgress);
    if (recentProgress.length < 2) return 0.5; // 默认值

    const velocities = [];
    for (let i = 1; i < recentProgress.length; i++) {
      const timeDiff = new Date(recentProgress[i].timestamp) - new Date(recentProgress[i-1].timestamp);
      const progressDiff = recentProgress[i].progress - recentProgress[i-1].progress;
      velocities.push(progressDiff / (timeDiff / (1000 * 60 * 60))); // 每小时进度
    }

    return velocities.reduce((sum, v) => sum + v, 0) / velocities.length;
  }

  evaluateMasteryConsistency(performanceMetrics) {
    const scores = Object.values(performanceMetrics.assessmentResults || {});
    if (scores.length === 0) return 0.5;

    const mean = scores.reduce((sum, score) => sum + score, 0) / scores.length;
    const variance = scores.reduce((sum, score) => sum + Math.pow(score - mean, 2), 0) / scores.length;
    const standardDeviation = Math.sqrt(variance);

    // 一致性与标准差成反比
    return Math.max(0, 1 - (standardDeviation / mean));
  }

  analyzeDifficultyAdaptation(learningProgress) {
    const difficultyProgression = this.getDifficultyProgression(learningProgress);
    let adaptationScore = 0;

    for (let i = 1; i < difficultyProgression.length; i++) {
      const currentDifficulty = difficultyProgression[i].difficulty;
      const currentMastery = difficultyProgression[i].mastery;
      const previousMastery = difficultyProgression[i-1].mastery;

      // 如果掌握度提高且难度适当，得分增加
      if (currentMastery > previousMastery && currentMastery >= 0.7) {
        adaptationScore += 0.2;
      }
      // 如果掌握度下降明显，难度可能过高
      if (currentMastery < previousMastery - 0.2) {
        adaptationScore -= 0.1;
      }
    }

    return Math.max(0, Math.min(1, adaptationScore));
  }

  assessEngagementLevel(learningProgress) {
    const engagementIndicators = {
      sessionDuration: this.calculateAverageSessionDuration(learningProgress),
      completionRate: this.calculateCompletionRate(learningProgress),
      interactionFrequency: this.calculateInteractionFrequency(learningProgress),
      voluntaryPractice: this.calculateVoluntaryPracticeRate(learningProgress)
    };

    // 综合计算参与度
    return (
      engagementIndicators.sessionDuration * 0.25 +
      engagementIndicators.completionRate * 0.35 +
      engagementIndicators.interactionFrequency * 0.25 +
      engagementIndicators.voluntaryPractice * 0.15
    );
  }

  identifyStrugglingAreas(performanceMetrics) {
    const strugglingAreas = [];
    
    for (const [nodeId, metrics] of Object.entries(performanceMetrics.knowledgePointMetrics || {})) {
      if (metrics.masteryScore < 0.5 || metrics.strugglingTime > metrics.expectedTime * 1.5) {
        strugglingAreas.push({
          nodeId,
          severity: this.calculateStruggleSeverity(metrics),
          issues: this.identifySpecificIssues(metrics)
        });
      }
    }

    return strugglingAreas.sort((a, b) => b.severity - a.severity);
  }

  identifyAcceleratedAreas(performanceMetrics) {
    const acceleratedAreas = [];
    
    for (const [nodeId, metrics] of Object.entries(performanceMetrics.knowledgePointMetrics || {})) {
      if (metrics.masteryScore >= 0.9 && metrics.completionTime < metrics.expectedTime * 0.7) {
        acceleratedAreas.push({
          nodeId,
          accelerationLevel: this.calculateAccelerationLevel(metrics),
          opportunities: this.identifyAccelerationOpportunities(nodeId)
        });
      }
    }

    return acceleratedAreas.sort((a, b) => b.accelerationLevel - a.accelerationLevel);
  }

  identifyStagnantAreas(learningProgress) {
    const stagnantAreas = [];
    const progressHistory = learningProgress.progressHistory || [];

    // 分析最近一段时间的进度变化
    const recentPeriod = 7; // 7天
    const recentProgress = progressHistory.filter(entry => {
      const entryDate = new Date(entry.timestamp);
      const cutoffDate = new Date(Date.now() - recentPeriod * 24 * 60 * 60 * 1000);
      return entryDate >= cutoffDate;
    });

    for (const [nodeId, pointData] of Object.entries(learningProgress.knowledgePoints || {})) {
      const nodeProgress = recentProgress.filter(entry => entry.nodeId === nodeId);
      
      if (nodeProgress.length > 1) {
        const progressChange = nodeProgress[nodeProgress.length - 1].masteryScore - nodeProgress[0].masteryScore;
        
        if (Math.abs(progressChange) < 0.05) { // 几乎没有进步
          stagnantAreas.push({
            nodeId,
            stagnationDuration: this.calculateStagnationDuration(nodeProgress),
            potentialCauses: this.identifyStagnationCauses(pointData)
          });
        }
      }
    }

    return stagnantAreas;
  }

  calculateTimeUtilization(learningProgress) {
    const totalAllocatedTime = this.calculateTotalAllocatedTime(learningProgress);
    const totalActiveTime = this.calculateTotalActiveTime(learningProgress);
    
    return totalAllocatedTime > 0 ? totalActiveTime / totalAllocatedTime : 0;
  }

  analyzeLearningPatterns(learningProgress) {
    return {
      preferredTimeSlots: this.identifyPreferredTimeSlots(learningProgress),
      learningRhythm: this.analyzeLearningRhythm(learningProgress),
      attentionSpan: this.estimateAttentionSpan(learningProgress),
      breakPreferences: this.analyzeBreakPreferences(learningProgress),
      difficultyTolerance: this.assessDifficultyTolerance(learningProgress)
    };
  }

  // 策略创建方法
  createPaceAdjustment(opportunity) {
    return {
      type: 'pace_adjustment',
      action: opportunity.recommendedAction,
      parameters: {
        speedMultiplier: opportunity.recommendedAction === 'increase_support' ? 0.8 : 1.2,
        additionalSupport: opportunity.recommendedAction === 'increase_support',
        targetImprovement: opportunity.targetImprovement
      }
    };
  }

  createDifficultyAdjustment(opportunity) {
    return {
      type: 'difficulty_adjustment',
      action: 'reorder_by_difficulty',
      parameters: {
        difficultyGradient: 'smoother',
        bufferContent: true,
        adaptiveThreshold: 0.7
      }
    };
  }

  createWeaknessReinforcement(opportunity) {
    return {
      type: 'weakness_reinforcement',
      action: 'add_remediation',
      parameters: {
        targetAreas: opportunity.targetAreas,
        reinforcementLevel: 'high',
        additionalPractice: true,
        alternativeExplanations: true
      }
    };
  }

  createSupportMechanism(opportunity) {
    return {
      type: 'support_mechanism',
      action: 'enhance_guidance',
      parameters: {
        guidanceLevel: 'high',
        feedbackFrequency: 'increased',
        mentorSupport: true
      }
    };
  }

  createAcceleration(opportunity) {
    return {
      type: 'acceleration',
      action: 'skip_redundant_content',
      parameters: {
        targetAreas: opportunity.targetAreas,
        accelerationFactor: 1.3,
        advancedContent: true
      }
    };
  }

  createStagnationResolution(opportunity) {
    return {
      type: 'stagnation_resolution',
      action: 'change_approach',
      parameters: {
        targetAreas: opportunity.targetAreas,
        alternativeStrategies: ['visual_learning', 'practical_application', 'peer_learning'],
        breakPattern: true
      }
    };
  }

  createAlternativeApproach(opportunity) {
    return {
      type: 'alternative_approach',
      action: 'diversify_methods',
      parameters: {
        multimodalContent: true,
        interactiveElements: true,
        realWorldApplications: true
      }
    };
  }

  createEngagementEnhancement(opportunity) {
    return {
      type: 'engagement_enhancement',
      action: 'add_gamification',
      parameters: {
        gamificationElements: ['points', 'badges', 'leaderboards'],
        socialFeatures: true,
        personalizedRewards: true
      }
    };
  }

  createTimeOptimization(opportunity) {
    return {
      type: 'time_optimization',
      action: 'redistribute_time',
      parameters: {
        priorityReallocation: true,
        efficientSequencing: true,
        deadTimeReduction: true
      }
    };
  }

  // 应用调整方法
  applyPriorityAdjustments(path, adjustments) {
    adjustments.forEach(adjustment => {
      if (adjustment.parameters.speedMultiplier !== 1.0) {
        path.sequence.forEach(item => {
          item.estimatedStudyTime = Math.round(item.estimatedStudyTime * adjustment.parameters.speedMultiplier);
        });
      }
    });
    return path;
  }

  applySequenceModifications(path, modifications) {
    modifications.forEach(modification => {
      switch (modification.action) {
        case 'skip_redundant_content':
          path = this.skipRedundantContent(path, modification.parameters);
          break;
        case 'change_approach':
          path = this.changeApproach(path, modification.parameters);
          break;
      }
    });
    return path;
  }

  applyContentEnhancements(path, enhancements) {
    enhancements.forEach(enhancement => {
      switch (enhancement.action) {
        case 'add_remediation':
          path = this.addRemediationContent(path, enhancement.parameters);
          break;
        case 'diversify_methods':
          path = this.diversifyMethods(path, enhancement.parameters);
          break;
        case 'add_gamification':
          path = this.addGamificationElements(path, enhancement.parameters);
          break;
      }
    });
    return path;
  }

  applyDifficultyAdjustments(path, adjustments) {
    adjustments.forEach(adjustment => {
      if (adjustment.action === 'reorder_by_difficulty') {
        path.sequence = this.reorderByDifficulty(path.sequence, adjustment.parameters);
      }
    });
    return path;
  }

  applyTimeAllocations(path, allocations) {
    allocations.forEach(allocation => {
      if (allocation.action === 'redistribute_time') {
        path = this.redistributeTime(path, allocation.parameters);
      }
    });
    return path;
  }

  applySupportMechanisms(path, mechanisms) {
    mechanisms.forEach(mechanism => {
      if (mechanism.action === 'enhance_guidance') {
        path.sequence.forEach(item => {
          item.supportLevel = mechanism.parameters.guidanceLevel;
          item.feedbackFrequency = mechanism.parameters.feedbackFrequency;
          if (mechanism.parameters.mentorSupport) {
            item.mentorSupport = true;
          }
        });
      }
    });
    return path;
  }

  // 辅助方法
  getRecentProgressData(learningProgress) {
    // 模拟获取最近进度数据
    return learningProgress.progressHistory || [];
  }

  getDifficultyProgression(learningProgress) {
    // 模拟获取难度进展数据
    return Object.entries(learningProgress.knowledgePoints || {}).map(([nodeId, data]) => ({
      nodeId,
      difficulty: this.getNodeDifficulty(nodeId),
      mastery: data.masteryScore || 0
    }));
  }

  getNodeDifficulty(nodeId) {
    const node = this.knowledgeGraph.nodes[nodeId];
    return node?.difficulty || 'medium';
  }

  calculateAverageSessionDuration(learningProgress) {
    const sessions = learningProgress.sessions || [];
    if (sessions.length === 0) return 0.5;
    
    const totalDuration = sessions.reduce((sum, session) => sum + session.duration, 0);
    const averageDuration = totalDuration / sessions.length;
    
    // 标准化到0-1范围 (假设理想时长为45分钟)
    return Math.min(1, averageDuration / 45);
  }

  calculateCompletionRate(learningProgress) {
    const planned = learningProgress.plannedActivities || 0;
    const completed = learningProgress.completedActivities || 0;
    return planned > 0 ? completed / planned : 0.5;
  }

  calculateInteractionFrequency(learningProgress) {
    const interactions = learningProgress.interactions || [];
    const sessions = learningProgress.sessions || [];
    
    if (sessions.length === 0) return 0.5;
    
    const totalInteractions = interactions.length;
    const totalSessions = sessions.length;
    
    // 假设每个会话理想互动次数为10
    const averageInteractions = totalInteractions / totalSessions;
    return Math.min(1, averageInteractions / 10);
  }

  calculateVoluntaryPracticeRate(learningProgress) {
    const totalPractice = learningProgress.totalPracticeTime || 0;
    const requiredPractice = learningProgress.requiredPracticeTime || 1;
    
    return Math.min(1, totalPractice / requiredPractice);
  }

  calculateStruggleSeverity(metrics) {
    let severity = 0;
    
    if (metrics.masteryScore < 0.3) severity += 0.4;
    else if (metrics.masteryScore < 0.5) severity += 0.2;
    
    if (metrics.strugglingTime > metrics.expectedTime * 2) severity += 0.3;
    else if (metrics.strugglingTime > metrics.expectedTime * 1.5) severity += 0.2;
    
    if (metrics.errorRate > 0.5) severity += 0.3;
    else if (metrics.errorRate > 0.3) severity += 0.1;
    
    return Math.min(1, severity);
  }

  identifySpecificIssues(metrics) {
    const issues = [];
    
    if (metrics.masteryScore < 0.5) issues.push('低掌握度');
    if (metrics.strugglingTime > metrics.expectedTime * 1.5) issues.push('学习时间过长');
    if (metrics.errorRate > 0.3) issues.push('错误率过高');
    if (metrics.helpRequestFrequency > 0.5) issues.push('频繁求助');
    
    return issues;
  }

  calculateAccelerationLevel(metrics) {
    let level = 0;
    
    if (metrics.masteryScore >= 0.95) level += 0.3;
    else if (metrics.masteryScore >= 0.9) level += 0.2;
    
    if (metrics.completionTime < metrics.expectedTime * 0.5) level += 0.4;
    else if (metrics.completionTime < metrics.expectedTime * 0.7) level += 0.2;
    
    if (metrics.errorRate < 0.1) level += 0.3;
    else if (metrics.errorRate < 0.2) level += 0.1;
    
    return Math.min(1, level);
  }

  identifyAccelerationOpportunities(nodeId) {
    // 基于知识图谱找到可以加速学习的相关知识点
    const opportunities = [];
    const relatedNodes = this.findRelatedNodes(nodeId);
    
    relatedNodes.forEach(relatedId => {
      opportunities.push({
        nodeId: relatedId,
        type: 'advanced_content',
        description: `可以学习${this.getNodeName(relatedId)}的高级内容`
      });
    });
    
    return opportunities;
  }

  findRelatedNodes(nodeId) {
    const related = [];
    
    if (this.knowledgeGraph.links) {
      this.knowledgeGraph.links.forEach(link => {
        if (link.source === nodeId && 
            (link.type === RELATION_TYPES.PROGRESSION || 
             link.type === RELATION_TYPES.APPLICATION)) {
          related.push(link.target);
        }
      });
    }
    
    return related;
  }

  getNodeName(nodeId) {
    const node = this.knowledgeGraph.nodes[nodeId];
    return node?.name || nodeId;
  }

  calculateStagnationDuration(nodeProgress) {
    if (nodeProgress.length < 2) return 0;
    
    const firstEntry = new Date(nodeProgress[0].timestamp);
    const lastEntry = new Date(nodeProgress[nodeProgress.length - 1].timestamp);
    
    return (lastEntry - firstEntry) / (1000 * 60 * 60 * 24); // 天数
  }

  identifyStagnationCauses(pointData) {
    const causes = [];
    
    if (pointData.masteryScore < 0.3) causes.push('基础概念理解困难');
    if (pointData.practiceTime < pointData.recommendedPracticeTime * 0.5) causes.push('练习时间不足');
    if (pointData.errorPattern?.includes('repeated_errors')) causes.push('重复性错误');
    if (pointData.motivationScore < 0.5) causes.push('学习动机不足');
    
    return causes;
  }

  calculateTotalAllocatedTime(learningProgress) {
    return learningProgress.totalAllocatedTime || 0;
  }

  calculateTotalActiveTime(learningProgress) {
    return learningProgress.totalActiveTime || 0;
  }

  identifyPreferredTimeSlots(learningProgress) {
    // 分析学习时间偏好
    const sessions = learningProgress.sessions || [];
    const timeSlots = {};
    
    sessions.forEach(session => {
      const hour = new Date(session.startTime).getHours();
      const slot = Math.floor(hour / 4); // 6个时间段
      timeSlots[slot] = (timeSlots[slot] || 0) + session.effectivenessScore || 0.5;
    });
    
    return Object.entries(timeSlots)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 2)
      .map(([slot]) => parseInt(slot));
  }

  analyzeLearningRhythm(learningProgress) {
    // 分析学习节奏
    const sessions = learningProgress.sessions || [];
    if (sessions.length < 3) return 'unknown';
    
    const intervals = [];
    for (let i = 1; i < sessions.length; i++) {
      const interval = new Date(sessions[i].startTime) - new Date(sessions[i-1].endTime);
      intervals.push(interval / (1000 * 60 * 60)); // 小时
    }
    
    const averageInterval = intervals.reduce((sum, interval) => sum + interval, 0) / intervals.length;
    
    if (averageInterval < 12) return 'intensive';
    if (averageInterval < 48) return 'regular';
    return 'spaced';
  }

  estimateAttentionSpan(learningProgress) {
    const sessions = learningProgress.sessions || [];
    const effectiveIntervals = sessions.map(session => session.effectiveTime || session.duration || 30);
    
    if (effectiveIntervals.length === 0) return 30; // 默认30分钟
    
    return effectiveIntervals.reduce((sum, time) => sum + time, 0) / effectiveIntervals.length;
  }

  analyzeBreakPreferences(learningProgress) {
    const sessions = learningProgress.sessions || [];
    const breaks = [];
    
    sessions.forEach(session => {
      if (session.breaks) {
        breaks.push(...session.breaks);
      }
    });
    
    if (breaks.length === 0) return { frequency: 'low', duration: 5 };
    
    const averageFrequency = breaks.length / sessions.length;
    const averageDuration = breaks.reduce((sum, brk) => sum + brk.duration, 0) / breaks.length;
    
    return {
      frequency: averageFrequency > 2 ? 'high' : averageFrequency > 1 ? 'medium' : 'low',
      duration: averageDuration
    };
  }

  assessDifficultyTolerance(learningProgress) {
    // 评估难度容忍度
    const difficultyPerformance = {};
    
    Object.entries(learningProgress.knowledgePoints || {}).forEach(([nodeId, data]) => {
      const difficulty = this.getNodeDifficulty(nodeId);
      if (!difficultyPerformance[difficulty]) {
        difficultyPerformance[difficulty] = [];
      }
      difficultyPerformance[difficulty].push(data.masteryScore || 0);
    });
    
    const tolerance = {};
    Object.entries(difficultyPerformance).forEach(([difficulty, scores]) => {
      const averageScore = scores.reduce((sum, score) => sum + score, 0) / scores.length;
      tolerance[difficulty] = averageScore;
    });
    
    return tolerance;
  }

  // 路径调整实现方法
  skipRedundantContent(path, parameters) {
    const { targetAreas, accelerationFactor } = parameters;
    
    path.sequence = path.sequence.filter(item => {
      if (targetAreas.some(area => area.nodeId === item.nodeId)) {
        // 对于加速区域，减少学习时间或跳过某些内容
        item.estimatedStudyTime = Math.round(item.estimatedStudyTime / accelerationFactor);
        item.accelerated = true;
        return true;
      }
      return true;
    });
    
    return path;
  }

  changeApproach(path, parameters) {
    const { targetAreas, alternativeStrategies } = parameters;
    
    path.sequence.forEach(item => {
      if (targetAreas.some(area => area.nodeId === item.nodeId)) {
        item.alternativeStrategies = alternativeStrategies;
        item.approachChanged = true;
      }
    });
    
    return path;
  }

  addRemediationContent(path, parameters) {
    const { targetAreas, reinforcementLevel } = parameters;
    const remediationItems = [];
    
    targetAreas.forEach(area => {
      // 为每个薄弱区域添加补救内容
      remediationItems.push({
        type: 'remediation',
        nodeId: area.nodeId,
        estimatedStudyTime: 45,
        reinforcementLevel: reinforcementLevel,
        specialSupport: true,
        remediationFor: area.nodeId
      });
    });
    
    // 将补救内容插入到相应位置
    const newSequence = [];
    path.sequence.forEach(item => {
      newSequence.push(item);
      
      // 在薄弱知识点后插入补救内容
      const remediation = remediationItems.find(rem => rem.nodeId === item.nodeId);
      if (remediation) {
        newSequence.push(remediation);
      }
    });
    
    path.sequence = newSequence;
    return path;
  }

  diversifyMethods(path, parameters) {
    path.sequence.forEach(item => {
      if (parameters.multimodalContent) {
        item.multimodalContent = true;
      }
      if (parameters.interactiveElements) {
        item.interactiveElements = true;
      }
      if (parameters.realWorldApplications) {
        item.realWorldApplications = true;
      }
    });
    
    return path;
  }

  addGamificationElements(path, parameters) {
    const { gamificationElements, socialFeatures, personalizedRewards } = parameters;
    
    path.sequence.forEach(item => {
      item.gamification = {
        elements: gamificationElements,
        socialFeatures: socialFeatures,
        personalizedRewards: personalizedRewards
      };
    });
    
    return path;
  }

  reorderByDifficulty(sequence, parameters) {
    const { difficultyGradient } = parameters;
    
    if (difficultyGradient === 'smoother') {
      // 重新排序以获得更平滑的难度梯度
      const difficultyOrder = { 'easy': 0, 'medium': 1, 'hard': 2, 'expert': 3 };
      
      return sequence.sort((a, b) => {
        const aDifficulty = difficultyOrder[a.node?.difficulty || 'medium'];
        const bDifficulty = difficultyOrder[b.node?.difficulty || 'medium'];
        return aDifficulty - bDifficulty;
      });
    }
    
    return sequence;
  }

  redistributeTime(path, parameters) {
    if (parameters.priorityReallocation) {
      // 根据优先级重新分配时间
      const totalTime = path.sequence.reduce((sum, item) => sum + item.estimatedStudyTime, 0);
      const highPriorityItems = path.sequence.filter(item => item.priority === 'high');
      const normalItems = path.sequence.filter(item => item.priority !== 'high');
      
      // 给高优先级项目分配更多时间
      highPriorityItems.forEach(item => {
        item.estimatedStudyTime *= 1.2;
      });
      
      // 调整其他项目时间以保持总时间不变
      const adjustment = totalTime / path.sequence.reduce((sum, item) => sum + item.estimatedStudyTime, 0);
      normalItems.forEach(item => {
        item.estimatedStudyTime = Math.round(item.estimatedStudyTime * adjustment);
      });
    }
    
    return path;
  }

  recalculatePathMetadata(path) {
    const totalTime = path.sequence.reduce((sum, item) => sum + (item.estimatedStudyTime || 0), 0);
    const knowledgePointCount = path.sequence.filter(item => item.type !== 'review' && item.type !== 'remediation').length;
    
    return {
      ...path.metadata,
      estimatedTotalTime: totalTime,
      totalKnowledgePoints: knowledgePointCount,
      optimized: true,
      lastOptimization: new Date().toISOString()
    };
  }

  generateOptimizationVersion() {
    return `v${Date.now().toString(36)}_${Math.random().toString(36).substr(2, 5)}`;
  }

  // 验证相关方法
  calculatePathEfficiency(path) {
    const totalTime = path.sequence.reduce((sum, item) => sum + item.estimatedStudyTime, 0);
    const knowledgePointCount = path.sequence.filter(item => item.type !== 'review').length;
    return knowledgePointCount / (totalTime / 60); // 每小时知识点数
  }

  calculateContentBalance(path) {
    const difficulties = path.sequence.map(item => item.node?.difficulty || 'medium');
    const distribution = { easy: 0, medium: 0, hard: 0, expert: 0 };
    
    difficulties.forEach(diff => distribution[diff]++);
    
    // 计算分布的均衡度
    const total = difficulties.length;
    const idealRatio = 0.25; // 理想情况下每种难度占25%
    const variance = Object.values(distribution).reduce((sum, count) => {
      const ratio = count / total;
      return sum + Math.pow(ratio - idealRatio, 2);
    }, 0);
    
    return 1 - variance; // 方差越小，均衡度越高
  }

  calculateAdaptabilityScore(path) {
    let adaptabilityScore = 0;
    
    // 检查是否有自适应元素
    path.sequence.forEach(item => {
      if (item.alternativeStrategies) adaptabilityScore += 0.1;
      if (item.multimodalContent) adaptabilityScore += 0.1;
      if (item.adaptiveSupport) adaptabilityScore += 0.1;
    });
    
    return Math.min(1, adaptabilityScore);
  }

  calculateEngagementPotential(path) {
    let engagementScore = 0;
    
    path.sequence.forEach(item => {
      if (item.gamification) engagementScore += 0.2;
      if (item.interactiveElements) engagementScore += 0.15;
      if (item.realWorldApplications) engagementScore += 0.1;
      if (item.socialFeatures) engagementScore += 0.1;
    });
    
    return Math.min(1, engagementScore / path.sequence.length);
  }

  // 风险评估方法
  assessOverloadRisk(path) {
    const avgStudyTime = path.sequence.reduce((sum, item) => sum + item.estimatedStudyTime, 0) / path.sequence.length;
    return avgStudyTime > 90 ? 'high' : avgStudyTime > 60 ? 'medium' : 'low';
  }

  assessGapRisk(path) {
    // 检查是否有知识缺口
    let gapCount = 0;
    
    path.sequence.forEach(item => {
      if (item.prerequisites) {
        const missingPrereqs = item.prerequisites.filter(prereq => 
          !path.sequence.some(seqItem => seqItem.nodeId === prereq)
        );
        gapCount += missingPrereqs.length;
      }
    });
    
    return gapCount > 5 ? 'high' : gapCount > 2 ? 'medium' : 'low';
  }

  assessPaceRisk(path, currentState) {
    const totalTime = path.sequence.reduce((sum, item) => sum + item.estimatedStudyTime, 0);
    const currentVelocity = currentState.learningVelocity;
    
    // 基于当前学习速度评估节奏风险
    const estimatedCompletion = totalTime / (currentVelocity * 60); // 小时
    
    return estimatedCompletion > 200 ? 'high' : estimatedCompletion > 100 ? 'medium' : 'low';
  }

  assessMotivationRisk(path) {
    const difficultItems = path.sequence.filter(item => 
      item.node?.difficulty === 'hard' || item.node?.difficulty === 'expert'
    ).length;
    
    const totalItems = path.sequence.length;
    const difficultRatio = difficultItems / totalItems;
    
    return difficultRatio > 0.4 ? 'high' : difficultRatio > 0.25 ? 'medium' : 'low';
  }

  calculateValidationScore(improvementMetrics, riskAssessment) {
    let score = 0;
    
    // 改进指标权重
    score += improvementMetrics.pathEfficiency * 0.3;
    score += improvementMetrics.contentBalance * 0.25;
    score += improvementMetrics.adaptabilityScore * 0.25;
    score += improvementMetrics.engagementPotential * 0.2;
    
    // 风险惩罚
    const riskPenalty = Object.values(riskAssessment).reduce((penalty, risk) => {
      return penalty + (risk === 'high' ? 0.15 : risk === 'medium' ? 0.05 : 0);
    }, 0);
    
    return Math.max(0, Math.min(1, score - riskPenalty));
  }

  generateValidationRecommendations(validation) {
    const recommendations = [];
    
    if (validation.validationScore < 0.6) {
      recommendations.push('建议进一步优化路径，当前优化效果不够理想');
    }
    
    Object.entries(validation.riskAssessment).forEach(([riskType, level]) => {
      if (level === 'high') {
        switch (riskType) {
          case 'overloadRisk':
            recommendations.push('学习负荷过重，建议减少单次学习时间');
            break;
          case 'gapRisk':
            recommendations.push('存在知识缺口，建议补充前置知识点');
            break;
          case 'paceRisk':
            recommendations.push('学习节奏过快，建议适当放缓');
            break;
          case 'motivationRisk':
            recommendations.push('难度过高可能影响动机，建议增加激励机制');
            break;
        }
      }
    });
    
    return recommendations;
  }

  generateOptimizationRecommendations(adjustmentStrategy) {
    const recommendations = [];
    
    Object.entries(adjustmentStrategy).forEach(([strategyType, adjustments]) => {
      if (adjustments.length > 0) {
        switch (strategyType) {
          case 'priorityAdjustments':
            recommendations.push('已调整学习优先级，重点关注核心知识点');
            break;
          case 'sequenceModifications':
            recommendations.push('已优化学习序列，提高学习效率');
            break;
          case 'contentEnhancements':
            recommendations.push('已增强学习内容，提供更多支持');
            break;
          case 'difficultyAdjustments':
            recommendations.push('已调整难度梯度，使学习更加平滑');
            break;
          case 'timeAllocations':
            recommendations.push('已优化时间分配，提高学习效果');
            break;
          case 'supportMechanisms':
            recommendations.push('已加强支持机制，提供更多帮助');
            break;
        }
      }
    });
    
    return recommendations;
  }

  recordOptimizationHistory(record) {
    this.optimizationHistory.push(record);
    
    // 保持历史记录数量限制
    if (this.optimizationHistory.length > 50) {
      this.optimizationHistory = this.optimizationHistory.slice(-50);
    }
  }
}

module.exports = AdaptivePathOptimizer; 