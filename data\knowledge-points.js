/**
 * 知识点数据配置
 * 用于知识点详情页面的相关知识展示
 */

// 分数四则运算相关知识点数据
const fractionOperationsKnowledge = {
  // 前置知识点
  prerequisites: [
    {
      id: 'pre001',
      name: '整数的加减法',
      grade: 6,
      chapter: '第二章',
      masteryRate: 95,
      showDetail: false,
      suggestion: '基础掌握良好，建议通过练习题进一步巩固计算速度和准确性。可以尝试一些复杂的多位数运算来提升熟练度。',
      learningTime: '15分钟',
      difficulty: 'easy',
      importanceLevel: 'high',
      keyPoints: ['加法运算规则', '减法运算规则', '进位与借位', '运算顺序'],
      practiceType: '基础练习',
      masteryTips: '每天坚持10道混合运算题，培养数感'
    },
    {
      id: 'pre002', 
      name: '分数的基本概念',
      grade: 5,
      chapter: '第三章',
      masteryRate: 88,
      showDetail: false,
      suggestion: '理解较好，建议重点练习分数比较大小和简化分数的方法。需要加强对分数意义的理解。',
      learningTime: '20分钟',
      difficulty: 'easy',
      importanceLevel: 'high',
      keyPoints: ['分数的意义', '分数与除法的关系', '真分数与假分数', '分数的大小比较'],
      practiceType: '概念理解',
      masteryTips: '结合实际生活例子理解分数，如切蛋糕、分披萨等'
    },
    {
      id: 'pre003',
      name: '小数的四则运算',
      grade: 6,
      chapter: '第一章', 
      masteryRate: 82,
      showDetail: false,
      suggestion: '需要加强小数乘除法练习，特别是小数点位置的确定。建议多做竖式计算练习。',
      learningTime: '25分钟',
      difficulty: 'medium',
      importanceLevel: 'medium',
      keyPoints: ['小数加减法', '小数乘法', '小数除法', '小数点的移动规律'],
      practiceType: '计算练习',
      masteryTips: '掌握小数点对齐的方法，熟记小数运算法则'
    }
  ],
  
  // 后续知识点
  subsequent: [
    {
      id: 'sub001',
      name: '分式方程',
      grade: 8,
      chapter: '第五章',
      masteryRate: 25,
      showDetail: false,
      suggestion: '需要先熟练掌握分数运算和一元一次方程，建议先巩固基础知识再学习分式方程的解法。',
      learningTime: '40分钟',
      difficulty: 'hard',
      importanceLevel: 'high',
      prerequisites: ['分数四则运算', '一元一次方程'],
      keyPoints: ['分式方程的定义', '解分式方程的步骤', '增根的概念', '分式方程的应用'],
      practiceType: '应用练习',
      masteryTips: '先化简再求解，注意检验是否产生增根'
    },
    {
      id: 'sub002',
      name: '根式运算',
      grade: 8,
      chapter: '第六章',
      masteryRate: 15,
      showDetail: false,
      suggestion: '属于进阶内容，需要对有理数运算非常熟练后再学习。建议先掌握完全平方数的概念。',
      learningTime: '35分钟',
      difficulty: 'hard',
      importanceLevel: 'medium',
      prerequisites: ['有理数运算', '分数四则运算'],
      keyPoints: ['二次根式的概念', '根式的化简', '根式的运算法则', '根式的有理化'],
      practiceType: '思维拓展',
      masteryTips: '掌握完全平方数，熟练进行根式化简'
    },
    {
      id: 'sub003',
      name: '二次方程',
      grade: 9,
      chapter: '第二章',
      masteryRate: 0,
      showDetail: false,
      suggestion: '高阶内容，需要完成代数基础学习后再挑战。这是初中数学的重难点之一。',
      learningTime: '50分钟',
      difficulty: 'very_hard',
      importanceLevel: 'high',
      prerequisites: ['一元一次方程', '分式方程', '完全平方公式'],
      keyPoints: ['一元二次方程的标准形式', '因式分解法', '公式法', '配方法'],
      practiceType: '综合应用',
      masteryTips: '掌握多种解法，根据题目特点选择最适合的方法'
    }
  ],
  
  // 相关知识点
  related: [
    {
      id: 'rel001',
      name: '小数运算',
      grade: 7,
      chapter: '第一章',
      masteryRate: 72,
      showDetail: false,
      suggestion: '与分数运算相似，可以对比学习提高理解效果。重点掌握小数与分数的转换。',
      learningTime: '30分钟',
      difficulty: 'medium',
      importanceLevel: 'medium',
      similarity: 85,
      keyPoints: ['小数与分数互化', '小数的近似值', '循环小数', '小数运算律'],
      practiceType: '对比学习',
      masteryTips: '理解小数和分数的本质联系，掌握互化方法'
    },
    {
      id: 'rel002',
      name: '百分数计算',
      grade: 7,
      chapter: '第四章',
      masteryRate: 55,
      showDetail: false,
      suggestion: '分数的实际应用，建议结合实际问题练习。重点掌握百分数与分数、小数的关系。',
      learningTime: '25分钟',
      difficulty: 'medium',
      importanceLevel: 'high',
      similarity: 75,
      keyPoints: ['百分数的意义', '百分数与分数互化', '百分数的应用', '增长率和降低率'],
      practiceType: '实际应用',
      masteryTips: '结合生活实例，如折扣、利率等问题来理解'
    },
    {
      id: 'rel003',
      name: '比例运算',
      grade: 7,
      chapter: '第三章',
      masteryRate: 48,
      showDetail: false,
      suggestion: '与分数运算密切相关，可以同步学习。重点理解比例的基本性质。',
      learningTime: '30分钟',
      difficulty: 'medium',
      importanceLevel: 'medium',
      similarity: 80,
      keyPoints: ['比的基本性质', '比例的基本性质', '正比例和反比例', '比例的应用'],
      practiceType: '同步学习',
      masteryTips: '掌握比例的基本性质：内项之积等于外项之积'
    }
  ],
  
  // 扩展知识点
  extended: [
    {
      id: 'ext001',
      name: '分数指数幂',
      grade: 9,
      chapter: '第七章',
      masteryRate: 8,
      showDetail: false,
      suggestion: '高难度内容，需要对分数运算和指数概念都很熟练。这是高中数学的预备知识。',
      learningTime: '45分钟',
      difficulty: 'very_hard',
      importanceLevel: 'low',
      challengeLevel: 'advanced',
      prerequisites: ['分数四则运算', '指数运算', '幂的运算法则'],
      keyPoints: ['分数指数的定义', '分数指数幂的运算', '根式与分数指数幂的互化', '综合运算'],
      practiceType: '挑战练习',
      masteryTips: '理解分数指数的几何意义，掌握与根式的转换关系'
    },
    {
      id: 'ext002',
      name: '连分数',
      grade: 9,
      chapter: '第八章',
      masteryRate: 5,
      showDetail: false,
      suggestion: '数学竞赛级别内容，适合对数学有浓厚兴趣的同学。可以了解数学的美妙之处。',
      learningTime: '60分钟',
      difficulty: 'very_hard',
      importanceLevel: 'low',
      challengeLevel: 'competition',
      prerequisites: ['分数四则运算', '无理数', '近似计算'],
      keyPoints: ['连分数的定义', '简单连分数', '循环连分数', '连分数的应用'],
      practiceType: '竞赛拓展',
      masteryTips: '了解连分数在数论中的应用，培养数学思维'
    },
    {
      id: 'ext003',
      name: '分数函数',
      grade: 9,
      chapter: '第九章',
      masteryRate: 0,
      showDetail: false,
      suggestion: '需要函数基础，属于高中预备内容。可以提前了解函数思想。',
      learningTime: '55分钟',
      difficulty: 'very_hard',
      importanceLevel: 'medium',
      challengeLevel: 'advanced',
      prerequisites: ['函数基础', '分式方程', 'x-y坐标系'],
      keyPoints: ['分式函数的定义', '分式函数的图像', '分式函数的性质', '渐近线概念'],
      practiceType: '预备学习',
      masteryTips: '重点理解函数的概念，为高中数学打好基础'
    }
  ]
};

// AI智能推荐配置
const aiRecommendations = {
  priority: [
    {
      id: 'rec001',
      title: '优先学习',
      subtitle: '基础薄弱点',
      description: '需要重点加强',
      knowledgeIds: ['pre003', 'rel003'],
      urgency: 'high',
      estimatedTime: '25分钟'
    },
    {
      id: 'rec002', 
      title: '巩固复习',
      subtitle: '已掌握内容',
      description: '保持熟练度',
      knowledgeIds: ['pre001', 'pre002'],
      urgency: 'medium',
      estimatedTime: '15分钟'
    },
    {
      id: 'rec003',
      title: '挑战进阶',
      subtitle: '拓展提升',
      description: '挑战自我',
      knowledgeIds: ['sub001', 'rel002'],
      urgency: 'low',
      estimatedTime: '35分钟'
    }
  ]
};

// 统计信息配置
const knowledgeStats = {
  prerequisites: {
    masteryRate: 89,
    totalCount: 3,
    suggestion: '基础扎实，可以放心学习新知识'
  },
  subsequent: {
    masteryRate: 34,
    totalCount: 3,
    suggestion: '需要先学习前置知识，建议循序渐进'
  },
  related: {
    masteryRate: 45,
    totalCount: 3,
    suggestion: '可对比学习，加深理解'
  },
  extended: {
    masteryRate: 12,
    totalCount: 3,
    suggestion: '挑战性内容，适合学有余力的同学'
  }
};

// 导出配置
module.exports = {
  fractionOperationsKnowledge,
  aiRecommendations,
  knowledgeStats
}; 