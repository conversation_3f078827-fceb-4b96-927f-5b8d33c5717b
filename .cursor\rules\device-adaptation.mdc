---
description: 
globs: 
alwaysApply: true
---
# 设备适配规范

本项目采用统一的设备适配方案，确保应用在各种尺寸和类型的设备上有一致的用户体验。

## 适配原则

- 使用CSS变量处理不同设备的屏幕尺寸和安全区域
- 通过媒体查询适配不同屏幕尺寸
- 特殊设备（iPad、折叠屏）单独处理
- JavaScript动态计算和更新关键尺寸变量

## 设备适配样式文件

设备适配相关的样式统一放在 [styles/device-adaptation.wxss](mdc:styles/device-adaptation.wxss) 文件中，包含：

- 媒体查询适配规则
- 设备特定的样式类
- 安全区域处理
- 响应式布局辅助类

## 设备变量

页面容器需要添加 `device-container` 类以使用以下变量：

```css
.device-container {
  --status-bar-height: 44px; /* 状态栏高度 */
  --nav-bar-height: 44px;    /* 导航栏高度 */
  --safe-bottom: 0px;        /* 底部安全区域高度 */
  --is-ipad: 0;              /* 是否是iPad设备 */
}
```

## 设备检测工具

设备检测和处理相关的函数封装在 [utils/utils.js](mdc:utils/utils.js) 中：

```javascript
// 检测设备类型
isIPhoneX()    // 判断是否为刘海屏iPhone
isIPad()       // 判断是否为iPad设备
isAndroid()    // 判断是否为Android设备

// 获取设备信息
getSafeAreaInfo()  // 获取安全区域信息
setPageCSSVariables(selector, info)  // 设置页面CSS变量
```

## 安全区域处理

### 顶部安全区域

顶部安全区域（刘海屏）处理示例：

```wxml
<view class="status-bar-height"></view>
```

### 底部安全区域

底部安全区域（Home Indicator）处理示例：

```css
padding-bottom: calc(20rpx + var(--safe-bottom));
```

## 响应式布局

### 大屏幕设备适配

对于平板和折叠屏设备，使用媒体查询控制内容区域最大宽度：

```css
@media screen and (min-width: 768px) {
  .container {
    max-width: 750rpx;
    margin-left: auto;
    margin-right: auto;
  }
}
```

### 小屏幕设备适配

对于小屏幕设备，优化布局和内边距：

```css
@media screen and (max-height: 700px) {
  .container {
    padding: 8rpx 0;
  }
}
```

## 特殊设备处理

### iPad设备

对于iPad设备，使用 `ipad-mode` 类添加特殊样式：

```wxml
<view class="container {{isIPad ? 'ipad-mode' : ''}}">
```

## 初始化设备信息

在页面的 `onLoad` 和 `onShow` 生命周期中初始化设备信息：

```javascript
onLoad: function() {
  this.initDeviceInfo();
}

initDeviceInfo: function() {
  const safeAreaInfo = deviceUtils.getSafeAreaInfo();
  this.setData({
    statusBarHeight: safeAreaInfo.statusBarHeight,
    navBarHeight: safeAreaInfo.navBarHeight,
    safeAreaBottom: safeAreaInfo.safeAreaBottom,
    isIPad: deviceUtils.isIPad()
  });
  
  // 设置CSS变量
  deviceUtils.setPageCSSVariables('.container', safeAreaInfo);
}
```

## 屏幕旋转处理

处理屏幕旋转事件，更新设备信息：

```javascript
onResize: function(res) {
  // 屏幕旋转时重新获取设备信息
  this.initDeviceInfo();
}
```

## 最佳实践

1. 所有页面容器都应添加 `device-container` 类
2. 底部固定元素必须考虑安全区域高度
3. 使用媒体查询而非设备检测处理布局差异
4. 避免硬编码固定高度，使用CSS变量和计算值
5. 布局设计时考虑小屏幕和大屏幕双场景
6. 优先使用flex布局确保布局灵活性
