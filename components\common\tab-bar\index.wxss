/* 底部导航栏 */
@import "/styles/device-adaptation.wxss";

.tab-bar {
  height: 110rpx;
  background-color: #fff;
  display: flex;
  justify-content: space-around;
  align-items: center;
  border-top: 1rpx solid #f0f0f0;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 90;
  padding-bottom: var(--safe-bottom, 0px);
  box-sizing: content-box;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.03);
  /* 增加iOS底部安全区域支持 */
  padding-bottom: constant(safe-area-inset-bottom); /* iOS 11.0-11.2 */
  padding-bottom: env(safe-area-inset-bottom); /* iOS 11.2+ */
}

/* iPhone X及以上机型的底部导航栏增强样式 */
.iphonex-tab-bar {
  padding-bottom: constant(safe-area-inset-bottom); /* iOS 11.0-11.2 */
  padding-bottom: env(safe-area-inset-bottom); /* iOS 11.2+ */
  box-sizing: content-box;
  background-color: #ffffff; /* 确保底部背景色与导航栏一致 */
}

.tab-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 10rpx 0;
  position: relative;
  transition: all 0.2s ease;
}

.tab-item-hover {
  opacity: 0.8;
  transform: scale(0.96);
}

.tab-item:active {
  opacity: 0.8;
  transform: scale(0.96);
}

.tab-item .tab-text {
  font-size: 12px;
  color: #999;
  margin-top: 6rpx;
  transition: color 0.2s ease;
}

/* 高亮状态的底部导航项 */
.tab-item.active .tab-text {
  color: #7B72FB;
  font-weight: 500;
}

.tab-item.active .icon-history {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M4 4h16a2 2 0 0 1 2 2v10a2 2 0 0 1-2 2H7l-4 4V6a2 2 0 0 1 2-2z' stroke='%237B72FB' stroke-width='2' fill='none'/%3E%3C/svg%3E");
}

.tab-item.active .icon-class {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2' stroke='%237B72FB' stroke-width='2' fill='none'/%3E%3Ccircle cx='9' cy='7' r='4' stroke='%237B72FB' stroke-width='2' fill='none'/%3E%3Cpath d='M23 21v-2a4 4 0 0 0-3-3.87' stroke='%237B72FB' stroke-width='2' fill='none'/%3E%3Cpath d='M16 3.13a4 4 0 0 1 0 7.75' stroke='%237B72FB' stroke-width='2' fill='none'/%3E%3C/svg%3E");
}

.tab-item.active .icon-ai {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Crect x='4' y='8' width='16' height='8' rx='4' stroke='%237B72FB' stroke-width='2' fill='none'/%3E%3Crect x='8' y='2' width='8' height='6' rx='2' stroke='%237B72FB' stroke-width='2' fill='none'/%3E%3Ccircle cx='8.5' cy='12' r='1' fill='%237B72FB'/%3E%3Ccircle cx='15.5' cy='12' r='1' fill='%237B72FB'/%3E%3Cpath d='M12 16v2' stroke='%237B72FB' stroke-width='2'/%3E%3C/svg%3E");
}

.tab-item.active .icon-notebook {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z' stroke='%237B72FB' stroke-width='2' fill='none'/%3E%3Cpath d='M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z' stroke='%237B72FB' stroke-width='2' fill='none'/%3E%3C/svg%3E");
}

.tab-item.active .icon-profile {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Ccircle cx='12' cy='8' r='4' stroke='%237B72FB' stroke-width='2' fill='none'/%3E%3Cpath d='M4 20v-1a4 4 0 0 1 4-4h8a4 4 0 0 1 4 4v1' stroke='%237B72FB' stroke-width='2' fill='none'/%3E%3C/svg%3E");
}

/* 图标样式 */
.icon {
  width: 48rpx;
  height: 48rpx;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.icon-history {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M4 4h16a2 2 0 0 1 2 2v10a2 2 0 0 1-2 2H7l-4 4V6a2 2 0 0 1 2-2z' stroke='%23999999' stroke-width='2' fill='none'/%3E%3C/svg%3E");
}

.icon-class {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2' stroke='%23999999' stroke-width='2' fill='none'/%3E%3Ccircle cx='9' cy='7' r='4' stroke='%23999999' stroke-width='2' fill='none'/%3E%3Cpath d='M23 21v-2a4 4 0 0 0-3-3.87' stroke='%23999999' stroke-width='2' fill='none'/%3E%3Cpath d='M16 3.13a4 4 0 0 1 0 7.75' stroke='%23999999' stroke-width='2' fill='none'/%3E%3C/svg%3E");
}

.icon-ai {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Crect x='4' y='8' width='16' height='8' rx='4' stroke='%23999999' stroke-width='2' fill='none'/%3E%3Crect x='8' y='2' width='8' height='6' rx='2' stroke='%23999999' stroke-width='2' fill='none'/%3E%3Ccircle cx='8.5' cy='12' r='1' fill='%23999999'/%3E%3Ccircle cx='15.5' cy='12' r='1' fill='%23999999'/%3E%3Cpath d='M12 16v2' stroke='%23999999' stroke-width='2'/%3E%3C/svg%3E");
}

.icon-notebook {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z' stroke='%23999999' stroke-width='2' fill='none'/%3E%3Cpath d='M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z' stroke='%23999999' stroke-width='2' fill='none'/%3E%3C/svg%3E");
}

.icon-profile {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Ccircle cx='12' cy='8' r='4' stroke='%23999999' stroke-width='2' fill='none'/%3E%3Cpath d='M4 20v-1a4 4 0 0 1 4-4h8a4 4 0 0 1 4 4v1' stroke='%23999999' stroke-width='2' fill='none'/%3E%3C/svg%3E");
}

.icon-container {
  width: 52rpx;
  height: 52rpx;
  display: flex;
  align-items: center;
  justify-content: center;
} 