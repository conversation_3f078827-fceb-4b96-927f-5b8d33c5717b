// 智能诊断模块演示程序 - 修复输出截断版本
// 简单的输出助手
const output = {
  add: (message, level = 'info') => {
    const levelIcons = {
      info: '📝',
      warn: '⚠️',
      error: '❌',
      success: '✅'
    };
    const icon = levelIcons[level] || '📝';
    console.log(`${icon} ${message}`);
  },
  createSection: (title) => ({
    add: (message) => console.log(`📝 ${message}`),
    separator: () => console.log('='.repeat(60)),
    subsection: (title) => console.log(`\n📋 ${title}`)
  })
};

// 模拟学生学习数据
const studentData = {
  studentId: 'demo_student_001',
  gradeLevel: 6,
  learningData: {
    'e6n001': { totalTime: 3600, attemptCount: 3, correctRate: 0.85, name: '分数乘整数' },
    'e6n002': { totalTime: 2400, attemptCount: 5, correctRate: 0.65, name: '一个数乘分数' },
    'e6n003': { totalTime: 4200, attemptCount: 4, correctRate: 0.75, name: '分数乘分数' },
    'e6n007': { totalTime: 1800, attemptCount: 6, correctRate: 0.45, name: '分数除以整数' },
    'e6n008': { totalTime: 3000, attemptCount: 7, correctRate: 0.35, name: '一个数除以分数' },
    'e6n014': { totalTime: 2700, attemptCount: 2, correctRate: 0.90, name: '圆的认识' },
    'e6n015': { totalTime: 3900, attemptCount: 3, correctRate: 0.80, name: '圆的周长' },
    'e6n018': { totalTime: 2100, attemptCount: 4, correctRate: 0.70, name: '百分数的意义' }
  }
};

/**
 * 演示知识掌握分析
 */
function demonstrateKnowledgeAnalysis() {
  const section = output.createSection('知识掌握分析');
  section.separator();
  section.add('📊 开始分析学生知识掌握情况...');
  
  let totalScore = 0;
  let totalCount = 0;
  const strengths = [];
  const weaknesses = [];
  
  section.subsection('知识点掌握情况详细分析');
  
  for (const [nodeId, data] of Object.entries(studentData.learningData)) {
    // 计算掌握度（正确率 60% + 时间效率 25% + 稳定性 15%）
    const timeEfficiency = Math.min(data.totalTime / 3600, 1) * 0.25;
    const stabilityBonus = data.attemptCount > 0 ? Math.min(1 / data.attemptCount, 0.15) : 0;
    const masteryScore = data.correctRate * 0.6 + timeEfficiency + stabilityBonus;
    
    totalScore += masteryScore;
    totalCount++;
    
    const level = masteryScore >= 0.8 ? '优秀' : masteryScore >= 0.6 ? '良好' : '薄弱';
    const emoji = masteryScore >= 0.8 ? '✅' : masteryScore >= 0.6 ? '⚠️' : '❌';
    
    section.add(`${emoji} ${data.name}: ${Math.round(masteryScore * 100)}% (${level})`);
    section.add(`   📊 正确率: ${Math.round(data.correctRate * 100)}% | ⏱️ 学习时长: ${Math.round(data.totalTime/60)}分钟 | 🔄 尝试次数: ${data.attemptCount}`);
    
    if (masteryScore >= 0.8) {
      strengths.push({ nodeId, name: data.name, score: masteryScore });
    } else if (masteryScore < 0.6) {
      weaknesses.push({ nodeId, name: data.name, score: masteryScore });
    }
  }
  
  const overallScore = totalScore / totalCount;
  const overallLevel = overallScore >= 0.9 ? '优秀' : 
                      overallScore >= 0.8 ? '良好' : 
                      overallScore >= 0.6 ? '中等' : '待提高';
  
  section.subsection('总体分析结果');
  section.add(`📈 整体掌握度: ${Math.round(overallScore * 100)}% (${overallLevel})`);
  section.add(`💪 优势知识点: ${strengths.length}个`);
  section.add(`⚠️ 薄弱知识点: ${weaknesses.length}个`);
  
  return { overallScore, overallLevel, strengths, weaknesses };
}

/**
 * 演示认知发展分析
 */
function demonstrateCognitiveAnalysis() {
  const section = output.createSection('认知发展分析');
  section.separator();
  section.add('🧠 开始分析学生认知发展水平...');
  
  const gradeLevel = studentData.gradeLevel;
  const developmentStage = gradeLevel <= 3 ? '具体运算早期' :
                          gradeLevel <= 6 ? '具体运算晚期' :
                          gradeLevel <= 9 ? '形式运算早期' : '形式运算成熟期';
  
  // 模拟认知能力评估
  const cognitiveCapabilities = {
    abstractThinking: 0.65 + Math.random() * 0.2,
    logicalReasoning: 0.70 + Math.random() * 0.15,
    spatialVisualization: 0.60 + Math.random() * 0.25,
    problemSolving: 0.68 + Math.random() * 0.18
  };
  
  section.add(`🎯 认知发展阶段: ${developmentStage}`);
  section.add(`📚 年级水平: ${gradeLevel}年级`);
  
  section.subsection('认知能力评估详情');
  for (const [ability, score] of Object.entries(cognitiveCapabilities)) {
    const level = score >= 0.8 ? '优秀' : score >= 0.6 ? '良好' : '发展中';
    const abilityName = {
      abstractThinking: '抽象思维',
      logicalReasoning: '逻辑推理', 
      spatialVisualization: '空间想象',
      problemSolving: '问题解决'
    }[ability];
    
    section.add(`🎯 ${abilityName}: ${Math.round(score * 100)}% (${level})`);
  }
  
  const avgCognitive = Object.values(cognitiveCapabilities).reduce((a, b) => a + b) / 4;
  const readinessForAdvancement = avgCognitive >= 0.75;
  
  section.add(`✨ 认知发展综合评估: ${Math.round(avgCognitive * 100)}%`);
  section.add(`🚀 进阶准备度: ${readinessForAdvancement ? '已准备好' : '需要巩固'}`);
  
  return { developmentStage, cognitiveCapabilities, readinessForAdvancement };
}

/**
 * 演示薄弱点分析
 */
function demonstrateWeaknessAnalysis(knowledgeAnalysis) {
  const section = output.createSection('薄弱点深度分析');
  section.separator();
  section.add('⚠️ 开始深度分析学习薄弱点...');
  
  const { weaknesses } = knowledgeAnalysis;
  
  if (weaknesses.length === 0) {
    section.add('🎉 恭喜！没有发现明显的薄弱点');
    return { weaknesses: [], recommendations: [] };
  }
  
  section.add(`📋 发现 ${weaknesses.length} 个薄弱知识点`);
  
  const recommendations = [];
  
  weaknesses.forEach((weakness, index) => {
    section.subsection(`薄弱点 ${index + 1}`);
    section.add(`❌ 知识点: ${weakness.name}`);
    section.add(`📊 掌握度: ${Math.round(weakness.score * 100)}%`);
    
    // 分析问题根因
    let rootCause = '';
    let solution = '';
    
    if (weakness.name.includes('分数除')) {
      rootCause = '可能缺乏倒数概念的理解';
      solution = '建议先学习倒数的认识，再进行分数除法练习';
    } else if (weakness.name.includes('乘分数')) {
      rootCause = '分数乘法概念理解不够深入';
      solution = '建议通过图形化方式加强分数乘法的意义理解';
    } else {
      rootCause = '基础概念需要进一步巩固';
      solution = '建议加强相关前置知识的复习';
    }
    
    section.add(`🔍 根因分析: ${rootCause}`);
    section.add(`💡 改进建议: ${solution}`);
    
    recommendations.push({
      weakness: weakness.name,
      rootCause,
      solution,
      priority: weakness.score < 0.4 ? '高' : '中'
    });
  });
  
  return { weaknesses, recommendations };
}

/**
 * 演示学习路径推荐
 */
function demonstrateLearningPath(weaknessAnalysis) {
  const section = output.createSection('个性化学习路径推荐');
  section.separator();
  section.add('🛤️ 开始生成个性化学习路径...');
  
  const { recommendations } = weaknessAnalysis;
  
  if (recommendations.length === 0) {
    section.add('🎯 当前学习状态良好，建议继续保持并适当提升难度');
    return;
  }
  
  section.add('📋 基于薄弱点分析的三阶段学习路径规划:');
  
  // 阶段一：立即处理
  section.subsection('阶段一: 紧急补强 (1-2周)');
  const highPriority = recommendations.filter(r => r.priority === '高');
  if (highPriority.length > 0) {
    highPriority.forEach((rec, index) => {
      section.add(`${index + 1}. 🎯 ${rec.weakness}`);
      section.add(`   📚 行动: ${rec.solution}`);
      section.add(`   ⏰ 预计时间: 8-12小时`);
      section.add(`   ✅ 成功标准: 掌握度提升至70%以上`);
    });
  } else {
    section.add('   ✅ 无需紧急处理的薄弱点');
  }
  
  // 阶段二：系统巩固
  section.subsection('阶段二: 系统巩固 (3-4周)');
  const mediumPriority = recommendations.filter(r => r.priority === '中');
  if (mediumPriority.length > 0) {
    mediumPriority.forEach((rec, index) => {
      section.add(`${index + 1}. 📚 ${rec.weakness}`);
      section.add(`   📖 行动: ${rec.solution}`);
      section.add(`   ⏰ 预计时间: 6-8小时`);
      section.add(`   ✅ 成功标准: 掌握度提升至80%以上`);
    });
  } else {
    section.add('   ✅ 基础巩固良好');
  }
  
  // 阶段三：能力提升
  section.subsection('阶段三: 能力提升 (5-8周)');
  section.add('1. 🚀 综合应用能力提升');
  section.add('   📈 行动: 多知识点综合练习');
  section.add('   ⏰ 预计时间: 15-20小时');
  section.add('   ✅ 成功标准: 能够熟练解决复合型问题');
  
  const totalHours = (highPriority.length * 10) + (mediumPriority.length * 7) + 18;
  section.add(`⏰ 预计总学习时间: ${totalHours}小时`);
  section.add(`📅 建议完成时间: ${Math.ceil(totalHours / 10)}周 (每周10小时)`);
}

/**
 * 演示核心素养分析
 */
function demonstrateCoreCompetencies() {
  const section = output.createSection('数学核心素养发展分析');
  section.separator();
  section.add('🎯 开始分析数学核心素养发展水平...');
  
  const competencies = {
    '数学抽象': 0.72 + Math.random() * 0.15,
    '逻辑推理': 0.68 + Math.random() * 0.18,
    '数学建模': 0.65 + Math.random() * 0.20,
    '直观想象': 0.70 + Math.random() * 0.15,
    '数学运算': 0.75 + Math.random() * 0.12,
    '数据分析': 0.60 + Math.random() * 0.25
  };
  
  section.subsection('六大核心素养发展水平');
  
  Object.entries(competencies).forEach(([competency, score]) => {
    const level = score >= 0.85 ? '高级' : 
                 score >= 0.70 ? '熟练' : 
                 score >= 0.55 ? '发展中' : '初级';
    
    const emoji = score >= 0.85 ? '🌟' : 
                 score >= 0.70 ? '✨' : 
                 score >= 0.55 ? '🔄' : '🌱';
    
    section.add(`${emoji} ${competency}: ${Math.round(score * 100)}% (${level})`);
    
    // 针对性建议
    if (score < 0.70) {
      let suggestion = '';
      switch(competency) {
        case '数学抽象':
          suggestion = '建议多接触抽象概念，如负数、分数等';
          break;
        case '逻辑推理':
          suggestion = '建议加强逻辑思维训练，如推理游戏';
          break;
        case '数学建模':
          suggestion = '建议多做实际应用题，培养建模思维';
          break;
        case '直观想象':
          suggestion = '建议使用图形化工具辅助学习';
          break;
        case '数学运算':
          suggestion = '建议加强基础运算练习';
          break;
        case '数据分析':
          suggestion = '建议接触统计图表分析';
          break;
      }
      section.add(`   💡 发展建议: ${suggestion}`);
    }
  });
  
  const avgCompetency = Object.values(competencies).reduce((a, b) => a + b) / 6;
  section.add(`📊 素养发展综合评估: ${Math.round(avgCompetency * 100)}%`);
  
  return competencies;
}

/**
 * 演示智能建议生成
 */
function demonstratePersonalizedAdvice(cognitiveAnalysis, competencies) {
  const section = output.createSection('个性化学习建议');
  section.separator();
  section.add('💡 开始生成个性化学习建议...');
  
  const { readinessForAdvancement, developmentStage } = cognitiveAnalysis;
  const avgCompetency = Object.values(competencies).reduce((a, b) => a + b) / 6;
  
  section.subsection('学习策略建议');
  
  if (developmentStage === '具体运算晚期') {
    section.add('📚 适合具象化学习方式，多使用图形、模型辅助理解');
    section.add('🎯 逐步引入抽象概念，但要有充分的具体支撑');
  } else if (developmentStage === '形式运算早期') {
    section.add('🧠 可以开始接触更多抽象概念和逻辑推理');
    section.add('🔍 适合进行假设推理和系统性思考训练');
  }
  
  section.subsection('家长指导建议');
  if (readinessForAdvancement) {
    section.add('✅ 孩子学习状态良好，可以适当增加学习挑战');
    section.add('🌟 鼓励孩子主动探索和思考数学问题');
  } else {
    section.add('🤝 当前需要巩固基础，家长应多给予耐心和鼓励');
    section.add('💪 重点关注孩子的学习过程而非结果');
  }
  
  section.subsection('教学方法建议');
  if (avgCompetency < 0.65) {
    section.add('🎮 建议采用游戏化教学，提高学习兴趣');
    section.add('🔄 注重基础概念的反复练习和巩固');
  } else {
    section.add('🔬 可以尝试探究式教学，培养独立思考能力');
    section.add('🎯 适当增加综合性和挑战性题目');
  }
  
  section.subsection('技术辅助建议');
  section.add('📱 推荐使用数学可视化软件辅助理解');
  section.add('💻 利用在线练习平台进行个性化训练');
  section.add('⏰ 建议每日学习时间控制在45-60分钟');
}

/**
 * 主演示程序
 */
async function runDemo() {
  output.add('🎯 智能诊断模块功能演示开始');
  output.add('='.repeat(60));
  output.add(`👨‍🎓 学生: ${studentData.studentId} (${studentData.gradeLevel}年级)`);
  
  // 简化进度显示
  const progressSteps = ['17%', '33%', '50%', '67%', '83%', '100%'];
  let currentStep = 0;
  const progress = {
    update: () => {
      if (currentStep < progressSteps.length) {
        output.add(`诊断进度: [${progressSteps[currentStep++]}] 完成`);
      }
    },
    complete: () => output.add(`诊断进度: [100%] 全部完成 ✅`)
  };
  
  try {
    // 1. 知识掌握分析
    progress.update();
    const knowledgeAnalysis = demonstrateKnowledgeAnalysis();
    
    // 2. 认知发展分析  
    progress.update();
    const cognitiveAnalysis = demonstrateCognitiveAnalysis();
    
    // 3. 薄弱点分析
    progress.update();
    const weaknessAnalysis = demonstrateWeaknessAnalysis(knowledgeAnalysis);
    
    // 4. 学习路径推荐
    progress.update();
    demonstrateLearningPath(weaknessAnalysis);
    
    // 5. 核心素养分析
    progress.update();
    const competencies = demonstrateCoreCompetencies();
    
    // 6. 个性化建议
    progress.update();
    demonstratePersonalizedAdvice(cognitiveAnalysis, competencies);
    
    progress.complete();
    
    output.add('🎊 智能诊断演示完成！');
    output.add('='.repeat(60));
    output.add('这就是基于知识图谱的智能诊断系统的核心功能展示！');
    output.add('系统能够全面分析学生的学习状况，并提供个性化的改进建议。');
    
    // 简化版本不保存文件
    output.add('📄 演示记录显示在控制台中', 'info');
    
  } catch (error) {
    output.add(`❌ 演示过程中出现错误: ${error.message}`, 'error');
  }
}

// 主函数
async function main() {
  output.add('🚀 启动智能诊断模块演示程序');
  
  // 检查命令行参数
  const args = process.argv.slice(2);
  
  if (args.includes('--help')) {
    output.add('使用方法:');
    output.add('  node demo-diagnosis-fixed.js          # 正常运行');
    output.add('  node demo-diagnosis-fixed.js --silent # 静默模式');
    output.add('  node demo-diagnosis-fixed.js --save   # 保存到文件');
    output.add('  node demo-diagnosis-fixed.js --demo   # 演示模式');
    return;
  }
  
  await runDemo();
  
  output.add('✨ 演示程序执行完成');
}

// 错误处理 - 云函数兼容版本
process.on('unhandledRejection', (reason, promise) => {
  output.add(`未处理的Promise拒绝: ${reason}`, 'error');
  // 云函数环境中不使用process.exit
  console.error('Unhandled Rejection:', reason);
});

process.on('uncaughtException', (error) => {
  output.add(`未捕获的异常: ${error.message}`, 'error');
  // 云函数环境中不使用process.exit
  console.error('Uncaught Exception:', error);
});

// 运行程序 - 云函数兼容版本
// 移除require.main检查，直接导出函数供云函数使用
// if (require.main === module) {
//   main().catch(error => {
//     output.add(`程序执行失败: ${error.message}`, 'error');
//     process.exit(1);
//   });
// }

// 云函数兼容的启动函数
async function startDemo() {
  try {
    output.add('🚀 启动智能诊断模块演示程序');
    await runDemo();
    output.add('✨ 演示程序执行完成');
    return {
      success: true,
      message: '演示完成',
      data: output.getAll()
    };
  } catch (error) {
    output.add(`程序执行失败: ${error.message}`, 'error');
    return {
      success: false,
      message: error.message,
      data: output.getAll()
    };
  }
}

module.exports = { 
  runDemo, 
  studentData, 
  main, 
  startDemo
}; 