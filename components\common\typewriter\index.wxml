<view class="typewriter-container" bindtap="onTap">
  <!-- 标准模式 -->
  <block wx:if="{{typingMode === 'standard' || typingMode === undefined}}">
    <text class="typewriter-text text-item" user-select="{{true}}">{{displayText}}</text>
  </block>
  
  <!-- 高亮模式 - 由于没有具体的高亮逻辑，这里只是提供一个基本结构 -->
  <block wx:elif="{{typingMode === 'highlight'}}">
    <view class="highlight-text">
      <text class="text-item" user-select="{{true}}">{{displayText}}</text>
    </view>
  </block>
  
  <!-- 淡入模式 -->
  <block wx:elif="{{typingMode === 'fade'}}">
    <text class="typewriter-text text-item fade-in" user-select="{{true}}">{{displayText}}</text>
  </block>
  
  <view class="typewriter-cursor {{isTyping && showCursor ? 'active' : ''}}" wx:if="{{showCursor && (!isComplete || isTyping)}}"></view>
</view> 