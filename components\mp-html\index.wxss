._root {
  padding: 0 !important;
  overflow-x: auto;
  overflow-y: hidden;
  -webkit-overflow-scrolling: touch;
  font-size: inherit;
  margin: 0;
}
._select {
  -webkit-user-select: text;
  user-select: text;
}

._root p,
._root div,
._root h1,
._root h2,
._root h3,
._root h4,
._root h5,
._root h6,
._root ul,
._root ol,
._root li,
._root pre,
._root blockquote {
  margin: 0 !important;
  padding: 0 !important;
  font-size: inherit !important;
  line-height: inherit !important;
}

._root span {
  font-size: inherit !important;
  line-height: inherit !important;
}

/* 减少段落和标题间的空白 */
._root p, ._root h1, ._root h2, ._root h3, ._root h4, ._root h5, ._root h6 {
  margin-top: 4px !important;
  margin-bottom: 4px !important;
}

/* 控制列表项间距 */
._root ul, ._root ol {
  margin-top: 4px !important;
  margin-bottom: 4px !important;
  padding-left: 16px !important;
}

._root li {
  margin-top: 2px !important;
  margin-bottom: 2px !important;
}

/* 控制表格和其他元素样式 */
._root table {
  margin: 4px 0 !important;
}

/* 控制代码块和引用样式 */
._root pre, ._root blockquote {
  margin: 4px 0 !important;
}