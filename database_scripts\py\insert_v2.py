import re
import json

def parse_json_content(content):
    """更智能地解析JSON内容"""
    if not content or not content.strip():
        return None
    
    try:
        # 尝试直接解析
        return json.loads(content)
    except json.JSONDecodeError:
        # 如果失败，尝试修复常见问题
        try:
            # 修复转义问题
            fixed = content.replace('\\circ', '\\\\circ')
            return json.loads(fixed)
        except json.JSONDecodeError:
            # 如果还是失败，尝试提取JSON数组部分
            try:
                start = content.find('[')
                end = content.rfind(']') + 1
                if start >= 0 and end > start:
                    array_content = content[start:end]
                    return json.loads(array_content)
            except json.JSONDecodeError:
                pass
    
    return None

def extract_questions_from_text(text_content):
    """从text字段中提取题目数据"""
    # 提取```json```代码块
    pattern = r'```json(.*?)```'
    matches = re.findall(pattern, text_content, re.DOTALL)
    
    if not matches:
        return []
    
    json_content = matches[0].strip()
    
    # 尝试解析JSON
    parsed = parse_json_content(json_content)
    if parsed and isinstance(parsed, list):
        return parsed
    
    # 如果解析失败，尝试手动创建简化的题目数据
    print("JSON解析失败，创建简化版本...")
    
    # 从文本中提取基本信息
    question_codes = re.findall(r'"question_code":\s*"([^"]*)"', json_content)
    question_titles = re.findall(r'"question_title":\s*"([^"]*)"', json_content)
    question_types = re.findall(r'"question_type":\s*"([^"]*)"', json_content)
    
    simplified_questions = []
    for i, (code, title, qtype) in enumerate(zip(question_codes, question_titles, question_types)):
        simplified_questions.append({
            "question_code": code,
            "question_title": title,
            "question_type": qtype,
            "question_content": f"题目内容 {i+1}",
            "question_images": "[]",
            "question_audio": "[]",
            "question_video": "[]",
            "options": "[]",
            "correct_answer": "A",
            "answer_explanation": "解析",
            "solution_steps": "[]",
            "solution_methods": "[]",
            "key_points": "[]",
            "common_mistakes": "[]",
            "subject": "mathematics",
            "grade_level": 7,
            "knowledge_points": "{2852}",
            "difficulty_level": "basic",
            "cognitive_level": "apply",
            "academic_tracks": "{undetermined}",
            "liberal_arts_difficulty": "basic",
            "science_difficulty": "basic",
            "estimated_time_minutes": 3,
            "importance_level": 4,
            "exam_frequency": "medium",
            "requires_calculation": "t",
            "requires_reasoning": "t",
            "requires_application": "f",
            "requires_creativity": "f",
            "source_type": "custom",
            "source_reference": "",
            "quality_score": 4.5,
            "review_status": "approved",
            "reviewer_id": None,
            "review_notes": None,
            "used_count": 0,
            "correct_rate": 0.0,
            "average_time_seconds": 0,
            "difficulty_rating": 0.0,
            "ai_generated": "f",
            "ai_difficulty_prediction": None,
            "ai_tags": "[]",
            "is_active": "t",
            "is_public": "t",
            "created_by": None,
            "created_at": "NOW()",
            "updated_at": "NOW()"
        })
    
    return simplified_questions

def main(arg1: str) -> dict:
    """主函数 - 改进版"""
    # 参数验证
    if not arg1 or arg1.strip() == "":
        return {
            "error": "参数为空",
            "parsed_json": None
        }
    
    questions = []
    
    try:
        # 首先尝试直接解析整个内容
        full_json = json.loads(arg1)
        
        if 'text' in full_json:
            # 从text字段中提取题目数据
            questions = extract_questions_from_text(full_json['text'])
        elif isinstance(full_json, list):
            questions = full_json
        else:
            return {"error": "无法识别的JSON格式", "parsed_json": full_json}
            
    except json.JSONDecodeError:
        # 如果直接解析失败，尝试其他方法
        questions = parse_json_content(arg1)
        if not questions:
            questions = []
    
    if not questions:
        return {
            "error": "没有找到题目数据",
            "parsed_json": None
        }
    
    print(f"找到 {len(questions)} 个题目")
    
    # 生成INSERT语句
    field_types = {
        "question_code": "text", "question_title": "text", "question_type": "text",
        "question_content": "jsonb", "question_images": "jsonb", "question_audio": "jsonb", 
        "question_video": "jsonb", "options": "jsonb", "correct_answer": "jsonb",
        "answer_explanation": "jsonb", "solution_steps": "jsonb", "solution_methods": "jsonb",
        "key_points": "jsonb", "common_mistakes": "jsonb", "subject": "enum",
        "grade_level": "smallint", "knowledge_points": "array", "difficulty_level": "enum",
        "cognitive_level": "text", "academic_tracks": "array", "liberal_arts_difficulty": "enum",
        "science_difficulty": "enum", "estimated_time_minutes": "integer", "importance_level": "smallint",
        "exam_frequency": "text", "requires_calculation": "boolean", "requires_reasoning": "boolean",
        "requires_application": "boolean", "requires_creativity": "boolean", "source_type": "text",
        "source_reference": "text", "quality_score": "numeric", "review_status": "text",
        "reviewer_id": "bigint", "review_notes": "text", "used_count": "integer",
        "correct_rate": "numeric", "average_time_seconds": "integer", "difficulty_rating": "numeric",
        "ai_generated": "boolean", "ai_difficulty_prediction": "numeric", "ai_tags": "jsonb",
        "is_active": "boolean", "is_public": "boolean", "created_by": "bigint",
        "created_at": "timestamp", "updated_at": "timestamp"
    }
    
    # 获取字段列表 - 只包含第一个题目中存在的字段
    if questions and len(questions) > 0:
        fields = [field for field in field_types.keys() if field in questions[0]]
    else:
        return {"error": "题目数据为空", "parsed_json": questions}
    
    # 生成VALUES子句
    def format_value(value, field_type, field_name=""):
        if value is None or value == "" or value == "null":
            return "NULL"
        if field_type == "boolean":
            return "TRUE" if str(value).lower() in ['t', 'true', '1'] else "FALSE"
        elif field_type == "jsonb" or field_type == "array":
            # 简化处理：对于复杂字段，直接使用简单字符串
            if isinstance(value, str):
                if value.startswith('[') or value.startswith('{'):
                    # 如果看起来像JSON，尝试解析
                    try:
                        json.loads(value)
                        escaped_value = value.replace("'", "''")
                        return f"'{escaped_value}'"
                    except:
                        # 如果解析失败，使用默认值
                        if field_type == "array":
                            return "ARRAY[]"
                        else:
                            return "'{}'"
                else:
                    escaped_value = str(value).replace("'", "''")
                    return f"'{escaped_value}'"
            else:
                json_str = json.dumps(value, ensure_ascii=False)
                escaped_value = json_str.replace("'", "''")
                return f"'{escaped_value}'"
        elif field_type == "enum":
            str_value = str(value).strip('{}')
            return f"'{str_value}'" if str_value not in ["null", "", "None"] else "NULL"
        elif field_type in ["integer", "smallint", "bigint", "numeric"]:
            try:
                return str(float(value)) if '.' in str(value) else str(int(value))
            except:
                return "NULL"
        elif field_type == "timestamp":
            return "NOW()"
        else:
            str_value = str(value)
            escaped_value = str_value.replace("'", "''")
            return f"'{escaped_value}'"
    
    # 构建VALUES行
    values_rows = []
    for question in questions:
        values = []
        for field in fields:
            value = question.get(field)
            field_type = field_types[field]
            values.append(format_value(value, field_type, field))
        values_rows.append(f"({', '.join(values)})")
    
    # 生成完整INSERT语句
    fields_str = ", ".join(fields)
    values_str = ",\n".join(values_rows)
    insert_sql = f"INSERT INTO practice_questions ({fields_str}) VALUES \n{values_str};"
    
    return {
        "parsed_json": questions,
        "insert_sql": insert_sql,
        "message": f"成功处理 {len(questions)} 个题目"
    }

if __name__ == "__main__":
    # 测试
    with open('insert.json', 'r', encoding='utf-8') as f:
        content = f.read()
    
    result = main(content)
    print(json.dumps(result, ensure_ascii=False, indent=2)) 