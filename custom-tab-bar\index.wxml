<!-- 底部导航栏组件 -->
<view class="tab-bar {{isIPad ? 'ipad-tab-bar' : ''}} {{isIPhoneX ? 'iphonex-tab-bar' : ''}}">
  <view wx:for="{{tabs}}" wx:key="id"
        class="tab-item {{activeTab === item.id ? 'active' : ''}}" 
        hover-class="tab-item-hover"
        hover-start-time="20"
        hover-stay-time="70"
        bindtap="onTabTap" 
        data-type="{{item.id}}">
    <view class="icon-container">
      <view class="icon {{item.icon}}"></view>
    </view>
    <text class="tab-text">{{item.text}}</text>
  </view>
</view> 