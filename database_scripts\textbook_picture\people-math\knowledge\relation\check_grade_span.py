#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
年级内部知识点关系grade_span检查脚本
功能：检查年级内部关系文件中的grade_span值是否都为0
作者：AI助手
创建时间：2025-01-22
"""

import re
import os
import sys
from typing import List, Dict, Tuple

class GradeSpanChecker:
    """年级内部关系grade_span检查器"""
    
    def __init__(self, file_path: str):
        """
        初始化检查器
        
        Args:
            file_path: SQL文件路径
        """
        self.file_path = file_path
        self.total_relationships = 0
        self.incorrect_grade_spans = []
        
    def check_sql_file(self) -> Dict:
        """
        检查SQL文件中的grade_span值
        
        Returns:
            检查结果字典
        """
        if not os.path.exists(self.file_path):
            return {
                'status': 'error',
                'message': f'文件不存在: {self.file_path}'
            }
        
        try:
            with open(self.file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 分行处理，逐行检查
            lines = content.split('\n')
            for line_num, line in enumerate(lines, 1):
                line = line.strip()
                
                # 跳过注释行和空行
                if not line or line.startswith('--') or line.startswith('/*') or line.startswith('*'):
                    continue
                
                # 查找包含VALUES的INSERT语句行
                if 'VALUES' in line.upper() and any(pattern in line for pattern in [
                    "'prerequisite'", "'related'", "'application_of'", "'successor'"
                ]):
                    self.total_relationships += 1
                    grade_span_value = self._extract_grade_span_from_line(line)
                    
                    if grade_span_value is not None and grade_span_value != 0:
                        self.incorrect_grade_spans.append({
                            'line_number': line_num,
                            'grade_span': grade_span_value,
                            'line_content': line[:150] + '...' if len(line) > 150 else line
                        })
            
            return self._generate_report()
            
        except Exception as e:
            return {
                'status': 'error',
                'message': f'读取文件出错: {str(e)}'
            }
    
    def _extract_grade_span_from_line(self, line: str) -> int:
        """
        从SQL行中提取grade_span值
        使用正则表达式查找 'vertical', 数字 或 'horizontal', 数字 的模式
        
        Args:
            line: SQL行内容
            
        Returns:
            grade_span值，如果解析失败返回None
        """
        try:
            # 查找 'vertical' 或 'horizontal' 后跟数字的模式
            patterns = [
                r"'vertical',\s*(\d+)",
                r"'horizontal',\s*(\d+)",
                r"'spiral',\s*(\d+)"
            ]
            
            for pattern in patterns:
                match = re.search(pattern, line)
                if match:
                    return int(match.group(1))
            
            # 如果上述模式没有匹配，尝试更通用的模式
            # 查找逗号分隔的数字模式，grade_span通常在特定位置
            # 根据INSERT语句的结构，grade_span是第10个参数
            
            # 提取VALUES部分
            values_match = re.search(r'VALUES\s*\((.*)\)', line, re.IGNORECASE)
            if values_match:
                values_content = values_match.group(1)
                params = self._split_parameters_simple(values_content)
                
                # grade_span是第10个参数（索引9）
                if len(params) >= 10:
                    grade_span_str = params[9].strip().strip('\'"')
                    return int(grade_span_str)
                    
        except (ValueError, IndexError) as e:
            print(f"解析grade_span时出错: {e}, line: {line[:100]}...")
            
        return None
    
    def _split_parameters_simple(self, values_str: str) -> List[str]:
        """
        简化的参数分割方法
        
        Args:
            values_str: VALUES子句内容
            
        Returns:
            参数列表
        """
        params = []
        current_param = ""
        paren_count = 0
        brace_count = 0
        in_string = False
        string_char = None
        
        for char in values_str:
            if not in_string and char in ['"', "'"]:
                in_string = True
                string_char = char
                current_param += char
            elif in_string and char == string_char:
                in_string = False
                string_char = None
                current_param += char
            elif in_string:
                current_param += char
            else:
                if char == '(':
                    paren_count += 1
                    current_param += char
                elif char == ')':
                    paren_count -= 1
                    current_param += char
                elif char == '{':
                    brace_count += 1
                    current_param += char
                elif char == '}':
                    brace_count -= 1
                    current_param += char
                elif char == ',' and paren_count == 0 and brace_count == 0:
                    params.append(current_param.strip())
                    current_param = ""
                else:
                    current_param += char
        
        if current_param.strip():
            params.append(current_param.strip())
        
        return params
    
    def _generate_report(self) -> Dict:
        """
        生成检查报告
        
        Returns:
            检查报告字典
        """
        if not self.incorrect_grade_spans:
            return {
                'status': 'success',
                'message': f'✅ 检查通过！所有 {self.total_relationships} 个关系的grade_span都正确设置为0',
                'total_relationships': self.total_relationships,
                'incorrect_count': 0,
                'incorrect_items': []
            }
        else:
            return {
                'status': 'warning',
                'message': f'⚠️  发现 {len(self.incorrect_grade_spans)} 个grade_span值不为0的关系',
                'total_relationships': self.total_relationships,
                'incorrect_count': len(self.incorrect_grade_spans),
                'incorrect_items': self.incorrect_grade_spans
            }
    
    def generate_fixed_sql(self, output_path: str = None) -> str:
        """
        生成修正后的SQL文件
        
        Args:
            output_path: 输出文件路径，如果为None则自动生成
            
        Returns:
            修正后的SQL文件路径
        """
        if not self.incorrect_grade_spans:
            print("没有需要修正的grade_span值")
            return None
        
        if output_path is None:
            base_name = os.path.splitext(self.file_path)[0]
            output_path = f"{base_name}_fixed.sql"
        
        try:
            with open(self.file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            # 修正问题行
            for item in self.incorrect_grade_spans:
                line_idx = item['line_number'] - 1  # 转换为0基索引
                if line_idx < len(lines):
                    original_line = lines[line_idx]
                    
                    # 替换grade_span值为0
                    # 查找并替换 'vertical', 数字 或 'horizontal', 数字 模式
                    patterns = [
                        (r"'vertical',\s*\d+", "'vertical', 0"),
                        (r"'horizontal',\s*\d+", "'horizontal', 0"),
                        (r"'spiral',\s*\d+", "'spiral', 0")
                    ]
                    
                    fixed_line = original_line
                    for pattern, replacement in patterns:
                        fixed_line = re.sub(pattern, replacement, fixed_line)
                    
                    lines[line_idx] = fixed_line
            
            # 写入修正后的文件
            with open(output_path, 'w', encoding='utf-8') as f:
                f.writelines(lines)
            
            print(f"✅ 修正后的SQL文件已保存到: {output_path}")
            return output_path
            
        except Exception as e:
            print(f"❌ 生成修正文件时出错: {str(e)}")
            return None


def main():
    """主函数"""
    print("=" * 80)
    print("年级内部知识点关系grade_span检查工具")
    print("=" * 80)
    
    # 检查单个文件
    target_file = "grade_1_internal_relationships.sql"
    if len(sys.argv) > 1:
        target_file = sys.argv[1]
    
    print(f"\n📋 检查文件: {target_file}")
    
    if not os.path.exists(target_file):
        print(f"❌ 文件不存在: {target_file}")
        return
    
    checker = GradeSpanChecker(target_file)
    result = checker.check_sql_file()
    
    print(f"\n📊 检查结果:")
    print(f"状态: {result['status']}")
    print(f"消息: {result['message']}")
    
    if result['status'] == 'warning':
        print(f"\n❌ 发现问题的关系:")
        for item in result['incorrect_items']:
            print(f"  - 行号 {item['line_number']}: grade_span = {item['grade_span']}")
            print(f"    内容: {item['line_content']}")
            print()
        
        # 询问是否生成修正文件
        print("发现需要修正的grade_span值，将自动生成修正文件...")
        fixed_file = checker.generate_fixed_sql()
        if fixed_file:
            print(f"修正文件路径: {fixed_file}")
    
    print("\n" + "=" * 80)


if __name__ == "__main__":
    main()