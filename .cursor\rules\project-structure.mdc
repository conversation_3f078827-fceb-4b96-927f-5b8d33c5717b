---
description: 
globs: 
alwaysApply: false
---
# 项目结构指南

本项目是一个K12数学学习智能体微信小程序，采用模块化架构设计。下面介绍主要的目录结构和文件用途。

## 根目录文件

- [app.js](mdc:app.js) - 小程序入口文件，包含全局应用逻辑
- [app.json](mdc:app.json) - 全局配置文件，定义页面路径、窗口设置等
- [app.wxss](mdc:app.wxss) - 全局样式文件
- [sitemap.json](mdc:sitemap.json) - 微信索引配置文件
- [package.json](mdc:package.json) - 项目依赖配置文件

## 核心目录结构

### pages目录（主包页面）

`pages`目录包含小程序的主包页面：

- `pages/index/` - 首页模块
- `pages/class/` - 班级模块
- `pages/ai/` - AI推荐模块
- `pages/notebook/` - 错题本模块
- `pages/profile/` - 个人中心模块

### 分包目录

- `packageHome/` - 首页相关功能分包
  - `pages/subject-list/` - 学科列表
  - `pages/chapter-list/` - 章节列表
  - `pages/knowledge-detail/` - 知识点详情

- `packageClass/` - 班级相关功能分包
  - `pages/class-detail/` - 班级详情
  - `pages/member-list/` - 成员列表
  - `pages/homework-list/` - 作业列表
  - `pages/homework-detail/` - 作业详情

- `packageAI/` - AI推荐相关功能分包
  - `pages/learning-path/` - 学习路径
  - `pages/practice-recommend/` - 练习推荐
  - `pages/knowledge-graph/` - 知识图谱
  - `pages/ai-assistant/` - AI助教

- `packageNotebook/` - 错题本相关功能分包
  - `pages/wrong-questions/` - 错题列表
  - `pages/collection/` - 收藏题目
  - `pages/notes/` - 学习笔记
  - `pages/analysis/` - 错题分析

- `packageProfile/` - 个人中心相关功能分包
  - `pages/settings/` - 设置
  - `pages/study-report/` - 学习报告
  - `pages/achievement/` - 成就系统
  - `pages/feedback/` - 反馈建议

### components目录

`components`目录按功能类型组织组件：

- `components/common/` - 通用UI组件
  - `nav-bar/` - 导航栏组件
  - `tab-bar/` - 标签栏组件
  - `loading/` - 加载组件
  - `toast/` - 提示组件

- `components/business/` - 业务相关组件
  - `math-formula/` - 数学公式组件
  - `knowledge-card/` - 知识点卡片
  - `practice-item/` - 练习题组件
  - `ai-chat/` - AI对话组件

- `components/form/` - 表单及输入组件
  - `input/` - 输入框组件
  - `formula-input/` - 公式输入组件
  - `upload/` - 上传组件

### styles目录

`styles`目录管理全局样式资源：

- [styles/theme.wxss](mdc:styles/theme.wxss) - 主题变量定义
- [styles/common.wxss](mdc:styles/common.wxss) - 通用样式类
- [styles/animation.wxss](mdc:styles/animation.wxss) - 动画效果定义
- [styles/iconfont.wxss](mdc:styles/iconfont.wxss) - 图标样式定义

### utils目录

`utils`目录包含工具函数和服务：

- `utils/http.js` - 网络请求封装
- `utils/storage.js` - 存储管理
- `utils/format.js` - 数据格式化
- `utils/math-parser.js` - 数学公式解析
- `utils/ai-service.js` - AI功能服务
- `utils/event-bus.js` - 事件总线

### models目录

`models`目录包含数据模型定义：

- `models/user.js` - 用户模型
- `models/knowledge-point.js` - 知识点模型
- `models/question.js` - 题目模型
- `models/class.js` - 班级模型

### data目录

`data`目录包含静态数据：

- `data/curriculum.js` - 课程教材数据
- `data/exercises.js` - 练习题库
- `data/knowledge-graph.js` - 知识点图谱

## 目录命名规范

- 所有目录使用小写字母和连字符命名
- 页面目录与页面同名
- 组件目录与组件同名
- 功能模块使用单个单词命名，如`utils`、`models`
- 多单词目录使用连字符连接，如`math-formula`

## 文件命名规范

1. **页面文件**
   - 页面目录：`pages/module-name/`
   - 页面文件：`index.js`、`index.wxml`、`index.wxss`、`index.json`

2. **组件文件**
   - 组件目录：`components/type/component-name/`
   - 组件文件：`index.js`、`index.wxml`、`index.wxss`、`index.json`

3. **工具文件**
   - 使用功能描述命名：`http.js`、`storage.js`
   - 多单词使用连字符：`math-parser.js`

4. **样式文件**
   - 全局样式：`app.wxss`
   - 主题文件：`theme.wxss`
   - 模块样式：`module-name.wxss`

## 实现参考

- 入口文件：[app.js](mdc:app.js)
- 全局配置：[app.json](mdc:app.json)
- 全局样式：[app.wxss](mdc:app.wxss)
- 项目配置：[project.config.json](mdc:project.config.json)