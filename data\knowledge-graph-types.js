// 知识图谱数据类型定义

// 知识点状态枚举
const STATUS_TYPES = {
  MASTERED: 'mastered',       // 已掌握
  LEARNING: 'learning',       // 学习中
  WEAK: 'weak',               // 薄弱
  NOT_MASTERED: 'not-mastered', // 未掌握
  NOT_STARTED: 'not-started'    // 未开始
};

// 知识点难度级别枚举
const DIFFICULTY_LEVELS = {
  EASY: 'easy',           // 简单
  MEDIUM: 'medium',       // 中等
  HARD: 'hard',           // 困难
  EXPERT: 'expert'        // 挑战
};

// 学期类型枚举
const SEMESTER_TYPES = {
  FIRST: 'first',   // 上学期
  SECOND: 'second'  // 下学期
};

// 知识点关联关系类型（根据数学教学规律优化）
const RELATION_TYPES = {
  PREREQUISITE: 'prerequisite',         // 前置知识点（必须掌握）
  PROGRESSION: 'progression',           // 进阶知识点（自然延伸）
  CORRELATION: 'correlation',           // 相关知识点（相互关联）
  APPLICATION: 'application',           // 应用知识点（实际应用）
  REINFORCEMENT: 'reinforcement',       // 强化知识点（加深理解）
  ANALOGY: 'analogy',                  // 类比关系（相似思维）
  CONTRAST: 'contrast',                // 对比关系（对照学习）
  INTEGRATION: 'integration',          // 综合关系（知识整合）
  DERIVATION: 'derivation',            // 推导关系（逻辑推出）
  ABSTRACTION: 'abstraction',          // 抽象关系（概念提升）
  SPECIALIZATION: 'specialization',    // 特殊化关系（具体应用）
  GENERALIZATION: 'generalization',    // 一般化关系（概念推广）
  PARALLEL: 'parallel',                // 并行关系（同步学习）
  REVERSE: 'reverse',                  // 逆向关系（互逆概念）
  FOUNDATION: 'foundation',            // 基础关系（认知基础）
  COGNITIVE_DEVELOPMENT: 'cognitive_development',  // 认知发展关系（思维发展）
  EXTENDS: 'extends'                   // 扩展关系（概念扩展）
};

// 关联强度级别（根据教学实践优化）
const RELATION_STRENGTH = {
  CRITICAL: 0.95,     // 关键关联（核心依赖，缺失严重影响后续学习）
  STRONG: 0.9,        // 强关联（重要依赖，必须掌握）
  IMPORTANT: 0.8,     // 重要关联（显著影响）
  MEDIUM: 0.7,        // 中等关联（有一定影响）
  MODERATE: 0.6,      // 适度关联（轻微影响）
  WEAK: 0.5,          // 弱关联（辅助理解）
  MINIMAL: 0.3,       // 最小关联（参考价值）
  AUXILIARY: 0.2      // 辅助关联（背景知识）
};
 
// 年级级别枚举
const GRADE_LEVELS = {
  // 小学
  ELEMENTARY_1: '小学一年级',
  ELEMENTARY_2: '小学二年级',
  ELEMENTARY_3: '小学三年级',
  ELEMENTARY_4: '小学四年级',
  ELEMENTARY_5: '小学五年级',
  ELEMENTARY_6: '小学六年级',
  // 初中
  JUNIOR_1: '初中一年级',
  JUNIOR_2: '初中二年级',
  JUNIOR_3: '初中三年级',
  // 高中
  SENIOR_1: '高中一年级',
  SENIOR_2: '高中二年级',
  SENIOR_3: '高中三年级'
};

// 学科分类枚举
const SUBJECT_CATEGORIES = {
  NUMBER_OPERATION: 'number_operation',        // 数与运算
  ALGEBRA: 'algebra',                         // 代数
  GEOMETRY: 'geometry',                       // 几何
  STATISTICS_PROBABILITY: 'statistics_probability', // 统计与概率
  COMPREHENSIVE_PRACTICE: 'comprehensive_practice'  // 综合与实践
};

// 数学核心素养枚举
const CORE_COMPETENCIES = {
  MATHEMATICAL_ABSTRACTION: 'mathematical_abstraction',     // 数学抽象
  LOGICAL_REASONING: 'logical_reasoning',                   // 逻辑推理
  MATHEMATICAL_MODELING: 'mathematical_modeling',           // 数学建模
  INTUITIVE_IMAGINATION: 'intuitive_imagination',           // 直观想象
  MATHEMATICAL_COMPUTATION: 'mathematical_computation',     // 数学运算
  DATA_ANALYSIS: 'data_analysis'                           // 数据分析
};

// 认知层次枚举（布鲁姆分类法）
const COGNITIVE_LEVELS = {
  REMEMBER: 'remember',           // 记忆
  UNDERSTAND: 'understand',       // 理解
  APPLY: 'apply',                // 应用
  ANALYZE: 'analyze',            // 分析
  EVALUATE: 'evaluate',          // 评价
  CREATE: 'create'               // 创造
};

// 教学策略枚举
const TEACHING_STRATEGIES = {
  DIRECT_INSTRUCTION: 'direct_instruction',     // 直接教学
  DISCOVERY_LEARNING: 'discovery_learning',     // 发现学习
  COOPERATIVE_LEARNING: 'cooperative_learning', // 合作学习
  PROBLEM_SOLVING: 'problem_solving',           // 问题解决
  INQUIRY_BASED: 'inquiry_based',               // 探究式学习
  GAME_BASED: 'game_based'                      // 游戏化学习
};

// 评估类型枚举
const ASSESSMENT_TYPES = {
  DIAGNOSTIC: 'diagnostic',       // 诊断性评估
  FORMATIVE: 'formative',        // 形成性评估
  SUMMATIVE: 'summative',        // 总结性评估
  PEER: 'peer',                  // 同伴评估
  SELF: 'self'                   // 自我评估
};

// 难度等级描述映射
const DIFFICULTY_DESCRIPTIONS = {
  [DIFFICULTY_LEVELS.EASY]: '基础掌握',
  [DIFFICULTY_LEVELS.MEDIUM]: '标准掌握',
  [DIFFICULTY_LEVELS.HARD]: '熟练掌握',
  [DIFFICULTY_LEVELS.EXPERT]: '精通掌握'
};

// 关系类型描述映射
const RELATION_TYPE_DESCRIPTIONS = {
  [RELATION_TYPES.PREREQUISITE]: '前置关系',
  [RELATION_TYPES.PROGRESSION]: '递进关系',
  [RELATION_TYPES.CORRELATION]: '相关关系',
  [RELATION_TYPES.APPLICATION]: '应用关系',
  [RELATION_TYPES.REINFORCEMENT]: '强化关系',
  [RELATION_TYPES.ANALOGY]: '类比关系',
  [RELATION_TYPES.CONTRAST]: '对比关系',
  [RELATION_TYPES.INTEGRATION]: '综合关系',
  [RELATION_TYPES.DERIVATION]: '推导关系',
  [RELATION_TYPES.ABSTRACTION]: '抽象关系',
  [RELATION_TYPES.SPECIALIZATION]: '特化关系',
  [RELATION_TYPES.GENERALIZATION]: '泛化关系',
  [RELATION_TYPES.PARALLEL]: '并行关系',
  [RELATION_TYPES.REVERSE]: '逆向关系',
  [RELATION_TYPES.FOUNDATION]: '基础关系',
  [RELATION_TYPES.COGNITIVE_DEVELOPMENT]: '认知发展关系',
  [RELATION_TYPES.EXTENDS]: '扩展关系'
};

// 状态类型描述映射
const STATUS_DESCRIPTIONS = {
  [STATUS_TYPES.MASTERED]: '已掌握',
  [STATUS_TYPES.LEARNING]: '学习中',
  [STATUS_TYPES.WEAK]: '薄弱',
  [STATUS_TYPES.NOT_MASTERED]: '未掌握',
  [STATUS_TYPES.NOT_STARTED]: '未开始'
};

// 学科分类描述映射
const SUBJECT_DESCRIPTIONS = {
  [SUBJECT_CATEGORIES.NUMBER_OPERATION]: '数与运算',
  [SUBJECT_CATEGORIES.ALGEBRA]: '代数',
  [SUBJECT_CATEGORIES.GEOMETRY]: '几何',
  [SUBJECT_CATEGORIES.STATISTICS_PROBABILITY]: '统计与概率',
  [SUBJECT_CATEGORIES.COMPREHENSIVE_PRACTICE]: '综合与实践'
};

// 核心素养描述映射
const COMPETENCY_DESCRIPTIONS = {
  [CORE_COMPETENCIES.MATHEMATICAL_ABSTRACTION]: '数学抽象',
  [CORE_COMPETENCIES.LOGICAL_REASONING]: '逻辑推理',
  [CORE_COMPETENCIES.MATHEMATICAL_MODELING]: '数学建模',
  [CORE_COMPETENCIES.INTUITIVE_IMAGINATION]: '直观想象',
  [CORE_COMPETENCIES.MATHEMATICAL_COMPUTATION]: '数学运算',
  [CORE_COMPETENCIES.DATA_ANALYSIS]: '数据分析'
};

// 认知层次描述映射
const COGNITIVE_DESCRIPTIONS = {
  [COGNITIVE_LEVELS.REMEMBER]: '记忆',
  [COGNITIVE_LEVELS.UNDERSTAND]: '理解',
  [COGNITIVE_LEVELS.APPLY]: '应用',
  [COGNITIVE_LEVELS.ANALYZE]: '分析',
  [COGNITIVE_LEVELS.EVALUATE]: '评价',
  [COGNITIVE_LEVELS.CREATE]: '创造'
};

// 推荐算法配置
const RECOMMENDATION_CONFIG = {
  // 推荐权重配置
  weights: {
    difficultyMatch: 0.3,      // 难度匹配权重
    topicRelevance: 0.4,       // 主题相关性权重
    learningPath: 0.2,         // 学习路径权重
    userProgress: 0.1          // 用户进度权重
  },
  
  // 难度递进配置
  difficultyProgression: {
    [DIFFICULTY_LEVELS.EASY]: [DIFFICULTY_LEVELS.MEDIUM],
    [DIFFICULTY_LEVELS.MEDIUM]: [DIFFICULTY_LEVELS.HARD, DIFFICULTY_LEVELS.EASY],
    [DIFFICULTY_LEVELS.HARD]: [DIFFICULTY_LEVELS.EXPERT, DIFFICULTY_LEVELS.MEDIUM],
    [DIFFICULTY_LEVELS.EXPERT]: [DIFFICULTY_LEVELS.HARD]
  },
  
  // 年级邻近权重
  gradeProximityWeight: 0.4,
  
  // 推荐算法参数
  recommendationParams: {
    maxRecommendations: 8,        // 最大推荐数量
    minConfidenceThreshold: 0.3,  // 最小置信度阈值
    diversityFactor: 0.2,         // 多样性因子
    personalizedWeight: 0.3       // 个性化权重
  }
};

// 学习分析配置
const LEARNING_ANALYTICS_CONFIG = {
  // 掌握度计算权重
  masteryWeights: {
    conceptUnderstanding: 0.4,    // 概念理解
    problemSolving: 0.3,          // 问题解决
    applicationAbility: 0.2,      // 应用能力
    timeEfficiency: 0.1           // 时间效率
  },
  
  // 学习状态阈值
  statusThresholds: {
    mastered: 0.85,               // 掌握阈值
    learning: 0.6,                // 学习中阈值
    weak: 0.4,                    // 薄弱阈值
    notMastered: 0.2              // 未掌握阈值
  },
  
  // 智能诊断参数
  diagnosticParams: {
    minSampleSize: 5,             // 最小样本数
    confidenceLevel: 0.8,         // 置信水平
    adaptiveThreshold: 0.7        // 自适应阈值
  }
};

// 导出枚举类型和配置
module.exports = {
  // 基础枚举
  STATUS_TYPES,
  DIFFICULTY_LEVELS,
  SEMESTER_TYPES,
  RELATION_TYPES,
  RELATION_STRENGTH,
  GRADE_LEVELS,
  SUBJECT_CATEGORIES,
  CORE_COMPETENCIES,
  COGNITIVE_LEVELS,
  TEACHING_STRATEGIES,
  ASSESSMENT_TYPES,
  
  // 描述映射
  DIFFICULTY_DESCRIPTIONS,
  RELATION_TYPE_DESCRIPTIONS,
  STATUS_DESCRIPTIONS,
  SUBJECT_DESCRIPTIONS,
  COMPETENCY_DESCRIPTIONS,
  COGNITIVE_DESCRIPTIONS,
  
  // 配置
  RECOMMENDATION_CONFIG,
  LEARNING_ANALYTICS_CONFIG
}; 