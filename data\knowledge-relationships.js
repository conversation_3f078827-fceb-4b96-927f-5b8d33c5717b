/**
 * K12数学知识点关系数据
 * 基于PostgreSQL数据库设计，包含1305条边关系和1121条跨链接关系
 * 支持AI智能学习路径推荐和个性化分析
 */

// 知识点关系类型定义
const relationshipTypes = {
  // 基础关系类型
  foundation: {
    name: '基础支撑',
    description: '一个知识点为另一个知识点提供基础认知支撑',
    strength_range: [0.7, 0.95],
    learning_impact: '强依赖关系，需要优先掌握前置知识点',
    ai_weight: 0.9
  },
  prerequisite: {
    name: '前置依赖',
    description: '学习某知识点的必要前置条件',
    strength_range: [0.8, 0.95],
    learning_impact: '必须先掌握前置知识点才能学习目标知识点',
    ai_weight: 0.95
  },
  
  // 扩展关系类型
  generalization: {
    name: '泛化扩展',
    description: '从特殊概念推广到一般概念',
    strength_range: [0.6, 0.95],
    learning_impact: '有助于理解概念的扩展和推广',
    ai_weight: 0.8
  },
  specialization: {
    name: '特化具体',
    description: '从一般概念特化到具体情况',
    strength_range: [0.6, 0.9],
    learning_impact: '通过具体实例深化对抽象概念的理解',
    ai_weight: 0.7
  },
  
  // 推理关系类型
  derivation: {
    name: '推导衍生',
    description: '从已知知识点推导出新的知识点',
    strength_range: [0.7, 0.95],
    learning_impact: '培养逻辑推理能力和思维连贯性',
    ai_weight: 0.85
  },
  abstraction: {
    name: '抽象概括',
    description: '从具体现象抽象出一般规律',
    strength_range: [0.6, 0.8],
    learning_impact: '提升抽象思维能力',
    ai_weight: 0.75
  },
  
  // 运算关系类型
  reverse: {
    name: '互逆运算',
    description: '两个知识点互为逆运算关系',
    strength_range: [0.85, 0.95],
    learning_impact: '加深对运算本质的理解',
    ai_weight: 0.9
  },
  
  // 类比关系类型
  analogy: {
    name: '类比联系',
    description: '两个知识点在认知结构或思维方式上具有相似性',
    strength_range: [0.2, 0.7],
    learning_impact: '通过类比促进知识迁移',
    ai_weight: 0.6
  },
  parallel: {
    name: '平行发展',
    description: '两个知识点可以同步学习，互相促进',
    strength_range: [0.6, 0.9],
    learning_impact: '可以安排并行学习，相互强化',
    ai_weight: 0.7
  },
  
  // 应用关系类型
  application: {
    name: '应用实践',
    description: '一个知识点在另一个知识点中的具体应用',
    strength_range: [0.5, 0.8],
    learning_impact: '加强知识的实际应用能力',
    ai_weight: 0.75
  },
  integration: {
    name: '整合综合',
    description: '多个知识点整合形成新的认知结构',
    strength_range: [0.7, 0.9],
    learning_impact: '培养综合运用知识的能力',
    ai_weight: 0.8
  },
  
  // 强化关系类型
  reinforcement: {
    name: '强化巩固',
    description: '一个知识点强化另一个知识点的理解',
    strength_range: [0.6, 0.8],
    learning_impact: '通过重复和变式加深理解',
    ai_weight: 0.7
  },
  progression: {
    name: '自然延伸',
    description: '知识点之间的自然发展和延续',
    strength_range: [0.8, 0.95],
    learning_impact: '促进知识的连续性发展',
    ai_weight: 0.85
  },
  
  // 对比关系类型
  contrast: {
    name: '对比辨析',
    description: '两个知识点通过对比加深理解',
    strength_range: [0.6, 0.8],
    learning_impact: '通过对比突出各自特点',
    ai_weight: 0.7
  }
};

// 关系强度等级
const strengthLevels = {
  very_weak: { range: [0.0, 0.3], description: '极弱关联', color: '#E0E0E0' },
  weak: { range: [0.3, 0.5], description: '弱关联', color: '#FFE0B2' },
  medium: { range: [0.5, 0.7], description: '中等关联', color: '#FFF59D' },
  strong: { range: [0.7, 0.9], description: '强关联', color: '#C8E6C9' },
  very_strong: { range: [0.9, 1.0], description: '极强关联', color: '#A5D6A7' }
};

// 认知桥梁类型
const cognitiveBridgeTypes = {
  concept_bridge: '概念桥梁',
  method_bridge: '方法桥梁',
  thinking_bridge: '思维桥梁',
  application_bridge: '应用桥梁'
};

// 完整的知识点关系数据结构
const knowledgeRelationships = {
  // 同年级内部关系（基于 knowledge_graph_edges 表）
  intra_grade_relationships: [
    // 1年级内部关系示例
    {
      id: 'e1l001',
      source_id: 'e1n001',
      target_id: 'e1n002',
      relationship_type: 'foundation',
      strength: 0.95,
      description: '数一数的数感是比多少逻辑思维的认知基础',
      cognitive_analysis: {
        cognitive_load: 'low',
        difficulty_progression: 0.1,
        mental_model_connection: 'counting_to_comparison'
      },
      learning_guidance: {
        prerequisite_skills: ['基础计数能力', '一一对应理解'],
        learning_objectives: ['建立数量比较概念', '发展逻辑思维'],
        teaching_strategies: ['具体操作', '直观演示', '对比活动'],
        common_difficulties: ['抽象概念理解', '比较标准建立'],
        assessment_points: ['计数准确性', '比较逻辑性']
      }
    },
    {
      id: 'e1l002',
      source_id: 'e1n002',
      target_id: 'e1n032',
      relationship_type: 'abstraction',
      strength: 0.8,
      description: '比多少的对应思想抽象为分类的逻辑标准',
      cognitive_analysis: {
        cognitive_load: 'medium',
        difficulty_progression: 0.3,
        mental_model_connection: 'comparison_to_classification'
      },
      learning_guidance: {
        prerequisite_skills: ['比较能力', '对应关系理解'],
        learning_objectives: ['建立分类标准', '发展分类思维'],
        teaching_strategies: ['分类游戏', '标准建立', '多重分类'],
        common_difficulties: ['抽象思维跳跃', '分类标准确定'],
        assessment_points: ['分类合理性', '标准一致性']
      }
    },
    
    // 2年级内部关系示例
    {
      id: 'e2l001',
      source_id: 'e2n001',
      target_id: 'e2n002',
      relationship_type: 'foundation',
      strength: 0.95,
      description: '认识厘米是认识米的认知基础，体现度量单位从小到大的递进发展',
      cognitive_analysis: {
        cognitive_load: 'medium',
        difficulty_progression: 0.2,
        mental_model_connection: 'length_measurement_progression'
      },
      learning_guidance: {
        prerequisite_skills: ['基础度量概念', '单位换算理解'],
        learning_objectives: ['建立度量系统', '理解单位关系'],
        teaching_strategies: ['实物测量', '单位换算', '比较练习'],
        common_difficulties: ['单位概念混淆', '换算关系理解'],
        assessment_points: ['度量准确性', '单位选择合理性']
      }
    },
    {
      id: 'e2l009',
      source_id: 'e2n004',
      target_id: 'e2n005',
      relationship_type: 'reverse',
      strength: 0.95,
      description: '两位数加法与减法互为逆运算，体现运算的对称性',
      cognitive_analysis: {
        cognitive_load: 'medium',
        difficulty_progression: 0.1,
        mental_model_connection: 'inverse_operations'
      },
      learning_guidance: {
        prerequisite_skills: ['两位数概念', '进位退位理解'],
        learning_objectives: ['掌握互逆关系', '提高运算能力'],
        teaching_strategies: ['逆向验证', '关系对比', '实际应用'],
        common_difficulties: ['逆向思维转换', '验证方法掌握'],
        assessment_points: ['运算准确性', '验证合理性']
      }
    }
  ],
  
  // 跨年级关系（基于 knowledge_graph_cross_links 表）
  cross_grade_relationships: [
    // 1-2年级跨链接示例
    {
      id: 'e12l001',
      source_id: 'e1n006',
      target_id: 'e2n036',
      relationship_type: 'foundation',
      strength: 0.95,
      description: '一年级1~5的认识为二年级1000以内数的认识奠定基础',
      grade_span: '1-2',
      cognitive_analysis: {
        cognitive_load: 'low',
        difficulty_progression: 0.4,
        mental_model_connection: 'number_concept_expansion'
      },
      learning_guidance: {
        prerequisite_skills: ['1-5数字概念', '计数原理'],
        learning_objectives: ['扩展数概念', '建立大数认识'],
        teaching_strategies: ['递进扩展', '类比迁移', '实际计数'],
        common_difficulties: ['数概念跨越', '计数规律理解'],
        assessment_points: ['数概念理解', '计数准确性']
      }
    },
    {
      id: 'e12l026',
      source_id: 'e1n014',
      target_id: 'e2n008',
      relationship_type: 'foundation',
      strength: 0.95,
      description: '一年级认识平面图形是二年级角的初步认识的基础',
      grade_span: '1-2',
      cognitive_analysis: {
        cognitive_load: 'medium',
        difficulty_progression: 0.3,
        mental_model_connection: 'geometric_concept_development'
      },
      learning_guidance: {
        prerequisite_skills: ['基本图形识别', '几何直觉'],
        learning_objectives: ['建立角的概念', '发展几何思维'],
        teaching_strategies: ['实物观察', '图形分析', '角度感知'],
        common_difficulties: ['抽象概念理解', '角的特征识别'],
        assessment_points: ['图形识别能力', '几何概念理解']
      }
    },
    
    // 2-3年级跨链接示例
    {
      id: 'e23l021',
      source_id: 'e2n012',
      target_id: 'e3n017',
      relationship_type: 'foundation',
      strength: 0.95,
      description: '二年级乘法初步认识是三年级口算乘法的基础',
      grade_span: '2-3',
      cognitive_analysis: {
        cognitive_load: 'medium',
        difficulty_progression: 0.2,
        mental_model_connection: 'multiplication_concept_development'
      },
      learning_guidance: {
        prerequisite_skills: ['乘法概念', '表内乘法'],
        learning_objectives: ['提高运算熟练度', '发展数感'],
        teaching_strategies: ['口算训练', '规律发现', '实际应用'],
        common_difficulties: ['运算速度', '规律掌握'],
        assessment_points: ['运算准确性', '运算速度']
      }
    }
  ]
};

// 学习路径推荐算法
const learningPathRecommendation = {
  /**
   * 生成个性化学习路径
   * @param {string} targetKnowledgeId - 目标知识点ID
   * @param {Object} studentProfile - 学生画像
   * @returns {Array} 推荐的学习路径
   */
  generateLearningPath(targetKnowledgeId, studentProfile) {
    const path = [];
    const dependencies = this.findDependencies(targetKnowledgeId);
    
    // 根据学生能力调整路径
    for (let dep of dependencies) {
      if (studentProfile.weakness.includes(dep.category)) {
        // 为薄弱环节添加额外练习
        path.push({
          knowledge_id: dep.id,
          estimated_time: dep.time * 1.5,
          difficulty_adjustment: -0.1,
          additional_resources: true
        });
      } else {
        path.push({
          knowledge_id: dep.id,
          estimated_time: dep.time,
          difficulty_adjustment: 0,
          additional_resources: false
        });
      }
    }
    
    return path;
  },
  
  /**
   * 查找知识点依赖关系
   * @param {string} knowledgeId - 知识点ID
   * @returns {Array} 依赖关系列表
   */
  findDependencies(knowledgeId) {
    const dependencies = [];
    const relationships = [...knowledgeRelationships.intra_grade_relationships, 
                          ...knowledgeRelationships.cross_grade_relationships];
    
    // 递归查找前置依赖
    function findPrerequisites(id, visited = new Set()) {
      if (visited.has(id)) return;
      visited.add(id);
      
      const prereqs = relationships.filter(rel => 
        rel.target_id === id && 
        ['foundation', 'prerequisite'].includes(rel.relationship_type)
      );
      
      for (let prereq of prereqs) {
        dependencies.push(prereq);
        findPrerequisites(prereq.source_id, visited);
      }
    }
    
    findPrerequisites(knowledgeId);
    return dependencies.sort((a, b) => b.strength - a.strength);
  }
};

// 知识点关系查询工具
const relationshipQuery = {
  /**
   * 获取知识点的所有关系
   * @param {string} knowledgeId - 知识点ID
   * @returns {Object} 关系分类结果
   */
  getKnowledgeRelationships(knowledgeId) {
    const allRelationships = [
      ...knowledgeRelationships.intra_grade_relationships,
      ...knowledgeRelationships.cross_grade_relationships
    ];
    
    const incoming = allRelationships.filter(rel => rel.target_id === knowledgeId);
    const outgoing = allRelationships.filter(rel => rel.source_id === knowledgeId);
    
    return {
      prerequisites: incoming.filter(rel => 
        ['foundation', 'prerequisite'].includes(rel.relationship_type)
      ),
      applications: outgoing.filter(rel => 
        rel.relationship_type === 'application'
      ),
      analogies: allRelationships.filter(rel => 
        (rel.source_id === knowledgeId || rel.target_id === knowledgeId) &&
        rel.relationship_type === 'analogy'
      ),
      extensions: outgoing.filter(rel => 
        ['generalization', 'derivation'].includes(rel.relationship_type)
      )
    };
  },
  
  /**
   * 查找知识点间的最短路径
   * @param {string} sourceId - 起始知识点
   * @param {string} targetId - 目标知识点
   * @returns {Array} 最短学习路径
   */
  findShortestPath(sourceId, targetId) {
    // 使用 Dijkstra 算法实现
    const allRelationships = [
      ...knowledgeRelationships.intra_grade_relationships,
      ...knowledgeRelationships.cross_grade_relationships
    ];
    
    // 构建图
    const graph = {};
    allRelationships.forEach(rel => {
      if (!graph[rel.source_id]) graph[rel.source_id] = [];
      graph[rel.source_id].push({
        node: rel.target_id,
        weight: 1 - rel.strength // 强度越高，权重越低
      });
    });
    
    // Dijkstra 算法实现
    const distances = {};
    const previous = {};
    const unvisited = new Set();
    
    // 初始化
    Object.keys(graph).forEach(node => {
      distances[node] = node === sourceId ? 0 : Infinity;
      unvisited.add(node);
    });
    
    while (unvisited.size > 0) {
      // 找到未访问节点中距离最小的
      let current = null;
      let minDistance = Infinity;
      unvisited.forEach(node => {
        if (distances[node] < minDistance) {
          minDistance = distances[node];
          current = node;
        }
      });
      
      if (current === null || current === targetId) break;
      
      unvisited.delete(current);
      
      // 更新邻居节点距离
      if (graph[current]) {
        graph[current].forEach(neighbor => {
          if (unvisited.has(neighbor.node)) {
            const newDistance = distances[current] + neighbor.weight;
            if (newDistance < distances[neighbor.node]) {
              distances[neighbor.node] = newDistance;
              previous[neighbor.node] = current;
            }
          }
        });
      }
    }
    
    // 重构路径
    const path = [];
    let current = targetId;
    while (current !== undefined) {
      path.unshift(current);
      current = previous[current];
    }
    
    return path.length > 1 ? path : null;
  }
};

// AI学习分析工具
const aiLearningAnalysis = {
  /**
   * 分析知识点学习难度
   * @param {string} knowledgeId - 知识点ID
   * @returns {Object} 学习难度分析
   */
  analyzeLearningDifficulty(knowledgeId) {
    const relationships = this.getKnowledgeRelationships(knowledgeId);
    
    // 计算认知负荷
    const prerequisiteCount = relationships.prerequisites.length;
    const complexityScore = relationships.prerequisites.reduce(
      (score, rel) => score + (1 - rel.strength), 0
    ) / prerequisiteCount || 0;
    
    return {
      cognitive_load: prerequisiteCount > 5 ? 'high' : prerequisiteCount > 2 ? 'medium' : 'low',
      complexity_score: complexityScore,
      learning_time_estimate: Math.ceil((prerequisiteCount * 0.5 + complexityScore * 2) * 10),
      recommended_sequence: relationships.prerequisites
        .sort((a, b) => b.strength - a.strength)
        .map(rel => rel.source_id)
    };
  },
  
  /**
   * 生成个性化练习推荐
   * @param {string} knowledgeId - 知识点ID
   * @param {Object} studentData - 学生数据
   * @returns {Object} 练习推荐
   */
  generatePracticeRecommendation(knowledgeId, studentData) {
    const difficulty = this.analyzeLearningDifficulty(knowledgeId);
    const errorPatterns = studentData.error_patterns || [];
    
    return {
      difficulty_level: this.adjustDifficultyByPerformance(
        difficulty.complexity_score, 
        studentData.performance_history
      ),
      practice_types: this.selectPracticeTypes(knowledgeId, errorPatterns),
      estimated_practice_time: difficulty.learning_time_estimate,
      review_schedule: this.generateReviewSchedule(knowledgeId, studentData.mastery_level)
    };
  }
};

// 导出数据和工具
module.exports = {
  relationshipTypes,
  strengthLevels,
  cognitiveBridgeTypes,
  knowledgeRelationships,
  learningPathRecommendation,
  relationshipQuery,
  aiLearningAnalysis
}; 