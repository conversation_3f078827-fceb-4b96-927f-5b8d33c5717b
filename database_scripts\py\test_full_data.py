import insert
import json

# 用户提供的完整数据
test_data = """{
  "text": "<think>\\n好的，我现在需要处理用户提供的关于角的度量的练习题生成任务。首先，我要仔细分析用户的需求。用户给了一个知识点ID 2852，以及知识点描述和详细解释，要求生成三组难度级别的题目，每组三题，共九题，包括选择题和判断题，并且每个题目都要有答案和解析。\\n\\n首先，我需要确认知识点的核心内容。角的度量涉及度、分、秒的转换，以及角度的表示方法。根据用户提供的解释，角度制起源于古巴比伦的六十进制，一个圆周分为360度。这可能涉及到角度的单位转换，比如度分秒之间的换算，以及如何正确表示角度。\\n\\n接下来，我需要确定题目的类型。用户要求的是选择题（单选、多选）和判断题。每个难度级别（基础、中级、高级）各三题，总共九题。我需要确保每个题目的难度递增，同时覆盖不同的知识点方面。\\n\\n对于基础题，可能需要直接的单位转换，比如将度转换为分，或者判断角度表示是否正确。例如，判断题可能涉及0°的表示是否正确，或者角度的单位是否正确。\\n\\n中级题目可能需要更复杂的计算，比如将度分秒转换为十进制度数，或者进行加减运算。例如，计算两个角度的和，并转换为正确的单位。\\n\\n高级题目可能涉及实际应用，比如在导航或工程中的角度测量问题，或者需要综合运用多个知识点的题目。例如，计算某个实际场景中的角度，并进行单位转换。\\n\\n然后，我需要生成题目代码。根据用户提供的示例，题目代码的格式是MATH_G[年级]S[学期]_CH[章节]_001_Q[编号]_[知识点ID]。但用户给出的知识点ID是2852，所以可能需要调整。例如，可能使用MATH_G7S1_CH1_001_Q001_2852这样的格式，但需要确认年级和章节是否合适。由于用户没有提供具体年级，可能需要假设为初中七年级，或者使用其他默认值。\\n\\n接下来，每个题目的结构需要符合用户提供的JSON格式。需要注意的是，question_content、options、correct_answer等字段需要以JSON格式存储，并且数学公式要用KaTeX语法。例如，如果题目中有公式，需要用$...$或$$...$$包裹。\\n\\n在生成题目时，要确保每个题目的难度级别正确。基础题可能涉及简单的单位转换，中级题可能需要多步骤计算，高级题可能涉及实际应用或综合问题。同时，答案和解析要详细，指出常见错误，帮助学生理解。\\n\\n还需要检查题目之间的差异性，避免重复。例如，基础题可以有不同的转换方式，中级题可以涉及不同的运算，高级题可以有不同的应用场景。\\n\\n最后，确保所有字段都符合数据库表结构的要求，如subject为mathematics，grade_level可能设为7（七年级），knowledge_points数组包含2852，difficulty_level对应basic、intermediate、advanced，cognitive_level根据题目难度选择remember、understand、apply等。\\n\\n现在开始逐一生成题目，确保每个题目符合要求，并且格式正确。\\n</think>```json\\n[\\n  {\\n    \\"question_code\\": \\"MATH_G7S1_CH1_001_Q001_2852\\",\\n    \\"question_title\\": \\"角度单位的基本概念\\",\\n    \\"question_type\\": \\"single_choice\\",\\n    \\"question_content\\": \\"{\\\\\\\"text\\\\\\\": \\\\\\\"下列角度单位换算正确的是\\\\\\\", \\\\\\\"format\\\\\\\": \\\\\\\"text\\\\\\\"}\\"",
  "usage": {
    "prompt_tokens": 4667,
    "prompt_unit_price": "0.0015",
    "prompt_price_unit": "0.001",
    "prompt_price": "0.0070005",
    "completion_tokens": 7637,
    "completion_unit_price": "0.006",
    "completion_price_unit": "0.001",
    "completion_price": "0.045822",
    "total_tokens": 12304,
    "total_price": "0.0528225",
    "currency": "RMB",
    "latency": 54.78169765000348
  },
  "finish_reason": "stop",
  "files": []
}"""

try:
    print("开始解析数据...")
    result = insert.main(test_data)
    print('SUCCESS: 函数执行成功')
    print('返回的键:', list(result.keys()))
    
    if 'parsed_json' in result:
        parsed = result['parsed_json']
        print(f'解析的数据类型: {type(parsed)}')
        if isinstance(parsed, list):
            print(f'题目数量: {len(parsed)}')
        else:
            print('parsed_json内容:', parsed)
    
    if 'insert_sql' in result:
        print('生成了INSERT SQL语句')
        sql = result['insert_sql']
        print(f'SQL长度: {len(sql)}')
        print('SQL前500字符:')
        print(sql[:500] + '...' if len(sql) > 500 else sql)
    else:
        print('没有生成INSERT SQL语句')
        
except Exception as e:
    print('ERROR:', str(e))
    import traceback
    traceback.print_exc() 