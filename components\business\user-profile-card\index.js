Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 用户登录状态
    isLoggedIn: {
      type: Boolean,
      value: false
    },
    // 用户信息
    userInfo: {
      type: Object,
      value: {}
    },
    // 用户昵称
    nickname: {
      type: String,
      value: ''
    },
    // 用户等级
    userLevel: {
      type: String,
      value: 'Lv.1'
    },
    // 等级进度(百分比)
    levelProgress: {
      type: Number,
      value: 60
    },
    // 成就勋章
    medals: {
      type: Array,
      value: []
    },
    // 勋章数量
    medalCount: {
      type: Number,
      value: 0
    },
    // 学校名称
    schoolName: {
      type: String,
      value: ''
    },
    // 班级名称
    className: {
      type: String,
      value: ''
    },
    // 已解题数量
    solvedCount: {
      type: Number,
      value: 0
    },
    // 错题数量
    wrongCount: {
      type: Number,
      value: 0
    },
    // 学习时长
    studyHours: {
      type: Number,
      value: 0
    },
    // 是否为iPad
    isIPad: {
      type: Boolean,
      value: false
    },
    // 是否隐藏设置按钮
    hideSettings: {
      type: Boolean,
      value: false
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 点击登录按钮
    onLogin() {
      this.triggerEvent('login');
    },
    
    // 点击设置按钮
    onEdit() {
      // 触发父组件的edit事件
      this.triggerEvent('edit');
      
      // 使用更可靠的跳转方法并添加错误处理
      const url = '/packageProfile/pages/settings/index';
      console.log('尝试跳转到:', url);
      
      wx.navigateTo({
        url: url,
        success: () => {
          console.log('成功跳转到设置页面');
        },
        fail: (err) => {
          console.error('跳转到设置页面失败:', err);
          // 尝试使用redirectTo作为备选方案
          wx.redirectTo({
            url: url,
            fail: (error) => {
              console.error('重定向到设置页面也失败:', error);
              // 显示错误提示
              wx.showToast({
                title: '无法打开设置页面',
                icon: 'none'
              });
            }
          });
        }
      });
    },
    
    // 点击勋章图标
    onMedalTap(e) {
      const medal = e.currentTarget.dataset.medal;
      if (medal) {
        console.log('点击勋章:', medal);
        
        // 根据勋章解锁状态处理点击事件
        if (medal.unlocked) {
          // 已解锁勋章 - 确保有描述信息
          const description = medal.description || this.getMedalDescription(medal);
          this.triggerEvent('medalTap', { 
            medal: {
              ...medal,
              description: description
            }
          });
        } else {
          // 未解锁勋章 - 显示获取条件
          this.triggerEvent('medalTap', { 
            medal: {
              ...medal,
              name: `未解锁: ${medal.name}`,
              description: `获取条件: ${medal.description || this.getMedalDescription(medal)}`
            }
          });
        }
      }
    },
    
    // 获取勋章描述 (保留此函数以便将来使用)
    getMedalDescription(medal) {
      const descriptions = {
        'icon-star-medal': '数学之星勋章：完成100道题目并保持90%以上的正确率',
        'icon-award-medal': '攻坚达人勋章：解决10道困难级别的数学问题',
        'icon-crown-medal': '学习先锋勋章：连续21天学习，每天至少30分钟',
        'icon-math-medal': '数学大师勋章：掌握所有初中数学必修知识点'
      };
      
      return descriptions[medal.icon] || `${medal.name}：完成特定学习任务获得的荣誉勋章`;
    },
    
    // 点击更多勋章按钮
    onMoreMedalsTap() {
      console.log('点击查看更多勋章');
      
      // 跳转到成就详情页面
      wx.navigateTo({
        url: '/packageProfile/pages/achievement/index',
        success: () => {
          console.log('成功跳转到成就详情页面');
        },
        fail: (err) => {
          console.error('跳转到成就详情页面失败:', err);
          wx.showToast({
            title: '无法打开成就详情页面',
            icon: 'none'
          });
        }
      });
    },
    
    /**
     * 点击等级标签
     */
    onLevelTap() {
      wx.navigateTo({
        url: '/packageProfile/pages/achievement/level-desc/index'
      });
    }
  }
}) 