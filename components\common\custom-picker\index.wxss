/* 自定义选择器样式 */
.custom-picker-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
}

.custom-picker-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  transition: all 0.3s;
}

.custom-picker {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fff;
  z-index: 1001;
  border-radius: 24rpx 24rpx 0 0;
  transform: translateY(100%);
  transition: all 0.3s ease;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

.custom-picker.show {
  transform: translateY(0);
}

.custom-picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.picker-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
}

.picker-btn {
  font-size: 28rpx;
  padding: 0 10rpx;
}

.picker-cancel {
  color: #666;
}

.picker-confirm {
  color: #3E7BFA;
  font-weight: 500;
}

.custom-picker-content {
  max-height: 500rpx;
  overflow: hidden;
}

.picker-scroll {
  height: 500rpx;
}

.picker-item {
  height: 90rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #333;
  position: relative;
  transition: all 0.2s;
}

.picker-item:not(:last-child)::after {
  content: '';
  position: absolute;
  left: 40rpx;
  right: 40rpx;
  bottom: 0;
  height: 1rpx;
  background-color: #f5f5f5;
}

.picker-item.active {
  color: #3E7BFA;
  font-weight: 500;
  background-color: rgba(62, 123, 250, 0.05);
}

.picker-item.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 8rpx;
  height: 32rpx;
  background-color: #3E7BFA;
  border-radius: 0 4rpx 4rpx 0;
} 