<!-- 自定义选择器组件 -->
<view class="custom-picker-container" wx:if="{{show}}">
  <!-- 背景蒙层 -->
  <view class="custom-picker-mask" bindtap="close"></view>
  
  <!-- 选择器主体 -->
  <view class="custom-picker {{show ? 'show' : ''}}" catchtap="preventBubble">
    <!-- 选择器头部 -->
    <view class="custom-picker-header">
      <view class="picker-btn picker-cancel" bindtap="close">取消</view>
      <view class="picker-title">{{title}}</view>
      <view class="picker-btn picker-confirm" bindtap="confirm">确定</view>
    </view>
    
    <!-- 选择器内容 -->
    <view class="custom-picker-content">
      <scroll-view scroll-y class="picker-scroll">
        <view 
          wx:for="{{options}}" 
          wx:key="index" 
          class="picker-item {{tempIndex === index ? 'active' : ''}}"
          bindtap="selectItem"
          data-index="{{index}}">
          {{item}}
        </view>
      </scroll-view>
    </view>
  </view>
</view> 