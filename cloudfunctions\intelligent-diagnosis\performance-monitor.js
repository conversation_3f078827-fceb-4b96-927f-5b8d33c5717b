// 智能诊断系统性能监控器 v3.0.0
const DiagnosisEngine = require('./diagnosis-engine');
const ReportGenerator = require('./report-generator');

class PerformanceMonitor {
  constructor() {
    this.startTime = Date.now();
    this.metrics = {
      system: {
        memoryUsage: [],
        responseTime: [],
        throughput: 0,
        errorRate: 0,
        uptime: 0
      },
      diagnosis: {
        totalRequests: 0,
        successfulRequests: 0,
        failedRequests: 0,
        averageResponseTime: 0,
        cacheHitRate: 0,
        concurrentUsers: 0
      },
      components: {
        diagnosisEngine: { status: 'unknown', performance: 0 },
        reportGenerator: { status: 'unknown', performance: 0 },
        knowledgeGraph: { status: 'unknown', performance: 0 },
        aiAnalyzer: { status: 'unknown', performance: 0 }
      }
    };
    this.alerts = [];
    this.thresholds = {
      maxResponseTime: 3000, // 3秒
      maxMemoryUsage: 200, // 200MB
      minCacheHitRate: 0.6, // 60%
      maxErrorRate: 0.05 // 5%
    };
  }

  /**
   * 开始性能监控
   */
  async startMonitoring() {
    console.log('🔍 开始性能监控...');
    
    try {
      // 初始化诊断引擎
      const diagnosisEngine = new DiagnosisEngine();
      await diagnosisEngine.initialize();
      
      // 初始化报告生成器
      const reportGenerator = new ReportGenerator();
      
      // 系统预热
      await this.warmupSystem(diagnosisEngine, reportGenerator);
      
      // 性能基准测试
      await this.runPerformanceBenchmark(diagnosisEngine, reportGenerator);
      
      // 压力测试
      await this.runStressTest(diagnosisEngine, reportGenerator);
      
      // 生成监控报告
      const monitoringReport = this.generateMonitoringReport();
      
      console.log('📊 性能监控完成');
      return monitoringReport;
      
    } catch (error) {
      console.error('❌ 性能监控失败:', error);
      throw error;
    }
  }

  /**
   * 系统预热
   */
  async warmupSystem(diagnosisEngine, reportGenerator) {
    console.log('🔥 系统预热中...');
    
    const warmupData = {
      studentId: 'warmup_student',
      gradeLevel: 'grade6',
      testResults: { 'node1': 0.8, 'node2': 0.7 },
      learningHistory: { activities: [] }
    };

    // 预热诊断引擎
    for (let i = 0; i < 3; i++) {
      try {
        await diagnosisEngine.comprehensiveDiagnosis(
          warmupData,
          warmupData.learningHistory,
          warmupData.testResults
        );
      } catch (error) {
        console.warn('预热请求失败:', error.message);
      }
    }

    // 预热报告生成器
    const mockDiagnosisData = {
      studentId: 'warmup_student',
      analysisResults: {
        aiEnhancedAnalysis: { overallScore: 0.8 },
        weaknessAnalysis: { weaknessPoints: [] }
      }
    };
    
    for (let i = 0; i < 2; i++) {
      try {
        await reportGenerator.generateComprehensiveReport(mockDiagnosisData);
      } catch (error) {
        console.warn('预热报告生成失败:', error.message);
      }
    }
    
    console.log('✅ 系统预热完成');
  }

  /**
   * 性能基准测试
   */
  async runPerformanceBenchmark(diagnosisEngine, reportGenerator) {
    console.log('⚡ 开始性能基准测试...');
    
    const testData = this.generateTestData(10);
    const results = {
      diagnosisEngine: [],
      reportGenerator: [],
      memoryUsage: []
    };

    for (let i = 0; i < testData.length; i++) {
      const data = testData[i];
      const initialMemory = process.memoryUsage();

      // 测试诊断引擎性能
      const diagnosisStart = Date.now();
      try {
        const diagnosisResult = await diagnosisEngine.comprehensiveDiagnosis(
          data.studentData,
          data.learningData,
          data.testResults
        );
        
        const diagnosisTime = Date.now() - diagnosisStart;
        results.diagnosisEngine.push(diagnosisTime);
        
        // 测试报告生成器性能
        const reportStart = Date.now();
        await reportGenerator.generateComprehensiveReport(diagnosisResult);
        const reportTime = Date.now() - reportStart;
        results.reportGenerator.push(reportTime);
        
      } catch (error) {
        console.warn(`基准测试 ${i+1} 失败:`, error.message);
        this.metrics.diagnosis.failedRequests++;
      }

      // 记录内存使用
      const finalMemory = process.memoryUsage();
      results.memoryUsage.push({
        heapUsed: finalMemory.heapUsed / 1024 / 1024, // MB
        heapTotal: finalMemory.heapTotal / 1024 / 1024, // MB
        external: finalMemory.external / 1024 / 1024 // MB
      });

      this.metrics.diagnosis.totalRequests++;
      this.metrics.diagnosis.successfulRequests++;
    }

    // 计算性能指标
    this.calculatePerformanceMetrics(results);
    console.log('✅ 性能基准测试完成');
  }

  /**
   * 压力测试
   */
  async runStressTest(diagnosisEngine, reportGenerator) {
    console.log('💪 开始压力测试...');
    
    const concurrentRequests = 5;
    const testDuration = 10000; // 10秒
    const testData = this.generateTestData(20);
    
    const startTime = Date.now();
    let completedRequests = 0;
    let failedRequests = 0;
    
    while (Date.now() - startTime < testDuration) {
      const promises = [];
      
      for (let i = 0; i < concurrentRequests; i++) {
        const data = testData[Math.floor(Math.random() * testData.length)];
        
        const promise = diagnosisEngine.comprehensiveDiagnosis(
          data.studentData,
          data.learningData,
          data.testResults
        ).then(result => {
          completedRequests++;
          return reportGenerator.generateComprehensiveReport(result);
        }).catch(error => {
          failedRequests++;
          console.warn('压力测试请求失败:', error.message);
        });
        
        promises.push(promise);
      }
      
      await Promise.allSettled(promises);
      
      // 短暂休息避免过载
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    const totalTime = Date.now() - startTime;
    this.metrics.diagnosis.throughput = (completedRequests / totalTime) * 1000; // 每秒请求数
    this.metrics.diagnosis.errorRate = failedRequests / (completedRequests + failedRequests);
    
    console.log(`✅ 压力测试完成: ${completedRequests} 成功, ${failedRequests} 失败`);
  }

  /**
   * 生成测试数据
   */
  generateTestData(count) {
    const testData = [];
    
    for (let i = 0; i < count; i++) {
      testData.push({
        studentData: {
          studentId: `test_student_${i}`,
          gradeLevel: `grade${Math.floor(Math.random() * 6) + 1}`,
          learningStyle: ['visual', 'auditory', 'kinesthetic'][Math.floor(Math.random() * 3)]
        },
        learningData: {
          activities: Array.from({ length: Math.floor(Math.random() * 10) + 1 }, () => ({
            nodeId: `node_${Math.floor(Math.random() * 100)}`,
            timeSpent: Math.floor(Math.random() * 300) + 60,
            score: Math.random()
          }))
        },
        testResults: Object.fromEntries(
          Array.from({ length: Math.floor(Math.random() * 10) + 5 }, (_, idx) => [
            `node_${idx}`,
            Math.random()
          ])
        )
      });
    }
    
    return testData;
  }

  /**
   * 计算性能指标
   */
  calculatePerformanceMetrics(results) {
    // 诊断引擎性能
    if (results.diagnosisEngine.length > 0) {
      this.metrics.diagnosis.averageResponseTime = 
        results.diagnosisEngine.reduce((sum, time) => sum + time, 0) / results.diagnosisEngine.length;
      
      this.metrics.components.diagnosisEngine.performance = 
        Math.max(0, 100 - (this.metrics.diagnosis.averageResponseTime / this.thresholds.maxResponseTime) * 100);
      
      this.metrics.components.diagnosisEngine.status = 
        this.metrics.diagnosis.averageResponseTime < this.thresholds.maxResponseTime ? 'good' : 'warning';
    }

    // 报告生成器性能
    if (results.reportGenerator.length > 0) {
      const avgReportTime = 
        results.reportGenerator.reduce((sum, time) => sum + time, 0) / results.reportGenerator.length;
      
      this.metrics.components.reportGenerator.performance = 
        Math.max(0, 100 - (avgReportTime / 2000) * 100); // 2秒为基准
      
      this.metrics.components.reportGenerator.status = 
        avgReportTime < 2000 ? 'good' : 'warning';
    }

    // 内存使用分析
    if (results.memoryUsage.length > 0) {
      const avgMemory = results.memoryUsage.reduce((sum, mem) => sum + mem.heapUsed, 0) / results.memoryUsage.length;
      this.metrics.system.memoryUsage.push(avgMemory);
      
      if (avgMemory > this.thresholds.maxMemoryUsage) {
        this.alerts.push({
          type: 'memory',
          severity: 'warning',
          message: `内存使用超过阈值: ${avgMemory.toFixed(2)}MB > ${this.thresholds.maxMemoryUsage}MB`,
          timestamp: new Date().toISOString()
        });
      }
    }

    // 检查错误率
    if (this.metrics.diagnosis.errorRate > this.thresholds.maxErrorRate) {
      this.alerts.push({
        type: 'error_rate',
        severity: 'critical',
        message: `错误率过高: ${(this.metrics.diagnosis.errorRate * 100).toFixed(2)}% > ${this.thresholds.maxErrorRate * 100}%`,
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * 生成监控报告
   */
  generateMonitoringReport() {
    const uptime = Date.now() - this.startTime;
    this.metrics.system.uptime = uptime;

    return {
      timestamp: new Date().toISOString(),
      uptime: `${Math.floor(uptime / 1000)}秒`,
      status: this.alerts.length === 0 ? 'healthy' : 'warning',
      performance: {
        overall: this.calculateOverallPerformance(),
        diagnosis: {
          averageResponseTime: `${this.metrics.diagnosis.averageResponseTime.toFixed(2)}ms`,
          throughput: `${this.metrics.diagnosis.throughput.toFixed(2)} req/s`,
          errorRate: `${(this.metrics.diagnosis.errorRate * 100).toFixed(2)}%`,
          successRate: `${((this.metrics.diagnosis.successfulRequests / this.metrics.diagnosis.totalRequests) * 100).toFixed(2)}%`
        }
      },
      components: this.metrics.components,
      alerts: this.alerts,
      recommendations: this.generateRecommendations(),
      systemHealth: {
        memory: this.metrics.system.memoryUsage.length > 0 ? 
          `${this.metrics.system.memoryUsage[this.metrics.system.memoryUsage.length - 1].toFixed(2)}MB` : 'Unknown',
        cpu: 'Normal', // 实际实现中可以获取CPU使用率
        network: 'Normal'
      }
    };
  }

  /**
   * 计算总体性能分数
   */
  calculateOverallPerformance() {
    const scores = Object.values(this.metrics.components)
      .filter(component => component.performance > 0)
      .map(component => component.performance);
    
    if (scores.length === 0) return 0;
    
    const avgScore = scores.reduce((sum, score) => sum + score, 0) / scores.length;
    
    // 根据告警数量调整分数
    const alertsPenalty = this.alerts.length * 10;
    return Math.max(0, avgScore - alertsPenalty);
  }

  /**
   * 生成优化建议
   */
  generateRecommendations() {
    const recommendations = [];

    if (this.metrics.diagnosis.averageResponseTime > this.thresholds.maxResponseTime) {
      recommendations.push('考虑优化算法或增加缓存机制以减少响应时间');
    }

    if (this.metrics.diagnosis.errorRate > this.thresholds.maxErrorRate) {
      recommendations.push('检查错误日志，优化错误处理机制');
    }

    if (this.metrics.system.memoryUsage.some(usage => usage > this.thresholds.maxMemoryUsage)) {
      recommendations.push('优化内存使用，检查是否存在内存泄漏');
    }

    if (this.metrics.diagnosis.throughput < 1) {
      recommendations.push('考虑优化并发处理能力');
    }

    if (recommendations.length === 0) {
      recommendations.push('系统性能良好，建议继续监控');
    }

    return recommendations;
  }
}

// 导出模块
module.exports = PerformanceMonitor;

// 注释掉不兼容的代码，云函数环境中不支持 require.main === module
// 如果需要执行性能监控，可以在云函数入口中直接调用
/*
// 如果直接运行此文件，执行性能监控
if (require.main === module) {
  async function runPerformanceMonitoring() {
    const monitor = new PerformanceMonitor();
    
    try {
      const report = await monitor.startMonitoring();
      
      console.log('\n📊 === 性能监控报告 ===');
      console.log(`🕐 运行时间: ${report.uptime}`);
      console.log(`📈 总体性能: ${report.performance.overall.toFixed(2)}/100`);
      console.log(`⚡ 平均响应时间: ${report.performance.diagnosis.averageResponseTime}`);
      console.log(`🚀 吞吐量: ${report.performance.diagnosis.throughput}`);
      console.log(`❌ 错误率: ${report.performance.diagnosis.errorRate}`);
      console.log(`💾 内存使用: ${report.systemHealth.memory}`);
      
      if (report.alerts.length > 0) {
        console.log('\n⚠️ === 告警信息 ===');
        report.alerts.forEach(alert => {
          console.log(`${alert.severity.toUpperCase()}: ${alert.message}`);
        });
      }
      
      if (report.recommendations.length > 0) {
        console.log('\n💡 === 优化建议 ===');
        report.recommendations.forEach((rec, index) => {
          console.log(`${index + 1}. ${rec}`);
        });
      }
      
      console.log('\n✨ 性能监控完成');
      
    } catch (error) {
      console.error('性能监控失败:', error);
      process.exit(1);
    }
  }

  runPerformanceMonitoring();
}
*/ 