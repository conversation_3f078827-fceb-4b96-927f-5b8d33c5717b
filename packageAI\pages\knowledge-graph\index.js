// 知识图谱页面 - 基于云函数和数据库的实现
const deviceUtils = require('../../../utils/device-info.js');
const navigator = require('../../../utils/navigator.js');

// 数据库映射常量
const DIFFICULTY_LEVELS = {
  'basic': 'basic',
  'intermediate': 'intermediate', 
  'advanced': 'advanced',
  'expert': 'expert',
  'master': 'master'
};

const DIFFICULTY_LEVEL_TEXT = {
  'basic': '基础',
  'intermediate': '中等',
  'advanced': '高级', 
  'expert': '专家',
  'master': '大师'
};

const RELATIONSHIP_TYPES = {
  'prerequisite': 'prerequisite',
  'successor': 'successor',
  'related': 'related',
  'extension': 'extension',
  'parallel': 'parallel',
  'contains': 'contains',
  'example_of': 'example_of',
  'application_of': 'application_of',
  'cross_subject': 'cross_subject'
};

const RELATIONSHIP_TYPE_TEXT = {
  'prerequisite': '前置知识',
  'successor': '后续知识',
  'related': '相关知识',
  'extension': '扩展知识',
  'parallel': '并行知识',
  'contains': '包含知识',
  'example_of': '示例知识',
  'application_of': '应用知识',
  'cross_subject': '跨学科知识'
};

const SUBJECT_TEXT = {
  'mathematics': '数学',
  'chinese': '语文',
  'english': '英语',
  'physics': '物理',
  'chemistry': '化学',
  'biology': '生物',
  'history': '历史',
  'geography': '地理',
  'politics': '政治'
};

const GRADE_LEVEL_MAP = {
  1: '小学一年级',
  2: '小学二年级',
  3: '小学三年级',
  4: '小学四年级',
  5: '小学五年级',
  6: '小学六年级',
  7: '初中一年级',
  8: '初中二年级',
  9: '初中三年级',
  10: '高中一年级',
  11: '高中二年级',
  12: '高中三年级'
};

// 高中教材配置
const HIGH_SCHOOL_TEXTBOOKS = {
  // 高中必修A版 - 2册
  'mandatory_a': {
    name: '必修A版',
    type: 'mandatory',
    books: [
      { grade: 10, volume: 1, nodeCodePrefix: 'MATH_G10M1A', name: '第一册' },
      { grade: 10, volume: 2, nodeCodePrefix: 'MATH_G10M2A', name: '第二册' }
    ]
  },
  // 高中必修B版 - 4册
  'mandatory_b': {
    name: '必修B版',
    type: 'mandatory',
    books: [
      { grade: 10, volume: 1, nodeCodePrefix: 'MATH_G10M1B', name: '第一册' },
      { grade: 10, volume: 2, nodeCodePrefix: 'MATH_G10M2B', name: '第二册' },
      { grade: 10, volume: 3, nodeCodePrefix: 'MATH_G10M3B', name: '第三册' },
      { grade: 10, volume: 4, nodeCodePrefix: 'MATH_G10M4B', name: '第四册' }
    ]
  },
  // 高中选择性必修A版 - 3册
  'elective_a': {
    name: '选择性必修A版',
    type: 'elective',
    books: [
      { grade: 11, volume: 1, nodeCodePrefix: 'MATH_G11E1A', name: '第一册' },
      { grade: 11, volume: 2, nodeCodePrefix: 'MATH_G11E2A', name: '第二册' },
      { grade: 11, volume: 3, nodeCodePrefix: 'MATH_G11E3A', name: '第三册' }
    ]
  },
  // 高中选择性必修B版 - 3册
  'elective_b': {
    name: '选择性必修B版',
    type: 'elective',
    books: [
      { grade: 11, volume: 1, nodeCodePrefix: 'MATH_G11E1B', name: '第一册' },
      { grade: 11, volume: 2, nodeCodePrefix: 'MATH_G11E2B', name: '第二册' },
      { grade: 11, volume: 3, nodeCodePrefix: 'MATH_G11E3B', name: '第三册' }
    ]
  }
};

Page({
  data: {
    isIPad: false,
    loading: true,
    selectedGrade: 7,           // 当前选择的年级(数字)
    selectedStage: 'junior',    // 当前选择的阶段
    selectedSubject: 'mathematics', // 当前选择的学科（固定为数学）
    selectedNode: null,         // 当前选中的节点
    showDetail: false,          // 详情面板是否可见
    showEmpty: false,           // 是否显示空状态
    
    // 高中教材选择相关
    selectedTextbookType: 'mandatory_a',  // 当前选择的教材类型
    selectedBook: null,                   // 当前选择的具体教材
    
    // 用户信息
    studentId: null,            // 学生ID（如果已登录）
    
    // 知识点数据
    nodes: [],                  // 知识点列表
    chaptersData: [],           // 按章节分组的知识点数据
    masteryData: {},            // 掌握状态数据 {nodeId: masteryInfo}
    
    // 筛选和排序
    searchKeyword: '',          // 搜索关键词
    selectedMasteryStatus: 'all', // 选择的掌握状态筛选
    sortOrder: 'default',       // 排序方式
    sortOrderText: '默认排序',
    isLoading: false,
    
    // 滚动控制
    scrollTop: 0,               // 滚动位置
    showBackToTop: false,       // 是否显示回到顶部按钮
    
    // 详情面板相关数据
    nodeContent: null,          // 知识点详细内容
    nodeRelationships: [],      // 知识点关系
    prerequisites: [],          // 前置知识点
    successors: [],            // 后续知识点
    related: [],               // 相关知识点
    applications: [],          // 应用知识点
    others: [],                // 其他关系
    loadingRelations: false,    // 相关知识点加载状态
    
    // 年级选择
    grades: [],

    // 状态栏高度
    statusBarHeight: 20,
    navBarHeight: 44,
    
    // 常量映射供模板使用
    DIFFICULTY_LEVELS,
    DIFFICULTY_LEVEL_TEXT,
    RELATIONSHIP_TYPE_TEXT,
    SUBJECT_TEXT,
    GRADE_LEVEL_MAP,
    HIGH_SCHOOL_TEXTBOOKS,
    
    // 掌握状态映射
    MASTERY_STATUS_TEXT: {
      'not_started': '未开始',
      'weak': '薄弱',
      'learning': '学习中',
      'mastered': '已掌握'
    },
    
    MASTERY_STATUS_COLORS: {
      'not_started': 'status-not-started',
      'weak': 'status-weak', 
      'learning': 'status-learning',
      'mastered': 'status-mastered'
    }
  },

  onLoad: function() {
    console.log('知识图谱页面加载开始');
    
    // 初始化设备信息
    this.initDeviceInfo();
    
    // 获取学生信息
    this.getStudentInfo();
    
    // 初始化年级列表
    this.initGrades();
    
    // 初始化高中教材选择
    this.initHighSchoolTextbooks();
    
    // 加载知识点数据
    this.loadKnowledgeData();
  },

  /**
   * 初始化设备信息
   */
  initDeviceInfo: function() {
    try {
      const deviceInfo = deviceUtils.getDeviceInfo();
      const systemInfo = wx.getSystemInfoSync();
      
      this.setData({
        isIPad: deviceInfo.isIPad,
        statusBarHeight: systemInfo.statusBarHeight || 20,
        navBarHeight: 44
      });
    } catch (error) {
      console.error('初始化设备信息失败:', error);
    }
  },

  /**
   * 获取学生信息
   */
  getStudentInfo: function() {
    try {
      // 尝试从全局数据或本地存储获取学生信息
      const app = getApp();
      const studentId = app.globalData?.userInfo?.id || 
                       wx.getStorageSync('studentId') || 
                       null;
      
      console.log('获取到学生ID:', studentId);
      this.setData({ studentId });
    } catch (error) {
      console.error('获取学生信息失败:', error);
      this.setData({ studentId: null });
    }
  },

  /**
   * 初始化年级列表
   */
  initGrades: function() {
    const selectedStage = this.data.selectedStage;
    const grades = this.getGradesByStage(selectedStage);
    this.setData({ grades });
  },

  /**
   * 初始化高中教材选择
   */
  initHighSchoolTextbooks: function() {
    if (this.data.selectedStage === 'senior') {
      // 默认选择必修A版第一册
      const defaultTextbookType = 'mandatory_a';
      const defaultBook = HIGH_SCHOOL_TEXTBOOKS[defaultTextbookType].books[0];
      
      this.setData({
        selectedTextbookType: defaultTextbookType,
        selectedBook: defaultBook,
        selectedGrade: defaultBook.grade
      });
    }
  },

  /**
   * 根据学段获取年级列表
   */
  getGradesByStage: function(stage) {
    switch(stage) {
      case 'primary':
        return [1, 2, 3, 4, 5, 6];
      case 'junior':
        return [7, 8, 9];
      case 'senior':
        // 高中不再显示简单的年级选择，而是显示教材选择
        return [];
      default:
        return [7, 8, 9];
    }
  },

  /**
   * 调用云函数
   */
  callKnowledgeGraphFunction: function(action, params = {}) {
    return new Promise((resolve, reject) => {
      console.log(`调用云函数: action=${action}`, params);
      
      wx.cloud.callFunction({
        name: 'knowledge-graph-query',
        data: {
          action,
          params
        },
        success: (res) => {
          console.log(`云函数调用成功: action=${action}`, res);
          if (res.result && res.result.success) {
            resolve(res.result.data);
          } else {
            console.error(`云函数返回错误: action=${action}`, res.result);
            reject(new Error(res.result?.error || '云函数调用失败'));
          }
        },
        fail: (error) => {
          console.error(`云函数调用失败: action=${action}`, error);
          reject(error);
            }
          });
      });
  },

  /**
   * 加载知识点数据
   */
  loadKnowledgeData: async function() {
    const { selectedGrade, selectedSubject, studentId, selectedStage, selectedBook } = this.data;
    
    this.setData({ loading: true, showEmpty: false });
    
    try {
      console.log('开始加载知识点数据:', { selectedGrade, selectedSubject, studentId, selectedStage, selectedBook });
      
      // 构建查询参数
      let queryParams = {
        gradeLevel: selectedGrade,
        subject: selectedSubject
      };
      
      // 如果是高中，添加教材node_code前缀信息
      if (selectedStage === 'senior' && selectedBook) {
        queryParams.nodeCodePrefix = selectedBook.nodeCodePrefix;
        
        console.log('高中教材查询参数:', queryParams);
      }
      
      // 调用云函数获取知识点数据
      const nodes = await this.callKnowledgeGraphFunction('getKnowledgeByGrade', queryParams);
      
      console.log('获取到知识点数据:', nodes);
      
      if (!nodes || nodes.length === 0) {
        this.setData({
          loading: false,
          showEmpty: true,
          nodes: [],
          chaptersData: [],
          masteryData: {}
        });
        return;
      }
      
      // 处理知识点数据
      const processedNodes = this.processKnowledgeNodes(nodes);
      
      // 加载掌握状态数据
      let masteryData = {};
      if (studentId) {
        try {
          const masteryList = await this.callKnowledgeGraphFunction('getStudentMastery', {
            studentId,
            gradeLevel: selectedGrade,
            subject: selectedSubject
          });
          
          // 转换为以nodeId为键的对象
          masteryData = {};
          if (masteryList && masteryList.length > 0) {
            masteryList.forEach(mastery => {
              masteryData[mastery.knowledge_node_id] = mastery;
            });
          }
          
          console.log('获取到掌握状态数据:', Object.keys(masteryData).length, '条');
    } catch (error) {
          console.error('加载掌握状态失败:', error);
          masteryData = {};
        }
      }
      
      // 为知识点添加掌握状态信息（基于student_knowledge_mastery表结构）
      const nodesWithMastery = processedNodes.map(node => {
        const masteryInfo = masteryData[node.id];
        return {
          ...node,
          masteryStatus: masteryInfo?.mastery_status || 'not_started',
          masteryPercentage: masteryInfo?.mastery_percentage || 0,
          
          // 文理科差异化掌握评估
          trackSpecificMastery: masteryInfo?.track_specific_mastery || {},
          liberalArtsScore: masteryInfo?.liberal_arts_score || 0,
          scienceScore: masteryInfo?.science_score || 0,
          
          // 能力维度掌握情况
          basicConceptMastery: masteryInfo?.basic_concept_mastery || false,
          calculationSkillMastery: masteryInfo?.calculation_skill_mastery || false,
          applicationAbilityMastery: masteryInfo?.application_ability_mastery || false,
          
          // 文理科特殊能力掌握
          abstractThinkingMastery: masteryInfo?.abstract_thinking_mastery || false,
          practicalApplicationMastery: masteryInfo?.practical_application_mastery || false,
          logicalProofMastery: masteryInfo?.logical_proof_mastery || false,
          
          // 学习统计
          totalStudyTimeMinutes: masteryInfo?.total_study_time_minutes || 0,
          practiceCount: masteryInfo?.practice_count || 0,
          correctCount: masteryInfo?.correct_count || 0,
          lastPracticeDate: masteryInfo?.last_practice_date,
          
          // 预测和建议
          predictedMasteryDays: masteryInfo?.predicted_mastery_days,
          recommendedPracticeCount: masteryInfo?.recommended_practice_count || 0,
          nextReviewDate: masteryInfo?.next_review_date,
          
          // AI评估
          aiAssessment: masteryInfo?.ai_assessment || {},
          weaknessAnalysis: masteryInfo?.weakness_analysis || [],
          improvementSuggestions: masteryInfo?.improvement_suggestions || []
        };
      });
      
      // 根据掌握状态筛选
      const filteredNodes = this.filterNodesByMasteryStatus(nodesWithMastery);
      
      // 按章节分组
      const chaptersData = this.groupNodesByChapter(filteredNodes);
      
        this.setData({
          loading: false,
        showEmpty: filteredNodes.length === 0,
        nodes: filteredNodes,
        chaptersData,
        masteryData
      });
      
    } catch (error) {
      console.error('加载知识点数据失败:', error);
      this.setData({
        loading: false,
        showEmpty: true,
        nodes: [],
        chaptersData: [],
        masteryData: {}
      });
      
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    }
  },
  
  /**
   * 根据掌握状态筛选节点
   */
  filterNodesByMasteryStatus: function(nodes) {
    const { selectedMasteryStatus } = this.data;
    
    if (selectedMasteryStatus === 'all') {
      return nodes;
    }
    
    return nodes.filter(node => node.masteryStatus === selectedMasteryStatus);
  },
  
  /**
   * 处理知识点数据
   */
  processKnowledgeNodes: function(nodes) {
    return nodes.map(node => ({
      id: node.id,
      code: node.node_code,
      name: node.node_name || '未命名知识点',
      subject: node.subject,
      gradeLevel: node.grade_level,
      semester: node.semester,
      chapterNumber: node.chapter_number,
      sectionNumber: node.section_number,
      chapterTitle: node.chapter_title,
      sectionTitle: node.section_title,
      unitName: node.unit_name,
      difficulty: node.difficulty,
      estimatedTimeMinutes: node.estimated_time_minutes,
      cognitiveComplexity: node.cognitive_complexity,
      importanceLevel: node.importance_level,
      examFrequency: node.exam_frequency,
      knowledgeType: node.knowledge_type,
      memoryTips: node.memory_tips,
      commonMisconceptions: node.common_misconceptions,
      curriculumStandard: node.curriculum_standard,
      examImportanceLevel: node.exam_importance_level,
      gaokaoFrequency: node.gaokao_frequency,
      zhongkaoApplicability: node.zhongkao_applicability,
      knowledgeApplicability: node.knowledge_applicability,
      targetAcademicTracks: node.target_academic_tracks,
      liberalArtsDifficulty: node.liberal_arts_difficulty,
      scienceDifficulty: node.science_difficulty,
      
      // 核心能力要求（基于数据库真实字段）
      requiresMemorization: node.requires_memorization,
      requiresUnderstanding: node.requires_understanding,
      requiresApplication: node.requires_application,
      requiresAnalysis: node.requires_analysis,
      requiresSynthesis: node.requires_synthesis,
      requiresEvaluation: node.requires_evaluation,
      
      // 基础能力要求
      requiresBasicConcept: node.requires_basic_concept,
      requiresCalculationSkill: node.requires_calculation_skill,
      requiresApplicationAbility: node.requires_application_ability,
      
      // 文理科特殊能力要求
      requiresAbstractThinking: node.requires_abstract_thinking,
      requiresPracticalApplication: node.requires_practical_application,
      requiresLogicalProof: node.requires_logical_proof,
      
      version: node.version,
      isActive: node.is_active,
      createdAt: node.created_at,
      updatedAt: node.updated_at
    }));
  },

  /**
   * 按章节分组知识点
   */
  groupNodesByChapter: function(nodes) {
    const chaptersMap = new Map();
    
    nodes.forEach(node => {
      const chapterKey = `${node.chapterNumber || 0}-${node.chapterTitle || '其他'}`;
      
      if (!chaptersMap.has(chapterKey)) {
        chaptersMap.set(chapterKey, {
          chapterNumber: node.chapterNumber || 0,
          chapterTitle: node.chapterTitle || '其他章节',
          displayTitle: node.chapterTitle || '其他章节',
          isSpecialChapter: !node.chapterNumber,
          nodes: []
        });
      }
      
      chaptersMap.get(chapterKey).nodes.push(node);
    });
    
    // 转换为数组并排序
    const chapters = Array.from(chaptersMap.values());
    chapters.sort((a, b) => {
      if (a.isSpecialChapter && !b.isSpecialChapter) return 1;
      if (!a.isSpecialChapter && b.isSpecialChapter) return -1;
      return (a.chapterNumber || 0) - (b.chapterNumber || 0);
    });
    
    // 每个章节内的节点按小节号排序
    chapters.forEach(chapter => {
      chapter.nodes.sort((a, b) => {
        const sectionA = parseFloat(a.sectionNumber) || 0;
        const sectionB = parseFloat(b.sectionNumber) || 0;
        if (sectionA !== sectionB) return sectionA - sectionB;
        return (a.importanceLevel || 0) - (b.importanceLevel || 0);
      });
    });
    
    return chapters;
  },

  /**
   * 学段变化处理
   */
  onStageChange: function(e) {
    const stage = e.currentTarget.dataset.stage;
    const grades = this.getGradesByStage(stage);
    
    if (stage === 'senior') {
      // 高中选择默认教材
      const defaultTextbookType = 'mandatory_a';
      const defaultBook = HIGH_SCHOOL_TEXTBOOKS[defaultTextbookType].books[0];
      
      this.setData({
        selectedStage: stage,
        grades,
        selectedTextbookType: defaultTextbookType,
        selectedBook: defaultBook,
        selectedGrade: defaultBook.grade
      });
    } else {
      // 小学和初中选择第一个年级
      const selectedGrade = grades[0];
      
      this.setData({
        selectedStage: stage,
        grades,
        selectedGrade,
        selectedTextbookType: null,
        selectedBook: null
      });
    }
    
    // 重新加载数据
    this.loadKnowledgeData();
  },

  /**
   * 年级变化处理
   */
  onGradeChange: function(e) {
    const grade = parseInt(e.currentTarget.dataset.grade);
    
    this.setData({ selectedGrade: grade });
    
    // 重新加载数据
    this.loadKnowledgeData();
  },



  /**
   * 教材类型选择
   */
  onTextbookTypeChange: function(e) {
    const textbookType = e.currentTarget.dataset.type;
    const textbookConfig = HIGH_SCHOOL_TEXTBOOKS[textbookType];
    
    if (textbookConfig && textbookConfig.books.length > 0) {
      // 选择该类型的第一册
      const firstBook = textbookConfig.books[0];
      
      this.setData({
        selectedTextbookType: textbookType,
        selectedBook: firstBook,
        selectedGrade: firstBook.grade
      });
      
      // 重新加载数据
      this.loadKnowledgeData();
    }
  },

  /**
   * 具体教材选择
   */
  onBookSelect: function(e) {
    const bookIndex = parseInt(e.currentTarget.dataset.index);
    const textbookConfig = HIGH_SCHOOL_TEXTBOOKS[this.data.selectedTextbookType];
    
    if (textbookConfig && textbookConfig.books[bookIndex]) {
      const selectedBook = textbookConfig.books[bookIndex];
      
      this.setData({
        selectedBook: selectedBook,
        selectedGrade: selectedBook.grade
      });
      
      // 重新加载数据
      this.loadKnowledgeData();
    }
  },

  /**
   * 知识点点击处理
   */
  onNodeClick: async function(e) {
    const nodeId = e.currentTarget.dataset.nodeId;
    if (!nodeId) return;
    
    console.log('点击知识点:', nodeId);
    
    // 查找节点数据
    const node = this.findNodeById(nodeId);
    if (!node) {
      console.error('未找到节点数据:', nodeId);
      return;
    }
    
    this.setData({
      selectedNode: node,
      showDetail: true,
      nodeContent: null,
      nodeRelationships: [],
      prerequisites: [],
      successors: [],
      related: [],
      applications: [],
      others: [],
      loadingRelations: true
    });
    
    // 异步加载详细数据
    await this.loadNodeDetails(nodeId);
  },

  /**
   * 查找节点
   */
  findNodeById: function(nodeId) {
    return this.data.nodes.find(node => node.id == nodeId);
  },

  /**
   * 加载节点详细信息
   */
  loadNodeDetails: async function(nodeId) {
    try {
      console.log('开始加载节点详细信息:', nodeId);
      
      // 设置加载状态
      this.setData({
        loadingRelations: true
      });
      
      // 并行加载内容和关系
      const [content, relationships] = await Promise.all([
        this.loadNodeContent(nodeId),
        this.loadNodeRelationships(nodeId)
      ]);
      
      console.log('节点详细信息加载完成:', { content, relationships });
      
      // 处理关系数据
      const processedRelationships = await this.processNodeRelationships(relationships);
      
      this.setData({
        nodeContent: content,
        nodeRelationships: relationships,
        loadingRelations: false,
        ...processedRelationships
      });
      
    } catch (error) {
      console.error('加载节点详细信息失败:', error);
      this.setData({
        loadingRelations: false
      });
    }
  },

  /**
   * 加载节点内容
   */
  loadNodeContent: async function(nodeId) {
    try {
      const content = await this.callKnowledgeGraphFunction('getKnowledgeContent', {
        nodeId
      });
      return content;
    } catch (error) {
      console.error('加载节点内容失败:', error);
        return null;
    }
  },

  /**
   * 加载节点关系
   */
  loadNodeRelationships: async function(nodeId) {
    try {
      const relationships = await this.callKnowledgeGraphFunction('getKnowledgeRelationships', {
        nodeId,
        relationshipType: null  // 查询所有关系类型
      });
      return relationships || [];
    } catch (error) {
      console.error('加载节点关系失败:', error);
      return [];
    }
  },

  /**
   * 处理节点关系数据
   */
  processNodeRelationships: async function(relationships) {
    if (!relationships || relationships.length === 0) {
      return {
            prerequisites: [],
        successors: [],
        related: [],
            applications: [],
            others: []
      };
    }
    
    // 按关系类型分组
    const grouped = {
      prerequisites: [],
      successors: [],
      related: [],
      applications: [],
      others: []
    };
    
    relationships.forEach(rel => {
      const isSource = rel.source_node_id == this.data.selectedNode.id;
      const targetNodeId = isSource ? rel.target_node_id : rel.source_node_id;
      const targetNodeName = isSource ? rel.target_name : rel.source_name;
      const targetNodeCode = isSource ? rel.target_code : rel.source_code;
      const targetGradeLevel = isSource ? rel.target_grade_level : rel.source_grade_level;
      const targetSubject = isSource ? rel.target_subject : rel.source_subject;
      const targetDifficulty = isSource ? rel.target_difficulty : rel.source_difficulty;
      
      const relatedNode = {
        id: targetNodeId,
        name: targetNodeName || '未命名',
        code: targetNodeCode,
        gradeLevel: targetGradeLevel,
        subject: targetSubject,
        difficulty: targetDifficulty,
        strength: rel.strength || 0,
        confidence: rel.confidence || 0,
        relationshipType: rel.relationship_type,
        relationshipTypeText: RELATIONSHIP_TYPE_TEXT[rel.relationship_type] || rel.relationship_type
      };
      
      // 根据关系类型分组
      switch (rel.relationship_type) {
          case 'prerequisite':
          if (isSource) {
            grouped.prerequisites.push(relatedNode);
            } else {
            grouped.successors.push(relatedNode);
            }
            break;
          case 'successor':
          if (isSource) {
            grouped.successors.push(relatedNode);
            } else {
            grouped.prerequisites.push(relatedNode);
            }
            break;
          case 'related':
          grouped.related.push(relatedNode);
            break;
          case 'application_of':
          grouped.applications.push(relatedNode);
            break;
          default:
          grouped.others.push(relatedNode);
      }
    });
    
    return grouped;
  },

  /**
   * 相关知识点点击处理
   */
  onRelatedNodeClick: async function(e) {
    const nodeId = e.currentTarget.dataset.nodeId;
    if (!nodeId) return;
    
    console.log('点击相关知识点:', nodeId);
    
    try {
      // 尝试从当前数据中查找
      let node = this.findNodeById(nodeId);
      
      // 如果当前数据中没有，尝试从云函数获取
      if (!node) {
        const nodes = await this.callKnowledgeGraphFunction('getKnowledgeNodes', {
          nodeIds: [nodeId]
        });
        
        if (nodes && nodes.length > 0) {
          const processedNodes = this.processKnowledgeNodes(nodes);
          node = processedNodes[0];
        }
      }
      
      if (!node) {
      wx.showToast({
          title: '知识点不存在',
        icon: 'none'
      });
      return;
    }
    
      // 更新选中节点并加载详情
      this.setData({
        selectedNode: node,
        nodeContent: null,
        nodeRelationships: [],
        prerequisites: [],
        successors: [],
        related: [],
        applications: [],
        others: [],
        loadingRelations: true
      });
      
      await this.loadNodeDetails(nodeId);
      
    } catch (error) {
      console.error('加载相关知识点失败:', error);
        wx.showToast({
        title: '加载失败',
          icon: 'none'
        });
      }
  },

  /**
   * 搜索知识点
   */
  onSearch: function(e) {
    const keyword = e.detail.value.trim();
    this.setData({ searchKeyword: keyword });
    
    if (keyword) {
      this.searchKnowledge(keyword);
    } else {
      this.loadKnowledgeData();
    }
  },

  /**
   * 掌握状态筛选切换
   */
  onMasteryStatusChange: function(e) {
    const status = e.currentTarget.dataset.status;
    console.log('切换掌握状态筛选:', status);
    
    this.setData({ 
      selectedMasteryStatus: status 
    });
    
    // 重新加载数据
    if (this.data.searchKeyword) {
      this.searchKnowledge(this.data.searchKeyword);
    } else {
      this.loadKnowledgeData();
    }
  },

  /**
   * 搜索知识点
   */
  searchKnowledge: async function(keyword) {
    this.setData({ loading: true });
    
    try {
      const { selectedGrade, selectedSubject, studentId } = this.data;
      
      const nodes = await this.callKnowledgeGraphFunction('searchKnowledge', {
        keyword,
        gradeLevel: selectedGrade,
        subject: selectedSubject,
        limit: 50
      });
      
      if (nodes && nodes.length > 0) {
        const processedNodes = this.processKnowledgeNodes(nodes);
        
        // 加载掌握状态数据
        let masteryData = {};
        if (studentId) {
          try {
            const masteryList = await this.callKnowledgeGraphFunction('getStudentMastery', {
              studentId,
              gradeLevel: selectedGrade,
              subject: selectedSubject
            });
            
            // 转换为以nodeId为键的对象
            masteryData = {};
            if (masteryList && masteryList.length > 0) {
              masteryList.forEach(mastery => {
                masteryData[mastery.knowledge_node_id] = mastery;
              });
            }
          } catch (error) {
            console.error('加载掌握状态失败:', error);
            masteryData = {};
          }
        }
        
        // 为知识点添加掌握状态信息（基于student_knowledge_mastery表结构）
        const nodesWithMastery = processedNodes.map(node => {
          const masteryInfo = masteryData[node.id];
          return {
            ...node,
            masteryStatus: masteryInfo?.mastery_status || 'not_started',
            masteryPercentage: masteryInfo?.mastery_percentage || 0,
            
            // 文理科差异化掌握评估
            trackSpecificMastery: masteryInfo?.track_specific_mastery || {},
            liberalArtsScore: masteryInfo?.liberal_arts_score || 0,
            scienceScore: masteryInfo?.science_score || 0,
            
            // 能力维度掌握情况
            basicConceptMastery: masteryInfo?.basic_concept_mastery || false,
            calculationSkillMastery: masteryInfo?.calculation_skill_mastery || false,
            applicationAbilityMastery: masteryInfo?.application_ability_mastery || false,
            
            // 文理科特殊能力掌握
            abstractThinkingMastery: masteryInfo?.abstract_thinking_mastery || false,
            practicalApplicationMastery: masteryInfo?.practical_application_mastery || false,
            logicalProofMastery: masteryInfo?.logical_proof_mastery || false,
            
            // 学习统计
            totalStudyTimeMinutes: masteryInfo?.total_study_time_minutes || 0,
            practiceCount: masteryInfo?.practice_count || 0,
            correctCount: masteryInfo?.correct_count || 0,
            lastPracticeDate: masteryInfo?.last_practice_date,
            
            // 预测和建议
            predictedMasteryDays: masteryInfo?.predicted_mastery_days,
            recommendedPracticeCount: masteryInfo?.recommended_practice_count || 0,
            nextReviewDate: masteryInfo?.next_review_date,
            
            // AI评估
            aiAssessment: masteryInfo?.ai_assessment || {},
            weaknessAnalysis: masteryInfo?.weakness_analysis || [],
            improvementSuggestions: masteryInfo?.improvement_suggestions || []
          };
        });
        
        // 根据掌握状态筛选
        const filteredNodes = this.filterNodesByMasteryStatus(nodesWithMastery);
        
        const chaptersData = this.groupNodesByChapter(filteredNodes);
        
        this.setData({
          loading: false,
          nodes: filteredNodes,
          chaptersData,
          showEmpty: filteredNodes.length === 0,
          masteryData
        });
      } else {
        this.setData({
          loading: false,
          nodes: [],
          chaptersData: [],
          showEmpty: true
        });
      }
      
    } catch (error) {
      console.error('搜索知识点失败:', error);
      this.setData({
        loading: false,
        showEmpty: true
      });
      
      wx.showToast({
        title: '搜索失败',
        icon: 'none'
      });
    }
  },

  /**
   * 开始学习
   */
  startLearning: function() {
    const selectedNode = this.data.selectedNode;
    if (!selectedNode) {
      wx.showToast({
        title: '请先选择知识点',
        icon: 'none'
      });
      return;
    }

    console.log('开始学习知识点:', selectedNode);

    // 关闭详情面板
    this.closeDetail();

    // 跳转到知识点详情页面
    wx.navigateTo({
      url: `/packageHome/pages/knowledge-detail/index?id=${selectedNode.id}&name=${encodeURIComponent(selectedNode.name || '')}&gradeLevel=${selectedNode.gradeLevel || ''}&subject=${selectedNode.subject || 'mathematics'}&from=knowledge-graph`,
      fail: (error) => {
        console.error('跳转到知识点详情页面失败:', error);
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 关闭详情面板
   */
  closeDetail: function() {
    this.setData({
      showDetail: false,
      selectedNode: null,
      nodeContent: null,
      nodeRelationships: []
    });
  },

  /**
   * 返回上一页
   */
  goBack: function() {
    if (navigator && navigator.goBack) {
      navigator.goBack();
    } else {
      wx.navigateBack();
    }
  },

  /**
   * 下拉刷新
   */
  onRefresh: function() {
    this.loadKnowledgeData();
  },

  /**
   * 滚动事件监听
   */
  onScroll: function(e) {
    const scrollTop = e.detail.scrollTop;
    
    // 当滚动超过200rpx时显示回到顶部按钮
    const shouldShow = scrollTop > 200;
    
    if (shouldShow !== this.data.showBackToTop) {
      this.setData({
        showBackToTop: shouldShow
      });
    }
  },

  /**
   * 滚动到顶部
   */
  scrollToTop: function() {
    // 先设置一个随机的scrollTop值来触发滚动，然后设置为0
    this.setData({
      scrollTop: Math.random()
    });
    
    setTimeout(() => {
      this.setData({
        scrollTop: 0,
        showBackToTop: false
      });
    }, 100);
    
    console.log('滚动到顶部');
  },

  /**
   * 阻止事件冒泡
   */
  stopPropagation: function() {
    // 阻止事件冒泡
  },

  /**
   * 阻止滑动穿透
   */
  preventTouchMove: function() {
    // 阻止滑动穿透
  }
}); 