// 导入知识图谱数据
const { 
  comprehensiveKnowledgeData,
  knowledgeGraphEdges, 
  DIFFICULTY_LEVELS,
  RELATIONSHIP_TYPES: EDGE_TYPES,
  STATUS_TYPES,
  GRADE_LEVELS,
  RELATION_STRENGTH
} = require('../../../data/comprehensive-knowledge-data.js');
const deviceUtils = require('../../../utils/device-info.js');
const navigator = require('../../../utils/navigator.js');
// 引入云数据库API
const cloudApi = require('../../../utils/cloud-api.js');

// 知识点关联关系类型（保持向后兼容）
const RELATION_TYPES = EDGE_TYPES;

// 年级级别映射
const GRADE_LEVEL_MAP = {
  '小学一年级': 1,
  '小学二年级': 2,
  '小学三年级': 3,
  '小学四年级': 4,
  '小学五年级': 5,
  '小学六年级': 6,
  '初中一年级': 7,
  '初中二年级': 8, 
  '初中三年级': 9,
  '高中一年级': 10,
  '高中二年级': 11,
  '高中三年级': 12
};

// 根据数字level获取年级名称
const LEVEL_GRADE_MAP = {
  1: '小学一年级',
  2: '小学二年级',
  3: '小学三年级',
  4: '小学四年级',
  5: '小学五年级',
  6: '小学六年级',
  7: '初中一年级',
  8: '初中二年级',
  9: '初中三年级',
  10: '高中一年级',
  11: '高中二年级',
  12: '高中三年级'
};

Page({
  data: {
    isIPad: false,             // 是否iPad设备
    loading: true,             // 加载状态
    scale: 1,                  // 当前缩放比例
    selectedGrade: GRADE_LEVELS.JUNIOR_1, // 当前选择的年级(文本格式)，默认初中一年级
    selectedLevel: 7,          // 当前选择的年级(数字格式)，默认初中一年级
    selectedStage: 'junior',   // 当前选择的阶段（小学/初中/高中）
    selectedNode: null,        // 当前选中的节点
    showDetail: false,         // 详情面板是否可见
    showEmpty: false,          // 是否显示空状态
    activeStatus: null,        // 当前筛选状态
    
    // 拖动相关状态
    draggingNodeId: null,      // 当前正在拖动的节点ID
    draggingStartX: 0,         // 拖动开始位置X
    draggingStartY: 0,         // 拖动开始位置Y
    
    // 图谱拖动相关
    graphDragging: false,      // 是否正在拖动整个图谱
    graphDragStartX: 0,        // 图谱拖动开始X坐标
    graphDragStartY: 0,        // 图谱拖动开始Y坐标
    graphOffsetX: 0,           // 图谱X方向偏移量
    graphOffsetY: 0,           // 图谱Y方向偏移量
    
    // 统计数据
    masteredCount: 0,
    learningCount: 0,
    weakCount: 0,
    notMasteredCount: 0,
    notStartedCount: 0,
    
    // 年级列表
    grades: [],
    
    // 知识点数据
    nodes: [],
    links: [],            // 连接线数据
    
    // 原始数据（用于筛选恢复）
    originalNodes: [],
    originalLinks: [],     // 原始连接线数据
    
    // 章节化数据
    chaptersData: [],      // 按章节分组的知识点数据
    
    // 添加排序相关变量和方法
    sortOrder: 'default', // 默认排序方式
    sortOrderText: '默认排序', // 当前排序方式文本
    isLoading: false, // 加载状态
    
    // 相关知识点
    relatedPoints: [], // 当前选中知识点的前置和后续知识点
    
    // 关系类型配置 - 基于数据库定义的关系类型
    relationSections: [
      { key: 'prerequisites', type: 'prerequisite', title: '前置知识点', description: '点击可查看详情' },
      { key: 'progressions', type: 'successor', title: '后续知识点' },
      { key: 'correlations', type: 'related', title: '相关知识点' },
      { key: 'applications', type: 'application_of', title: '应用知识点' },
      { key: 'others', type: 'others', title: '其他关系' }
    ],

    // 状态栏高度
    statusBarHeight: 20,
    navBarHeight: 44
  },

  // 容器尺寸缓存
  containerInfo: {
    width: 0,
    height: 0,
    initialized: false
  },

  onLoad: function() {
    // 初始化设备信息
    this.initDeviceInfo();
    
    // 测试云函数连接
    this.testCloudFunctionConnection();
    
    // 根据默认年级设置正确的阶段和level
    const defaultGrade = this.data.selectedGrade;
    let selectedStage = 'junior';
    let selectedLevel = 7; // 默认初中一年级
    
    if (defaultGrade.startsWith('小学')) {
      selectedStage = 'primary';
      // 从年级名称中提取数字，默认为1
      const match = defaultGrade.match(/\d+/);
      const gradeNum = match && match.length > 0 ? parseInt(match[0]) : 1;
      selectedLevel = gradeNum; // 小学年级，1-6
    } else if (defaultGrade.startsWith('初中')) {
      selectedStage = 'junior';
      // 从年级名称中提取数字，默认为1
      const match = defaultGrade.match(/\d+/);
      const gradeNum = match && match.length > 0 ? parseInt(match[0]) : 1;
      selectedLevel = gradeNum + 6; // 初中年级，7-9
    } else if (defaultGrade.startsWith('高中')) {
      selectedStage = 'senior';
      // 从年级名称中提取数字，默认为1
      const match = defaultGrade.match(/\d+/);
      const gradeNum = match && match.length > 0 ? parseInt(match[0]) : 1;
      selectedLevel = gradeNum + 9; // 高中年级，10-12
    }
    
    // 根据学段筛选对应的年级列表
    let filteredGrades = this.getGradesByStage(selectedStage);
    
    // 将常量添加到页面数据中供WXML使用
    this.setData({
      selectedStage: selectedStage,
      selectedLevel: selectedLevel,
      grades: filteredGrades,
      // 添加常量到页面数据
      STATUS_MASTERED: STATUS_TYPES.MASTERED,
      STATUS_LEARNING: STATUS_TYPES.LEARNING,
      STATUS_WEAK: STATUS_TYPES.WEAK,
      STATUS_NOT_MASTERED: STATUS_TYPES.NOT_MASTERED,
      STATUS_NOT_STARTED: STATUS_TYPES.NOT_STARTED,
      DIFFICULTY_EASY: DIFFICULTY_LEVELS.EASY,
      DIFFICULTY_MEDIUM: DIFFICULTY_LEVELS.MEDIUM,
      DIFFICULTY_HARD: DIFFICULTY_LEVELS.HARD,
      DIFFICULTY_EXPERT: DIFFICULTY_LEVELS.EXPERT,
      GRADE_LEVEL_MAP: GRADE_LEVEL_MAP,
      LEVEL_GRADE_MAP: LEVEL_GRADE_MAP
    });
    
    // 加载知识图谱数据
    this.loadKnowledgeGraph();
    
    // 初始化容器尺寸
    this.initContainerSize();
    
    // 添加错误处理
    wx.onError((error) => {
      console.error('小程序发生错误:', error);
    });
  },

  // 根据学段获取年级列表
  getGradesByStage: function(stage) {
    let filteredGrades = [];
    
    // 确保使用完整的年级名称进行过滤
    switch(stage) {
      case 'primary':
        filteredGrades = [
          GRADE_LEVELS.ELEMENTARY_1,
          GRADE_LEVELS.ELEMENTARY_2,
          GRADE_LEVELS.ELEMENTARY_3,
          GRADE_LEVELS.ELEMENTARY_4,
          GRADE_LEVELS.ELEMENTARY_5,
          GRADE_LEVELS.ELEMENTARY_6
        ];
        break;
      case 'junior':
        filteredGrades = [
          GRADE_LEVELS.JUNIOR_1,
          GRADE_LEVELS.JUNIOR_2,
          GRADE_LEVELS.JUNIOR_3
        ];
        break;
      case 'senior':
        filteredGrades = [
          GRADE_LEVELS.SENIOR_1,
          GRADE_LEVELS.SENIOR_2,
          GRADE_LEVELS.SENIOR_3
        ];
        break;
      default:
        // 使用所有年级
        filteredGrades = Object.values(GRADE_LEVELS);
    }
    
    // 确保有年级可显示
    if (filteredGrades.length === 0) {
      filteredGrades = Object.values(GRADE_LEVELS);
    }
    
    return filteredGrades;
  },

  onShow: function() {
    // 显式设置导航栏标题，防止显示其他页面标题
    wx.setNavigationBarTitle({
      title: '数学知识图谱'
    });
    
    // 延时再次确保标题正确设置
    setTimeout(() => {
      wx.setNavigationBarTitle({
        title: '数学知识图谱'
      });
    }, 100);
    
    // 更新设备信息
    this.initDeviceInfo();
  },
  
  onResize: function() {
    // 屏幕旋转时重新初始化设备信息
    this.initDeviceInfo();
    // 重新获取容器尺寸
    this.initContainerSize();
    // 重新布局知识图谱
    this.layoutGraph();
  },
  
  // 初始化容器尺寸
  initContainerSize: function() {
    const query = wx.createSelectorQuery();
    query.select('.graph-container').boundingClientRect(rect => {
      if (rect) {
        this.containerInfo = {
          width: rect.width,
          height: rect.height,
          initialized: true
        };
      }
    }).exec();
  },
  
  // 初始化设备信息
  initDeviceInfo: function() {
    try {
      const deviceInfo = deviceUtils.getDeviceInfo();
      const safeArea = deviceInfo.safeAreaBottom || 0;
      
      this.setData({
        statusBarHeight: deviceInfo.statusBarHeight,
        navBarHeight: deviceInfo.navBarHeight,
        safeAreaBottom: deviceInfo.safeAreaBottom,
        screenHeight: deviceInfo.screenHeight,
        isIPad: deviceInfo.isIPad,
        isIphoneX: deviceInfo.isIPhoneX
      });
      
      // 将关键值附加到页面样式变量
      deviceUtils.setPageCSSVariables('.container', deviceInfo);
    } catch (e) {
      console.error('获取设备信息失败', e);
      
      // 使用默认值
      this.setData({
        statusBarHeight: 20,
        navBarHeight: 64,
        safeAreaBottom: 0,
        isIPad: false,
        isIphoneX: false
      });
    }
  },

  // 设置CSS变量
  setCSSVariables: function(variables) {
    try {
      wx.createSelectorQuery()
        .select('.container')
        .fields({ node: true, size: true }, function(res) {
          if (res && res.node) {
            const style = res.node.style;
            for (const [key, value] of Object.entries(variables)) {
              style.setProperty(key, value);
            }
          }
        })
        .exec();
    } catch (e) {
      console.error('设置CSS变量失败', e);
    }
  },

  // 返回上一页
  goBack: function() {
    navigator.navigateBack(1, {
      onFail: () => {
        navigator.reLaunch(navigator.PATHS.INDEX);
      }
    });
  },
  
  // 切换阶段（小学/初中/高中）
  onStageChange: function(e) {
    const stage = e.currentTarget.dataset.stage;
    if (stage === this.data.selectedStage) return;
    
    // 获取对应学段的年级列表
    let filteredGrades = this.getGradesByStage(stage);
    let defaultGrade = '';
    let defaultLevel = 1;
    
    // 选择适当的默认年级
    switch(stage) {
      case 'primary':
        defaultGrade = filteredGrades.length > 0 ? filteredGrades[0] : GRADE_LEVELS.ELEMENTARY_1;
        defaultLevel = 1; // 小学一年级
        break;
      case 'junior':
        defaultGrade = filteredGrades.length > 0 ? filteredGrades[0] : GRADE_LEVELS.JUNIOR_1;
        defaultLevel = 7; // 初中一年级
        break;
      case 'senior':
        defaultGrade = filteredGrades.length > 0 ? filteredGrades[0] : GRADE_LEVELS.SENIOR_1;
        defaultLevel = 10; // 高中一年级
        break;
    }
    
    // 更新数据，重新加载知识图谱
    this.setData({
      selectedStage: stage,
      selectedGrade: defaultGrade,
      selectedLevel: defaultLevel,
      grades: filteredGrades,
      activeStatus: null // 重置筛选状态
    }, () => {
      this.loadKnowledgeGraph();
      
      // 添加日志以便调试
      console.log('切换到学段:', stage);
      console.log('可用年级:', filteredGrades);
      console.log('默认选中年级:', defaultGrade);
      console.log('默认选中level:', defaultLevel);
    });
  },
  
  // 滚动到对应学段的年级
  scrollToGradeByStage: function(stage) {
    // 延时确保DOM已更新
    setTimeout(() => {
      const query = wx.createSelectorQuery();
      let selector = '.grade-item';
      
      // 查找当前选中的年级元素
      query.selectAll(selector).boundingClientRect().exec(res => {
        if (!res || !res[0] || !res[0].length) return;
        
        const grades = res[0];
        let targetIndex = 0;
        
        // 根据学段找到起始年级的位置
        if (stage === 'primary') {
          targetIndex = 0; // 小学年级的起始位置
        } else if (stage === 'junior') {
          // 找到第一个初中年级
          targetIndex = grades.findIndex(grade => 
            grade.dataset && grade.dataset.grade && grade.dataset.grade.includes('初中'));
        } else if (stage === 'senior') {
          // 找到第一个高中年级
          targetIndex = grades.findIndex(grade => 
            grade.dataset && grade.dataset.grade && grade.dataset.grade.includes('高中'));
        }
        
        if (targetIndex < 0) targetIndex = 0;
        
        // 创建选择器并获取滚动视图的引用
        wx.createSelectorQuery()
          .select('.grade-scroll')
          .node()
          .exec(nodeRes => {
            if (nodeRes && nodeRes[0] && nodeRes[0].node) {
              const scrollView = nodeRes[0].node;
              
              // 计算需要滚动的位置
              let scrollLeft = 0;
              for (let i = 0; i < targetIndex; i++) {
                if (grades[i]) {
                  scrollLeft += grades[i].width;
                }
              }
              
              // 执行滚动
              scrollView.scrollLeft = scrollLeft;
            }
          });
      });
    }, 100);
  },
  
  // 更新知识点统计
  updateStatistics: function(nodes) {
    if (!nodes || nodes.length === 0) return;
    
    let masteredCount = 0;
    let learningCount = 0;
    let weakCount = 0;
    let notMasteredCount = 0;
    let notStartedCount = 0;
    
    nodes.forEach(node => {
      switch(node.status) {
        case STATUS_TYPES.MASTERED:
          masteredCount++;
          break;
        case STATUS_TYPES.LEARNING:
          learningCount++;
          break;
        case STATUS_TYPES.WEAK:
          weakCount++;
          break;
        case STATUS_TYPES.NOT_MASTERED:
          notMasteredCount++;
          break;
        case STATUS_TYPES.NOT_STARTED:
          notStartedCount++;
          break;
      }
    });
    
    this.setData({
      masteredCount,
      learningCount,
      weakCount,
      notMasteredCount,
      notStartedCount
    });
  },
  
  // 从云数据库获取知识图谱数据
  async fetchKnowledgeGraphFromCloud() {
    try {
      // 使用level字段查询
      const level = this.data.selectedLevel;
      console.log('调用knowledge-graph-query云函数获取知识图谱数据，level:', level);
      
      // 调用knowledge-graph-query云函数获取知识点数据
      const nodesResult = await wx.cloud.callFunction({
        name: 'knowledge-graph-query',
        data: {
          action: 'getKnowledgeByGrade',
          params: {
            gradeLevel: level,
            subject: 'mathematics'  // 使用数据库中的实际学科标识
          }
        }
      });
      
      console.log('知识点查询结果:', nodesResult);
      
      // 检查结果格式 - 兼容多种返回格式
      let nodeData = [];
      if (nodesResult && nodesResult.result) {
        if (nodesResult.result.data && Array.isArray(nodesResult.result.data)) {
          // 标准格式: { result: { data: [...] } }
          nodeData = nodesResult.result.data;
        } else if (Array.isArray(nodesResult.result)) {
          // 直接数组格式: { result: [...] }
          nodeData = nodesResult.result;
        } else if (nodesResult.result.success && nodesResult.result.data) {
          // 带success标志: { result: { success: true, data: [...] } }
          nodeData = nodesResult.result.data;
        }
      }
      
      console.log('解析后的节点数据:', nodeData);
      
      if (!nodeData || nodeData.length === 0) {
        console.warn('未获取到知识点数据');
        return { nodes: [], links: [] };
      }
      
      const nodes = nodeData.map((node, index) => {
        // 根据数据库实际字段结构进行映射
        const mappedNode = {
          // 基础标识信息
          id: node.node_code || node.id || `node_${index}`,
          name: node.node_name || node.name || `知识点${index + 1}`,
          
          // 年级和学期信息
          level: node.grade_level || level,
          semester: node.semester || 'first',
          
          // 学科分类
          subject: node.subject || 'mathematics',
          domain_id: node.domain_id,
          textbook_edition_id: node.textbook_edition_id,
          knowledge_applicability: node.knowledge_applicability || 'universal',
          target_academic_tracks: node.target_academic_tracks || [],
          
          // 教材定位
          chapter: node.chapter_number !== undefined ? node.chapter_number : 1,
          section: node.section_number || '1',
          chapterTitle: node.chapter_title || '',
          sectionTitle: node.section_title || '',
          unitName: node.unit_name || '',
          
          // 学习属性
          difficulty: this.mapDifficultyLevel(node.difficulty || 'basic'),
          estimatedTime: node.estimated_time_minutes || 45,
          cognitiveComplexity: node.cognitive_complexity || 2,
          importance: node.importance_level || 3,
          examFrequency: node.exam_frequency || 'medium',
          
          // 文理科差异化属性
          liberalArtsDifficulty: this.mapDifficultyLevel(node.liberal_arts_difficulty),
          scienceDifficulty: this.mapDifficultyLevel(node.science_difficulty),
          liberalArtsEmphasis: node.liberal_arts_emphasis_points || [],
          scienceEmphasis: node.science_emphasis_points || [],
          
          // 知识点类型和状态
          knowledgeType: node.knowledge_type || 'concept',
          memoryTips: node.memory_tips || '',
          commonMisconceptions: node.common_misconceptions || [],
          
          // 教学大纲和考试相关
          curriculumStandard: node.curriculum_standard || 'national_2022',
          examImportance: node.exam_importance_level || 3,
          gaoKaoFrequency: node.gaokao_frequency || 'medium',
          zhongKaoApplicability: node.zhongkao_applicability !== false,
          
          // 核心能力标签
          requiresMemorization: node.requires_memorization || false,
          requiresUnderstanding: node.requires_understanding !== false,
          requiresApplication: node.requires_application || false,
          requiresAnalysis: node.requires_analysis || false,
          requiresSynthesis: node.requires_synthesis || false,
          requiresEvaluation: node.requires_evaluation || false,
          
          // 能力要求
          requiresBasicConcept: node.requires_basic_concept !== false,
          requiresCalculationSkill: node.requires_calculation_skill || false,
          requiresApplicationAbility: node.requires_application_ability || false,
          
          // 文理科特殊能力要求
          requiresAbstractThinking: node.requires_abstract_thinking || false,
          requiresPracticalApplication: node.requires_practical_application || false,
          requiresLogicalProof: node.requires_logical_proof || false,
          
          // 版本控制
          version: node.version || 1,
          isActive: node.is_active !== false,
          
          // 前端显示状态（可能来自学生掌握状态表）
          status: node.mastery_status || node.status || 'not-started',
          description: node.description || node.unit_name || node.section_title || '',
          
          // 生成合理的初始位置
          x: Math.random() * 500 + 150,
          y: Math.random() * 300 + 100
        }; 
        return mappedNode;
      });
      
      // 获取知识点关系数据
      let links = [];
      if (nodes.length > 0) {
        try {
          const relationshipsResult = await wx.cloud.callFunction({
            name: 'knowledge-graph-query',
            data: {
              action: 'getKnowledgeRelationships',
              params: {
                nodeId: nodes[0].id // 获取第一个节点的关系作为示例
              }
            }
          });
          
          if (relationshipsResult && relationshipsResult.result && relationshipsResult.result.data) {
            links = relationshipsResult.result.data.map(rel => ({
              source: rel.source_node_id,
              target: rel.target_node_id,
              type: rel.relationship_type,
              strength: rel.strength || 0.5
            }));
          }
        } catch (relError) {
          console.warn('获取关系数据失败:', relError);
        }
      }
      
      console.log(`成功获取知识图谱数据: ${nodes.length}个节点, ${links.length}个连接`);
      
      return { nodes, links };
    } catch (error) {
      console.error('获取知识图谱数据失败:', error);
      throw error;
    }
  },
  
  // 加载知识图谱
  loadKnowledgeGraph: async function() {
    this.setData({
      loading: true,
      isLoading: true
    });
    
    try {
       
      
      // 尝试从云数据库获取数据
      const { nodes, links } = await this.fetchKnowledgeGraphFromCloud();
      
      // 隐藏加载提示
      wx.hideLoading();
      
      if (!nodes || nodes.length === 0) {
        // 无数据的情况，显示空状态
        this.setData({
          nodes: [],
          links: [],
          originalNodes: [],
          originalLinks: [],
          filteredNodes: [],
          loading: false,
          isLoading: false,
          showEmpty: true,
          masteredCount: 0,
          learningCount: 0,
          weakCount: 0,
          notMasteredCount: 0,
          notStartedCount: 0
        });
        return;
      }
      
      // 统计各状态知识点数量
      let masteredCount = 0;
      let learningCount = 0;
      let weakCount = 0;
      let notMasteredCount = 0;
      let notStartedCount = 0;
      
      // 确保所有节点都有有效的状态
      const validatedNodes = nodes.map(item => {
        // 如果状态无效，设置为未开始状态
        if (!item.status || typeof item.status !== 'string' || 
            ![STATUS_TYPES.MASTERED, STATUS_TYPES.LEARNING, 
              STATUS_TYPES.WEAK, STATUS_TYPES.NOT_MASTERED, 
              STATUS_TYPES.NOT_STARTED].includes(item.status)) {
          item.status = STATUS_TYPES.NOT_STARTED;
        }
        return item;
      });
      
      // 重新统计
      validatedNodes.forEach(item => {
        switch(item.status) {
          case STATUS_TYPES.MASTERED:
            masteredCount++;
            break;
          case STATUS_TYPES.LEARNING:
            learningCount++;
            break;
          case STATUS_TYPES.WEAK:
            weakCount++;
            break;
          case STATUS_TYPES.NOT_MASTERED:
            notMasteredCount++;
            break;
          case STATUS_TYPES.NOT_STARTED:
            notStartedCount++;
            break;
        }
      });
      
      // 处理数据，转换为视图需要的格式
      this.setData({
        originalNodes: validatedNodes,
        originalLinks: links, // 更新连接线数据
        filteredNodes: validatedNodes,
        nodes: validatedNodes,
        links: links,
        loading: false,
        isLoading: false,
        showEmpty: validatedNodes.length === 0,
        masteredCount,
        learningCount,
        weakCount,
        notMasteredCount,
        notStartedCount
      }, () => {
        // 数据加载完成后，应用筛选并生成章节化数据
        this.applyFilters();
      });
    } catch (e) {
      console.error('加载知识图谱数据失败', e);
      
      // 隐藏加载提示
      wx.hideLoading();
      
      // 显示错误提示
      wx.showModal({
        title: '加载失败',
        content: '无法获取数据，请检查网络连接后重试',
        showCancel: false,
        success: () => {
          this.setData({
            loading: false,
            isLoading: false,
            showEmpty: true
          });
        }
      });
    }
  },
  
  // 布局知识图谱（力导向布局简化版）
  layoutGraph: function() {
    try {
      if (!this.containerInfo.initialized) {
        // 容器尺寸未初始化，先初始化
        this.initContainerSize();
        return;
      }
      
      // 防止无效布局
      if (!this.data.nodes || this.data.nodes.length === 0) return;
      
      const { width, height } = this.containerInfo;
      
      // 对节点进行简单布局（例如圆形布局）
      const nodeCount = this.data.nodes.length;
      const centerX = width / 2;
      const centerY = height / 2;
      const radius = Math.min(width, height) * 0.4;
      
      let updatedNodes = this.data.nodes.map((node, index) => {
        // 计算节点在圆上的位置
        const angle = (index / nodeCount) * 2 * Math.PI;
        return {
          ...node,
          x: centerX + radius * Math.cos(angle),
          y: centerY + radius * Math.sin(angle)
        };
      });
      
      // 更新节点位置
      this.setData({
        nodes: updatedNodes
      });
    } catch (e) {
      console.error('布局图谱失败', e);
    }
  },

  // 计算节点位置（改进的力导向布局）
  calculateNodePositions: function(nodes, width, height) {
    // 计算节点半径（rpx 转 px）
    const nodeRadiusMap = {
      1: 45, // 一级节点半径
      2: 40, // 二级节点半径
      3: 35  // 三级节点半径
    };
    
    // 分组节点
    const levelNodes = {};
    for (let level = 1; level <= 3; level++) {
      levelNodes[level] = nodes.filter(node => node.level === level);
    }
    
    // 中心点
    const centerX = width / 2;
    const centerY = height / 2;
    
    // 不同级别节点分布的半径，增加间距，避免重叠
    const levelRadiusMap = {
      1: Math.min(width, height) * 0.2,  // 一级节点分布半径
      2: Math.min(width, height) * 0.4,  // 二级节点分布半径
      3: Math.min(width, height) * 0.6   // 三级节点分布半径
    };
    
    // 为每层设置节点位置
    Object.keys(levelNodes).forEach(level => {
      const currentLevelNodes = levelNodes[level];
      const radius = levelRadiusMap[level];
      const nodeCount = currentLevelNodes.length;
      
      // 不同层级的起始角度错开，避免重叠
      const startAngle = (level % 2 === 0) ? 0 : Math.PI / nodeCount;
      
      // 计算节点位置
      currentLevelNodes.forEach((node, index) => {
        // 在圆上均匀分布节点
        const angle = startAngle + (2 * Math.PI * index) / Math.max(nodeCount, 1);
        
        // 计算节点坐标
        const x = centerX + radius * Math.cos(angle);
        const y = centerY + radius * Math.sin(angle);
        
        // 添加小随机偏移，避免完全对称导致连线重叠
        const offsetScale = 0.08; // 随机偏移比例
        const randomOffsetX = radius * offsetScale * (Math.random() - 0.5);
        const randomOffsetY = radius * offsetScale * (Math.random() - 0.5);
        
        // 设置节点位置
        node.x = x + randomOffsetX;
        node.y = y + randomOffsetY;
        node.radius = nodeRadiusMap[level];
      });
    });
    
    // 节点防碰撞优化
    this.preventNodeOverlap(nodes, nodeRadiusMap, width, height);
  },
  
  // 防止节点重叠
  preventNodeOverlap: function(nodes, nodeRadiusMap, width, height) {
    const iterations = 30; // 迭代次数
    const k = 40; // 力系数
    
    for (let iter = 0; iter < iterations; iter++) {
      // 每次迭代计算节点间作用力并移动节点
      for (let i = 0; i < nodes.length; i++) {
        const node1 = nodes[i];
        let dx = 0, dy = 0;
        
        // 当前节点与其他节点间防碰撞力
        for (let j = 0; j < nodes.length; j++) {
          if (i === j) continue;
          
          const node2 = nodes[j];
          
          // 计算节点间距离
          const distX = node1.x - node2.x;
          const distY = node1.y - node2.y;
          const dist = Math.sqrt(distX * distX + distY * distY);
          
          // 计算两节点所需最小距离（两节点半径和额外间距）
          const minDist = node1.radius + node2.radius + 15;
          
          // 如果距离小于最小安全距离，产生斥力
          if (dist < minDist) {
            const force = k * (minDist - dist) / dist;
            dx += distX * force / dist;
            dy += distY * force / dist;
          }
        }
        
        // 边界限制，防止节点移出可视区域
        const padding = node1.radius + 10;
        const borderForce = 0.1;
        
        if (node1.x < padding) dx += borderForce * (padding - node1.x);
        if (node1.x > width - padding) dx += borderForce * (width - padding - node1.x);
        if (node1.y < padding) dy += borderForce * (padding - node1.y);
        if (node1.y > height - padding) dy += borderForce * (height - padding - node1.y);
        
        // 根据计算的力更新节点位置
        node1.x += dx * 0.1;
        node1.y += dy * 0.1;
      }
    }
  },
  
  // 切换年级
  onGradeChange: function(e) {
    const grade = e.currentTarget.dataset.grade;
    const level = GRADE_LEVEL_MAP[grade] || 7; // 默认初中一年级
    
    this.setData({
      selectedGrade: grade,
      selectedLevel: level
    }, () => {
      // 重新加载对应年级的知识图谱
      this.loadKnowledgeGraph();
      
      // 更新阶段选择
      let selectedStage = this.data.selectedStage;
      if (grade.startsWith('小学')) {
        selectedStage = 'primary';
      } else if (grade.startsWith('初中')) {
        selectedStage = 'junior';
      } else if (grade.startsWith('高中')) {
        selectedStage = 'senior';
      }
      
      if (selectedStage !== this.data.selectedStage) {
        this.setData({
          selectedStage: selectedStage
        });
      }
    });
  },

  /**
   * 映射数据库中的难度级别到前端显示格式
   * 数据库字段：difficulty_level_enum (basic/intermediate/advanced/expert/master)
   */
  mapDifficultyLevel: function(difficulty) {
    if (!difficulty) return 'medium';
    
    const difficultyMap = {
      'basic': 'easy',
      'intermediate': 'medium', 
      'advanced': 'hard',
      'expert': 'expert',
      'master': 'expert'  // master级别映射为expert显示
    };
    
    return difficultyMap[difficulty.toLowerCase()] || difficulty;
  },

  // 根据关系类型字符串映射到关系类型常量
  // 数据库定义的关系类型: prerequisite, successor, related, extension, parallel, contains, example_of, application_of, cross_subject
  mapRelationshipType: function(typeString) {
    // 标准化类型字符串，去除空格和转为小写
    const normalizedType = (typeString || '').toLowerCase().trim();
    
    // 数据库关系类型映射表（严格按照数据库定义）
    // 数据库定义的关系类型: prerequisite, successor, related, extension, parallel, contains, example_of, application_of, cross_subject
    const typeMapping = {
      // 数据库标准关系类型 - 直接映射
      'prerequisite': 'prerequisite',      // 前置知识关系
      'successor': 'successor',            // 后续知识关系  
      'related': 'related',                // 相关知识关系
      'extension': 'extension',            // 扩展知识关系
      'parallel': 'parallel',              // 并行知识关系
      'contains': 'contains',              // 包含关系
      'example_of': 'example_of',          // 示例关系
      'application_of': 'application_of',  // 应用关系
      'cross_subject': 'cross_subject'     // 跨学科关联
    };
    
    // 返回映射的类型，如果没有匹配则返回默认类型（related - 相关关系）
    return typeMapping[normalizedType] || 'related';
  },

  // 获取关系类型的中文显示文本（基于数据库定义）
  getRelationTypeText: function(relationType) {
    const relationTexts = {
      'prerequisite': '前置知识',        // 前置知识关系
      'successor': '后续知识',          // 后续知识关系
      'related': '相关知识',            // 相关知识关系
      'extension': '扩展知识',          // 扩展知识关系
      'parallel': '并行知识',           // 并行知识关系
      'contains': '包含知识',           // 包含关系
      'example_of': '示例知识',         // 示例关系
      'application_of': '应用知识',     // 应用关系
      'cross_subject': '跨学科知识'     // 跨学科关联
    };
    
    return relationTexts[relationType] || '相关知识';
  },

  // 显示节点详情
  async showNodeDetail(node) {
    if (!node) {
      console.error('showNodeDetail: 节点数据为空');
      wx.showToast({
        title: '知识点数据错误',
        icon: 'none'
      });
      return;
    }
    
    console.log('显示节点详情:', node);
    
    try {
      // 确保页面标题保持正确
      wx.setNavigationBarTitle({
        title: '数学知识图谱'
      });
      
      // 禁用主页面滚动
      wx.pageScrollTo({
        scrollTop: 0,
        duration: 0
      });
      
      // 构建完整的selectedNode数据
      const selectedNodeData = {
        id: node.id,
        name: node.name || '未知知识点',
        description: node.description || node.unitName || node.sectionTitle || '暂无描述',
        status: node.status || 'not-started',
        difficulty: node.difficulty || 'medium',
        level: node.level,
        semester: node.semester,
        subject: node.subject || 'mathematics',
        chapter: node.chapter,
        section: node.section,
        chapterTitle: node.chapterTitle,
        sectionTitle: node.sectionTitle,
        unitName: node.unitName,
        importance: node.importance || 3,
        examFrequency: node.examFrequency || 'medium',
        // 状态和难度文本
        statusText: this.getStatusText(node.status),
        statusClass: this.getStatusClass(node.status),
        difficultyText: this.getDifficultyText(node.difficulty),
        difficultyClass: this.getDifficultyClass(node.difficulty),
        // 初始化空的关系数据，等待异步加载
        relations: {
          prerequisites: [],
          progressions: [],
          correlations: [],
          applications: [],
          others: []
        }
      };
      
      console.log('设置selectedNode数据:', selectedNodeData);
      
      // 设置基本信息，先显示详情面板
      this.setData({
        selectedNode: selectedNodeData,
        showDetail: true
      }, () => {
        console.log('详情面板已显示，selectedNode:', this.data.selectedNode);
      });
      
      // 显示加载中
      wx.showLoading({
        title: '加载详情数据...',
        mask: false
      });
      
      // 从云数据库加载详细内容和关系数据
      try {
        // 并行加载详细内容和关系数据
        const [contentResult, relationResult] = await Promise.allSettled([
          this.loadNodeContentFromCloud(node),
          this.loadNodeRelationshipsFromCloud(node)
        ]);
        
        // 处理内容数据结果
        if (contentResult.status === 'fulfilled' && contentResult.value) {
          console.log('内容数据加载成功:', contentResult.value);
          // 更新描述信息
          this.setData({
            'selectedNode.description': contentResult.value.description || 
                                        contentResult.value.detailed_explanation || 
                                        selectedNodeData.description
          });
        } else if (contentResult.status === 'rejected') {
          console.error('内容数据加载失败:', contentResult.reason);
        }
        
        // 处理关系数据结果
        if (relationResult.status === 'rejected') {
          console.error('关系数据加载失败:', relationResult.reason);
          wx.showToast({
            title: '关系数据加载失败',
            icon: 'none',
            duration: 2000
          });
        }
        
      } catch (error) {
        console.error('加载详情数据出错:', error);
        wx.showToast({
          title: '数据加载出错',
          icon: 'none'
        });
      }
      
      // 加载完成
      wx.hideLoading();
      
      // 再次确保标题正确
      wx.setNavigationBarTitle({
        title: '数学知识图谱'
      });
      
    } catch (error) {
      console.error('显示节点详情失败:', error);
      wx.hideLoading();
      wx.showToast({
        title: '显示详情失败',
        icon: 'none'
      });
    }
  },

  // 从云数据库加载节点详细内容
  async loadNodeContentFromCloud(node) {
    try {
      console.log('开始加载节点详细内容:', node.id);
      
      const contentResult = await wx.cloud.callFunction({
        name: 'knowledge-graph-query',
        data: {
          action: 'getKnowledgeContent',
          params: {
            nodeId: node.id
          }
        }
      });
      
      console.log('节点内容查询结果:', contentResult);
      
      if (contentResult && contentResult.result && contentResult.result.success && contentResult.result.data) {
        return contentResult.result.data;
      } else {
        console.log('未找到节点详细内容');
        return null;
      }
    } catch (error) {
      console.error('加载节点详细内容失败:', error);
      throw error;
    }
  },
  
  // 从云数据库加载节点关系数据
  async loadNodeRelationshipsFromCloud(node) {
    try {
      console.log('开始加载节点关系数据:', node.id);
      
      // 调用knowledge-graph-query云函数获取关系数据
      const relationshipsResult = await wx.cloud.callFunction({
        name: 'knowledge-graph-query',
        data: {
          action: 'getKnowledgeRelationships',
          params: {
            nodeId: node.id,
            subject: 'mathematics'  // 确保使用正确的学科标识
          }
        }
      });
      
      console.log('关系数据查询结果:', relationshipsResult);
      
      // 检查云函数调用是否成功
      if (!relationshipsResult || !relationshipsResult.result) {
        console.error('云函数调用失败:', relationshipsResult);
        throw new Error('云函数调用失败');
      }
      
      if (!relationshipsResult.result.success) {
        console.error('云函数返回错误:', relationshipsResult.result.error);
        throw new Error(relationshipsResult.result.error || '查询失败');
      }
      
      // 检查结果格式并转换为兼容格式
      let relationshipsData = { links: [] };
      if (relationshipsResult.result.data && Array.isArray(relationshipsResult.result.data)) {
        relationshipsData.links = relationshipsResult.result.data.map(rel => ({
          source: rel.source_node_id,
          target: rel.target_node_id,
          type: rel.relationship_type,
          strength: rel.strength || 0.8,
          confidence: rel.confidence || 0.8,
          // 文理科关系强度
          liberalArtsStrength: rel.liberal_arts_strength,
          scienceStrength: rel.science_strength,
          // 学习路径信息
          learningGapDays: rel.learning_gap_days,
          prerequisiteCoverage: rel.prerequisite_coverage,
          difficultyIncrease: rel.difficulty_increase || 0.0,
          // 跨年级关联
          crossGradeType: rel.cross_grade_type,
          gradeSpan: rel.grade_span,
          trackSpecificNotes: rel.track_specific_notes || {},
          // 节点信息
          sourceName: rel.source_name,
          sourceCode: rel.source_code,
          sourceGradeLevel: rel.source_grade_level,
          targetName: rel.target_name,
          targetCode: rel.target_code,
          targetGradeLevel: rel.target_grade_level
        }));
      }
      
      console.log('转换后的关系数据:', relationshipsData.links.length, '条');
      
      if (!relationshipsData.links || relationshipsData.links.length === 0) {
        console.log('未找到节点关系数据');
        // 设置空的关系数据
        this.setData({
          'selectedNode.relations': {
            prerequisites: [],
            progressions: [],
            correlations: [],
            applications: [],
            others: []
          }
        });
        return false;
      }
      
      console.log('从云数据库获取到关系数据:', relationshipsData.links.length);
      
      // 处理关系数据（基于数据库定义的关系类型）
      let prerequisites = [];
      let progressions = [];
      let correlations = [];
      let applications = [];
      let others = [];
      
      // 记录需要从云端获取的节点ID
      const missingNodeIds = new Set();
      
      // 首先收集所有关联节点ID
      relationshipsData.links.forEach(connection => {
        const relatedNodeId = connection.source === node.id ? connection.target : connection.source;
        const relatedNode = this.findNodeById(relatedNodeId);
        if (!relatedNode) {
          missingNodeIds.add(relatedNodeId);
        }
      });
      
      // 如果有节点不在当前视图中，从云端批量获取它们
      let externalNodes = {};
      if (missingNodeIds.size > 0) {
        console.log('需要从云端获取外部节点:', Array.from(missingNodeIds));
        
        try {
          // 使用knowledge-graph-query云函数批量获取外部节点信息
          const batchResult = await wx.cloud.callFunction({
            name: 'knowledge-graph-query',
            data: {
              action: 'getKnowledgeNodes',
              params: {
                nodeIds: Array.from(missingNodeIds),
                subject: 'mathematics',  // 确保使用正确的学科标识
                limit: 100
              }
            }
          });
          
          console.log('批量查询外部节点结果:', batchResult);
          
          if (batchResult && batchResult.result && batchResult.result.success && batchResult.result.data) {
            // 转换为以ID为key的对象格式，确保与数据库字段匹配
            batchResult.result.data.forEach(node => {
              const nodeKey = node.node_code || node.id;
              externalNodes[nodeKey] = {
                id: node.node_code || node.id,
                name: node.node_name || node.name,
                status: node.mastery_status || node.status || 'not-started',
                difficulty: this.mapDifficultyLevel(node.difficulty),
                level: node.grade_level,
                semester: node.semester,
                subject: node.subject,
                chapter: node.chapter_number,
                section: node.section_number,
                chapterTitle: node.chapter_title,
                sectionTitle: node.section_title,
                unitName: node.unit_name,
                knowledgeType: node.knowledge_type,
                description: node.description || node.unit_name || node.section_title || '',
                importance: node.importance_level || 3,
                examFrequency: node.exam_frequency || 'medium'
              };
            });
          }
          
          console.log('成功获取外部节点:', Object.keys(externalNodes).length);
        } catch (error) {
          console.error('获取外部节点失败:', error);
        }
      }
      
      // 查找节点函数，支持当前视图和外部节点
      const findNodeWithFallback = (nodeId) => {
        // 首先在当前视图中查找
        const localNode = this.findNodeById(nodeId);
        if (localNode) return localNode;
        
        // 如果当前视图中没有，尝试从外部节点中获取
        return externalNodes[nodeId] || null;
      };
      
      // 遍历关系数据
      for (const connection of relationshipsData.links) {
        // 获取关联节点ID (与当前节点相连的另一端)
        const relatedNodeId = connection.source === node.id ? connection.target : connection.source;
        
        // 获取关联节点数据 (使用增强版查找函数)
        const relatedNode = findNodeWithFallback(relatedNodeId);
        
        if (!relatedNode) {
          console.log('未找到关联节点:', relatedNodeId);
          continue; // 跳过找不到的节点
        }
        
        // 确保节点ID保持一致，优先使用标准ID格式
        let nodeId = relatedNodeId;
        // 如果关联节点有标准ID，使用标准ID
        if (relatedNode.id && relatedNode.id !== relatedNodeId) {
          console.log('使用标准ID替换云数据库ID:', relatedNodeId, '->', relatedNode.id);
          nodeId = relatedNode.id;
        }
        
        // 处理云数据库ID格式
        if (/^[0-9a-f]{24,32}$/.test(nodeId) && relatedNode.standardId) {
          console.log('将云数据库ID转换为标准ID:', nodeId, '->', relatedNode.standardId);
          nodeId = relatedNode.standardId;
        }
        
        // 准备关联节点数据
        const relatedNodeData = {
          name: relatedNode.name,
          id: nodeId, // 使用标准化后的ID
          status: relatedNode.status || 'not-started', // 确保有状态
          difficulty: relatedNode.difficulty || 'medium', // 确保有难度
          direction: connection.source === node.id ? 'outgoing' : 'incoming',
          strength: connection.strength || 1,
          // 添加额外信息
          isExternalNode: !this.findNodeById(nodeId), // 是否为外部节点
          grade: relatedNode.grade, // 所属年级
          gradeText: relatedNode.grade || this.getGradeFromNodeId(nodeId).grade, // 显示的年级文本
          virtual: !!relatedNode.virtual // 是否是虚拟节点
        };
        
        // 如果未知年级，尝试通过级别推断
        if (!relatedNodeData.gradeText && relatedNode.level) {
          relatedNodeData.gradeText = this.getGradeTextByLevel(relatedNode.level);
        }
        
        // 如果仍是未知年级，尝试通过名称推断
        if (!relatedNodeData.gradeText || relatedNodeData.gradeText === '未知年级') {
          if (relatedNode.name.includes('数轴') || relatedNode.name.includes('有理数')) {
            relatedNodeData.gradeText = '小学六年级';
          } else if (relatedNode.name.includes('二次根式')) {
            relatedNodeData.gradeText = '初中二年级';
          } else if (relatedNode.name.includes('分数乘整数') || relatedNode.name === '分数乘整数') {
            relatedNodeData.gradeText = '小学五年级';
          }
        }
        
        // 从云数据库获取的类型字符串映射到前端常量
        const relationshipType = this.mapRelationshipType(connection.type);
        
        // 根据数据库定义的关系类型分类
        switch(relationshipType) {
          case 'prerequisite':
            // 前置知识关系
            if (connection.target === node.id) {
              prerequisites.push(relatedNodeData);
            } else {
              progressions.push(relatedNodeData);
            }
            break;
          case 'successor':
            // 后续知识关系
            if (connection.source === node.id) {
              progressions.push(relatedNodeData);
            } else {
              prerequisites.push(relatedNodeData);
            }
            break;
          case 'related':
            // 相关知识关系
            correlations.push(relatedNodeData);
            break;
          case 'extension':
            // 扩展知识关系
            progressions.push(relatedNodeData);
            break;
          case 'parallel':
            // 并行知识关系
            correlations.push(relatedNodeData);
            break;
          case 'contains':
            // 包含关系
            if (connection.source === node.id) {
              progressions.push(relatedNodeData);
            } else {
              prerequisites.push(relatedNodeData);
            }
            break;
          case 'example_of':
          case 'application_of':
            // 示例关系和应用关系
            applications.push(relatedNodeData);
            break;
          case 'cross_subject':
            // 跨学科关联
            correlations.push(relatedNodeData);
            break;
          // 其他未定义的关系类型
          default:
            others.push({
              ...relatedNodeData,
              relationType: relationshipType,
              relationTypeText: this.getRelationTypeText(relationshipType)
            });
            break;
        }
      }
      
      // 构建关系数据
      const relatedPoints = {
        prerequisites,
        progressions,
        correlations,
        applications,
        others
      };
      
      // 按关联强度排序
      this.sortRelationsByStrength(relatedPoints);
      
      // 打印统计信息
      console.log('关系统计:', {
        prerequisites: prerequisites.length,
        progressions: progressions.length,
        correlations: correlations.length,
        applications: applications.length,
        others: others.length
      });
      
      // 更新节点详情数据
      this.setData({
        'selectedNode.relations': relatedPoints
      });
      
      return true;
    } catch (error) {
      console.error('加载节点关系失败:', error);
      throw error;
    }
  },
  
  // 从节点ID获取年级信息
  getGradeFromNodeId: function(nodeId) {
    if (!nodeId || typeof nodeId !== 'string' || nodeId.length < 2) return { grade: '', level: 0 };
    
    // 详细记录日志
    console.log('尝试从节点ID获取年级信息:', nodeId);
    
    const prefix = nodeId.substring(0, 2);
    const gradeMap = {
      'e1': { grade: '小学一年级', level: 1 },
      'e2': { grade: '小学二年级', level: 2 },
      'e3': { grade: '小学三年级', level: 3 },
      'e4': { grade: '小学四年级', level: 4 },
      'e5': { grade: '小学五年级', level: 5 },
      'e6': { grade: '小学六年级', level: 6 },
      'j1': { grade: '初中一年级', level: 7 },
      'j2': { grade: '初中二年级', level: 8 },
      'j3': { grade: '初中三年级', level: 9 },
      's1': { grade: '高中一年级', level: 10 },
      's2': { grade: '高中二年级', level: 11 },
      's3': { grade: '高中三年级', level: 12 }
    };
    
    const result = gradeMap[prefix] || { grade: '', level: 0 };
    console.log('从ID推断年级结果:', prefix, result);
    return result;
  },
  
  // 根据数字级别获取年级文本
  getGradeTextByLevel: function(level) {
    if (!level || typeof level !== 'number') return '未知年级';
    
    return LEVEL_GRADE_MAP[level] || '未知年级';
  },
  
  // 获取知识点状态文本
  getStatusText: function(status) {
    const statusTexts = {
      [STATUS_TYPES.MASTERED]: '已掌握',
      [STATUS_TYPES.LEARNING]: '学习中',
      [STATUS_TYPES.WEAK]: '薄弱',
      [STATUS_TYPES.NOT_MASTERED]: '未掌握',
      [STATUS_TYPES.NOT_STARTED]: '未开始'
    };
    return statusTexts[status] || '未开始';
  },
  
  // 获取知识点状态样式类
  getStatusClass: function(status) {
    const statusClasses = {
      [STATUS_TYPES.MASTERED]: 'status-mastered',
      [STATUS_TYPES.LEARNING]: 'status-learning',
      [STATUS_TYPES.WEAK]: 'status-weak',
      [STATUS_TYPES.NOT_MASTERED]: 'status-not-mastered',
      [STATUS_TYPES.NOT_STARTED]: 'status-not-started'
    };
    return statusClasses[status] || '';
  },
  
  // 获取难度文本
  getDifficultyText: function(difficulty) {
    const difficultyTexts = {
      [DIFFICULTY_LEVELS.EASY]: '简单',
      [DIFFICULTY_LEVELS.MEDIUM]: '中等',
      [DIFFICULTY_LEVELS.HARD]: '困难',
      [DIFFICULTY_LEVELS.EXPERT]: '挑战'
    };
    return difficultyTexts[difficulty] || '中等';
  },
  
  // 获取难度样式类
  getDifficultyClass: function(difficulty) {
    const difficultyClasses = {
      [DIFFICULTY_LEVELS.EASY]: 'difficulty-easy',
      [DIFFICULTY_LEVELS.MEDIUM]: 'difficulty-medium',
      [DIFFICULTY_LEVELS.HARD]: 'difficulty-hard',
      [DIFFICULTY_LEVELS.EXPERT]: 'difficulty-expert'
    };
    return difficultyClasses[difficulty] || 'difficulty-medium';
  },
  
  // 点击相关知识点
  onRelatedPointClick: function(e) {
    const pointId = e.currentTarget.dataset.pointId;
    const pointName = e.currentTarget.dataset.pointName;
    
    console.log('点击相关知识点', pointId, pointName);
    
    // 如果没有ID，直接返回
    if (!pointId) {
      console.error('知识点ID缺失，无法查询');
      wx.showToast({
        title: '知识点信息不完整',
        icon: 'none'
      });
      return;
    }
    
    // 优先使用ID查找当前年级中的节点
    const relatedNode = this.findNodeById(pointId);
    if (relatedNode) {
      console.log('在当前年级找到节点:', relatedNode);
      this.showNodeDetail(relatedNode);
      return;
    }
    
    // 当前年级未找到，显示加载提示
    wx.showLoading({
      title: '查询知识点...',
      mask: true
    });
    
    // 检查是否是云数据库ID格式
    const isCloudId = /^[0-9a-f]{24,32}$/.test(pointId);
    
    // 调用云函数查询知识点信息
    wx.cloud.callFunction({
      name: 'database-query',
      data: {
        type: 'knowledge-graph-node-detail',
        id: pointId
      }
    })
    .then(res => {
      wx.hideLoading();
      console.log('知识点查询结果:', res);
      
      if (res.result && res.result.success && res.result.data) {
        const nodeInfo = res.result.data;
        console.log('获取到知识点信息:', nodeInfo);
        
        // 构造一个标准节点对象，确保关键字段存在
        const standardizedNodeInfo = {
          id: pointId,
          name: pointName || `知识点${pointId}`,
          // 如果返回的nodeInfo是node和links的格式，则取node字段
          ...(nodeInfo.node || nodeInfo)
        };
        
        // 获取年级信息
        let gradeText = '未知年级';
        let level = 7; // 默认初中一年级
        
        // 1. 直接从nodeInfo中获取年级
        if (standardizedNodeInfo.grade) {
          gradeText = standardizedNodeInfo.grade;
          level = standardizedNodeInfo.level || GRADE_LEVEL_MAP[standardizedNodeInfo.grade] || 7;
          console.log('从节点数据获取年级:', gradeText, level);
        }
        // 2. 从level获取年级文本
        else if (standardizedNodeInfo.level && typeof standardizedNodeInfo.level === 'number') {
          level = standardizedNodeInfo.level;
          gradeText = LEVEL_GRADE_MAP[level] || '未知年级';
          console.log('从level获取年级:', level, gradeText);
        }
        // 3. 从标准ID推断年级
        else if (standardizedNodeInfo.id && standardizedNodeInfo.id.length >= 2) {
          const gradeInfo = this.getGradeFromNodeId(standardizedNodeInfo.id);
          if (gradeInfo && gradeInfo.grade) {
            gradeText = gradeInfo.grade;
            level = gradeInfo.level;
            console.log('从ID推断年级:', gradeText, level);
          }
        }
        
        // 4. 特殊知识点处理
        if (gradeText === '未知年级') {
          // 特殊知识点映射
          const specialKnowledgePoints = {
            '在数轴上表示数': { grade: '小学六年级', level: 6 },
            '认识几分之一': { grade: '小学二年级', level: 2 },
            '分数乘整数': { grade: '小学五年级', level: 5 },
            '有理数': { grade: '初中一年级', level: 7 },
            '二次根式': { grade: '初中二年级', level: 8 }
          };
          
          const nodeName = standardizedNodeInfo.name;
          
          // 精确匹配
          if (specialKnowledgePoints[nodeName]) {
            const specialInfo = specialKnowledgePoints[nodeName];
            gradeText = specialInfo.grade;
            level = specialInfo.level;
            console.log('特殊知识点匹配:', nodeName, gradeText, level);
          }
          // 关键词匹配
          else if (nodeName) {
            if (nodeName.includes('分数') && (nodeName.includes('认识') || nodeName.includes('几分之'))) {
              gradeText = '小学二年级';
              level = 2;
            } else if (nodeName.includes('数轴') || nodeName.includes('有理数')) {
              gradeText = '小学六年级';
              level = 6;
            } else if (nodeName.includes('二次根式')) {
              gradeText = '初中二年级';
              level = 8;
            } else if (nodeName.includes('分数乘整数')) {
              gradeText = '小学五年级';
              level = 5;
            }
            
            if (gradeText !== '未知年级') {
              console.log('关键词匹配年级:', nodeName, gradeText, level);
            }
          }
        }
        
        // 更新节点对象中的年级信息
        standardizedNodeInfo.grade = gradeText;
        standardizedNodeInfo.level = level;
        
        // 提示用户该知识点在其他年级
        wx.showModal({
          title: '提示',
          content: `该知识点"${standardizedNodeInfo.name}"属于${gradeText}，是否切换到对应年级查看？`,
          confirmText: '切换',
          cancelText: '取消',
          success: (res) => {
            if (res.confirm) {
              console.log('用户确认切换到年级:', gradeText, level);
              // 切换到对应年级
              this.setData({
                selectedGrade: gradeText,
                selectedLevel: level
              }, () => {
                // 重新加载知识图谱
                this.loadKnowledgeGraph().then(() => {
                  // 加载完成后查找并显示该节点
                  setTimeout(() => {
                    // 尝试查找节点：先用标准ID，再用名称
                    const targetNode = this.findNodeById(standardizedNodeInfo.id) || 
                                     (standardizedNodeInfo.name ? this.findNodeByName(standardizedNodeInfo.name) : null);
                    
                    if (targetNode) {
                      console.log('找到目标知识点:', targetNode);
                      this.showNodeDetail(targetNode);
                    } else {
                      console.error('未找到知识点:', standardizedNodeInfo.name, standardizedNodeInfo.id);
                      // 创建一个临时节点对象以显示一些基本信息
                      const tempNode = {
                        ...standardizedNodeInfo,
                        status: 'not-started',
                        difficulty: 'medium',
                        relations: {
                          prerequisites: [],
                          progressions: [],
                          correlations: [],
                          applications: [],
                          reinforcements: [],
                          analogies: [],
                          contrasts: [],
                          others: []
                        }
                      };
                      this.showNodeDetail(tempNode);
                      
                      wx.showToast({
                        title: '知识点数据不完整',
                        icon: 'none'
                      });
                    }
                  }, 500);
                }).catch(err => {
                  console.error('加载知识图谱失败:', err);
                  wx.showToast({
                    title: '加载知识图谱失败',
                    icon: 'none'
                  });
                });
              });
            }
          }
        });
      } else {
        console.error('查询知识点失败，无效响应:', res);
        // 创建一个默认节点，避免无法显示任何内容
        const defaultNode = {
          id: pointId,
          name: pointName || `知识点${pointId}`,
          grade: this.getGradeFromNodeId(pointId).grade || '未知年级',
          level: this.getGradeFromNodeId(pointId).level || 7,
          status: 'not-started',
          difficulty: 'medium',
          virtual: true
        };
        
        wx.showModal({
          title: '提示',
          content: `未找到该知识点"${defaultNode.name}"的详细信息，但可能属于${defaultNode.grade}，是否切换查看？`,
          confirmText: '切换',
          cancelText: '取消',
          success: (res) => {
            if (res.confirm) {
              this.setData({
                selectedGrade: defaultNode.grade,
                selectedLevel: defaultNode.level
              }, () => {
                this.loadKnowledgeGraph();
              });
            }
          }
        });
      }
    })
    .catch(err => {
      wx.hideLoading();
      console.error('查询知识点出错:', err);
      
      // 创建一个默认节点，避免无法显示任何内容
      const fallbackNode = {
        id: pointId,
        name: pointName || `知识点${pointId}`,
        grade: this.getGradeFromNodeId(pointId).grade || '未知年级',
        level: this.getGradeFromNodeId(pointId).level || 7,
        status: 'not-started',
        difficulty: 'medium',
        virtual: true
      };
      
      wx.showModal({
        title: '查询失败',
        content: `无法获取知识点"${fallbackNode.name}"的信息，请检查网络连接后重试`,
        showCancel: false
      });
    });
  },

  // 查找指定ID的节点
  findNodeById: function(nodeId) {
    if (!this.data.nodes || !nodeId) return null;
    return this.data.nodes.find(node => node.id === nodeId);
  },

  // 查找指定名称的节点
  findNodeByName: function(nodeName) {
    if (!this.data.nodes || !nodeName) return null;
    
    // 简化名称进行比较，忽略空格和标点
    const simplifiedName = this.simplifyName(nodeName);
    
    return this.data.nodes.find(node => 
      this.simplifyName(node.name) === simplifiedName
    );
  },

  // 简化名称，移除空格和标点
  simplifyName: function(name) {
    if (!name) return '';
    return name.replace(/[\s\.,，。、；：''""]/g, '').toLowerCase();
  },

  // 关闭详情面板
  closeDetail: function() {
    // 确保页面标题保持正确
    wx.setNavigationBarTitle({
      title: '数学知识图谱'
    });
    
    this.setData({
      showDetail: false
    });
    
    // 一段时间后清空选中节点，避免闪烁
    setTimeout(() => {
      this.setData({
        selectedNode: null
      });
      
      // 再次确保标题正确
      wx.setNavigationBarTitle({
        title: '数学知识图谱'
      });
    }, 300);
  },

  // 阻止事件冒泡
  stopPropagation: function(e) {
    // 阻止事件继续传播
  },

  // 阻止触摸移动事件
  preventTouchMove: function(e) {
    // 阻止触摸移动事件
  },

  // 开始学习
  onStartLearning: function() {
    if (!this.data.selectedNode || !this.data.selectedNode.id) {
      console.error('未选中知识点或知识点ID缺失');
      return;
    }
    
    const nodeId = this.data.selectedNode.id;
    const nodeName = this.data.selectedNode.name;
    
    // 记录日志
    console.log('从详情页开始学习知识点:', nodeName);
    
    // 关闭详情面板
    this.closeDetail();
    
    // 导航到知识点详情页面
    navigator.navigateTo(navigator.PATHS.KNOWLEDGE_DETAIL, {
      id: nodeId,
      title: nodeName
    }, {
      onFail: (error) => {
        console.error('导航失败:', error);
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  },

  // 重置筛选
  resetFilters: function() {
    this.setData({
      activeStatus: null,
      sortOrder: 'default',
      sortOrderText: '默认排序'
    }, () => {
      this.applyFilters();
    });
  },
  
  // 处理状态筛选
  onStatusFilter: function(e) {
    const status = e.currentTarget.dataset.status;
    
    console.log('状态筛选:', status);
    
    // 如果点击的是当前已选中的状态，则取消选择
    if (status === this.data.activeStatus) {
      this.setData({
        activeStatus: null
      }, () => {
        this.applyFilters();
      });
      return;
    }
    
    // 设置新的筛选状态
    this.setData({
      activeStatus: status
    }, () => {
      this.applyFilters();
      
      // 显示筛选结果提示
      const countMap = {
        'mastered': this.data.masteredCount,
        'learning': this.data.learningCount,
        'weak': this.data.weakCount,
        'not-mastered': this.data.notMasteredCount,
        'not-started': this.data.notStartedCount
      };
      
      const labelMap = {
        'mastered': '已掌握',
        'learning': '学习中',
        'weak': '薄弱',
        'not-mastered': '未掌握',
        'not-started': '未学习'
      };
      
      wx.showToast({
        title: `已筛选${labelMap[status]}知识点`,
        icon: 'none',
        duration: 1500
      });
    });
  },
  
  // 应用所有筛选条件
  applyFilters: function() {
    // 从原始数据开始
    let filteredNodes = [...this.data.originalNodes];
    
    // 应用状态筛选
    if (this.data.activeStatus) {
      // 状态匹配映射
      const statusMap = {
        'mastered': STATUS_TYPES.MASTERED,
        'learning': STATUS_TYPES.LEARNING,
        'weak': STATUS_TYPES.WEAK,
        'not-mastered': STATUS_TYPES.NOT_MASTERED,
        'not-started': STATUS_TYPES.NOT_STARTED
      };
      
      // 获取要筛选的状态常量
      const targetStatus = statusMap[this.data.activeStatus];
      
      if (targetStatus) {
        console.log('按状态筛选:', targetStatus);
        filteredNodes = filteredNodes.filter(node => node.status === targetStatus);
        console.log('筛选结果数量:', filteredNodes.length);
      }
    }
    
    // 应用排序
    filteredNodes = this.applySorting(filteredNodes);
    
    // 生成章节化数据
    const chaptersData = this.generateChaptersData(filteredNodes);
    
    // 更新视图
    this.setData({
      filteredNodes: filteredNodes,
      chaptersData: chaptersData,
      showEmpty: filteredNodes.length === 0
    });
  },

  // 应用排序
  applySorting: function(nodes) {
    const sortOrder = this.data.sortOrder;
    let sortedNodes = [...nodes];
    
    switch (sortOrder) {
      case 'difficulty-asc':
        // 按难度从易到难排序
        sortedNodes.sort((a, b) => {
          const difficultyOrder = { 
            [DIFFICULTY_LEVELS.EASY]: 1, 
            [DIFFICULTY_LEVELS.MEDIUM]: 2, 
            [DIFFICULTY_LEVELS.HARD]: 3, 
            [DIFFICULTY_LEVELS.EXPERT]: 4 
          };
          return (difficultyOrder[a.difficulty] || 2) - (difficultyOrder[b.difficulty] || 2);
        });
        break;
      case 'difficulty-desc':
        // 按难度从难到易排序
        sortedNodes.sort((a, b) => {
          const difficultyOrder = { 
            [DIFFICULTY_LEVELS.EASY]: 1, 
            [DIFFICULTY_LEVELS.MEDIUM]: 2, 
            [DIFFICULTY_LEVELS.HARD]: 3, 
            [DIFFICULTY_LEVELS.EXPERT]: 4 
          };
          return (difficultyOrder[b.difficulty] || 2) - (difficultyOrder[a.difficulty] || 2);
        });
        break;
      case 'name':
        // 按名称字母顺序排序
        sortedNodes.sort((a, b) => a.name.localeCompare(b.name, 'zh'));
        break;
      case 'status':
        // 按状态排序
        sortedNodes.sort((a, b) => {
          const statusOrder = { 
            [STATUS_TYPES.MASTERED]: 1, 
            [STATUS_TYPES.LEARNING]: 2, 
            [STATUS_TYPES.WEAK]: 3, 
            [STATUS_TYPES.NOT_MASTERED]: 4, 
            [STATUS_TYPES.NOT_STARTED]: 5 
          };
          return (statusOrder[a.status] || 5) - (statusOrder[b.status] || 5);
        });
        break;
      default:
        // 保持原始顺序
        break;
    }
    
    return sortedNodes;
  },
  
  // 切换排序方式
  onToggleSort: function() {
    // 循环切换排序方式
    let nextSortOrder = 'default';
    let nextSortText = '默认排序';
    
    switch (this.data.sortOrder) {
      case 'default':
        nextSortOrder = 'difficulty-asc';
        nextSortText = '难度(易到难)';
        break;
      case 'difficulty-asc':
        nextSortOrder = 'difficulty-desc';
        nextSortText = '难度(难到易)';
        break;
      case 'difficulty-desc':
        nextSortOrder = 'name';
        nextSortText = '名称排序';
        break;
      case 'name':
        nextSortOrder = 'status';
        nextSortText = '状态排序';
        break;
      case 'status':
        nextSortOrder = 'default';
        nextSortText = '默认排序';
        break;
    }
    
    this.setData({
      sortOrder: nextSortOrder,
      sortOrderText: nextSortText
    }, () => {
      this.applyFilters();
    });
  },
  
  // 从卡片开始学习
  onStartLearningFromCard: function(e) {
    // 增强版事件冒泡防护，避免触发onNodeClick
    if (e) {
      if (typeof e.stopPropagation === 'function') {
        e.stopPropagation();
      }
      
      // 兼容处理：尝试另一种事件冒泡阻止方法
      if (e.preventDefault && typeof e.preventDefault === 'function') {
        e.preventDefault();
      }
      
      // 直接从e.currentTarget获取数据，避免e.dataset可能不存在
      const nodeId = e && e.currentTarget && e.currentTarget.dataset ? 
                    e.currentTarget.dataset.nodeId : null;
      
      if (!nodeId) {
        console.error('知识点学习：缺少节点ID');
        return;
      }
      
      // 查找节点
      const node = this.findNodeById(nodeId);
      if (!node) {
        console.error('知识点学习：未找到节点:', nodeId);
        return;
      }
      
      // 记录日志
      console.log('开始学习知识点:', node.name);
      
      // 导航到知识点详情页面
      navigator.navigateTo(navigator.PATHS.KNOWLEDGE_DETAIL, {
        id: nodeId,
        title: node.name
      }, {
        onFail: (error) => {
          console.error('导航失败:', error);
          wx.showToast({
            title: '页面跳转失败',
            icon: 'none'
          });
        }
      });
    } else {
      console.error('知识点学习：缺少事件对象');
    }
  },

  // 处理页面下拉刷新
  onRefresh: function() {
    // 安全地刷新数据
    try {
      this.refreshGraph();
    } catch (e) {
      console.error('下拉刷新失败', e);
    } finally {
      // 停止下拉刷新动画
      wx.stopPullDownRefresh();
    }
  },

  // 刷新图谱
  refreshGraph: function() {
    // 防止连续多次刷新
    if (this.data.isLoading) return;
    
    this.setData({
      isLoading: true
    });
    
    // 使用setTimeout确保状态更新后再刷新图表
    setTimeout(() => {
      try {
        // 重新加载数据
        this.loadKnowledgeGraph();
      } catch (e) {
        console.error('刷新图谱失败', e);
        this.setData({
          isLoading: false
        });
      }
    }, 100);
  },

  // 防止内存泄漏，清理DOM相关引用
  onUnload: function() {
    // 清理任何可能导致内存泄漏的引用
    this.containerInfo = null;
  },

  // 处理节点点击
  onNodeClick: function(e) {
    const nodeId = e.currentTarget.dataset.nodeId;
    if (!nodeId) {
      console.error('节点点击事件：缺少节点ID');
      return;
    }
    
    // 查找节点
    const node = this.findNodeById(nodeId);
    if (!node) {
      console.error('节点点击事件：未找到节点:', nodeId);
      return;
    }
    
    // 显示节点详情
    this.showNodeDetail(node);
  },

  // 按关联强度排序
  sortRelationsByStrength: function(relatedPoints) {
    // 对每个关系类型数组按强度排序（从高到低：强 -> 中 -> 弱）
    for (const type in relatedPoints) {
      if (Array.isArray(relatedPoints[type])) {
        relatedPoints[type].sort((a, b) => {
          // 如果强度值相同，则按名称排序作为次要排序条件
          if (b.strength === a.strength) {
            return a.name.localeCompare(b.name, 'zh');
          }
          // 主要按强度从高到低排序
          return b.strength - a.strength;
        });
      }
    }
  },

  // 生成章节化数据
  generateChaptersData: function(nodes) {
    if (!nodes || nodes.length === 0) {
      return [];
    }



    // 按章节分组
    const chaptersMap = new Map();
    
    nodes.forEach(node => {
      const chapterNum = node.chapter || 0;
      // 章节号为0的显示原标题，其他显示"第X章 标题"
      let chapterTitle = node.chapterTitle || '';
      let displayTitle = '';
      
      if (chapterNum === 0) {
        // 综合实践等特殊章节，直接显示标题
        displayTitle = chapterTitle || '综合与实践';
      } else {
        // 正常章节，显示"第X章 标题"
        displayTitle = chapterTitle ? `第${chapterNum}章 ${chapterTitle}` : `第${chapterNum}章`;
      }
      
      const chapterKey = `${chapterNum}_${chapterTitle}`;
      
      if (!chaptersMap.has(chapterKey)) {
        chaptersMap.set(chapterKey, {
          chapterNumber: chapterNum,
          chapterTitle: chapterTitle,
          displayTitle: displayTitle,
          isSpecialChapter: chapterNum === 0, // 标记是否为特殊章节
          nodes: []
        });
      }
      
      chaptersMap.get(chapterKey).nodes.push(node);
    });
    
    // 转换为数组并排序
    const chapters = Array.from(chaptersMap.values());
    
    // 按章节号排序：正常章节在前(1,2,3...)，特殊章节(0)在后
    chapters.sort((a, b) => {
      const aNum = parseInt(a.chapterNumber);
      const bNum = parseInt(b.chapterNumber);
      
      // 特殊处理：章节号为0的放在最后
      if (aNum === 0 && bNum !== 0) return 1;  // a是0，b不是0，a排后面
      if (aNum !== 0 && bNum === 0) return -1; // a不是0，b是0，b排后面
      
      // 都是0或都不是0，按章节号正常排序
      return aNum - bNum;
    });
    
    // 每章节内按小节排序
    chapters.forEach(chapter => {
      console.log(`开始排序章节: ${chapter.displayTitle}, 节点数: ${chapter.nodes.length}`);
      
      chapter.nodes.sort((a, b) => {
        // 首先按小节号排序
        const aSectionNum = this.parseSectionNumber(a.section);
        const bSectionNum = this.parseSectionNumber(b.section);
        
        if (aSectionNum !== bSectionNum) {
          return aSectionNum - bSectionNum;
        }
        
        // 小节号相同时按名称排序
        return a.name.localeCompare(b.name, 'zh');
      });
    });
    
    console.log('生成章节化数据:', chapters);
    return chapters;
  },

  // 解析小节号（支持 1.1, 1.2.3, P.1 等格式）
  parseSectionNumber: function(section) {
    if (!section) return 999; // 没有小节号的排在最后
    
    // 如果是数字，直接返回
    if (typeof section === 'number') {
      return section;
    }
    
    const sectionStr = section.toString().trim();
    
    // 处理特殊格式：P.1, P.2 等（综合实践活动）
    if (sectionStr.startsWith('P.')) {
      const practiceNum = sectionStr.replace('P.', '');
      return parseInt(practiceNum) || 0;
    }
    
    // 处理小数格式：1.1, 1.2, 2.1, 2.2 等
    if (sectionStr.includes('.')) {
      const parts = sectionStr.split('.');
      if (parts.length >= 2) {
        const mainPart = parseInt(parts[0]) || 0;
        const subPart = parseInt(parts[1]) || 0;
        // 将小节号转换为便于排序的数字：1.1 -> 11, 1.2 -> 12, 2.1 -> 21, 2.2 -> 22
        return mainPart * 10 + subPart;
      }
    }
    
    // 提取第一个数字
    const match = sectionStr.match(/^(\d+)/);
    return match ? parseInt(match[1]) : 999;
  },

  // 测试云函数连接
  testCloudFunctionConnection: async function() {
    try {
      console.log('开始测试云函数连接...');
      
      const testResult = await wx.cloud.callFunction({
        name: 'knowledge-graph-query',
        data: {
          action: 'getKnowledgeNodes',
          params: {
            subject: 'mathematics',
            gradeLevel: 7,
            limit: 1
          }
        }
      });
      
      console.log('云函数连接测试结果:', testResult);
      
      if (testResult && testResult.result && testResult.result.success) {
        console.log('✅ 云函数连接正常');
        wx.showToast({
          title: '云函数连接正常',
          icon: 'success',
          duration: 1000
        });
      } else {
        console.error('❌ 云函数连接异常:', testResult);
        wx.showToast({
          title: '云函数连接异常',
          icon: 'error',
          duration: 2000
        });
      }
    } catch (error) {
      console.error('❌ 云函数连接测试失败:', error);
      wx.showToast({
        title: '云函数连接失败',
        icon: 'error',
        duration: 2000
      });
    }
  },

}); 