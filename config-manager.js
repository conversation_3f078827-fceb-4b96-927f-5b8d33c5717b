// 专业配置管理器
const fs = require('fs');
const path = require('path');

class ConfigManager {
  constructor() {
    this.envConfig = require('./env.config.js');
  }

  /**
   * 获取云环境ID
   */
  getCloudEnvId() {
    return this.envConfig.CLOUD_ENV_ID;
  }

  /**
   * 获取数据库配置
   */
  getDatabaseConfig() {
    return this.envConfig.DATABASE.POSTGRESQL;
  }

  /**
   * 获取云函数配置
   */
  getCloudFunctionConfig() {
    return this.envConfig.CLOUD_FUNCTIONS;
  }

  /**
   * 更新所有配置文件
   */
  updateAllConfigs() {
    const envId = this.getCloudEnvId();
    const region = this.envConfig.CLOUD_REGION;

    // 更新 project.config.json
    this.updateProjectConfig(envId);
    
    // 更新 cloudbaserc.json
    this.updateCloudbaseConfig(envId, region);
    
    // 更新 .cloudbase/config.json
    this.updateCloudbaseRcConfig(envId, region);

    console.log('✅ 所有配置文件已更新');
  }

  /**
   * 更新项目配置
   */
  updateProjectConfig(envId) {
    const configPath = path.join(__dirname, 'project.config.json');
    if (fs.existsSync(configPath)) {
      let config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
      config.cloudBaseConfig.envId = envId;
      fs.writeFileSync(configPath, JSON.stringify(config, null, 2));
      console.log('✅ project.config.json 已更新');
    }
  }

  /**
   * 更新 cloudbaserc 配置
   */
  updateCloudbaseConfig(envId, region) {
    const configPath = path.join(__dirname, 'cloudbaserc.json');
    if (fs.existsSync(configPath)) {
      let config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
      config.envId = envId;
      
      // 更新云函数配置
      const functionConfig = this.getCloudFunctionConfig();
      config.functions = config.functions.map(func => ({
        ...func,
        ...functionConfig
      }));
      
      fs.writeFileSync(configPath, JSON.stringify(config, null, 2));
      console.log('✅ cloudbaserc.json 已更新');
    }
  }

  /**
   * 更新 .cloudbase/config.json 配置
   */
  updateCloudbaseRcConfig(envId, region) {
    const configPath = path.join(__dirname, '.cloudbase/config.json');
    if (fs.existsSync(configPath)) {
      let config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
      config.envId = envId;
      config.region = region;
      fs.writeFileSync(configPath, JSON.stringify(config, null, 2));
      console.log('✅ .cloudbase/config.json 已更新');
    }
  }

  /**
   * 验证配置完整性
   */
  validateConfigs() {
    const errors = [];
    const warnings = [];

    // 检查环境ID格式
    const envId = this.getCloudEnvId();
    if (!envId || !envId.startsWith('cloud')) {
      errors.push('环境ID格式不正确');
    }

    // 检查数据库配置
    const dbConfig = this.getDatabaseConfig();
    if (!dbConfig.host || !dbConfig.database) {
      errors.push('数据库配置不完整');
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings
    };
  }
}

// 单例模式
let instance = null;

function getConfigManager() {
  if (!instance) {
    instance = new ConfigManager();
  }
  return instance;
}

module.exports = {
  ConfigManager,
  getConfigManager
};

// 如果直接运行此文件，执行配置更新
if (require.main === module) {
  const configManager = getConfigManager();
  configManager.updateAllConfigs();
  
  const validation = configManager.validateConfigs();
  if (validation.valid) {
    console.log('🎉 配置验证通过');
  } else {
    console.error('❌ 配置验证失败:', validation.errors);
  }
} 