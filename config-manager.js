// 微信小程序配置管理器
class ConfigManager {
  constructor() {
    // 在小程序环境中，配置通常通过require直接引入
    try {
      this.envConfig = require('./env.config.js');
    } catch (e) {
      console.warn('环境配置文件未找到，使用默认配置');
      this.envConfig = this.getDefaultConfig();
    }
  }

  /**
   * 获取默认配置
   */
  getDefaultConfig() {
    return {
      CLOUD_ENV_ID: 'cloud1-0g3pg16f43f63333',
      CLOUD_REGION: 'ap-shanghai',
      DATABASE: {
        POSTGRESQL: {
          host: 'localhost',
          port: 5432,
          database: 'student_db',
          username: 'postgres',
          password: ''
        }
      },
      CLOUD_FUNCTIONS: {
        timeout: 30,
        runtime: 'Nodejs14.15'
      }
    };
  }

  /**
   * 获取云环境ID
   */
  getCloudEnvId() {
    return this.envConfig.CLOUD_ENV_ID;
  }

  /**
   * 获取数据库配置
   */
  getDatabaseConfig() {
    return this.envConfig.DATABASE.POSTGRESQL;
  }

  /**
   * 获取云函数配置
   */
  getCloudFunctionConfig() {
    return this.envConfig.CLOUD_FUNCTIONS;
  }

  /**
   * 获取云存储配置
   */
  getCloudStorageConfig() {
    return {
      envId: this.getCloudEnvId(),
      traceUser: true
    };
  }

  /**
   * 获取API配置
   */
  getApiConfig() {
    return {
      baseUrl: 'https://api.weixin.qq.com',
      timeout: 10000,
      retryCount: 3
    };
  }

  /**
   * 验证配置完整性
   */
  validateConfigs() {
    const errors = [];
    const warnings = [];

    // 检查环境ID格式
    const envId = this.getCloudEnvId();
    if (!envId || typeof envId !== 'string') {
      errors.push('环境ID格式不正确');
    }

    // 检查数据库配置
    const dbConfig = this.getDatabaseConfig();
    if (!dbConfig || !dbConfig.host || !dbConfig.database) {
      errors.push('数据库配置不完整');
    }

    // 检查云函数配置
    const funcConfig = this.getCloudFunctionConfig();
    if (!funcConfig || !funcConfig.timeout) {
      warnings.push('云函数配置可能不完整');
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * 获取完整配置信息
   */
  getAllConfigs() {
    return {
      envId: this.getCloudEnvId(),
      database: this.getDatabaseConfig(),
      cloudFunction: this.getCloudFunctionConfig(),
      cloudStorage: this.getCloudStorageConfig(),
      api: this.getApiConfig()
    };
  }

  /**
   * 更新配置（在小程序环境中主要用于运行时配置更新）
   */
  updateConfig(key, value) {
    if (this.envConfig && typeof this.envConfig === 'object') {
      this.envConfig[key] = value;
      console.log(`✅ 配置 ${key} 已更新`);
    } else {
      console.warn(`⚠️ 无法更新配置 ${key}`);
    }
  }

  /**
   * 打印配置信息（调试用）
   */
  printConfigs() {
    console.log('📋 当前配置信息:');
    console.log('环境ID:', this.getCloudEnvId());
    console.log('数据库配置:', this.getDatabaseConfig());
    console.log('云函数配置:', this.getCloudFunctionConfig());
    
    const validation = this.validateConfigs();
    if (validation.valid) {
      console.log('🎉 配置验证通过');
    } else {
      console.error('❌ 配置验证失败:', validation.errors);
      if (validation.warnings.length > 0) {
        console.warn('⚠️ 配置警告:', validation.warnings);
      }
    }
  }
}

// 单例模式
let instance = null;

function getConfigManager() {
  if (!instance) {
    instance = new ConfigManager();
  }
  return instance;
}

// 导出模块
module.exports = {
  ConfigManager,
  getConfigManager
}; 