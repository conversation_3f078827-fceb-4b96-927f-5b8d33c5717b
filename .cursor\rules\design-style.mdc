---
description: 
globs: 
alwaysApply: true
---
# 设计风格指南

本项目采用清新、专业的教育类产品设计风格，以提供良好的学习体验。

## 色彩系统

- **主色调**：蓝色(#3E7BFA)，象征知识和智慧
- **辅助色**：
  - 绿色(#42B983)：表示成功和进步
  - 橙色(#F5A623)：表示警告和提示
  - 红色(#F56C6C)：表示错误和重要信息
- **中性色**：
  - 深灰(#333333)：主要文本
  - 中灰(#666666)：次要文本
  - 浅灰(#999999)：辅助文本
  - 背景灰(#F5F7FA)：页面背景

## 字体规范

- 主标题：18px，加粗
- 次标题：16px，加粗
- 正文：14px，常规
- 辅助文本：12px，常规
- 强调文本使用主色或加粗处理

## 布局规范

- 内容区域左右边距：32rpx
- 卡片组件圆角：16rpx
- 组件间距：30rpx
- 内容分区明确，避免视觉拥挤
- 重要内容置顶，符合F型阅读模式

## 交互设计

- 按钮点击区域不小于88rpx高
- 列表项高度统一为110rpx
- 所有可点击元素必须有视觉反馈
- 加载状态使用统一的Loading组件
- 表单提交必须有明确的成功/失败反馈

## 图标使用

- 统一使用SVG图标，通过CSS背景图实现
- 图标大小：普通状态32rpx，强调状态40rpx
- 图标颜色与文字保持一致
- 关键功能配合文字使用图标，增强识别度
- 使用.icon基础类和.icon-{name}命名图标

## 动画效果

- 页面切换使用滑动过渡动画
- 弹窗显示使用淡入效果
- 按钮点击有轻微缩放反馈
- 列表加载使用渐显动画
- 状态变化使用平滑过渡

## 空状态设计

- 每个列表页面必须有空状态设计
- 空状态包含图标、文字说明和操作建议
- 使用友好语言，避免技术术语
- 空状态页面保持简洁，减少视觉干扰

## 卡片设计

- 内容卡片使用白色背景和轻微阴影
- 卡片内部边距统一为24rpx
- 卡片之间间距为24rpx
- 卡片可点击区域清晰可见
- 卡片内容层次分明，重要信息突出显示

## 表单设计

- 输入框高度统一为88rpx
- 标签与输入框垂直对齐
- 必填项使用红色星号标记
- 错误提示显示在输入框下方，使用红色文字
- 提交按钮宽度与表单等宽

## 加载状态

- 页面加载使用顶部loading条
- 局部加载使用圆形loading图标
- 下拉刷新使用微信原生样式
- 数据加载中显示骨架屏
- 上拉加载更多使用文字提示

## 数学公式展示

- 公式使用专业排版，保持标准数学符号风格
- 复杂公式可放大查看
- 公式步骤使用逐步展示方式
- 关键公式使用主题色强调
- 公式与说明文字间距为16rpx

## 响应式设计

- 小程序同时支持手机和平板设备
- 平板设备优化大屏体验，内容居中显示
- 手机设备保证内容紧凑，减少滚动
- 横屏模式自动调整布局和字体大小
- 考虑不同屏幕比例的显示效果

## 样式文件组织

- 主题变量：[styles/theme.wxss](mdc:styles/theme.wxss)
- 通用样式：[styles/common.wxss](mdc:styles/common.wxss)
- 动画效果：[styles/animation.wxss](mdc:styles/animation.wxss)
- 图标样式：[styles/icons.wxss](mdc:styles/icons.wxss)
- 组件样式：每个组件定义独立的wxss文件
