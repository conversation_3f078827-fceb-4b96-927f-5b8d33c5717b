---
description: 
globs: 
alwaysApply: false
---
# SVG图表Base64编码显示规范

## 概述
为保证图表在各种设备上的一致性显示，雷达图组件必须使用SVG转Base64编码后通过image标签展示，而非使用Canvas直接绘制。

## 实现要求

1. **禁止直接使用Canvas绘制图表**
   - 除非作为降级备选方案，不应直接使用Canvas绘制雷达图
   - SVG应当是首选的图表绘制方式

2. **SVG内容必须转换为Base64编码**
   - SVG字符串内容必须先转换为Base64编码
   - 使用标准的Data URL格式: `data:image/svg+xml;base64,BASE64_CONTENT`

3. **必须使用image标签显示**
   - 转换后的Base64编码必须通过image标签显示
   - 不应使用rich-text或其他方式直接渲染SVG内容

4. **降级方案**
   - 在不支持SVG Base64编码的情况下，可降级为Canvas绘制
   - 降级方案必须作为备选，默认使用SVG Base64方案

## 参考实现

### 组件实现
雷达图组件实现可参考: [components/business/radar-chart/index.js](mdc:components/business/radar-chart/index.js)

### 模板示例
```xml
<image 
  wx:if="{{imageLoaded && svgBase64}}"
  src="{{svgBase64}}"
  class="svg-image"
  mode="aspectFit"
  style="width: {{width}}px; height: {{height}}px;"
></image>
```

### SVG转Base64参考代码
```javascript
// 将SVG字符串转换为Base64编码
convertSvgToBase64(svgString) {
  try {
    // 对SVG进行URI编码
    const encodedSvg = encodeURIComponent(svgString);
    
    // 使用Base64编码
    const base64Svg = `data:image/svg+xml;base64,${wx.arrayBufferToBase64(new Uint8Array([...unescape(encodedSvg)].map(c => c.charCodeAt(0))))}`;
    
    return base64Svg;
  } catch (error) {
    console.error('SVG转Base64失败:', error);
    return null;
  }
}
```

## 性能注意事项

1. **缓存生成的Base64编码**
   - 避免频繁重新生成Base64，应合理缓存
   - 只有在SVG内容变更时才重新生成Base64编码

2. **控制SVG大小**
   - SVG字符串不宜过大，避免生成过长的Base64编码
   - 移除SVG中不必要的属性和元素

3. **优化Image组件**
   - 合理设置image的mode属性，推荐使用aspectFit
   - 使用惰性加载，只在需要时才生成和显示图表

