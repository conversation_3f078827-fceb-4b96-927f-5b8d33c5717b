<view class="share-overlay {{visible ? 'show' : ''}}" bindtap="close">
  <view class="share-box" catchtap="stopPropagation">
    <view class="share-header">
      <view class="share-title">{{title}}</view>
      <view class="share-close" bindtap="close">×</view>
    </view>
    <view class="share-options">
      <view class="share-option" bindtap="shareToFriend">
        <view class="share-icon wechat-icon"></view>
        <view class="share-option-text">微信好友</view>
      </view>
      <view class="share-option" bindtap="shareToMoment" wx:if="{{showMoment}}">
        <view class="share-icon moment-icon"></view>
        <view class="share-option-text">朋友圈</view>
      </view>
      <view class="share-option" bindtap="generatePoster" wx:if="{{showPoster}}">
        <view class="share-icon poster-icon"></view>
        <view class="share-option-text">生成海报</view>
      </view>
      <view class="share-option" bindtap="copyShareLink" wx:if="{{showLink}}">
        <view class="share-icon link-icon"></view>
        <view class="share-option-text">复制链接</view>
      </view>
    </view>
  </view>
</view> 