#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通用知识点编码验证工具 - 高级版V2.0
支持所有年级、学期的知识点验证，自动发现知识点定义文件
专家编写：K12数学教育专家、软件工程专家
创建时间：2025-01-22
适用范围：小学1-6年级、初中7-9年级、高中10-12年级
"""

import re
import sys
import os
import glob
import argparse
from collections import defaultdict
from pathlib import Path

class KnowledgeNodeValidator:
    """通用知识点验证器"""
    
    def __init__(self, nodes_dir='../mini', verbose=False):
        """
        初始化验证器
        
        Args:
            nodes_dir: 知识点定义文件目录
            verbose: 是否显示详细信息
        """
        self.nodes_dir = Path(nodes_dir)
        self.verbose = verbose
        self.existing_nodes = set()
        self.node_files = []
        
        # 知识点编码模式（支持所有已知格式）
        self.node_patterns = [
            # 小学格式：MATH_G{年级}S{学期}_{类型}_{编号}
            r"MATH_G(\d{1,2})S([12])_(CH\d+|INTRO|PRACTICE)_(\d+)",
            # 高中格式：MATH_G{年级}{模块}_{类型}_{编号}
            r"MATH_G(\d{1,2})(MANDATORY|ELECTIVE)_(\w+)_(\d+)",
            # 其他可能的格式
            r"MATH_G(\d{1,2})([A-Z_]+)_(\d+)"
        ]
        
    def discover_node_files(self):
        """自动发现知识点定义文件"""
        if not self.nodes_dir.exists():
            raise FileNotFoundError(f"知识点目录不存在: {self.nodes_dir}")
        
        # 搜索所有可能的知识点文件
        patterns = [
            "grade_*_nodes.sql",
            "grade_*_semester_*_nodes.sql", 
            "grade_*_mandatory_*_nodes.sql",
            "grade_*_elective_*_nodes.sql"
        ]
        
        self.node_files = []
        for pattern in patterns:
            files = list(self.nodes_dir.glob(pattern))
            self.node_files.extend(files)
        
        self.node_files = sorted(set(self.node_files))
        
        if self.verbose:
            print(f"[目录] 发现 {len(self.node_files)} 个知识点定义文件:")
            for file in self.node_files:
                print(f"   - {file.name}")
        
        return self.node_files
    
    def extract_nodes_from_file(self, file_path):
        """从单个文件提取知识点编码"""
        nodes = set()
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 使用多个模式匹配知识点编码
            for pattern in self.node_patterns:
                matches = re.findall(f"'({pattern.replace('(', '').replace(')', '')})'", content)
                for match in matches:
                    if isinstance(match, tuple):
                        nodes.add(''.join(match))
                    else:
                        nodes.add(match)
            
            # 通用匹配：所有以MATH_开头的编码
            general_pattern = r"'(MATH_G\d+[^']+)'"
            general_matches = re.findall(general_pattern, content)
            nodes.update(general_matches)
            
        except Exception as e:
            print(f"[警告] 读取文件 {file_path} 时出错: {e}")
        
        return nodes
    
    def load_all_existing_nodes(self):
        """加载所有已存在的知识点编码"""
        self.discover_node_files()
        self.existing_nodes = set()
        
        for file_path in self.node_files:
            nodes = self.extract_nodes_from_file(file_path)
            self.existing_nodes.update(nodes)
            
            if self.verbose:
                print(f"[文件] {file_path.name}: {len(nodes)} 个知识点")
        
        return self.existing_nodes
    
    def extract_relation_nodes(self, relation_file):
        """从关系文件中提取引用的知识点编码"""
        relation_nodes = set()
        
        try:
            with open(relation_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 匹配SQL中的完整知识点编码（排除LIKE通配符）
            patterns = [
                r"node_code = '(MATH_G\d+[^'%]+)'",  # 排除包含%的LIKE模式
                r"WHERE node_code = '(MATH_G\d+[^'%]+)'",
                r"AND node_code = '(MATH_G\d+[^'%]+)'"
            ]
            
            for pattern in patterns:
                matches = re.findall(pattern, content)
                # 进一步过滤：确保不包含通配符
                for match in matches:
                    if '%' not in match and '_' not in match.split('_')[-1]:
                        relation_nodes.add(match)
            
        except Exception as e:
            print(f"[警告] 读取关系文件 {relation_file} 时出错: {e}")
        
        return sorted(relation_nodes)
    
    def analyze_node_distribution(self, nodes):
        """分析知识点分布"""
        distribution = defaultdict(lambda: defaultdict(lambda: defaultdict(list)))
        
        for node in nodes:
            # 解析知识点编码
            parsed = self.parse_node_code(node)
            if parsed:
                grade = parsed['grade']
                semester = parsed.get('semester', '未知')
                chapter = parsed.get('chapter', '未知')
                number = parsed.get('number', 0)
                
                distribution[f"G{grade}"][semester][chapter].append(number)
        
        return distribution
    
    def parse_node_code(self, node_code):
        """解析知识点编码"""
        # 小学格式
        pattern1 = r"MATH_G(\d+)S([12])_(CH\d+|INTRO|PRACTICE)_(\d+)"
        match1 = re.match(pattern1, node_code)
        if match1:
            return {
                'grade': int(match1.group(1)),
                'semester': f"S{match1.group(2)}",
                'chapter': match1.group(3),
                'number': int(match1.group(4))
            }
        
        # 高中格式
        pattern2 = r"MATH_G(\d+)([A-Z_]+)_([A-Z0-9_]+)_(\d+)"
        match2 = re.match(pattern2, node_code)
        if match2:
            return {
                'grade': int(match2.group(1)),
                'module': match2.group(2),
                'chapter': match2.group(3),
                'number': int(match2.group(4))
            }
        
        return None
    
    def print_distribution_stats(self, distribution):
        """打印分布统计"""
        print("\n[统计] 知识点编码分布统计:")
        print("=" * 80)
        
        for grade in sorted(distribution.keys()):
            print(f"\n[年级] {grade}:")
            
            for semester in sorted(distribution[grade].keys()):
                total_in_semester = sum(len(distribution[grade][semester][ch]) 
                                      for ch in distribution[grade][semester])
                print(f"   [学期] {semester}: {total_in_semester}个知识点")
                
                for chapter in sorted(distribution[grade][semester].keys()):
                    numbers = sorted(distribution[grade][semester][chapter])
                    if numbers:
                        print(f"      [章节] {chapter}: {min(numbers):03d}-{max(numbers):03d} ({len(numbers)}个)")
    
    def validate_relation_file(self, relation_file):
        """验证关系文件中的知识点编码"""
        print(f"\n[验证] 验证文件: {relation_file}")
        print("=" * 80)
        
        # 加载所有现有知识点
        if not self.existing_nodes:
            print("[加载] 加载知识点定义...")
            self.load_all_existing_nodes()
            print(f"[成功] 已加载 {len(self.existing_nodes)} 个知识点编码")
        
        # 提取关系文件中的知识点
        relation_nodes = self.extract_relation_nodes(relation_file)
        print(f"[统计] 关系文件中引用了 {len(relation_nodes)} 个知识点")
        
        # 检查缺失的知识点
        missing_nodes = [node for node in relation_nodes if node not in self.existing_nodes]
        
        if missing_nodes:
            print(f"\n[错误] 发现 {len(missing_nodes)} 个不存在的知识点编码:")
            for i, node in enumerate(missing_nodes, 1):
                print(f"   {i:2d}. {node}")
                
            # 分析缺失知识点的模式
            print("\n[分析] 缺失知识点分析:")
            missing_distribution = self.analyze_node_distribution(missing_nodes)
            for grade in sorted(missing_distribution.keys()):
                count = sum(sum(len(missing_distribution[grade][sem][ch]) 
                              for ch in missing_distribution[grade][sem])
                           for sem in missing_distribution[grade])
                print(f"   {grade}: {count}个缺失")
        else:
            print("\n[成功] 所有知识点编码都存在！")
        
        # 显示现有知识点分布
        if self.verbose:
            existing_distribution = self.analyze_node_distribution(self.existing_nodes)
            self.print_distribution_stats(existing_distribution)
        
        return len(missing_nodes) == 0


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="通用知识点编码验证工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 验证单个关系文件
  python validate_nodes.py grade_1_internal_relationships.sql
  
  # 详细模式验证
  python validate_nodes.py -v grade_6_internal_relationships.sql
  
  # 指定知识点目录
  python validate_nodes.py -d ../mini grade_7_internal_relationships.sql
  
  # 批量验证所有关系文件
  python validate_nodes.py *.sql
        """
    )
    
    parser.add_argument('files', nargs='+', help='要验证的关系文件')
    parser.add_argument('-d', '--nodes-dir', default='../mini', 
                       help='知识点定义文件目录 (默认: ../mini)')
    parser.add_argument('-v', '--verbose', action='store_true',
                       help='显示详细信息')
    parser.add_argument('--stats-only', action='store_true',
                       help='仅显示统计信息，不验证文件')
    
    args = parser.parse_args()
    
    # 创建验证器
    validator = KnowledgeNodeValidator(args.nodes_dir, args.verbose)
    
    try:
        if args.stats_only:
            # 仅显示统计信息
            print("[统计] 知识点统计信息")
            print("=" * 50)
            validator.load_all_existing_nodes()
            distribution = validator.analyze_node_distribution(validator.existing_nodes)
            validator.print_distribution_stats(distribution)
            return
        
        # 验证文件
        success_count = 0
        total_count = 0
        
        for file_pattern in args.files:
            # 支持通配符
            matching_files = glob.glob(file_pattern)
            if not matching_files:
                print(f"[警告] 未找到匹配的文件: {file_pattern}")
                continue
            
            for file_path in matching_files:
                if not os.path.exists(file_path):
                    print(f"[警告] 文件不存在: {file_path}")
                    continue
                
                total_count += 1
                if validator.validate_relation_file(file_path):
                    success_count += 1
        
        # 总结
        print(f"\n[完成] 验证完成:")
        print(f"   [成功] 成功: {success_count}/{total_count}")
        print(f"   [失败] 失败: {total_count - success_count}/{total_count}")
        
        if success_count == total_count:
            print("\n[成功] 所有文件验证通过！")
            sys.exit(0)
        else:
            print("\n[警告] 部分文件验证失败，请检查缺失的知识点")
            sys.exit(1)
            
    except Exception as e:
        print(f"[错误] 程序执行错误: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main() 