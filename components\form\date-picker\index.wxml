<view class="date-picker-container" wx:if="{{show}}">
  <!-- 背景蒙层 -->
  <view class="date-picker-mask" bindtap="onCancel"></view>
  
  <!-- 日期选择器主体 -->
  <view class="date-picker-panel">
    <!-- 选择器头部 -->
    <view class="date-picker-header">
      <view class="picker-btn picker-cancel" bindtap="onCancel">取消</view>
      <view class="picker-title">{{title}}</view>
      <view class="picker-btn picker-confirm" bindtap="onConfirm">确定</view>
    </view>
    
    <!-- 选择器内容区域 -->
    <view class="date-picker-body">
      <picker-view 
        class="date-picker-view" 
        value="{{[selectedYear, selectedMonth, selectedDay]}}"
        bindchange="onValueChange"
        indicator-style="height: 80rpx;"
        indicator-class="picker-indicator"
        mask-class="picker-mask">
        <!-- 年份列 -->
        <picker-view-column>
          <view wx:for="{{years}}" wx:key="index" class="picker-item">{{item}}</view>
        </picker-view-column>
        
        <!-- 月份列 -->
        <picker-view-column>
          <view wx:for="{{months}}" wx:key="index" class="picker-item">{{item}}</view>
        </picker-view-column>
        
        <!-- 日期列 -->
        <picker-view-column>
          <view wx:for="{{days}}" wx:key="index" class="picker-item">{{item}}</view>
        </picker-view-column>
      </picker-view>
    </view>
  </view>
</view> 