---
description: 
globs: 
alwaysApply: false
---
# 分包结构指南

本项目采用微信小程序分包加载机制，将不同业务模块拆分为独立分包，提高首次启动加载速度。

## 主包与分包

### 主包 (`pages/`)

主包包含以下基础页面：
- `pages/index/index` - 首页
- `pages/class/index` - 班级
- `pages/ai/index` - AI推荐
- `pages/notebook/index` - 错题本
- `pages/profile/index` - 个人中心

### 分包结构

1. **首页分包 (`packageHome/`)**
   - `pages/subject-list/index` - 学科列表页
   - `pages/chapter-list/index` - 章节列表页
   - `pages/knowledge-detail/index` - 知识点详情页

2. **班级分包 (`packageClass/`)**
   - `pages/class-detail/index` - 班级详情页
   - `pages/member-list/index` - 成员列表页
   - `pages/homework-list/index` - 作业列表页
   - `pages/homework-detail/index` - 作业详情页

3. **AI推荐分包 (`packageAI/`)**
   - `pages/learning-path/index` - 学习路径页
   - `pages/practice-recommend/index` - 练习推荐页
   - `pages/knowledge-graph/index` - 知识图谱页
   - `pages/ai-assistant/index` - AI助教页

4. **错题本分包 (`packageNotebook/`)**
   - `pages/wrong-questions/index` - 错题列表页
   - `pages/collection/index` - 收藏题目页
   - `pages/notes/index` - 学习笔记页
   - `pages/analysis/index` - 错题分析页

5. **个人中心分包 (`packageProfile/`)**
   - `pages/settings/index` - 设置页
   - `pages/study-report/index` - 学习报告页
   - `pages/achievement/index` - 成就系统页
   - `pages/feedback/index` - 反馈建议页

## 分包预加载配置

```json
{
  "preloadRule": {
    "pages/index/index": {
      "network": "all",
      "packages": ["packageHome"]
    },
    "pages/class/index": {
      "network": "all",
      "packages": ["packageClass"]
    },
    "pages/ai/index": {
      "network": "all",
      "packages": ["packageAI"]
    },
    "pages/notebook/index": {
      "network": "all",
      "packages": ["packageNotebook"]
    },
    "pages/profile/index": {
      "network": "all",
      "packages": ["packageProfile"]
    }
  }
}
```

## 分包开发规范

1. **分包原则**
   - 主包只包含首页和框架必需代码
   - 相关功能放在同一分包内
   - 分包大小控制在2MB以内
   - 避免分包间相互依赖

2. **资源管理**
   - 分包独立的资源放在分包目录内
   - 共用资源放在主包内
   - 静态资源使用CDN加载
   - 合理使用分包预下载

3. **组件管理**
   - 分包私有组件放在分包内的components目录
   - 共用组件放在主包的components目录
   - 避免组件循环依赖

4. **性能优化**
   - 合理使用分包预加载
   - 控制分包大小
   - 优化首次启动加载
   - 使用按需加载

## 实现参考

- 分包配置：[app.json](mdc:app.json)
- 主包页面：[pages/index/index.js](mdc:pages/index/index.js)
- 分包页面：[packageHome/pages/subject-list/index.js](mdc:packageHome/pages/subject-list/index.js)

## 目录结构示例

```
student/
├── pages/                # 主包页面
│   ├── index/           # 首页
│   ├── class/           # 班级
│   ├── ai/              # AI推荐
│   ├── notebook/        # 错题本
│   └── profile/         # 我的
├── packageHome/         # 首页分包
│   └── pages/
│       ├── subject-list/
│       ├── chapter-list/
│       └── knowledge-detail/
├── packageClass/        # 班级分包
│   └── pages/
│       ├── class-detail/
│       ├── member-list/
│       ├── homework-list/
│       └── homework-detail/
├── packageAI/          # AI推荐分包
│   └── pages/
│       ├── learning-path/
│       ├── practice-recommend/
│       ├── knowledge-graph/
│       └── ai-assistant/
├── packageNotebook/    # 错题本分包
│   └── pages/
│       ├── wrong-questions/
│       ├── collection/
│       ├── notes/
│       └── analysis/
└── packageProfile/     # 个人中心分包
    └── pages/
        ├── settings/
        ├── study-report/
        ├── achievement/
        └── feedback/
```

## 配置参考

- 主包配置：[app.json](mdc:app.json)
- 分包页面配置：
  - [packageHome/pages/subject-list/index.json](mdc:packageHome/pages/subject-list/index.json)
  - [packageClass/pages/class-detail/index.json](mdc:packageClass/pages/class-detail/index.json)
  - [packageAI/pages/learning-path/index.json](mdc:packageAI/pages/learning-path/index.json)
  - [packageNotebook/pages/wrong-questions/index.json](mdc:packageNotebook/pages/wrong-questions/index.json)
  - [packageProfile/pages/settings/index.json](mdc:packageProfile/pages/settings/index.json)