{"version": "2.0", "envId": "cloud1-0g3pg16f43f63333", "framework": {"name": "tcb", "plugins": {"node": {"use": "@cloudbase/framework-plugin-node", "inputs": {"name": "knowledge-graph-query", "path": "/cloudfunctions"}}}}, "functionRoot": "cloudfunctions", "functions": [{"name": "knowledge-graph-query", "timeout": 60, "envVariables": {}, "runtime": "Nodejs16.13", "memorySize": 512, "installDependency": true}, {"name": "database-query", "timeout": 60, "envVariables": {}, "runtime": "Nodejs16.13", "memorySize": 512, "installDependency": true}, {"name": "intelligent-diagnosis", "timeout": 60, "envVariables": {}, "runtime": "Nodejs16.13", "memorySize": 512, "installDependency": true}, {"name": "login", "timeout": 60, "envVariables": {}, "runtime": "Nodejs16.13", "memorySize": 512, "installDependency": true}, {"name": "user-knowledge-status", "timeout": 60, "envVariables": {}, "runtime": "Nodejs16.13", "memorySize": 512, "installDependency": true}]}