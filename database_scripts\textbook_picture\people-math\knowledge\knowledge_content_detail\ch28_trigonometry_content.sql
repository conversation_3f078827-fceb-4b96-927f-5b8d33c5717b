-- ===================================================================
-- 人民教育出版社 九年级数学下册 第28章：锐角三角函数 - 知识点内容详情
-- 严格按照标准节点定义编写，共21个知识点
-- ===================================================================

INSERT INTO knowledge_content_details (
    node_id, description, detailed_explanation, key_points, formulas,
    examples, concepts_breakdown, common_mistakes, learning_tips,
    liberal_arts_content, science_content,
    content_version, is_current, language_code,
    content_quality_score, review_status
) VALUES

-- MATH_G9S2_CH28_001: 锐角三角函数的概念
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S2_CH28_001'),
'锐角三角函数是描述直角三角形中角度与边长关系的重要数学概念',
'锐角三角函数是数学中的重要概念，它建立了直角三角形中锐角大小与边长比值之间的对应关系。这个概念的提出解决了几何测量中的基本问题：如何通过角度计算距离，或通过距离确定角度。在直角三角形中，当一个锐角确定后，三边的比值关系就完全确定，这种比值关系不依赖于三角形的大小，只与角度相关。锐角三角函数包括正弦、余弦、正切等，它们为解决测量、导航、工程计算等实际问题提供了强有力的数学工具，是从几何直观向代数计算转化的重要桥梁。',
'[
  "建立角度与边长比值的对应关系",
  "比值关系只与角度相关，不依赖三角形大小",
  "包括正弦、余弦、正切等基本函数",
  "是几何问题代数化的重要工具",
  "在测量、工程等领域有广泛应用"
]',
'[
  {
    "name": "正弦函数定义",
    "formula": "sin A = 对边/斜边",
    "description": "锐角A的正弦函数基本定义"
  },
  {
    "name": "余弦函数定义",
    "formula": "cos A = 邻边/斜边",
    "description": "锐角A的余弦函数基本定义"
  },
  {
    "name": "正切函数定义",
    "formula": "tan A = 对边/邻边",
    "description": "锐角A的正切函数基本定义"
  },
  {
    "name": "比值不变性",
    "formula": "角度确定，比值唯一确定",
    "description": "三角函数的本质特征"
  }
]',
'[
  {
    "title": "探索角度与边长比值的关系",
    "problem": "画出几个不同大小但都含有45°角的直角三角形，测量并计算对边与邻边的比值",
    "solution": "①画出边长为3cm、4cm、5cm的45°直角三角形②测量每个三角形45°角的对边和邻边③计算比值：3÷3=1，4÷4=1，5÷5=1④发现规律：无论三角形大小如何，45°角的对边与邻边比值都等于1⑤得出结论：比值只与角度有关，与三角形大小无关。",
    "analysis": "通过实际测量验证了锐角三角函数概念的核心思想"
  }
]',
'[
  {
    "concept": "比值不变性",
    "explanation": "相同角度的三角形中对应边比值恒定",
    "example": "所有30°直角三角形的对边与斜边比值都是1/2"
  },
  {
    "concept": "角度决定性",
    "explanation": "角度的大小唯一确定了边长比值",
    "example": "角度是自变量，比值是因变量"
  },
  {
    "concept": "实用价值",
    "explanation": "解决实际测量和计算问题的数学工具",
    "example": "测量建筑物高度、计算坡度等"
  }
]',
'[
  "认为比值与三角形大小有关",
  "混淆角度与边长的关系",
  "不理解相似三角形的性质",
  "忽视三角函数的实际意义"
]',
'[
  "实验验证法：通过测量不同三角形验证比值不变性",
  "概念理解法：深刻理解角度决定比值的本质",
  "类比学习法：类比函数概念理解三角函数",
  "应用导向法：结合实际应用理解概念价值"
]',
'{
  "emphasis": ["历史发展", "实际需求"],
  "application": ["古代测量", "生活应用"],
  "connection": ["数学史", "文化传承"]
}',
'{
  "emphasis": ["概念本质", "逻辑推理"],
  "application": ["数学理论", "抽象思维"],
  "connection": ["函数概念", "几何代数化"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'),

-- MATH_G9S2_CH28_002: 正弦
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S2_CH28_002'),
'正弦是锐角三角函数的基本概念，定义为对边与斜边的比值',
'正弦是锐角三角函数中最重要的基本概念之一。在直角三角形中，锐角A的正弦值定义为角A的对边长度与斜边长度的比值，用符号sin A表示。正弦函数具有重要的数学性质：对于锐角，正弦值在0和1之间；正弦值随角度增大而增大；正弦函数是周期函数的基础。正弦的概念不仅在数学中占有重要地位，在物理学的波动现象、工程学的信号分析、天文学的周期运动等领域都有广泛应用。掌握正弦概念是学习三角学的关键基础。',
'[
  "定义为直角三角形中对边与斜边的比值",
  "用符号sin A表示锐角A的正弦值",
  "锐角的正弦值在0和1之间",
  "正弦值随角度增大而增大",
  "是周期函数和波动现象的数学基础"
]',
'[
  {
    "name": "正弦定义",
    "formula": "sin A = 对边/斜边",
    "description": "锐角A的正弦值的基本定义"
  },
  {
    "name": "取值范围",
    "formula": "0 < sin A < 1 (锐角A)",
    "description": "锐角正弦的值域范围"
  },
  {
    "name": "单调性",
    "formula": "0° < A₁ < A₂ < 90° ⟹ sin A₁ < sin A₂",
    "description": "锐角范围内正弦函数的单调递增性"
  }
]',
'[
  {
    "title": "计算特殊角的正弦值",
    "problem": "在直角三角形ABC中，∠C=90°，∠A=30°，BC=3cm，求sin A的值",
    "solution": "在30°-60°-90°直角三角形中，三边之比为1:√3:2。由BC=3cm（30°角的对边），得AC=3√3cm，AB=6cm。因此：sin 30° = BC/AB = 3/6 = 1/2。",
    "analysis": "特殊角的正弦值是三角函数学习的重要内容，30°角的正弦值为1/2"
  }
]',
'[
  {
    "concept": "对边识别",
    "explanation": "正确识别直角三角形中角的对边",
    "example": "对边是不与该角相邻的直角边"
  },
  {
    "concept": "比值性质",
    "explanation": "正弦是两个长度的比值，无量纲",
    "example": "结果是纯数值，没有长度单位"
  },
  {
    "concept": "几何意义",
    "explanation": "正弦值反映了角度大小的几何特征",
    "example": "角度越大，对边相对越长，正弦值越大"
  }
]',
'[
  "混淆对边和邻边的概念",
  "认为正弦值有长度量纲",
  "不理解正弦的几何意义",
  "记错正弦函数的定义"
]',
'[
  "图形标注法：在直角三角形中明确标注对边和斜边",
  "比值理解法：强调正弦是比值而非长度",
  "特例记忆法：熟记30°、45°、60°的正弦值",
  "几何直观法：通过几何图形理解正弦的意义"
]',
'{
  "emphasis": ["几何直观", "概念理解"],
  "application": ["日常测量", "艺术设计"],
  "connection": ["几何美学", "实用数学"]
}',
'{
  "emphasis": ["严格定义", "逻辑推理"],
  "application": ["数学证明", "理论计算"],
  "connection": ["函数理论", "数学分析"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'),

-- MATH_G9S2_CH28_003: 余弦
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S2_CH28_003'),
'余弦是锐角三角函数的基本概念，定义为邻边与斜边的比值',
'余弦是锐角三角函数的另一个基础概念。在直角三角形中，锐角A的余弦值定义为角A的邻边长度与斜边长度的比值，用符号cos A表示。余弦函数与正弦函数既有密切联系又有本质区别：余弦值也在0和1之间，但随角度增大而减小；余弦与正弦互为余函数，即cos A = sin(90°-A)。余弦在数学、物理、工程等领域都有重要应用，特别是在描述投影关系、功率计算、振动分析等方面发挥关键作用。理解余弦概念对掌握三角恒等式和解决实际问题都具有重要意义。',
'[
  "定义为直角三角形中邻边与斜边的比值",
  "用符号cos A表示锐角A的余弦值",
  "锐角的余弦值在0和1之间",
  "余弦值随角度增大而减小",
  "与正弦函数互为余函数关系"
]',
'[
  {
    "name": "余弦定义",
    "formula": "cos A = 邻边/斜边",
    "description": "锐角A的余弦值的基本定义"
  },
  {
    "name": "取值范围",
    "formula": "0 < cos A < 1 (锐角A)",
    "description": "锐角余弦的值域范围"
  },
  {
    "name": "单调性",
    "formula": "0° < A₁ < A₂ < 90° ⟹ cos A₁ > cos A₂",
    "description": "锐角范围内余弦函数的单调递减性"
  },
  {
    "name": "余角关系",
    "formula": "cos A = sin(90° - A)",
    "description": "余弦与正弦的余角关系"
  }
]',
'[
  {
    "title": "计算特殊角的余弦值",
    "problem": "在直角三角形ABC中，∠C=90°，∠A=60°，AB=8cm，求cos A的值",
    "solution": "在30°-60°-90°直角三角形中，三边之比为1:√3:2。由AB=8cm（斜边），得AC=4cm（60°角的邻边），BC=4√3cm。因此：cos 60° = AC/AB = 4/8 = 1/2。",
    "analysis": "60°角的余弦值为1/2，这与30°角的正弦值相等，体现了余角关系"
  }
]',
'[
  {
    "concept": "邻边识别",
    "explanation": "正确识别直角三角形中角的邻边",
    "example": "邻边是与该角相邻的直角边"
  },
  {
    "concept": "单调性质",
    "explanation": "余弦函数在锐角范围内单调递减",
    "example": "角度越大，邻边相对越短，余弦值越小"
  },
  {
    "concept": "余角互补",
    "explanation": "余弦与正弦的互补关系",
    "example": "cos 30° = sin 60° = √3/2"
  }
]',
'[
  "混淆邻边和对边的概念",
  "搞错余弦函数的单调性",
  "不理解余弦与正弦的关系",
  "记错余弦函数的定义"
]',
'[
  "边的区分法：明确区分邻边、对边、斜边",
  "单调性记忆法：余弦随角度增大而减小",
  "对比学习法：对比余弦与正弦的异同",
  "余角关系法：利用余角关系理解余弦"
]',
'{
  "emphasis": ["几何关系", "空间概念"],
  "application": ["建筑设计", "投影计算"],
  "connection": ["空间几何", "实用计算"]
}',
'{
  "emphasis": ["函数性质", "理论分析"],
  "application": ["数学推理", "函数研究"],
  "connection": ["函数理论", "数学分析"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G9S2_CH28_004: 正切
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S2_CH28_004'),
'正切是锐角三角函数的基本概念，定义为对边与邻边的比值',
'正切是锐角三角函数的第三个基本概念。在直角三角形中，锐角A的正切值定义为角A的对边长度与邻边长度的比值，用符号tan A表示。正切函数具有独特的性质：正切值可以大于1；正切值随角度增大而增大；当角度接近90°时，正切值趋于无穷大。正切函数在实际应用中有特殊的几何意义，它直接反映了坡度、倾斜程度等概念。在工程测量、坡度计算、屋顶设计等领域，正切函数是最直接和实用的工具。掌握正切概念对理解斜率、导数等后续数学概念也有重要意义。',
'[
  "定义为直角三角形中对边与邻边的比值",
  "用符号tan A表示锐角A的正切值",
  "正切值可以大于1，没有上界",
  "正切值随角度增大而增大",
  "直接反映坡度和倾斜程度"
]',
'[
  {
    "name": "正切定义",
    "formula": "tan A = 对边/邻边",
    "description": "锐角A的正切值的基本定义"
  },
  {
    "name": "取值范围",
    "formula": "tan A > 0 (锐角A)",
    "description": "锐角正切值为正数，无上界"
  },
  {
    "name": "单调性",
    "formula": "0° < A₁ < A₂ < 90° ⟹ tan A₁ < tan A₂",
    "description": "锐角范围内正切函数的单调递增性"
  },
  {
    "name": "比值关系",
    "formula": "tan A = sin A / cos A",
    "description": "正切与正弦、余弦的关系"
  }
]',
'[
  {
    "title": "计算45°角的正切值",
    "problem": "在等腰直角三角形ABC中，∠C=90°，∠A=∠B=45°，AC=BC=5cm，求tan A的值",
    "solution": "在等腰直角三角形中，两直角边相等。对于∠A：对边BC=5cm，邻边AC=5cm。因此：tan 45° = BC/AC = 5/5 = 1。",
    "analysis": "45°角的正切值为1，这是一个重要的特殊值，反映了等腰直角三角形的性质"
  }
]',
'[
  {
    "concept": "坡度意义",
    "explanation": "正切值直接表示坡度或倾斜程度",
    "example": "坡度为1:2表示正切值为0.5"
  },
  {
    "concept": "无界性质",
    "explanation": "正切值可以任意大，没有上界",
    "example": "当角度接近90°时，正切值趋于无穷"
  },
  {
    "concept": "比值独立性",
    "explanation": "正切值不涉及斜边，只与两直角边有关",
    "example": "只需知道对边和邻边的比例"
  }
]',
'[
  "认为正切值不能大于1",
  "混淆正切与正弦、余弦的关系",
  "不理解正切的几何意义",
  "搞错对边和邻边的比值顺序"
]',
'[
  "坡度理解法：通过坡度概念理解正切的意义",
  "比值强化法：强调正切是对边比邻边",
  "特值记忆法：熟记45°正切值为1",
  "几何直观法：通过图形理解正切的几何意义"
]',
'{
  "emphasis": ["实用价值", "几何直观"],
  "application": ["坡度计算", "建筑设计"],
  "connection": ["实际测量", "工程应用"]
}',
'{
  "emphasis": ["函数性质", "理论关系"],
  "application": ["数学分析", "理论推导"],
  "connection": ["函数理论", "微积分基础"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'),

-- MATH_G9S2_CH28_005: 三角函数的定义
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S2_CH28_005'),
'三角函数是角度与比值对应关系的系统性数学概念',
'三角函数的定义是将正弦、余弦、正切等概念统一为一个完整的数学体系。在直角三角形中，对于锐角A，我们定义：sin A = 对边/斜边，cos A = 邻边/斜边，tan A = 对边/邻边。这三个函数构成了锐角三角函数的基本体系，它们之间存在密切的内在联系。三角函数的定义不仅为解决几何问题提供了工具，更重要的是建立了角度与数值之间的函数关系，为后续学习三角恒等式、解三角形、研究周期现象等奠定了理论基础。这个定义体系体现了数学的系统性和完整性。',
'[
  "统一定义正弦、余弦、正切三个基本函数",
  "建立角度与数值的函数对应关系",
  "构成完整的锐角三角函数体系",
  "为解决几何和物理问题提供工具",
  "体现数学概念的系统性和完整性"
]',
'[
  {
    "name": "正弦函数",
    "formula": "sin A = 对边/斜边",
    "description": "角A的正弦函数定义"
  },
  {
    "name": "余弦函数",
    "formula": "cos A = 邻边/斜边",
    "description": "角A的余弦函数定义"
  },
  {
    "name": "正切函数",
    "formula": "tan A = 对边/邻边",
    "description": "角A的正切函数定义"
  },
  {
    "name": "基本关系",
    "formula": "tan A = sin A / cos A",
    "description": "三角函数之间的基本关系"
  }
]',
'[
  {
    "title": "三角函数的系统应用",
    "problem": "在直角三角形ABC中，∠C=90°，∠A=α，边长分别为a、b、c（a为∠A的对边，b为∠A的邻边，c为斜边），写出∠A的三个三角函数值",
    "solution": "根据三角函数的定义：①sin α = a/c（对边比斜边）②cos α = b/c（邻边比斜边）③tan α = a/b（对边比邻边）。这三个函数值完全确定了角α的大小，体现了三角函数的完整性。",
    "analysis": "三角函数的系统定义为解决各种三角形问题提供了完整的工具"
  }
]',
'[
  {
    "concept": "系统完整性",
    "explanation": "三个函数构成完整的描述角度的数学体系",
    "example": "正弦、余弦、正切各有不同的几何意义和应用"
  },
  {
    "concept": "函数对应",
    "explanation": "每个角度对应唯一的三角函数值",
    "example": "角度确定，三个函数值都确定"
  },
  {
    "concept": "内在联系",
    "explanation": "三个函数之间存在密切的数学关系",
    "example": "tan A = sin A / cos A等恒等式"
  }
]',
'[
  "孤立地理解每个三角函数",
  "不理解三角函数的系统性",
  "混淆不同函数的定义和应用",
  "忽视三角函数之间的内在联系"
]',
'[
  "系统理解法：将三角函数作为整体系统学习",
  "对比学习法：对比三个函数的定义和性质",
  "联系强化法：强调三角函数之间的内在联系",
  "应用统一法：用统一的方法解决三角形问题"
]',
'{
  "emphasis": ["系统思维", "整体理解"],
  "application": ["综合应用", "系统设计"],
  "connection": ["系统思维", "整体把握"]
}',
'{
  "emphasis": ["理论体系", "逻辑结构"],
  "application": ["理论建构", "逻辑推理"],
  "connection": ["数学体系", "理论框架"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G9S2_CH28_006: 特殊角的三角函数值
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S2_CH28_006'),
'特殊角30°、45°、60°的三角函数值是三角学的基础数据',
'特殊角的三角函数值是三角学中最重要的基础数据。30°、45°、60°这三个特殊角的三角函数值具有简洁的表达形式，在数学计算和实际应用中频繁出现。这些特殊值不是偶然的，而是来源于特殊的几何图形：30°和60°来自等边三角形的性质，45°来自等腰直角三角形的性质。掌握这些特殊值不仅能提高计算效率，更重要的是为理解三角函数的性质和变化规律提供了关键的参考点。在工程设计、建筑结构、物理计算等领域，这些特殊角度经常出现，其三角函数值的准确掌握是解决实际问题的基础。',
'[
  "30°、45°、60°是最重要的特殊角",
  "特殊角的三角函数值有简洁的表达形式",
  "来源于等边三角形和等腰直角三角形",
  "为理解三角函数性质提供参考点",
  "在实际应用中频繁出现"
]',
'[
  {
    "name": "30°角的三角函数值",
    "formula": "sin 30° = 1/2, cos 30° = √3/2, tan 30° = √3/3",
    "description": "30°角的三个基本三角函数值"
  },
  {
    "name": "45°角的三角函数值",
    "formula": "sin 45° = √2/2, cos 45° = √2/2, tan 45° = 1",
    "description": "45°角的三个基本三角函数值"
  },
  {
    "name": "60°角的三角函数值",
    "formula": "sin 60° = √3/2, cos 60° = 1/2, tan 60° = √3",
    "description": "60°角的三个基本三角函数值"
  },
  {
    "name": "余角关系验证",
    "formula": "sin 30° = cos 60°, sin 60° = cos 30°",
    "description": "特殊角体现的余角关系"
  }
]',
'[
  {
    "title": "推导30°角的三角函数值",
    "problem": "在边长为2的等边三角形ABC中，从顶点A向BC作垂线AD，求∠BAD的三角函数值",
    "solution": "①等边三角形三个角都是60°，AD是角平分线，所以∠BAD=30°②由等边三角形性质：BD=1，AD=√3③在直角三角形ABD中：sin 30°=BD/AB=1/2，cos 30°=AD/AB=√3/2，tan 30°=BD/AD=1/√3=√3/3",
    "analysis": "通过等边三角形的几何性质推导出30°角的精确三角函数值"
  }
]',
'[
  {
    "concept": "几何来源",
    "explanation": "特殊角的三角函数值来源于特殊几何图形",
    "example": "30°和60°来自等边三角形，45°来自等腰直角三角形"
  },
  {
    "concept": "精确表达",
    "explanation": "特殊角的三角函数值可以用根式精确表示",
    "example": "sin 60° = √3/2，不是近似值而是精确值"
  },
  {
    "concept": "余角对称",
    "explanation": "30°和60°、45°和45°互为余角，体现对称性",
    "example": "sin 30° = cos 60° = 1/2"
  }
]',
'[
  "记错特殊角的三角函数值",
  "不理解特殊角的几何来源",
  "混淆30°和60°的三角函数值",
  "把根式表达写成小数近似值"
]',
'[
  "几何推导法：通过特殊三角形推导特殊值",
  "对称记忆法：利用余角关系记忆",
  "图表整理法：制作特殊角三角函数值表",
  "反复练习法：通过大量练习熟练掌握"
]',
'{
  "emphasis": ["几何直观", "精确表达"],
  "application": ["建筑角度", "工艺设计"],
  "connection": ["几何美学", "精确科学"]
}',
'{
  "emphasis": ["数学精确性", "理论推导"],
  "application": ["理论计算", "数学证明"],
  "connection": ["数学严谨性", "逻辑推理"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'),

-- MATH_G9S2_CH28_007: 三角函数值的计算
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S2_CH28_007'),
'三角函数值的计算是应用三角函数解决实际问题的基本技能',
'三角函数值的计算包括直接计算和间接计算两种方式。直接计算是指已知直角三角形的边长，直接应用定义计算三角函数值；间接计算是指利用三角函数的性质、特殊值、恒等式等进行计算。计算过程中需要注意角度的正确识别、边长的准确测量或计算、结果的合理性检验。掌握三角函数值的计算方法是解决测量、导航、工程设计等实际问题的基础。计算技能的培养不仅要求学生熟练掌握计算步骤，更要理解计算的几何意义和实际背景，形成从问题到数学模型再到计算结果的完整思维链条。',
'[
  "包括直接计算和间接计算两种方式",
  "需要正确识别角度和边长关系",
  "要注意结果的合理性检验",
  "是解决实际问题的基础技能",
  "需要理解计算的几何意义"
]',
'[
  {
    "name": "直接计算公式",
    "formula": "已知边长a、b、c，求sin A = a/c, cos A = b/c, tan A = a/b",
    "description": "根据三角形边长直接计算三角函数值"
  },
  {
    "name": "勾股定理应用",
    "formula": "a² + b² = c²",
    "description": "利用勾股定理求未知边长"
  },
  {
    "name": "比例关系",
    "formula": "利用相似三角形的比例关系",
    "description": "通过比例关系简化计算"
  },
  {
    "name": "特殊值应用",
    "formula": "利用30°、45°、60°的已知函数值",
    "description": "通过特殊值进行快速计算"
  }
]',
'[
  {
    "title": "计算非特殊角的三角函数值",
    "problem": "在直角三角形ABC中，∠C=90°，AC=3，BC=4，求∠A的三个三角函数值",
    "solution": "①先用勾股定理求斜边：AB=√(AC²+BC²)=√(3²+4²)=√25=5②确定∠A的对边和邻边：对边BC=4，邻边AC=3，斜边AB=5③计算三角函数值：sin A=4/5=0.8，cos A=3/5=0.6，tan A=4/3≈1.33",
    "analysis": "通过勾股定理和三角函数定义，可以精确计算任意直角三角形中角的三角函数值"
  }
]',
'[
  {
    "concept": "计算步骤",
    "explanation": "按照确定的步骤进行系统计算",
    "example": "先求边长，再识别关系，最后计算函数值"
  },
  {
    "concept": "精度控制",
    "explanation": "根据实际需要控制计算精度",
    "example": "工程计算通常保留3-4位有效数字"
  },
  {
    "concept": "结果检验",
    "explanation": "通过多种方法验证计算结果",
    "example": "利用sin²A + cos²A = 1检验"
  }
]',
'[
  "计算步骤混乱，缺乏系统性",
  "不注意计算精度的控制",
  "忽视结果的合理性检验",
  "混淆对边、邻边、斜边的关系"
]',
'[
  "步骤化计算法：按固定步骤进行计算",
  "图形辅助法：画图帮助理解边角关系",
  "检验确认法：用多种方法验证结果",
  "实例练习法：通过大量实例提高熟练度"
]',
'{
  "emphasis": ["实用技能", "精确计算"],
  "application": ["工程测量", "建筑设计"],
  "connection": ["实用数学", "技术应用"]
}',
'{
  "emphasis": ["计算技巧", "理论应用"],
  "application": ["数学计算", "理论研究"],
  "connection": ["计算数学", "数值分析"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G9S2_CH28_008: 锐角三角函数的性质
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S2_CH28_008'),
'锐角三角函数具有单调性、有界性、余角关系等重要数学性质',
'锐角三角函数的性质是三角学理论的重要组成部分。在锐角范围内，正弦函数和正切函数都是单调递增的，余弦函数是单调递减的；正弦值和余弦值都在0和1之间，正切值可以任意大；同一角的正弦值和余弦值满足平方和等于1的恒等式；互为余角的两个锐角的正弦值和余弦值可以互换。这些性质不仅具有重要的理论意义，在实际应用中也发挥着关键作用，例如在函数图象分析、不等式证明、最值问题求解等方面都有广泛应用。深入理解这些性质有助于学生建立完整的三角函数概念体系。',
'[
  "正弦函数和正切函数在锐角范围内单调递增",
  "余弦函数在锐角范围内单调递减",
  "正弦值和余弦值的取值范围都是(0,1)",
  "同一角的正弦和余弦满足平方和恒等式",
  "互为余角的锐角具有特殊的函数值关系"
]',
'[
  {
    "name": "正弦函数单调性",
    "formula": "0° < α < β < 90° ⟹ sin α < sin β",
    "description": "锐角正弦函数的单调递增性"
  },
  {
    "name": "余弦函数单调性",
    "formula": "0° < α < β < 90° ⟹ cos α > cos β",
    "description": "锐角余弦函数的单调递减性"
  },
  {
    "name": "平方和恒等式",
    "formula": "sin²A + cos²A = 1",
    "description": "同角正弦和余弦的平方和恒等于1"
  },
  {
    "name": "余角公式",
    "formula": "sin A = cos(90° - A), cos A = sin(90° - A)",
    "description": "互为余角的三角函数关系"
  }
]',
'[
  {
    "title": "利用三角函数性质比较大小",
    "problem": "不计算具体数值，比较sin 25°、sin 45°、sin 65°的大小关系",
    "solution": "根据正弦函数在锐角范围内的单调递增性：因为0° < 25° < 45° < 65° < 90°，所以sin 25° < sin 45° < sin 65°。可以进一步利用余角关系：sin 25° = cos 65°，所以sin 25° < sin 45° < sin 65°。",
    "analysis": "利用三角函数的单调性质可以不经过数值计算直接比较函数值大小"
  }
]',
'[
  {
    "concept": "单调性应用",
    "explanation": "利用单调性比较三角函数值大小",
    "example": "角度大，正弦值大；角度大，余弦值小"
  },
  {
    "concept": "有界性意义",
    "explanation": "正弦和余弦值的有界性反映了比值的几何限制",
    "example": "直角三角形中，直角边总是小于斜边"
  },
  {
    "concept": "恒等式应用",
    "explanation": "平方和恒等式是重要的计算和证明工具",
    "example": "已知sin A，可求cos A = √(1 - sin²A)"
  }
]',
'[
  "混淆不同函数的单调性",
  "不理解三角函数的有界性",
  "忽视余角关系的应用",
  "不会利用恒等式进行计算"
]',
'[
  "图象理解法：通过函数图象理解单调性",
  "性质归纳法：系统归纳各种性质",
  "对比学习法：对比不同函数的性质异同",
  "应用练习法：在实际问题中应用性质"
]',
'{
  "emphasis": ["规律发现", "性质理解"],
  "application": ["趋势分析", "规律把握"],
  "connection": ["数学规律", "自然法则"]
}',
'{
  "emphasis": ["理论性质", "逻辑推理"],
  "application": ["数学证明", "理论分析"],
  "connection": ["函数理论", "数学分析"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'),

-- MATH_G9S2_CH28_009: 三角函数的应用
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S2_CH28_009'),
'三角函数在测量、导航、工程等领域有广泛的实际应用',
'三角函数的应用是数学与现实世界联系的重要桥梁。在实际生活中，三角函数被广泛应用于高度测量、距离计算、角度确定、坡度设计等各个方面。无论是古代的测量术，还是现代的GPS导航、建筑工程、机械设计，都离不开三角函数的支持。三角函数应用的核心在于将实际问题转化为数学模型，通过建立直角三角形，识别已知条件和待求量，选择合适的三角函数关系式进行求解。这一过程不仅体现了数学的实用价值，也展现了数学建模思想的重要性，是培养学生应用数学解决实际问题能力的重要内容。',
'[
  "在测量、导航、工程等领域应用广泛",
  "核心是将实际问题转化为数学模型",
  "通过建立直角三角形解决问题",
  "体现数学的实用价值和建模思想",
  "是连接数学与现实世界的重要桥梁"
]',
'[
  {
    "name": "高度测量",
    "formula": "h = d × tan α (d为水平距离，α为仰角)",
    "description": "利用三角函数测量建筑物、山峰等的高度"
  },
  {
    "name": "距离计算",
    "formula": "d = h / tan α (h为高度，α为俯角)",
    "description": "通过已知高度和角度计算水平距离"
  },
  {
    "name": "坡度设计",
    "formula": "坡度 = tan α = 高度差/水平距离",
    "description": "道路、屋顶等的坡度设计计算"
  },
  {
    "name": "方向角确定",
    "formula": "利用正弦、余弦确定方向角",
    "description": "导航和定位中的方向角计算"
  }
]',
'[
  {
    "title": "测量建筑物高度",
    "problem": "小明站在距离一座大楼30米的地方，测得大楼顶部的仰角为53°，已知小明的眼睛距地面1.6米，求大楼的高度",
    "solution": "①画出示意图，设大楼高度为h米②在直角三角形中：tan 53° = (h-1.6)/30③查表或计算得tan 53° ≈ 1.33④解得：h-1.6 = 30×1.33 = 39.9⑤因此大楼高度：h = 39.9 + 1.6 = 41.5米",
    "analysis": "通过建立直角三角形模型，利用正切函数成功解决了实际测量问题"
  }
]',
'[
  {
    "concept": "建模思维",
    "explanation": "将实际问题抽象为数学模型",
    "example": "测量问题转化为直角三角形求解"
  },
  {
    "concept": "角度识别",
    "explanation": "正确识别和测量仰角、俯角等",
    "example": "仰角是视线与水平面的夹角"
  },
  {
    "concept": "精度要求",
    "explanation": "根据实际需要确定计算精度",
    "example": "建筑设计通常要求厘米级精度"
  }
]',
'[
  "不会将实际问题转化为数学模型",
  "混淆仰角、俯角等角度概念",
  "忽视实际情况的复杂因素",
  "计算精度与实际需求不符"
]',
'[
  "建模引导法：引导学生建立数学模型",
  "图形辅助法：通过画图理解实际问题",
  "案例分析法：分析典型应用案例",
  "实践体验法：实际测量体验应用过程"
]',
'{
  "emphasis": ["实际应用", "生活联系"],
  "application": ["日常测量", "生活问题"],
  "connection": ["生活数学", "实用技能"]
}',
'{
  "emphasis": ["数学建模", "理论应用"],
  "application": ["工程计算", "科学研究"],
  "connection": ["应用数学", "技术发展"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G9S2_CH28_010: 锐角三角函数综合
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S2_CH28_010'),
'锐角三角函数的综合应用整合了定义、性质、计算和实际应用',
'锐角三角函数的综合应用是对前面所学知识的系统整合和深化。它要求学生能够综合运用三角函数的定义、特殊值、性质和计算方法，解决复杂的几何和实际问题。综合应用的核心在于问题分析能力和知识整合能力，需要学生能够识别问题的本质，选择合适的知识点和方法，进行系统的推理和计算。这一阶段的学习不仅巩固了基础知识，更重要的是培养了数学思维和解决问题的能力。综合应用题往往涉及多个知识点的交叉运用，体现了数学知识的系统性和完整性，为后续学习解三角形等内容奠定了坚实基础。',
'[
  "整合三角函数的定义、性质、计算和应用",
  "要求具备问题分析和知识整合能力",
  "涉及复杂几何和实际问题的解决",
  "体现数学知识的系统性和完整性",
  "培养数学思维和解决问题的能力"
]',
'[
  {
    "name": "知识综合公式",
    "formula": "sin²A + cos²A = 1, tan A = sin A / cos A",
    "description": "基本恒等式的综合应用"
  },
  {
    "name": "特殊值应用",
    "formula": "利用30°、45°、60°的函数值解决复杂问题",
    "description": "特殊角在综合问题中的应用"
  },
  {
    "name": "几何关系式",
    "formula": "结合勾股定理和三角函数解决几何问题",
    "description": "几何与三角函数的综合运用"
  },
  {
    "name": "实际应用模型",
    "formula": "建立数学模型解决实际测量问题",
    "description": "实际问题的数学建模"
  }
]',
'[
  {
    "title": "综合性几何计算",
    "problem": "在等腰三角形ABC中，AB=AC=13，BC=10，求sin∠BAC的值",
    "solution": "①作AD⊥BC于D，由等腰三角形性质知BD=DC=5②在Rt△ABD中，由勾股定理：AD²=AB²-BD²=13²-5²=169-25=144，所以AD=12③要求sin∠BAC，需要∠BAC的对边，由于∠BAC不是直角三角形中的角，考虑∠BAD④在Rt△ABD中：sin∠BAD = BD/AB = 5/13⑤由于AD平分∠BAC，所以∠BAC = 2∠BAD，但这里要用另一种方法⑥设∠BAC = α，则在等腰三角形中利用面积公式：S = (1/2)×BC×AD = (1/2)×AB×AC×sin α⑦即：(1/2)×10×12 = (1/2)×13×13×sin α⑧解得：sin α = 120/169",
    "analysis": "综合运用等腰三角形性质、勾股定理、三角函数定义和面积公式解决复杂几何问题"
  }
]',
'[
  {
    "concept": "问题分析",
    "explanation": "系统分析问题的结构和要求",
    "example": "识别已知条件、待求量和可能的解题路径"
  },
  {
    "concept": "知识整合",
    "explanation": "将多个知识点有机结合解决问题",
    "example": "三角函数与几何、代数的综合运用"
  },
  {
    "concept": "方法选择",
    "explanation": "根据问题特点选择最优解题方法",
    "example": "直接计算vs间接推导的方法选择"
  }
]',
'[
  "知识点运用孤立，缺乏整合思维",
  "问题分析不充分，解题思路混乱",
  "不善于选择合适的解题方法",
  "计算过程中出现概念性错误"
]',
'[
  "系统梳理法：系统梳理相关知识点",
  "问题分解法：将复杂问题分解为简单问题",
  "方法对比法：对比不同解题方法的优劣",
  "错题分析法：通过错题分析提高解题能力"
]',
'{
  "emphasis": ["综合应用", "能力培养"],
  "application": ["复杂问题", "综合设计"],
  "connection": ["能力提升", "思维发展"]
}',
'{
  "emphasis": ["理论综合", "逻辑体系"],
  "application": ["数学研究", "理论证明"],
  "connection": ["数学体系", "逻辑思维"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G9S2_CH28_011: 解直角三角形的概念
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S2_CH28_011'),
'解直角三角形是指由已知条件求出直角三角形所有未知元素的过程',
'解直角三角形是三角学中的核心问题，它将三角函数的理论知识转化为解决实际问题的有力工具。在直角三角形中，有6个基本元素：3个角（包括一个直角）和3条边。由于直角已知，实际上只需要确定5个未知元素。根据三角函数关系和几何性质，通常只需要知道其中的2个独立元素（至少一条边），就能求出所有其他元素。解直角三角形的过程体现了数学中从已知到未知的推理思维，不仅在数学学习中具有重要地位，在测量、工程、物理等实际应用中更是不可或缺的基本技能。掌握解直角三角形的方法是进一步学习解斜三角形和应用数学的基础。',
'[
  "由已知条件求出直角三角形所有未知元素",
  "需要至少2个独立元素且至少包含一条边",
  "综合运用三角函数和几何性质",
  "体现从已知到未知的数学推理",
  "是测量和工程应用的基础技能"
]',
'[
  {
    "name": "基本关系式",
    "formula": "sin A = a/c, cos A = b/c, tan A = a/b",
    "description": "利用三角函数关系求边和角"
  },
  {
    "name": "角度关系",
    "formula": "A + B = 90° (A、B为两锐角)",
    "description": "直角三角形两锐角互余"
  },
  {
    "name": "勾股定理",
    "formula": "a² + b² = c²",
    "description": "利用边长关系求未知边"
  },
  {
    "name": "面积公式",
    "formula": "S = (1/2)ab = (1/2)bc·sin A = (1/2)ac·sin B",
    "description": "多种面积计算方法"
  }
]',
'[
  {
    "title": "解直角三角形的基本类型",
    "problem": "在直角三角形ABC中，∠C=90°，已知∠A=35°，c=20，求其他所有元素",
    "solution": "①由角度关系：∠B = 90° - 35° = 55°②由正弦函数：a = c·sin A = 20×sin 35° ≈ 20×0.574 = 11.48③由余弦函数：b = c·cos A = 20×cos 35° ≈ 20×0.819 = 16.38④验证：a² + b² = 11.48² + 16.38² ≈ 131.8 + 268.3 = 400.1 ≈ 20² ✓",
    "analysis": "已知一角一边的情况下，可以通过三角函数关系和角度关系求出所有未知元素"
  }
]',
'[
  {
    "concept": "解题策略",
    "explanation": "根据已知条件选择合适的关系式",
    "example": "已知角和斜边用正弦或余弦，已知两边用勾股定理"
  },
  {
    "concept": "元素完整性",
    "explanation": "确保求出三角形的所有边和角",
    "example": "3条边、2个锐角、1个直角（已知）"
  },
  {
    "concept": "结果验证",
    "explanation": "用不同方法验证计算结果的正确性",
    "example": "用勾股定理验证边长关系"
  }
]',
'[
  "已知条件不充分，无法唯一确定三角形",
  "选择的关系式与已知条件不匹配",
  "计算过程中出现数值错误",
  "忽视结果的验证和检查"
]',
'[
  "分析判断法：先分析已知条件的类型",
  "关系选择法：根据已知选择合适的数学关系",
  "步骤规范法：按照规范步骤进行求解",
  "验证确认法：多种方法验证计算结果"
]',
'{
  "emphasis": ["逻辑推理", "系统思维"],
  "application": ["实际测量", "工程计算"],
  "connection": ["逻辑思维", "实用技能"]
}',
'{
  "emphasis": ["数学理论", "严谨推导"],
  "application": ["理论研究", "数学证明"],
  "connection": ["数学体系", "逻辑结构"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'),

-- MATH_G9S2_CH28_012: 由边求角
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S2_CH28_012'),
'由三角形的边长关系求出对应角度的三角函数值和角度大小',
'由边求角是解直角三角形的重要类型，当已知三角形的边长关系时，需要通过三角函数的反向运算来求出角度。这个过程的核心是理解三角函数的反函数概念：已知三角函数值，求对应的角度。在实际计算中，我们通常先计算出三角函数值，然后通过查表、计算器或特殊值来确定角度大小。由边求角的方法在工程测量、建筑设计、物理实验等领域有重要应用，例如确定斜坡的倾斜角度、测量山坡的坡度角等。掌握这种方法不仅巩固了三角函数的概念，更为解决实际问题提供了有效途径。',
'[
  "已知边长关系求对应角度",
  "核心是三角函数的反向运算",
  "需要通过函数值确定角度大小",
  "在工程测量等领域应用广泛",
  "体现了函数与反函数的关系"
]',
'[
  {
    "name": "正弦反函数应用",
    "formula": "已知sin A = k，求角A",
    "description": "通过正弦值求角度"
  },
  {
    "name": "余弦反函数应用",
    "formula": "已知cos A = k，求角A",
    "description": "通过余弦值求角度"
  },
  {
    "name": "正切反函数应用",
    "formula": "已知tan A = k，求角A",
    "description": "通过正切值求角度"
  },
  {
    "name": "特殊值识别",
    "formula": "识别30°、45°、60°的函数值",
    "description": "快速识别特殊角度"
  }
]',
'[
  {
    "title": "利用边长比值求角度",
    "problem": "在直角三角形ABC中，∠C=90°，a=5，b=12，c=13，求∠A和∠B的大小",
    "solution": "①先验证是否为直角三角形：5² + 12² = 25 + 144 = 169 = 13²，确认为直角三角形②计算sin A：sin A = a/c = 5/13 ≈ 0.385③查表或用计算器：A = arcsin(0.385) ≈ 22.6°④由互余关系：∠B = 90° - 22.6° = 67.4°⑤验证：cos A = b/c = 12/13 ≈ 0.923，arccos(0.923) ≈ 22.6° ✓",
    "analysis": "通过边长比值计算三角函数值，再通过反函数求出角度"
  }
]',
'[
  {
    "concept": "反函数理解",
    "explanation": "理解三角函数与其反函数的关系",
    "example": "sin A = 0.5 ⟺ A = 30°（锐角范围内）"
  },
  {
    "concept": "计算精度",
    "explanation": "根据实际需要确定角度计算精度",
    "example": "测量中通常精确到0.1°或1°"
  },
  {
    "concept": "多重验证",
    "explanation": "用不同三角函数验证角度结果",
    "example": "用正弦求出的角度可用余弦验证"
  }
]',
'[
  "不理解反函数的概念",
  "计算三角函数值时出错",
  "角度精度控制不当",
  "忽视结果的合理性检验"
]',
'[
  "反函数理解法：深入理解函数与反函数关系",
  "多步验证法：用多种方法验证角度结果",
  "精度控制法：根据实际需要控制计算精度",
  "特殊值记忆法：熟记特殊角的函数值"
]',
'{
  "emphasis": ["反向思维", "实际应用"],
  "application": ["角度测量", "工程设计"],
  "connection": ["逆向思维", "实用技能"]
}',
'{
  "emphasis": ["反函数理论", "数学计算"],
  "application": ["理论计算", "数学分析"],
  "connection": ["函数理论", "数值计算"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G9S2_CH28_013: 由角求边
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S2_CH28_013'),
'由已知角度和部分边长关系求出三角形的其他边长',
'由角求边是解直角三角形的另一重要类型，当已知角度和某些边长时，利用三角函数的定义直接计算其他边长。这种方法直观易懂，是三角函数定义的直接应用。在实际问题中，由角求边的情况十分常见，比如已知建筑物的仰角和观察距离求建筑物高度，已知斜坡的倾斜角和斜面长度求水平距离等。这类问题的解决过程体现了数学建模的思想：将实际问题抽象为直角三角形模型，明确已知条件和待求量，选择合适的三角函数关系进行计算。掌握由角求边的方法是应用三角函数解决实际问题的基础。',
'[
  "由已知角度和部分边长求其他边长",
  "是三角函数定义的直接应用",
  "在实际测量问题中应用广泛",
  "体现数学建模的基本思想",
  "是解决实际问题的重要方法"
]',
'[
  {
    "name": "已知角和斜边求直角边",
    "formula": "a = c·sin A, b = c·cos A",
    "description": "利用正弦、余弦函数求直角边"
  },
  {
    "name": "已知角和一直角边求另一直角边",
    "formula": "b = a·cot A = a/tan A, a = b·tan A",
    "description": "利用正切函数求另一直角边"
  },
  {
    "name": "已知角和一直角边求斜边",
    "formula": "c = a/sin A = b/cos A",
    "description": "利用三角函数求斜边"
  },
  {
    "name": "多种方法验证",
    "formula": "用勾股定理验证：a² + b² = c²",
    "description": "验证计算结果的正确性"
  }
]',
'[
  {
    "title": "测量旗杆高度问题",
    "problem": "某人站在距离旗杆底部15米处，测得旗杆顶端的仰角为37°，已知此人眼睛距地面1.7米，求旗杆的高度",
    "solution": "①画出示意图，设旗杆高度为h米②在直角三角形中，水平距离为15米，仰角为37°③由正切函数：tan 37° = (h-1.7)/15④查表得tan 37° ≈ 0.754⑤解得：h-1.7 = 15×0.754 = 11.31⑥因此旗杆高度：h = 11.31 + 1.7 = 13.01米",
    "analysis": "通过建立直角三角形模型，利用正切函数解决实际测量问题"
  }
]',
'[
  {
    "concept": "函数选择",
    "explanation": "根据已知条件选择合适的三角函数",
    "example": "已知角和斜边选择正弦或余弦，已知角和邻边选择正切"
  },
  {
    "concept": "单位统一",
    "explanation": "确保所有长度量使用相同单位",
    "example": "统一用米或厘米作为长度单位"
  },
  {
    "concept": "实际意义",
    "explanation": "理解计算结果的实际物理意义",
    "example": "高度、距离等都应为正值"
  }
]',
'[
  "选择错误的三角函数关系",
  "混淆对边、邻边、斜边的关系",
  "计算过程中单位不统一",
  "忽视实际问题的背景条件"
]',
'[
  "关系识别法：明确角与边的对应关系",
  "图形辅助法：画图明确几何关系",
  "单位检查法：确保计算过程中单位统一",
  "实际验证法：检查结果的实际合理性"
]',
'{
  "emphasis": ["实际应用", "几何直观"],
  "application": ["高度测量", "距离计算"],
  "connection": ["实用数学", "几何应用"]
}',
'{
  "emphasis": ["理论计算", "精确计算"],
  "application": ["数学计算", "理论研究"],
  "connection": ["计算数学", "理论应用"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'),

-- MATH_G9S2_CH28_014: 解直角三角形的应用
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S2_CH28_014'),
'解直角三角形在测量、工程、物理等实际领域的综合应用',
'解直角三角形的应用是数学理论与实际问题结合的典型体现。在现实生活中，许多问题都可以转化为直角三角形模型来解决，如测量不可直接到达的高度和距离、计算工程中的角度和长度、分析物理中的力的分解等。这类应用问题的特点是背景丰富、情境真实、需要建模思维。解决这类问题的关键在于：正确理解题意，准确建立数学模型，合理选择求解方法，验证结果的实际意义。通过这些应用，学生不仅巩固了三角函数知识，更重要的是培养了数学建模能力和解决实际问题的能力，体验了数学的实用价值和美妙之处。',
'[
  "在测量、工程、物理等领域广泛应用",
  "需要将实际问题转化为数学模型",
  "考查数学建模和解决问题的能力",
  "体现数学理论与实际的结合",
  "培养应用数学解决实际问题的能力"
]',
'[
  {
    "name": "高度测量模型",
    "formula": "h = d·tan α + h₀ (d为水平距离，α为仰角，h₀为观察高度)",
    "description": "测量建筑物、山峰等高度"
  },
  {
    "name": "距离测量模型",
    "formula": "d = h/tan α (h为高度差，α为俯角)",
    "description": "测量水平距离或斜面距离"
  },
  {
    "name": "角度确定模型",
    "formula": "α = arctan(h/d)",
    "description": "确定坡度角、仰角、俯角等"
  },
  {
    "name": "分力计算模型",
    "formula": "Fx = F·cos α, Fy = F·sin α",
    "description": "物理中力的分解应用"
  }
]',
'[
  {
    "title": "综合测量应用",
    "problem": "为了测量河对岸一座塔的高度，在河的这一边选择A、B两点，测得AB=120m，从A点测得塔顶的仰角为30°，从B点测得塔顶的仰角为45°，且A、B、塔底在同一水平线上，求塔的高度",
    "solution": "①设塔高为h，塔底为C点，AC=x，则BC=120-x②在Rt△ACD中：tan 30° = h/x，即h = x·tan 30° = x/√3③在Rt△BCD中：tan 45° = h/(120-x)，即h = 120-x④由②③：x/√3 = 120-x⑤解得：x = 120√3/(√3+1) = 120√3(√3-1)/2 = 60(3-√3)⑥因此：h = 60(3-√3)/√3 = 60(√3-1) ≈ 60×0.732 = 43.9m",
    "analysis": "通过建立两个直角三角形模型，利用两个观测点的数据求解塔的高度"
  }
]',
'[
  {
    "concept": "建模能力",
    "explanation": "将复杂实际问题抽象为数学模型",
    "example": "多点观测问题转化为多个直角三角形"
  },
  {
    "concept": "综合应用",
    "explanation": "综合运用多种数学知识解决问题",
    "example": "三角函数、方程、几何等知识的综合"
  },
  {
    "concept": "实际验证",
    "explanation": "验证数学结果的实际合理性",
    "example": "检查高度、距离、角度的合理范围"
  }
]',
'[
  "不能正确理解实际问题的背景",
  "建立数学模型时出现错误",
  "计算过程中忽视实际约束条件",
  "结果缺乏实际意义的检验"
]',
'[
  "情境分析法：深入理解问题的实际背景",
  "建模指导法：引导学生建立正确的数学模型",
  "综合应用法：综合运用多种数学知识",
  "实际检验法：验证结果的实际合理性"
]',
'{
  "emphasis": ["实际应用", "综合能力"],
  "application": ["工程测量", "日常应用"],
  "connection": ["应用数学", "实践能力"]
}',
'{
  "emphasis": ["数学建模", "理论综合"],
  "application": ["科学研究", "工程计算"],
  "connection": ["应用数学", "科学技术"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G9S2_CH28_015: 方向角与坡度角
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S2_CH28_015'),
'方向角和坡度角是三角函数在导航和工程中的重要应用概念',
'方向角和坡度角是解直角三角形在实际应用中的两个重要概念。方向角是指从某一基准方向（通常是正北方向）顺时针转到目标方向所成的角度，在导航、测绘、军事等领域广泛应用；坡度角是指斜面与水平面所成的角度，在道路设计、建筑工程、地形测量等方面具有重要意义。这两个概念的核心都是通过三角函数建立角度与距离、高度等量之间的关系。掌握方向角和坡度角的概念及计算方法，不仅能够解决具体的实际问题，更重要的是培养学生的空间想象能力和实际应用能力，体现了数学与生活的紧密联系。',
'[
  "方向角用于导航和位置确定",
  "坡度角用于工程设计和地形分析",
  "都通过三角函数建立角度与距离的关系",
  "在实际生活中有广泛应用",
  "体现数学与实际生活的紧密联系"
]',
'[
  {
    "name": "方向角定义",
    "formula": "从正北方向顺时针测量的角度",
    "description": "导航中确定目标方向的角度"
  },
  {
    "name": "坡度角定义",
    "formula": "斜面与水平面的夹角α",
    "description": "表示倾斜程度的角度"
  },
  {
    "name": "坡度计算",
    "formula": "坡度 = tan α = 高度差/水平距离",
    "description": "坡度的数值表示方法"
  },
  {
    "name": "坡度百分比",
    "formula": "坡度% = tan α × 100%",
    "description": "工程中常用的坡度表示法"
  }
]',
'[
  {
    "title": "道路坡度设计问题",
    "problem": "某山区公路在水平距离1200m内上升了240m，求这段公路的坡度角和坡度百分比",
    "solution": "①根据坡度定义：tan α = 高度差/水平距离 = 240/1200 = 0.2②求坡度角：α = arctan(0.2) ≈ 11.3°③计算坡度百分比：坡度% = tan α × 100% = 0.2 × 100% = 20%④答案：这段公路的坡度角约为11.3°，坡度百分比为20%",
    "analysis": "通过直角三角形模型计算道路的坡度角和坡度百分比，体现了三角函数在工程中的应用"
  }
]',
'[
  {
    "concept": "角度标准",
    "explanation": "理解不同领域中角度的测量标准",
    "example": "方向角从正北方向顺时针测量"
  },
  {
    "concept": "单位换算",
    "explanation": "掌握角度和百分比等不同表示方法",
    "example": "坡度角11.3°对应20%的坡度"
  },
  {
    "concept": "实际意义",
    "explanation": "理解角度数值的实际含义和应用价值",
    "example": "20%的坡度表示相当陡峭，需要注意安全"
  }
]',
'[
  "混淆不同角度的测量基准",
  "不理解坡度角与坡度百分比的关系",
  "忽视角度大小的实际意义",
  "计算中出现单位换算错误"
]',
'[
  "概念辨析法：明确区分不同角度概念",
  "实际体验法：通过实际测量体验角度含义",
  "换算练习法：熟练掌握不同表示方法的换算",
  "应用导向法：结合实际应用理解概念意义"
]',
'{
  "emphasis": ["实际应用", "空间概念"],
  "application": ["导航定位", "工程设计"],
  "connection": ["空间思维", "实用技能"]
}',
'{
  "emphasis": ["数学概念", "精确计算"],
  "application": ["理论计算", "工程数学"],
  "connection": ["数学理论", "工程应用"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'),

-- MATH_G9S2_CH28_016: 解直角三角形的综合练习
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S2_CH28_016'),
'解直角三角形综合练习强化学生的计算技能和应用能力',
'解直角三角形的综合练习是对本章知识的系统巩固和深化应用。通过不同类型的练习，学生能够熟练掌握各种解三角形的方法，提高计算的准确性和速度，增强对三角函数应用的理解。综合练习包括基础计算练习、应用问题练习、综合分析练习等多个层次，既有对基本概念和方法的强化，也有对复杂问题的综合分析。通过系统的练习，学生不仅能够巩固所学知识，更重要的是能够形成解决三角形问题的一般性思路和方法，提高数学思维能力和问题解决能力。这种综合性的练习是连接理论学习与实际应用的重要桥梁。',
'[
  "系统巩固解直角三角形的各种方法",
  "提高计算准确性和解题速度",
  "包含基础、应用、综合等多层次练习",
  "培养解决三角形问题的一般思路",
  "是理论学习与实际应用的桥梁"
]',
'[
  {
    "name": "基础计算练习",
    "formula": "熟练运用sin A、cos A、tan A的定义",
    "description": "强化基本三角函数计算能力"
  },
  {
    "name": "综合解题练习",
    "formula": "综合运用三角函数、勾股定理等",
    "description": "提高综合解题能力"
  },
  {
    "name": "应用问题练习",
    "formula": "建立数学模型解决实际问题",
    "description": "增强实际应用能力"
  },
  {
    "name": "检验方法练习",
    "formula": "多种方法验证结果正确性",
    "description": "培养严谨的数学态度"
  }
]',
'[
  {
    "title": "综合性解三角形练习",
    "problem": "如图，在一个直角三角形游乐场设计中，需要建造一个观景台。已知从地面点A到观景台顶部B的距离为50米，仰角为37°；从另一个地面点C（与A在同一水平线上）到观景台顶部B的距离为40米，求AC之间的距离",
    "solution": "①画出示意图，设观景台高度为h，A到观景台底部的水平距离为x₁，C到观景台底部的水平距离为x₂②在直角三角形中，由A点的条件：sin 37° = h/50，所以h = 50×sin 37° ≈ 50×0.6 = 30米③cos 37° = x₁/50，所以x₁ = 50×cos 37° ≈ 50×0.8 = 40米④设C点到观景台底部距离为x₂，∠BCH = β（H为观景台底部）⑤在直角三角形BCH中：sin β = h/40 = 30/40 = 0.75，所以β = arcsin(0.75) ≈ 48.6°⑥cos β = x₂/40，所以x₂ = 40×cos 48.6° ≈ 40×0.66 = 26.4米⑦因此AC距离 = |x₁ - x₂| = |40 - 26.4| = 13.6米",
    "analysis": "综合运用正弦、余弦函数和反三角函数，通过多步计算解决复杂的几何设计问题"
  }
]',
'[
  {
    "concept": "问题分类",
    "explanation": "按照不同类型分类练习，形成解题模式",
    "example": "计算型、应用型、证明型等不同类型"
  },
  {
    "concept": "方法归纳",
    "explanation": "归纳总结不同情况下的解题方法",
    "example": "已知边求角、已知角求边等标准方法"
  },
  {
    "concept": "能力提升",
    "explanation": "通过练习提升综合分析和解决问题的能力",
    "example": "从单一知识点到综合应用"
  }
]',
'[
  "练习缺乏系统性，随意性强",
  "只注重计算，忽视理解和应用",
  "不注重解题方法的总结归纳",
  "缺乏对错误的分析和反思"
]',
'[
  "分层练习法：从基础到综合循序渐进",
  "归类总结法：按类型总结解题方法",
  "错题分析法：深入分析错误原因",
  "能力导向法：注重能力培养而非单纯计算"
]',
'{
  "emphasis": ["技能熟练", "综合应用"],
  "application": ["解题技巧", "实际运用"],
  "connection": ["技能巩固", "能力提升"]
}',
'{
  "emphasis": ["系统训练", "方法掌握"],
  "application": ["数学计算", "逻辑推理"],
  "connection": ["知识巩固", "思维发展"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G9S2_CH28_017: 测量中的三角函数
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S2_CH28_017'),
'三角函数在各种测量活动中的具体应用和操作方法',
'测量中的三角函数是数学知识在实际生活中的重要应用领域。从古代的大地测量到现代的工程建设，从简单的高度测量到复杂的GPS导航，三角函数都发挥着不可替代的作用。在实际测量中，我们经常需要测量一些无法直接到达或直接测量的距离和高度，这时三角函数就成为了解决问题的有力工具。测量活动不仅需要掌握三角函数的理论知识，更需要了解实际测量的方法、工具的使用、误差的控制等实践技能。通过学习测量中的三角函数应用，学生能够深刻体会数学的实用价值，培养动手实践能力和科学素养。',
'[
  "三角函数在测量中有广泛应用",
  "可以测量无法直接到达的距离和高度",
  "需要掌握测量方法和工具使用",
  "要注意误差控制和精度要求",
  "体现数学的实用价值和科学性"
]',
'[
  {
    "name": "测角仪器",
    "formula": "使用经纬仪、测角器等测量角度",
    "description": "专业测量工具的使用方法"
  },
  {
    "name": "高度测量",
    "formula": "h = d·tan α + h₀",
    "description": "利用仰角或俯角测量高度"
  },
  {
    "name": "距离测量",
    "formula": "d = h/tan α",
    "description": "通过高度和角度计算距离"
  },
  {
    "name": "误差控制",
    "formula": "多次测量取平均值，估算误差范围",
    "description": "提高测量精度的方法"
  }
]',
'[
  {
    "title": "学校旗杆高度测量实践",
    "problem": "用简易测角器测量学校旗杆的高度，设计测量方案并实施",
    "solution": "①准备工具：自制测角器（量角器+重锤线）、卷尺、计算器②选择测量点：在距旗杆一定距离的平地上选择测量点B③测量数据：用卷尺测量B点到旗杆底部A的距离d=30m，用测角器测量从B点看旗杆顶部的仰角α=35°④计算高度：h = d·tan α = 30×tan 35° ≈ 30×0.7 = 21m⑤加上测量者眼高：总高度 = 21 + 1.6 = 22.6m⑥验证测量：换一个距离重新测量验证结果",
    "analysis": "通过实际测量活动，学生体验了三角函数在测量中的应用，培养了动手能力和科学精神"
  }
]',
'[
  {
    "concept": "测量原理",
    "explanation": "理解三角测量的基本原理和方法",
    "example": "通过已知边和角来计算未知边"
  },
  {
    "concept": "工具使用",
    "explanation": "掌握各种测量工具的正确使用方法",
    "example": "测角器的使用、卷尺的读数等"
  },
  {
    "concept": "精度意识",
    "explanation": "了解测量精度的重要性和控制方法",
    "example": "多次测量、误差分析、结果验证"
  }
]',
'[
  "不理解测量的基本原理",
  "测量工具使用不当",
  "忽视测量误差的存在",
  "缺乏验证意识"
]',
'[
  "实践体验法：通过实际测量体验原理",
  "工具训练法：熟练掌握测量工具使用",
  "精度强化法：强调测量精度的重要性",
  "验证习惯法：培养结果验证的好习惯"
]',
'{
  "emphasis": ["实践操作", "生活应用"],
  "application": ["实际测量", "生活技能"],
  "connection": ["实践能力", "科学素养"]
}',
'{
  "emphasis": ["测量原理", "精确计算"],
  "application": ["科学测量", "工程应用"],
  "connection": ["科学方法", "技术应用"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'),

-- MATH_G9S2_CH28_018: 建筑中的三角函数
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S2_CH28_018'),
'三角函数在建筑设计和工程建设中的重要应用',
'建筑中的三角函数应用体现了数学与工程技术的紧密结合。在建筑设计和施工过程中，三角函数被广泛应用于结构设计、角度计算、坡度确定、力学分析等各个方面。无论是屋顶的倾斜角度、楼梯的坡度、桥梁的拱形设计，还是地基的斜率、排水的坡度等，都需要运用三角函数进行精确计算。建筑中的三角函数应用不仅要求计算准确，更要考虑安全性、实用性、美观性等多重因素。通过学习建筑中的三角函数应用，学生能够了解数学在现代建筑中的重要作用，培养工程思维和实际应用能力，为将来可能的相关专业学习奠定基础。',
'[
  "在建筑设计和施工中应用广泛",
  "涉及角度、坡度、力学等多个方面",
  "要求计算准确并考虑多重因素",
  "体现数学与工程技术的结合",
  "培养工程思维和应用能力"
]',
'[
  {
    "name": "屋顶坡度设计",
    "formula": "坡度 = tan α = 屋顶高度/水平跨度的一半",
    "description": "确定屋顶的倾斜角度"
  },
  {
    "name": "楼梯设计",
    "formula": "踏步角度α，tan α = 踢面高度/踏面宽度",
    "description": "楼梯舒适度和安全性设计"
  },
  {
    "name": "斜拉索角度",
    "formula": "利用三角函数计算桥梁斜拉索的角度和长度",
    "description": "桥梁工程中的力学计算"
  },
  {
    "name": "坡道设计",
    "formula": "坡道坡度 = 高度差/水平距离",
    "description": "无障碍设施的坡度标准"
  }
]',
'[
  {
    "title": "屋顶角度设计问题",
    "problem": "某住宅的屋顶为等腰三角形，屋顶跨度为12米，考虑到排水和美观，要求屋顶坡度在30°到45°之间，求屋顶高度的取值范围",
    "solution": "①屋顶为等腰三角形，跨度12米，则底边一半为6米②设屋顶高度为h，屋顶坡度角为α③由三角函数关系：tan α = h/6④当α = 30°时：h = 6×tan 30° = 6×√3/3 = 2√3 ≈ 3.46米⑤当α = 45°时：h = 6×tan 45° = 6×1 = 6米⑥因此屋顶高度的取值范围为：2√3米 ≤ h ≤ 6米，即约3.46米到6米之间",
    "analysis": "通过三角函数计算确定了符合建筑要求的屋顶高度范围，体现了数学在建筑设计中的实用价值"
  }
]',
'[
  {
    "concept": "功能性设计",
    "explanation": "建筑设计需要满足功能性要求",
    "example": "屋顶坡度要利于排水，楼梯坡度要便于行走"
  },
  {
    "concept": "安全性考虑",
    "explanation": "角度和坡度的设计要确保使用安全",
    "example": "楼梯角度过陡会影响安全，坡道过陡不利于通行"
  },
  {
    "concept": "标准规范",
    "explanation": "建筑设计要遵循相关的标准和规范",
    "example": "无障碍坡道坡度不应超过1:12"
  }
]',
'[
  "只考虑数学计算，忽视实际功能",
  "不了解建筑设计的相关标准",
  "缺乏安全性和实用性意识",
  "脱离实际的理想化计算"
]',
'[
  "功能导向法：以建筑功能为导向进行设计",
  "标准学习法：了解相关的建筑标准和规范",
  "实例分析法：分析实际建筑中的角度应用",
  "综合考虑法：综合考虑多种因素进行设计"
]',
'{
  "emphasis": ["实用设计", "生活应用"],
  "application": ["建筑设计", "日常建设"],
  "connection": ["实用技术", "生活改善"]
}',
'{
  "emphasis": ["工程数学", "技术应用"],
  "application": ["建筑工程", "结构设计"],
  "connection": ["工程技术", "科学应用"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G9S2_CH28_019: 航海中的三角函数
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S2_CH28_019'),
'三角函数在航海导航和海洋工程中的专门应用',
'航海中的三角函数应用是三角学在交通运输和海洋工程领域的重要体现。在海上航行中，由于缺乏明显的地面参照物，三角函数成为确定位置、计算航向、测量距离的重要工具。从古代的天文导航到现代的GPS系统，从简单的方位角计算到复杂的海洋工程测量，三角函数都发挥着关键作用。航海中的三角函数应用具有精度要求高、误差影响大、实时性强等特点，任何小的计算错误都可能导致严重的后果。通过学习航海中的三角函数应用，学生能够了解数学在现代交通运输中的重要作用，培养严谨的科学态度和精确的计算能力。',
'[
  "在航海导航中发挥关键作用",
  "用于位置确定、航向计算、距离测量",
  "精度要求高，误差影响大",
  "从古代天文导航到现代GPS系统",
  "培养严谨的科学态度和计算能力"
]',
'[
  {
    "name": "方位角计算",
    "formula": "方位角 = arctan(东西方向位移/南北方向位移)",
    "description": "确定目标相对于当前位置的方向"
  },
  {
    "name": "航行距离计算",
    "formula": "利用三角函数计算两点间的实际距离",
    "description": "根据坐标差和方位角计算航行距离"
  },
  {
    "name": "航向修正",
    "formula": "考虑海流、风向等因素的航向修正计算",
    "description": "实际航海中的航向调整"
  },
  {
    "name": "位置定位",
    "formula": "通过多个参照点的角度测量确定当前位置",
    "description": "三角定位的基本原理"
  }
]',
'[
  {
    "title": "航海定位问题",
    "problem": "一艘船从港口A出发，先向东航行60海里到达点B，再向东北方向（与正东方向成45°角）航行80海里到达点C，求点C相对于港口A的距离和方位角",
    "solution": "①建立坐标系：以港口A为原点，正东为x轴正方向，正北为y轴正方向②点B的坐标：B(60, 0)③点C的位置：从B点向东北方向（45°角）航行80海里⑤C点坐标：Cx = 60 + 80×cos 45° = 60 + 80×√2/2 = 60 + 40√2⑥Cy = 0 + 80×sin 45° = 80×√2/2 = 40√2⑦计算AC距离：|AC| = √[(60+40√2)² + (40√2)²] = √[3600 + 4800√2 + 3200 + 3200] = √[10000 + 4800√2] ≈ √[10000 + 6788] = √16788 ≈ 129.6海里⑧计算方位角：tan θ = Cy/Cx = 40√2/(60+40√2) ≈ 56.6/116.6 ≈ 0.486⑨θ = arctan(0.486) ≈ 25.9°⑩答案：点C距离港口A约129.6海里，方位角约25.9°（东偏北25.9°）",
    "analysis": "通过建立坐标系和运用三角函数，成功解决了复杂的航海定位问题"
  }
]',
'[
  {
    "concept": "坐标系统",
    "explanation": "在航海中建立合适的坐标系统",
    "example": "以港口为原点，东西南北为坐标轴"
  },
  {
    "concept": "方位表示",
    "explanation": "掌握不同的方位角表示方法",
    "example": "东偏北30°、方位角030°等表示法"
  },
  {
    "concept": "精度要求",
    "explanation": "航海计算对精度有很高要求",
    "example": "位置误差可能导致航线偏离"
  }
]',
'[
  "坐标系建立不当",
  "方位角理解错误",
  "计算精度不够",
  "忽视实际航海因素"
]',
'[
  "坐标建模法：正确建立航海坐标系",
  "方位训练法：熟练掌握方位角表示",
  "精度强化法：强调计算精度的重要性",
  "实际联系法：结合实际航海情况分析"
]',
'{
  "emphasis": ["导航技能", "实际应用"],
  "application": ["航海导航", "交通运输"],
  "connection": ["实用技能", "交通技术"]
}',
'{
  "emphasis": ["精确计算", "科学导航"],
  "application": ["航海科学", "导航技术"],
  "connection": ["科学技术", "精密工程"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'),

-- MATH_G9S2_CH28_020: 物理中的三角函数
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S2_CH28_020'),
'三角函数在物理学中的重要应用，特别是力的分解和合成',
'物理中的三角函数应用是数学与自然科学紧密结合的典型体现。在物理学中，三角函数被广泛应用于力学、光学、电学、振动和波动等各个分支。特别是在力学中，力的分解和合成、斜面上物体的运动分析、圆周运动的参数计算等都离不开三角函数。三角函数不仅是物理问题的计算工具，更是理解物理现象本质的重要手段。通过三角函数，我们可以将复杂的物理过程分解为简单的数学关系，从而深入理解自然规律。学习物理中的三角函数应用，有助于培养学生的科学思维，提高分析和解决物理问题的能力，为进一步学习高中物理奠定坚实基础。',
'[
  "在物理学各分支中广泛应用",
  "特别重要的是力的分解和合成",
  "是理解物理现象的重要手段",
  "帮助将复杂过程分解为简单关系",
  "培养科学思维和物理分析能力"
]',
'[
  {
    "name": "力的分解",
    "formula": "Fx = F·cos θ, Fy = F·sin θ",
    "description": "将力分解为水平和竖直分量"
  },
  {
    "name": "力的合成",
    "formula": "F合 = √(Fx² + Fy²), tan α = Fy/Fx",
    "description": "由分力计算合力的大小和方向"
  },
  {
    "name": "斜面受力分析",
    "formula": "F∥ = mg·sin θ, F⊥ = mg·cos θ",
    "description": "物体在斜面上的受力分析"
  },
  {
    "name": "简谐振动",
    "formula": "x = A·sin(ωt + φ)",
    "description": "振动位移与时间的三角函数关系"
  }
]',
'[
  {
    "title": "斜面上物体的力学分析",
    "problem": "质量为5kg的物体放在倾角为30°的光滑斜面上，求物体沿斜面向下的加速度和对斜面的压力",
    "solution": "①分析受力：物体受重力mg = 5×10 = 50N，斜面支持力N②建立坐标系：沿斜面向下为x轴正方向，垂直斜面向上为y轴正方向③力的分解：重力沿斜面分量：Fx = mg·sin 30° = 50×0.5 = 25N⑤重力垂直斜面分量：Fy = mg·cos 30° = 50×√3/2 = 25√3N⑥由牛顿第二定律：沿x方向：ma = Fx = 25N⑦加速度：a = 25/5 = 5m/s²⑧垂直方向受力平衡：N = Fy = 25√3 ≈ 43.3N⑨答案：加速度为5m/s²，对斜面压力为25√3N",
    "analysis": "通过三角函数分解重力，成功分析了斜面上物体的运动和受力情况"
  }
]',
'[
  {
    "concept": "矢量分解",
    "explanation": "理解物理量的矢量性质和分解方法",
    "example": "力、速度、加速度等都是矢量"
  },
  {
    "concept": "坐标选择",
    "explanation": "合理选择坐标系简化问题分析",
    "example": "斜面问题选择沿斜面和垂直斜面的坐标"
  },
  {
    "concept": "物理意义",
    "explanation": "理解数学计算结果的物理含义",
    "example": "分力的方向和大小的物理意义"
  }
]',
'[
  "不理解矢量的分解概念",
  "坐标系选择不当",
  "忽视计算结果的物理意义",
  "数学计算与物理过程脱节"
]',
'[
  "矢量理解法：深入理解矢量的概念和性质",
  "坐标优化法：选择最适合的坐标系",
  "物理联系法：将数学计算与物理过程联系",
  "实例分析法：通过典型实例强化理解"
]',
'{
  "emphasis": ["科学应用", "物理理解"],
  "application": ["物理分析", "科学研究"],
  "connection": ["科学思维", "自然理解"]
}',
'{
  "emphasis": ["精确计算", "理论分析"],
  "application": ["物理计算", "科学研究"],
  "connection": ["数理结合", "科学方法"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G9S2_CH28_021: 数学活动：制作简易测角器
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S2_CH28_021'),
'通过制作和使用简易测角器的数学活动巩固三角函数知识',
'制作简易测角器的数学活动是将理论知识与实践操作相结合的综合性学习活动。通过亲手制作测角器，学生能够深入理解角度测量的原理，体验三角函数在实际测量中的应用。这项活动不仅巩固了三角函数的概念和计算方法，更重要的是培养了学生的动手能力、创新思维和科学精神。在制作过程中，学生需要考虑测量精度、仪器结构、使用方法等多个方面，这有助于培养工程思维和问题解决能力。通过实际测量活动，学生能够体验数学在现实生活中的应用价值，增强学习数学的兴趣和信心，形成理论与实践相结合的学习习惯。',
'[
  "将理论知识与实践操作相结合",
  "深入理解角度测量的原理",
  "培养动手能力和创新思维",
  "体验数学的实际应用价值",
  "形成理论与实践结合的学习习惯"
]',
'[
  {
    "name": "材料准备",
    "formula": "量角器、重锤线、吸管、硬纸板等",
    "description": "制作简易测角器的基本材料"
  },
  {
    "name": "制作原理",
    "formula": "利用重力方向确定垂直基准线",
    "description": "测角器的工作原理"
  },
  {
    "name": "测量方法",
    "formula": "读取瞄准线与垂直线的夹角",
    "description": "使用测角器进行角度测量"
  },
  {
    "name": "应用计算",
    "formula": "h = d·tan α（高度=距离×正切值）",
    "description": "利用测得角度计算高度或距离"
  }
]',
'[
  {
    "title": "制作和使用简易测角器",
    "problem": "制作一个简易测角器，并用它测量学校旗杆的高度",
    "solution": "制作步骤：①准备材料：量角器、细线、小重物（橡皮或钥匙）、吸管、胶带②制作过程：将吸管用胶带固定在量角器的中心，作为瞄准器；在量角器中心系一根细线，线端系小重物作为重锤③使用方法：通过吸管瞄准目标，读取重锤线与量角器刻度的角度④测量实践：选择距离旗杆30米的位置，通过测角器瞄准旗杆顶部，读得仰角35°⑤计算高度：h = 30×tan 35° ≈ 30×0.7 = 21米⑥加上测量者眼高1.6米：旗杆总高度 ≈ 22.6米⑦验证测量：从不同距离再次测量验证结果",
    "analysis": "通过制作和使用简易测角器，学生实际体验了三角函数在测量中的应用"
  }
]',
'[
  {
    "concept": "原理理解",
    "explanation": "理解测角器的工作原理和重力基准",
    "example": "重锤线始终指向地心方向"
  },
  {
    "concept": "精度分析",
    "explanation": "分析影响测量精度的各种因素",
    "example": "角度读数精度、距离测量精度等"
  },
  {
    "concept": "误差控制",
    "explanation": "学会控制和减小测量误差",
    "example": "多次测量取平均值、改进测量方法"
  }
]',
'[
  "制作过程粗糙，影响测量精度",
  "不理解测角器的工作原理",
  "测量方法不当",
  "忽视误差分析和控制"
]',
'[
  "原理先行法：先理解原理再动手制作",
  "精工细作法：认真制作，确保测量精度",
  "实测验证法：通过实际测量验证理论",
  "误差分析法：分析误差来源并改进方法"
]',
'{
  "emphasis": ["动手实践", "创新制作"],
  "application": ["实际制作", "生活应用"],
  "connection": ["实践能力", "创新思维"]
}',
'{
  "emphasis": ["科学制作", "精确测量"],
  "application": ["科学实验", "工程制作"],
  "connection": ["科学方法", "工程思维"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved');