<!-- 导航栏组件 -->
<view class="nav-bar {{fixed ? 'nav-bar-fixed' : ''}} {{shadow ? 'nav-bar-shadow' : ''}} {{plain ? 'nav-bar-plain' : ''}}" style="background-color: {{transparent ? 'transparent' : bgColor}}; color: {{textColor}};">
  <!-- 状态栏 -->
  <view class="nav-bar-status" style="height: {{statusBarHeight}}px;"></view>
  
  <!-- 导航栏内容区 -->
  <view class="nav-bar-content" style="height: {{navBarHeight}}px;">
    <!-- 左侧区域 -->
    <view class="nav-bar-left">
      <block wx:if="{{showBack || back}}">
        <view class="nav-bar-button" bindtap="handleBack">
          <view class="icon-back"></view>
          <text wx:if="{{backText}}" class="nav-bar-button-text">{{backText}}</text>
        </view>
      </block>
      <block wx:if="{{showHome}}">
        <view class="nav-bar-button" bindtap="handleHome">
          <view class="icon-home"></view>
          <text wx:if="{{homeText}}" class="nav-bar-button-text">{{homeText}}</text>
        </view>
      </block>
      <!-- 自定义左侧内容插槽 -->
      <slot name="left"></slot>
    </view>
    
    <!-- 中间标题区域 -->
    <view class="nav-bar-center">
      <block wx:if="{{title}}">
        <text class="nav-bar-title {{plain ? 'nav-bar-title-plain' : ''}}">{{title}}</text>
      </block>
      <!-- 自定义中间内容插槽 -->
      <slot name="center"></slot>
    </view>
    
    <!-- 右侧区域 -->
    <view class="nav-bar-right">
      <!-- 自定义右侧内容插槽 -->
      <slot name="right"></slot>
    </view>
  </view>
</view>

<!-- 占位元素，用于固定定位时占位 -->
<view wx:if="{{fixed}}" style="height: {{plain ? (statusBarHeight + navBarHeight * 0.7) : (statusBarHeight + navBarHeight)}}px;"></view> 