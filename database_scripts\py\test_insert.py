import insert
import json

test_data = """{
  "text": "```json\\n[\\n  {\\n    \\"question_code\\": \\"MATH_G7S1_CH1_001_Q001_2852\\",\\n    \\"question_title\\": \\"角度单位的基本概念\\",\\n    \\"question_type\\": \\"single_choice\\",\\n    \\"question_content\\": \\"{\\\\\\\"text\\\\\\": \\\\\\\"下列角度单位换算正确的是\\\\\\", \\\\\\\"format\\\\\\": \\\\\\\"text\\\\\\"}\\",\\n    \\"question_images\\": \\"[]\\",\\n    \\"question_audio\\": \\"[]\\",\\n    \\"question_video\\": \\"[]\\",\\n    \\"options\\": \\"[{\\\\\\\"label\\\\\\": \\\\\\\"A\\\\\\", \\\\\\\"content\\\\\\": \\\\\\\"1°=60′\\\\\\\"}, {\\\\\\\"label\\\\\\": \\\\\\\"B\\\\\\", \\\\\\\"content\\\\\\": \\\\\\\"1′=60″\\\\\\\"}, {\\\\\\\"label\\\\\\": \\\\\\\"C\\\\\\", \\\\\\\"content\\\\\\": \\\\\\\"1°=100′\\\\\\\"}, {\\\\\\\"label\\\\\\": \\\\\\\"D\\\\\\", \\\\\\\"content\\\\\\": \\\\\\\"1′=100″\\\\\\"}]\\",\\n    \\"correct_answer\\": \\"{\\\\\\\"explanation\\\\\\": \\\\\\\"角度制采用六十进制，1°=60′，1′=60″，选项A和B均正确，但单选题需选最直接正确的描述\\\\\\", \\\\\\\"correct_option\\\\\\": \\\\\\\"A\\\\\\"}\\",\\n    \\"answer_explanation\\": \\"{\\\\\\\"detailed_analysis\\\\\\": \\\\\\\"角度单位采用六十进制，1度等于60分，1分等于60秒。选项A正确描述了度分关系，而B虽然正确但可能作为多选题选项。单选题中A为更基础的正确选项。\\\\\\"}\\",\\n    \\"solution_steps\\": \\"[{\\\\\\\"step\\\\\\": 1, \\\\\\\"content\\\\\\": \\\\\\\"回忆角度单位换算规则\\\\\\\"}, {\\\\\\\"step\\\\\\": 2, \\\\\\\"content\\\\\\": \\\\\\\"1°=60′，1′=60″\\\\\\\"}, {\\\\\\\"step\\\\\\": 3, \\\\\\\"content\\\\\\": \\\\\\\"验证选项A和B的正确性\\\\\\\"}, {\\\\\\\"step\\\\\\": 4, \\\\\\\"content\\\\\\": \\\\\\\"选择最直接的正确选项A\\\\\\"}]\\",\\n    \\"solution_methods\\": \\"[{\\\\\\\"method\\\\\\": \\\\\\\"单位换算法\\\\\\", \\\\\\\"description\\\\\\": \\\\\\\"应用角度制的六十进制规则进行判断\\\\\\"}]\\",\\n    \\"key_points\\": \\"[\\\\\\"度分秒换算\\\\\\", \\\\\\"六十进制概念\\\\\\", \\\\\\"单位符号识别\\\\\\"]\\",\\n    \\"common_mistakes\\": \\"[\\\\\\"混淆十进制与六十进制\\\\\\", \\\\\\"误记分秒关系\\\\\\"]\\",\\n    \\"subject\\": \\"mathematics\\",\\n    \\"grade_level\\": 7,\\n    \\"knowledge_points\\": \\"{2852}\\",\\n    \\"difficulty_level\\": \\"basic\\",\\n    \\"cognitive_level\\": \\"remember\\",\\n    \\"academic_tracks\\": \\"{undetermined}\\",\\n    \\"liberal_arts_difficulty\\": \\"basic\\",\\n    \\"science_difficulty\\": \\"basic\\",\\n    \\"estimated_time_minutes\\": 2,\\n    \\"importance_level\\": 5,\\n    \\"exam_frequency\\": \\"high\\",\\n    \\"requires_calculation\\": \\"f\\",\\n    \\"requires_reasoning\\": \\"t\\",\\n    \\"requires_application\\": \\"f\\",\\n    \\"requires_creativity\\": \\"f\\",\\n    \\"source_type\\": \\"textbook\\",\\n    \\"source_reference\\": \\"\\",\\n    \\"quality_score\\": 4.8,\\n    \\"review_status\\": \\"approved\\",\\n    \\"reviewer_id\\": null,\\n    \\"review_notes\\": null,\\n    \\"used_count\\": 0,\\n    \\"correct_rate\\": 0.0,\\n    \\"average_time_seconds\\": 0,\\n    \\"difficulty_rating\\": 0.0,\\n    \\"ai_generated\\": \\"f\\",\\n    \\"ai_difficulty_prediction\\": null,\\n    \\"ai_tags\\": \\"[]\\",\\n    \\"is_active\\": \\"t\\",\\n    \\"is_public\\": \\"t\\",\\n    \\"created_by\\": null,\\n    \\"created_at\\": \\"2023-10-05T08:00:00Z\\",\\n    \\"updated_at\\": \\"2023-10-05T08:00:00Z\\"\\n  }\\n]\\n```"
}"""

try:
    result = insert.main(test_data)
    print('SUCCESS:')
    print(json.dumps(result, indent=2, ensure_ascii=False))
except Exception as e:
    print('ERROR:', str(e))
    import traceback
    traceback.print_exc() 