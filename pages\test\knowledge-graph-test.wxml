<!--pages/test/knowledge-graph-test.wxml-->
<view class="container">
  <!-- 页面标题 -->
  <view class="page-header">
    <text class="page-title">知识图谱云函数测试</text>
    <text class="page-subtitle">基于PostgreSQL数据库的知识点查询系统</text>
  </view>

  <!-- 操作按钮区 -->
  <view class="action-section">
    <button class="action-btn primary" bindtap="runAllTests" disabled="{{loading}}">
      {{loading ? '测试中...' : '运行全部测试'}}
    </button>
    <button class="action-btn secondary" bindtap="clearResults">清空结果</button>
  </view>

  <!-- 单项测试按钮 -->
  <view class="test-section">
    <text class="section-title">单项测试</text>
    
    <view class="test-grid">
      <button class="test-btn" bindtap="testGetKnowledgeNodes" disabled="{{loading}}">
        获取知识点列表
      </button>
      <button class="test-btn" bindtap="testSearchKnowledge" disabled="{{loading}}">
        搜索知识点
      </button>
      <button class="test-btn" bindtap="testGetKnowledgeByGrade" disabled="{{loading}}">
        按年级查询
      </button>
      <button class="test-btn" bindtap="testGetStudentMastery" disabled="{{loading}}">
        学生掌握状态
      </button>
      <button class="test-btn" bindtap="testUpdateMasteryStatus" disabled="{{loading}}">
        更新掌握状态
      </button>
      <button class="test-btn" bindtap="testGetKnowledgeRelationships" disabled="{{loading}}">
        知识点关系
      </button>
      <button class="test-btn" bindtap="testGetPrerequisites" disabled="{{loading}}">
        前置知识点
      </button>
      <button class="test-btn" bindtap="testGetLearningPath" disabled="{{loading}}">
        学习路径
      </button>
      <button class="test-btn" bindtap="testGetRecommendations" disabled="{{loading}}">
        个性化推荐
      </button>
      <button class="test-btn" bindtap="testGetKnowledgeContent" disabled="{{loading}}">
        知识点内容
      </button>
    </view>
  </view>

  <!-- 测试结果区 -->
  <view class="results-section" wx:if="{{testResults.length > 0}}">
    <text class="section-title">测试结果 ({{testResults.length}})</text>
    
    <view class="results-list">
      <view 
        class="result-item {{item.success ? 'success' : 'error'}}" 
        wx:for="{{testResults}}" 
        wx:key="id"
        bindtap="viewDetail"
        data-index="{{index}}"
      >
        <view class="result-header">
          <text class="result-title">{{item.testName}}</text>
          <view class="result-status {{item.success ? 'success' : 'error'}}">
            {{item.success ? '✓' : '✗'}}
          </view>
        </view>
        <view class="result-meta">
          <text class="result-time">{{item.timestamp}}</text>
          <text class="result-tip">点击查看详情</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{testResults.length === 0 && !loading}}">
    <text class="empty-text">暂无测试结果</text>
    <text class="empty-hint">点击上方按钮开始测试</text>
  </view>

  <!-- 加载状态 -->
  <view class="loading-state" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">正在执行测试...</text>
  </view>

  <!-- 数据库连接信息 -->
  <view class="db-info">
    <text class="db-title">数据库连接信息</text>
    <view class="db-details">
      <text class="db-item">服务器: **************:5434</text>
      <text class="db-item">数据库: k12</text>
      <text class="db-item">用户: postgres</text>
      <text class="db-item">表结构: 04_knowledge_graph_enhanced.sql</text>
    </view>
  </view>
</view> 