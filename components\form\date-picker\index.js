Component({
  properties: {
    // 是否显示选择器
    show: {
      type: Boolean,
      value: false
    },
    // 标题
    title: {
      type: String,
      value: '选择日期'
    },
    // 已选择的日期，格式：YYYY-MM-DD
    date: {
      type: String,
      value: ''
    },
    // 最小可选日期，格式：YYYY-MM-DD
    minDate: {
      type: String,
      value: ''
    },
    // 最大可选日期，格式：YYYY-MM-DD
    maxDate: {
      type: String,
      value: ''
    },
    // 年份范围，默认展示从当前年份往前10年、往后10年
    yearRange: {
      type: Number,
      value: 10
    }
  },
  
  data: {
    years: [],
    months: [],
    days: [],
    selectedYear: 0,
    selectedMonth: 0,
    selectedDay: 0,
    minYear: 0,
    maxYear: 0,
    minMonth: 1,
    maxMonth: 12,
    minDay: 1,
    maxDay: 31,
    animation: null
  },
  
  lifetimes: {
    attached() {
      // 创建动画实例
      this.data.animation = wx.createAnimation({
        duration: 300,
        timingFunction: 'ease',
      });
    }
  },
  
  observers: {
    'show': function(show) {
      if (show) {
        this.initDateData();
      }
    }
  },
  
  methods: {
    // 初始化日期数据
    initDateData() {
      // 设置日期范围限制
      this.setDateRange();
      
      // 生成年份选项
      const years = this.generateYears();
      
      // 生成月份选项
      const months = this.generateMonths();
      
      // 预设选择值
      let selectedDate = this.getInitialDate();
      
      // 查找预选中索引
      let yearIndex = years.findIndex(y => y === `${selectedDate.year}年`);
      let monthIndex = months.findIndex(m => m === `${selectedDate.month}月`);
      
      if (yearIndex === -1) yearIndex = 0;
      if (monthIndex === -1) monthIndex = 0;
      
      // 生成天数
      const days = this.generateDays(years[yearIndex], months[monthIndex]);
      let dayIndex = days.findIndex(d => d === `${selectedDate.day}日`);
      if (dayIndex === -1) dayIndex = 0;
      
      this.setData({
        years,
        months,
        days,
        selectedYear: yearIndex,
        selectedMonth: monthIndex,
        selectedDay: dayIndex
      });
    },
    
    // 设置日期范围限制
    setDateRange() {
      const now = new Date();
      const currentYear = now.getFullYear();
      const yearRange = this.properties.yearRange;
      
      // 默认范围：当前年份前后10年
      let minYear = currentYear - yearRange;
      let maxYear = currentYear + yearRange;
      
      // 如果设置了最小日期
      if (this.properties.minDate) {
        const minDateObj = new Date(this.properties.minDate);
        if (!isNaN(minDateObj.getTime())) {
          minYear = Math.max(minYear, minDateObj.getFullYear());
        }
      }
      
      // 如果设置了最大日期
      if (this.properties.maxDate) {
        const maxDateObj = new Date(this.properties.maxDate);
        if (!isNaN(maxDateObj.getTime())) {
          maxYear = Math.min(maxYear, maxDateObj.getFullYear());
        }
      }
      
      this.setData({
        minYear,
        maxYear
      });
    },
    
    // 生成年份选项
    generateYears() {
      const { minYear, maxYear } = this.data;
      const years = [];
      
      for (let i = minYear; i <= maxYear; i++) {
        years.push(`${i}年`);
      }
      
      return years;
    },
    
    // 生成月份选项
    generateMonths() {
      const months = [];
      for (let i = 1; i <= 12; i++) {
        months.push(`${i}月`);
      }
      return months;
    },
    
    // 获取初始选择日期
    getInitialDate() {
      let year, month, day;
      const now = new Date();
      
      // 如果有初始日期，则解析
      if (this.properties.date) {
        const dateParts = this.properties.date.split('-');
        if (dateParts.length === 3) {
          year = parseInt(dateParts[0]);
          month = parseInt(dateParts[1]);
          day = parseInt(dateParts[2]);
          
          // 验证日期有效性
          if (isNaN(year) || isNaN(month) || isNaN(day) || 
              month < 1 || month > 12 || day < 1 || day > 31) {
            year = now.getFullYear();
            month = now.getMonth() + 1;
            day = now.getDate();
          }
        } else {
          year = now.getFullYear();
          month = now.getMonth() + 1;
          day = now.getDate();
        }
      } else {
        // 默认当前日期
        year = now.getFullYear();
        month = now.getMonth() + 1;
        day = now.getDate();
      }
      
      // 确保日期在有效范围内
      if (year < this.data.minYear) year = this.data.minYear;
      if (year > this.data.maxYear) year = this.data.maxYear;
      
      return { year, month, day };
    },
    
    // 根据年月生成对应的天数选项
    generateDays(year, month) {
      if (!year || !month) return [];
      
      // 去掉年月的文字后缀
      const yearNum = parseInt(year.replace('年', ''));
      const monthNum = parseInt(month.replace('月', ''));
      
      // 获取该月的天数
      const daysInMonth = new Date(yearNum, monthNum, 0).getDate();
      
      const days = [];
      for (let i = 1; i <= daysInMonth; i++) {
        days.push(`${i}日`);
      }
      
      return days;
    },
    
    // 检查日期是否有效
    isValidDate(year, month, day) {
      const date = new Date(year, month - 1, day);
      return date.getFullYear() === year && 
             date.getMonth() === month - 1 && 
             date.getDate() === day;
    },
    
    // 年份选择改变
    onYearChange(e) {
      const yearIndex = parseInt(e.detail.value);
      const selectedYear = this.data.years[yearIndex];
      
      // 重新生成天数
      const days = this.generateDays(selectedYear, this.data.months[this.data.selectedMonth]);
      
      // 如果当前选中的日不在新的日期范围内，则调整为有效值
      let dayIndex = this.data.selectedDay;
      if (dayIndex >= days.length) {
        dayIndex = days.length - 1;
      }
      
      this.setData({
        selectedYear: yearIndex,
        days,
        selectedDay: dayIndex
      });
    },
    
    // 月份选择改变
    onMonthChange(e) {
      const monthIndex = parseInt(e.detail.value);
      const selectedMonth = this.data.months[monthIndex];
      
      // 重新生成天数
      const days = this.generateDays(this.data.years[this.data.selectedYear], selectedMonth);
      
      // 如果当前选中的日不在新的日期范围内，则调整为有效值
      let dayIndex = this.data.selectedDay;
      if (dayIndex >= days.length) {
        dayIndex = days.length - 1;
      }
      
      this.setData({
        selectedMonth: monthIndex,
        days,
        selectedDay: dayIndex
      });
    },
    
    // 日期选择改变
    onDayChange(e) {
      this.setData({
        selectedDay: parseInt(e.detail.value)
      });
    },
    
    // 处理值改变
    onValueChange(e) {
      const value = e.detail.value;
      const yearIndex = value[0];
      const monthIndex = value[1];
      const dayIndex = value[2];
      
      // 检查年份是否改变
      if (yearIndex !== this.data.selectedYear || monthIndex !== this.data.selectedMonth) {
        // 重新生成天数
        const days = this.generateDays(this.data.years[yearIndex], this.data.months[monthIndex]);
        
        // 调整日期索引，确保在有效范围内
        let newDayIndex = dayIndex;
        if (newDayIndex >= days.length) {
          newDayIndex = days.length - 1;
        }
        
        this.setData({
          selectedYear: yearIndex,
          selectedMonth: monthIndex,
          selectedDay: newDayIndex,
          days
        });
      } else {
        this.setData({
          selectedYear: yearIndex,
          selectedMonth: monthIndex,
          selectedDay: dayIndex
        });
      }
    },
    
    // 取消选择
    onCancel() {
      this.triggerEvent('cancel');
    },
    
    // 确认选择
    onConfirm() {
      const yearStr = this.data.years[this.data.selectedYear];
      const monthStr = this.data.months[this.data.selectedMonth];
      const dayStr = this.data.days[this.data.selectedDay];
      
      if (!yearStr || !monthStr || !dayStr) {
        wx.showToast({
          title: '请选择完整日期',
          icon: 'none'
        });
        return;
      }
      
      // 构建日期
      const year = parseInt(yearStr.replace('年', ''));
      const month = parseInt(monthStr.replace('月', ''));
      const day = parseInt(dayStr.replace('日', ''));
      
      // 验证日期有效性
      if (!this.isValidDate(year, month, day)) {
        wx.showToast({
          title: '日期无效，请重新选择',
          icon: 'none'
        });
        return;
      }
      
      // 检查日期是否在允许范围内
      if (this.properties.minDate) {
        const minDate = new Date(this.properties.minDate);
        const selectedDate = new Date(year, month - 1, day);
        if (selectedDate < minDate) {
          wx.showToast({
            title: `日期不能早于 ${this.properties.minDate}`,
            icon: 'none'
          });
          return;
        }
      }
      
      if (this.properties.maxDate) {
        const maxDate = new Date(this.properties.maxDate);
        const selectedDate = new Date(year, month - 1, day);
        if (selectedDate > maxDate) {
          wx.showToast({
            title: `日期不能晚于 ${this.properties.maxDate}`,
            icon: 'none'
          });
          return;
        }
      }
      
      // 格式化日期
      const formattedDate = `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
      
      this.triggerEvent('confirm', { date: formattedDate });
    }
  }
}) 