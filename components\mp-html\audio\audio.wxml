<view wx:if="{{controls}}" class="_contain"><view class="_poster" style="background-image:url('{{poster}}')"><view class="_button" bindtap="{{playing?'pause':'play'}}"><view class="{{playing?'_pause':'_play'}}"/></view></view><view class="_title"><view class="_name">{{name||'未知音频'}}</view><view class="_author">{{author||'未知作者'}}</view></view><slider class="_slider" activeColor="#585959" block-size="12" disabled="{{error}}" value="{{value}}" bindchanging="_seeking" bindchange="_seeked"/><view class="_time">{{time}}</view></view>