// 知识图谱数据类型定义 - 云函数本地版本
// 从主项目data文件中同步的类型定义

/**
 * 难度等级枚举
 */
const DIFFICULTY_LEVELS = {
  EASY: 'easy',
  MEDIUM: 'medium', 
  HARD: 'hard',
  EXPERT: 'expert'
};

/**
 * 学期枚举
 */
const SEMESTER_TYPES = {
  FIRST: 'first',
  SECOND: 'second'
};

/**
 * 知识点状态类型
 */
const STATUS_TYPES = {
  MASTERED: 'mastered',       // 已掌握
  LEARNING: 'learning',       // 学习中
  WEAK: 'weak',               // 薄弱
  NOT_MASTERED: 'not-mastered', // 未掌握
  NOT_STARTED: 'not-started'    // 未开始
};

/**
 * 知识关系类型枚举
 */
const RELATION_TYPES = {
  FOUNDATION: 'foundation',         // 基础关系
  GENERALIZATION: 'generalization', // 泛化关系
  SPECIALIZATION: 'specialization', // 特化关系
  DERIVATION: 'derivation',         // 推导关系
  PREREQUISITE: 'prerequisite',     // 前置关系
  APPLICATION: 'application',       // 应用关系
  ANALOGY: 'analogy',              // 类比关系
  ABSTRACTION: 'abstraction',       // 抽象关系
  INTEGRATION: 'integration',       // 整合关系
  REINFORCEMENT: 'reinforcement',   // 强化关系
  REVERSE: 'reverse',               // 逆向关系
  CONTRAST: 'contrast',             // 对比关系
  PROGRESSION: 'progression',       // 递进关系
  PARALLEL: 'parallel',             // 并行关系
  CORRELATION: 'correlation'        // 相关关系
};

/**
 * 关联强度级别
 */
const RELATION_STRENGTH = {
  STRONG: 0.9,        // 强关联（必须掌握）
  MEDIUM: 0.7,        // 中等关联（重要）
  WEAK: 0.5,          // 弱关联（辅助）
  MINIMAL: 0.3        // 最小关联（参考）
};

/**
 * 年级级别枚举
 */
const GRADE_LEVELS = {
  // 小学
  ELEMENTARY_1: '小学一年级',
  ELEMENTARY_2: '小学二年级',
  ELEMENTARY_3: '小学三年级',
  ELEMENTARY_4: '小学四年级',
  ELEMENTARY_5: '小学五年级',
  ELEMENTARY_6: '小学六年级',
  // 初中
  JUNIOR_1: '初中一年级',
  JUNIOR_2: '初中二年级',
  JUNIOR_3: '初中三年级',
  // 高中
  SENIOR_1: '高中一年级',
  SENIOR_2: '高中二年级',
  SENIOR_3: '高中三年级'
};

/**
 * 数学核心素养枚举
 */
const CORE_COMPETENCIES = {
  MATHEMATICAL_ABSTRACTION: 'mathematical_abstraction',     // 数学抽象
  LOGICAL_REASONING: 'logical_reasoning',                   // 逻辑推理
  MATHEMATICAL_MODELING: 'mathematical_modeling',           // 数学建模
  INTUITIVE_IMAGINATION: 'intuitive_imagination',           // 直观想象
  MATHEMATICAL_OPERATION: 'mathematical_operation',         // 数学运算
  DATA_ANALYSIS: 'data_analysis'                           // 数据分析
};

module.exports = {
  DIFFICULTY_LEVELS,
  SEMESTER_TYPES,
  STATUS_TYPES,
  RELATION_TYPES,
  RELATION_STRENGTH,
  GRADE_LEVELS,
  CORE_COMPETENCIES
}; 