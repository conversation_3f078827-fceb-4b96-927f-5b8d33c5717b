// 班级/年级能力对比分析组件
Component({
  properties: {
    // 对比数据
    comparisonData: {
      type: Object,
      value: null,
      observer: function(newVal) {
        if (newVal) {
          this.processComparisonData();
        }
      }
    },
    // 对比模式：class, grade, school
    comparisonMode: {
      type: String,
      value: 'class'
    },
    // 用户数据
    studentData: {
      type: Object,
      value: null
    },
    // 图表类型：radar, bar, bubble
    chartType: {
      type: String,
      value: 'radar'
    },
    // 显示统计信息
    showStatistics: {
      type: Boolean,
      value: true
    }
  },

  data: {
    processedData: null,
    chartReady: false,
    
    // 统计信息
    statistics: {
      totalStudents: 0,
      userRank: 0,
      userPercentile: 0,
      strengthAreas: [],
      improvementAreas: []
    },
    
    // 可视化配置
    visualization: {
      svgContent: '',
      width: 350,
      height: 280
    },
    
    // 交互状态
    selectedDimension: null,
    showDetails: false,
    
    // 能力维度配置
    dimensions: [
      { key: 'calculation', name: '计算能力', color: '#3E7BFA' },
      { key: 'reasoning', name: '推理能力', color: '#42B983' },
      { key: 'spatial', name: '空间想象', color: '#F5A623' },
      { key: 'analysis', name: '数据分析', color: '#E64340' },
      { key: 'application', name: '应用能力', color: '#9C27B0' },
      { key: 'logical', name: '逻辑思维', color: '#FF5722' }
    ]
  },

  lifetimes: {
    ready() {
      if (this.properties.comparisonData) {
        this.processComparisonData();
      }
    }
  },

  methods: {
    /**
     * 处理对比数据
     */
    processComparisonData() {
      const data = this.properties.comparisonData;
      const studentData = this.properties.studentData;
      
      if (!data || !studentData) return;

      try {
        const processed = this.analyzeComparison(data, studentData);
        this.setData({
          processedData: processed,
          chartReady: true
        });
        
        this.generateVisualization();
        this.calculateStatistics(processed);
        
      } catch (error) {
        console.error('处理对比数据失败:', error);
      }
    },

    /**
     * 分析对比数据
     */
    analyzeComparison(comparisonData, studentData) {
      const mode = this.properties.comparisonMode;
      
      return {
        student: this.normalizeAbilityData(studentData),
        class: this.normalizeAbilityData(comparisonData.classAverage),
        grade: this.normalizeAbilityData(comparisonData.gradeAverage),
        school: this.normalizeAbilityData(comparisonData.schoolAverage),
        
        // 分布数据
        distribution: {
          class: comparisonData.classDistribution || [],
          grade: comparisonData.gradeDistribution || [],
          school: comparisonData.schoolDistribution || []
        },
        
        // 排名信息
        rankings: {
          classRank: comparisonData.classRank || 0,
          gradeRank: comparisonData.gradeRank || 0,
          schoolRank: comparisonData.schoolRank || 0,
          totalStudents: {
            class: comparisonData.classSize || 0,
            grade: comparisonData.gradeSize || 0,
            school: comparisonData.schoolSize || 0
          }
        },
        
        // 百分位数据
        percentiles: {
          class: comparisonData.classPercentile || 0,
          grade: comparisonData.gradePercentile || 0,
          school: comparisonData.schoolPercentile || 0
        }
      };
    },

    /**
     * 标准化能力数据
     */
    normalizeAbilityData(data) {
      if (!data) return {};
      
      return {
        calculation: data.calculation_ability || data.calculationAbility || 0,
        reasoning: data.reasoning_ability || data.reasoningAbility || 0,
        spatial: data.spatial_thinking || data.spatialThinking || 0,
        analysis: data.data_analysis || data.dataAnalysis || 0,
        application: data.application_ability || data.applicationAbility || 0,
        logical: data.logical_thinking || data.logicalThinking || 0,
        overall: data.overall_score || data.overallScore || 0
      };
    },

    /**
     * 生成可视化图表
     */
    generateVisualization() {
      const chartType = this.properties.chartType;
      
      switch(chartType) {
        case 'radar':
          this.generateRadarComparison();
          break;
        case 'bar':
          this.generateBarComparison();
          break;
        case 'bubble':
          this.generateBubbleComparison();
          break;
        default:
          this.generateRadarComparison();
      }
    },

    /**
     * 生成雷达图对比
     */
    generateRadarComparison() {
      const data = this.data.processedData;
      const dimensions = this.data.dimensions;
      const { width, height } = this.data.visualization;
      
      const centerX = width / 2;
      const centerY = height / 2;
      const radius = Math.min(width, height) / 2 - 40;
      const angleStep = (2 * Math.PI) / dimensions.length;
      
      let svg = `
        <svg width="${width}" height="${height}" viewBox="0 0 ${width} ${height}" xmlns="http://www.w3.org/2000/svg">
          <defs>
            ${this.generateComparisonGradients()}
          </defs>
          
          <!-- 背景网格 -->
          ${this.generateRadarGrid(centerX, centerY, radius, angleStep, dimensions)}
          
          <!-- 年级平均线 -->
          ${this.generateRadarPolygon(data.grade, centerX, centerY, radius, angleStep, '#94A3B8', 0.3, 'grade-average')}
          
          <!-- 班级平均线 -->
          ${this.generateRadarPolygon(data.class, centerX, centerY, radius, angleStep, '#64748B', 0.5, 'class-average')}
          
          <!-- 学生数据线 -->
          ${this.generateRadarPolygon(data.student, centerX, centerY, radius, angleStep, '#3E7BFA', 0.8, 'student-data')}
          
          <!-- 轴标签 -->
          ${this.generateRadarLabels(centerX, centerY, radius + 15, angleStep, dimensions)}
          
          <!-- 图例 -->
          ${this.generateComparisonLegend(width)}
        </svg>
      `;
      
      this.setData({
        'visualization.svgContent': svg
      });
    },

    /**
     * 生成柱状图对比
     */
    generateBarComparison() {
      const data = this.data.processedData;
      const dimensions = this.data.dimensions;
      const { width, height } = this.data.visualization;
      
      const padding = { top: 30, right: 30, bottom: 60, left: 50 };
      const chartWidth = width - padding.left - padding.right;
      const chartHeight = height - padding.top - padding.bottom;
      
      const barWidth = chartWidth / dimensions.length / 4;
      const maxValue = 100;
      
      let svg = `
        <svg width="${width}" height="${height}" viewBox="0 0 ${width} ${height}" xmlns="http://www.w3.org/2000/svg">
          <!-- Y轴网格线 -->
          ${this.generateBarGrid(padding, chartWidth, chartHeight, maxValue)}
          
          <!-- 柱状图 -->
          ${this.generateBarColumns(data, dimensions, padding, chartWidth, chartHeight, barWidth, maxValue)}
          
          <!-- X轴标签 -->
          ${this.generateBarLabels(dimensions, padding, chartWidth, chartHeight, barWidth)}
          
          <!-- Y轴标签 -->
          ${this.generateBarYLabels(padding, chartHeight, maxValue)}
          
          <!-- 图例 -->
          ${this.generateBarLegend(width, padding)}
        </svg>
      `;
      
      this.setData({
        'visualization.svgContent': svg
      });
    },

    /**
     * 生成气泡图对比
     */
    generateBubbleComparison() {
      const data = this.data.processedData;
      const dimensions = this.data.dimensions;
      const { width, height } = this.data.visualization;
      
      const padding = { top: 30, right: 30, bottom: 50, left: 50 };
      const chartWidth = width - padding.left - padding.right;
      const chartHeight = height - padding.top - padding.bottom;
      
      let svg = `
        <svg width="${width}" height="${height}" viewBox="0 0 ${width} ${height}" xmlns="http://www.w3.org/2000/svg">
          <!-- 网格 -->
          ${this.generateBubbleGrid(padding, chartWidth, chartHeight)}
          
          <!-- 气泡 -->
          ${this.generateBubbles(data, padding, chartWidth, chartHeight)}
          
          <!-- 轴标签 -->
          ${this.generateBubbleAxes(padding, chartWidth, chartHeight)}
        </svg>
      `;
      
      this.setData({
        'visualization.svgContent': svg
      });
    },

    /**
     * 生成雷达图多边形
     */
    generateRadarPolygon(abilityData, centerX, centerY, radius, angleStep, color, opacity, className) {
      if (!abilityData) return '';
      
      const dimensions = this.data.dimensions;
      let points = [];
      
      dimensions.forEach((dimension, index) => {
        const value = abilityData[dimension.key] || 0;
        const angle = index * angleStep - Math.PI / 2;
        const r = (value / 100) * radius;
        const x = centerX + r * Math.cos(angle);
        const y = centerY + r * Math.sin(angle);
        points.push(`${x},${y}`);
      });
      
      return `
        <polygon points="${points.join(' ')}" 
                fill="${color}" 
                fill-opacity="${opacity * 0.3}" 
                stroke="${color}" 
                stroke-width="2" 
                stroke-opacity="${opacity}"
                class="${className}"/>
      `;
    },

    /**
     * 生成雷达图网格
     */
    generateRadarGrid(centerX, centerY, radius, angleStep, dimensions) {
      let grid = '';
      
      // 同心圆网格
      for (let i = 1; i <= 5; i++) {
        const r = (radius / 5) * i;
        grid += `
          <circle cx="${centerX}" cy="${centerY}" r="${r}" 
                  fill="none" stroke="#E5E7EB" stroke-width="1" opacity="0.5"/>
        `;
      }
      
      // 射线网格
      dimensions.forEach((dimension, index) => {
        const angle = index * angleStep - Math.PI / 2;
        const endX = centerX + radius * Math.cos(angle);
        const endY = centerY + radius * Math.sin(angle);
        
        grid += `
          <line x1="${centerX}" y1="${centerY}" x2="${endX}" y2="${endY}" 
                stroke="#E5E7EB" stroke-width="1" opacity="0.5"/>
        `;
      });
      
      return grid;
    },

    /**
     * 生成雷达图标签
     */
    generateRadarLabels(centerX, centerY, labelRadius, angleStep, dimensions) {
      let labels = '';
      
      dimensions.forEach((dimension, index) => {
        const angle = index * angleStep - Math.PI / 2;
        const x = centerX + labelRadius * Math.cos(angle);
        const y = centerY + labelRadius * Math.sin(angle);
        
        labels += `
          <text x="${x}" y="${y}" text-anchor="middle" dominant-baseline="central" 
                font-size="11" fill="#374151" font-weight="500">
            ${dimension.name}
          </text>
        `;
      });
      
      return labels;
    },

    /**
     * 计算统计信息
     */
    calculateStatistics(data) {
      const mode = this.properties.comparisonMode;
      const rankings = data.rankings;
      const percentiles = data.percentiles;
      
      // 计算优势和待改进领域
      const studentData = data.student;
      const comparisonData = mode === 'class' ? data.class : 
                           mode === 'grade' ? data.grade : data.school;
      
      const strengths = [];
      const improvements = [];
      
      this.data.dimensions.forEach(dimension => {
        const studentScore = studentData[dimension.key] || 0;
        const avgScore = comparisonData[dimension.key] || 0;
        const difference = studentScore - avgScore;
        
        if (difference > 10) {
          strengths.push({
            dimension: dimension.name,
            score: studentScore,
            advantage: difference.toFixed(1)
          });
        } else if (difference < -5) {
          improvements.push({
            dimension: dimension.name,
            score: studentScore,
            gap: Math.abs(difference).toFixed(1)
          });
        }
      });
      
      this.setData({
        statistics: {
          totalStudents: rankings.totalStudents[mode] || 0,
          userRank: rankings[mode + 'Rank'] || 0,
          userPercentile: percentiles[mode] || 0,
          strengthAreas: strengths,
          improvementAreas: improvements
        }
      });
    },

    /**
     * 生成对比渐变
     */
    generateComparisonGradients() {
      return `
        <linearGradient id="studentGradient" x1="0%" y1="0%" x2="0%" y2="100%">
          <stop offset="0%" style="stop-color:#3E7BFA;stop-opacity:0.4" />
          <stop offset="100%" style="stop-color:#3E7BFA;stop-opacity:0.1" />
        </linearGradient>
        <linearGradient id="classGradient" x1="0%" y1="0%" x2="0%" y2="100%">
          <stop offset="0%" style="stop-color:#64748B;stop-opacity:0.3" />
          <stop offset="100%" style="stop-color:#64748B;stop-opacity:0.1" />
        </linearGradient>
        <linearGradient id="gradeGradient" x1="0%" y1="0%" x2="0%" y2="100%">
          <stop offset="0%" style="stop-color:#94A3B8;stop-opacity:0.2" />
          <stop offset="100%" style="stop-color:#94A3B8;stop-opacity:0.05" />
        </linearGradient>
      `;
    },

    /**
     * 生成对比图例
     */
    generateComparisonLegend(width) {
      const legendItems = [
        { label: '我的能力', color: '#3E7BFA' },
        { label: '班级平均', color: '#64748B' },
        { label: '年级平均', color: '#94A3B8' }
      ];
      
      let legend = '';
      const itemWidth = 80;
      const startX = (width - legendItems.length * itemWidth) / 2;
      
      legendItems.forEach((item, index) => {
        const x = startX + index * itemWidth;
        legend += `
          <rect x="${x}" y="10" width="12" height="3" fill="${item.color}"/>
          <text x="${x + 18}" y="16" font-size="10" fill="#374151">${item.label}</text>
        `;
      });
      
      return legend;
    },

    /**
     * 切换对比模式
     */
    onComparisonModeChange(e) {
      const mode = e.detail.value;
      this.triggerEvent('modeChange', { mode });
    },

    /**
     * 切换图表类型
     */
    onChartTypeChange(e) {
      const chartType = e.detail.value;
      this.triggerEvent('chartTypeChange', { chartType });
    },

    /**
     * 选择维度查看详情
     */
    onDimensionSelect(e) {
      const dimension = e.currentTarget.dataset.dimension;
      this.setData({
        selectedDimension: dimension,
        showDetails: true
      });
    },

    /**
     * 关闭详情
     */
    onCloseDetails() {
      this.setData({
        showDetails: false,
        selectedDimension: null
      });
    },

    /**
     * 导出对比分析
     */
    onExportAnalysis() {
      this.triggerEvent('exportAnalysis', {
        comparisonData: this.data.processedData,
        statistics: this.data.statistics,
        chartType: this.properties.chartType
      });
    }
  }
}); 