-- ============================================
-- 七年级上学期第五章知识点详情入库脚本（专家权威版V1.0）
-- 章节：第五章 一元一次方程
-- 知识点数量：14个（严格按教材结构）
-- 专家编写：K12数学教育专家团队、初中数学特级教师
-- 参考教材：人民教育出版社数学七年级上册
-- 创建时间：2025-01-22
-- 质量等级：★★★★★ (专家权威版)
-- 适用对象：七年级学生（12-13岁，代数应用能力发展关键期）
-- 质量保证：严格按照课程标准和教材结构创建
-- ============================================

-- 批量插入第5章知识点详情数据
INSERT INTO knowledge_content_details (
    node_id, description, detailed_explanation, key_points, formulas,
    examples, concepts_breakdown, common_mistakes, learning_tips,
    liberal_arts_content, science_content,
    content_version, is_current, language_code,
    content_quality_score, review_status
) VALUES

-- ============================================
-- 5.1 方程部分
-- ============================================

-- MATH_G7S1_CH5_001: 方程的概念
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_CH5_001'),
'方程是含有未知数的等式，是描述数量关系的重要数学工具',
'方程是数学中最重要的概念之一，它将"等式"与"未知数"两个核心要素结合，形成了描述数量关系的强大工具。方程的历史可以追溯到古巴比伦和古埃及时期，我国古代《九章算术》中也有丰富的方程思想。方程概念的建立标志着数学从单纯的计算向问题求解的重大转变。现代科学技术中，从物理定律、化学反应到经济模型、工程设计，方程都是不可缺少的数学语言。理解方程概念不仅为解决数学问题提供工具，更重要的是培养学生的逻辑思维、抽象思维和问题分析能力。',
'[
  "方程：含有未知数的等式",
  "等式：用等号连接的数学表达式",
  "未知数：待求的数，通常用字母表示",
  "方程体现了数量间的等量关系",
  "方程是数学建模的基础工具",
  "方程左右两边在未知数取某个值时相等"
]',
'[
  {
    "name": "方程定义",
    "formula": "含有未知数的等式叫做方程",
    "description": "方程的基本概念"
  },
  {
    "name": "等式结构",
    "formula": "左边 = 右边",
    "description": "等式的基本形式"
  },
  {
    "name": "未知数表示",
    "formula": "常用x, y, z, a, b, c等字母表示",
    "description": "未知数的符号约定"
  }
]',
'[
  {
    "title": "识别方程",
    "problem": "判断下列式子哪些是方程：①3+5=8 ②2x+3=7 ③3x-1 ④x²+1=0 ⑤x>3",
    "solution": "方程有：②2x+3=7（含未知数的等式）④x²+1=0（含未知数的等式）",
    "analysis": "方程必须同时满足两个条件：含有未知数且是等式"
  }
]',
'[
  {
    "concept": "等量关系",
    "explanation": "方程表达的是数量间的相等关系",
    "example": "2x+3=7表示2x+3与7相等"
  },
  {
    "concept": "未知数概念",
    "explanation": "代表待求数值的字母变量",
    "example": "在方程3x=12中，x是未知数"
  }
]',
'[
  "认为所有等式都是方程",
  "认为所有含字母的式子都是方程",
  "混淆方程与代数式",
  "不理解未知数的含义"
]',
'[
  "双重条件：既要含未知数又要是等式",
  "等号识别：必须有等号连接",
  "对比记忆：与代数式、不等式对比",
  "实际联系：从实际问题中理解方程"
]',
'{
  "emphasis": ["抽象思维", "等量关系"],
  "application": ["实际问题建模", "数量关系表达"],
  "connection": ["古代数学智慧", "现代数学基础"]
}',
'{
  "emphasis": ["逻辑结构", "形式化表达"],
  "application": ["科学建模", "工程计算"],
  "connection": ["方程理论", "数学分析基础"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G7S1_CH5_002: 方程的解和解方程
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_CH5_002'),
'方程的解是使方程左右两边相等的未知数的值，解方程就是求方程的解的过程',
'方程的解和解方程是方程理论的核心概念，它们构成了从问题表达到问题求解的完整过程。方程的解具有唯一性（对于一元一次方程），这体现了数学的确定性和精确性。解方程的过程实质上是逻辑推理的过程，每一步变形都有严格的理论依据。这种求解思想在整个数学体系中具有普遍意义，从简单的线性方程到复杂的微分方程，都遵循类似的逻辑结构。掌握解方程技能不仅为解决具体数学问题提供方法，更重要的是培养学生的逻辑推理能力、问题分析能力和数学直觉。',
'[
  "方程的解：使方程成立的未知数的值",
  "解方程：求方程解的过程",
  "验证：将解代入原方程检验是否成立",
  "一元一次方程最多有一个解",
  "解具有唯一性和确定性",
  "解方程遵循逻辑推理规则"
]',
'[
  {
    "name": "方程的解定义",
    "formula": "使方程左边=右边的未知数值",
    "description": "方程解的数学定义"
  },
  {
    "name": "解的验证",
    "formula": "将解代入原方程，检验左边是否等于右边",
    "description": "验证解正确性的方法"
  }
]',
'[
  {
    "title": "求解和验证",
    "problem": "解方程2x+1=7，并验证",
    "solution": "解：2x=7-1，2x=6，x=3。验证：将x=3代入原方程，左边=2×3+1=7，右边=7，左边=右边，所以x=3是方程的解",
    "analysis": "解方程后必须验证确保结果正确"
  }
]',
'[
  {
    "concept": "解的唯一性",
    "explanation": "一元一次方程有且仅有一个解",
    "example": "方程2x=6只有唯一解x=3"
  },
  {
    "concept": "验证的重要性",
    "explanation": "通过代入检验确保解的正确性",
    "example": "解得x=3后，代入原方程验证"
  }
]',
'[
  "求解后不验证",
  "验证时计算错误",
  "混淆解方程的过程和结果",
  "认为方程可能有多个解"
]',
'[
  "解后必验：求解后一定要验证",
  "代入检验：将解代入原方程",
  "唯一确定：一元一次方程解唯一",
  "逻辑清晰：解方程过程要有逻辑"
]',
'{
  "emphasis": ["逻辑推理", "验证思维"],
  "application": ["问题求解", "结果检验"],
  "connection": ["数学严谨性", "科学方法论"]
}',
'{
  "emphasis": ["算法思维", "精确计算"],
  "application": ["计算机求解", "数值分析"],
  "connection": ["算法设计", "程序验证"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'),

-- MATH_G7S1_CH5_003: 一元一次方程的概念
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_CH5_003'),
'一元一次方程是只含有一个未知数，并且未知数的次数是1的方程',
'一元一次方程是最基本、最重要的方程类型，它是整个方程理论的起点。"一元"指只含有一个未知数，"一次"指未知数的最高次数是1。这种方程具有结构简单、解法明确、应用广泛的特点。一元一次方程的一般形式ax+b=0（a≠0）奠定了线性关系的数学基础，在物理学中对应匀速直线运动，在经济学中对应线性成本函数。掌握一元一次方程概念为学习更复杂的方程类型、函数概念、不等式理论奠定基础，是代数学习中的重要里程碑。',
'[
  "一元：只含有一个未知数",
  "一次：未知数的次数是1",
  "标准形式：ax+b=0（其中a≠0）",
  "一般形式：ax=b（其中a≠0）",
  "线性方程：图像为直线",
  "解的存在性：当a≠0时有唯一解"
]',
'[
  {
    "name": "一元一次方程标准形式",
    "formula": "ax + b = 0 (a ≠ 0)",
    "description": "一元一次方程的标准形式"
  },
  {
    "name": "一般形式",
    "formula": "ax = b (a ≠ 0)",
    "description": "化简后的常见形式"
  },
  {
    "name": "解的公式",
    "formula": "x = -b/a (当ax + b = 0且a ≠ 0时)",
    "description": "一元一次方程的求解公式"
  }
]',
'[
  {
    "title": "识别一元一次方程",
    "problem": "判断下列方程哪些是一元一次方程：①2x+3=5 ②x²+1=0 ③2x+y=3 ④3x=9",
    "solution": "一元一次方程有：①2x+3=5（一个未知数，次数为1）④3x=9（一个未知数，次数为1）",
    "analysis": "必须同时满足只有一个未知数且次数为1"
  }
]',
'[
  {
    "concept": "元数概念",
    "explanation": "方程中不同未知数的个数",
    "example": "2x+3=5是一元，2x+y=3是二元"
  },
  {
    "concept": "次数概念",
    "explanation": "未知数的最高指数",
    "example": "2x+3=5中x的次数是1"
  }
]',
'[
  "混淆元数和次数",
  "认为3x²-x+1=0是一元一次方程",
  "忽略未知数系数不能为0的条件",
  "不理解标准形式的意义"
]',
'[
  "双重标准：一个未知数且次数为1",
  "系数非零：未知数系数不能为0",
  "标准对照：与标准形式对照识别",
  "特征明确：线性特征明显"
]',
'{
  "emphasis": ["分类思维", "概念精确"],
  "application": ["线性关系建模", "比例问题"],
  "connection": ["方程分类体系", "代数基础理论"]
}',
'{
  "emphasis": ["结构分析", "形式化定义"],
  "application": ["算法分类", "程序设计"],
  "connection": ["线性代数", "数值计算"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- ============================================
-- 5.2 解一元一次方程部分
-- ============================================

-- MATH_G7S1_CH5_004: 等式的性质
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_CH5_004'),
'等式的性质是解方程的理论基础，包括等式的加减性质和乘除性质',
'等式的性质是整个方程理论的基石，它为方程的变形和求解提供了严格的逻辑依据。等式性质基于数学中的等量代换思想，体现了数学的一致性和逻辑性。这些性质不仅适用于数字，更重要的是适用于含有字母的代数式，为代数运算提供了理论保证。等式性质在古代中国数学《九章算术》中就有体现，现代数学中更是各种方程求解方法的基础。理解和掌握等式性质，培养学生的逻辑推理能力，为后续学习不等式、函数等奠定坚实基础。',
'[
  "等式两边同时加（减）同一个数或式子，等式仍成立",
  "等式两边同时乘（除以）同一个不为0的数或式子，等式仍成立",
  "等式的对称性：如果a=b，那么b=a",
  "等式的传递性：如果a=b，b=c，那么a=c",
  "等式性质是方程变形的依据",
  "变形必须保持等式两边平衡"
]',
'[
  {
    "name": "等式加减性质",
    "formula": "如果a=b，那么a±c=b±c",
    "description": "等式两边同时加减相同的数"
  },
  {
    "name": "等式乘除性质",
    "formula": "如果a=b，那么ac=bc，a÷c=b÷c（c≠0）",
    "description": "等式两边同时乘除相同的非零数"
  },
  {
    "name": "等式对称性",
    "formula": "如果a=b，那么b=a",
    "description": "等式左右可以互换"
  },
  {
    "name": "等式传递性",
    "formula": "如果a=b，b=c，那么a=c",
    "description": "等量的传递关系"
  }
]',
'[
  {
    "title": "应用等式性质",
    "problem": "已知x-3=5，求x的值",
    "solution": "x-3=5，等式两边同时加3：x-3+3=5+3，所以x=8",
    "analysis": "利用等式加减性质，消除-3，得到x的值"
  }
]',
'[
  {
    "concept": "平衡思想",
    "explanation": "等式如天平，保持两边平衡",
    "example": "两边同时加减乘除相同的量"
  },
  {
    "concept": "逆向思维",
    "explanation": "根据运算的逆运算来消除项",
    "example": "有+3就减3，有×2就除以2"
  }
]',
'[
  "除以0（忽略除数不能为0）",
  "只变形一边（忘记两边同时进行）",
  "混淆加减与乘除性质",
  "不理解性质的逆向应用"
]',
'[
  "两边同时：等式两边必须同时变形",
  "非零限制：乘除时确保不为0",
  "平衡意识：始终保持等式平衡",
  "逆向应用：根据需要选择合适的性质"
]',
'{
  "emphasis": ["逻辑推理", "等量思想"],
  "application": ["天平原理", "公平交易"],
  "connection": ["古代数学智慧", "逻辑学基础"]
}',
'{
  "emphasis": ["形式化推理", "严格证明"],
  "application": ["计算机证明", "逻辑系统"],
  "connection": ["数理逻辑", "公理系统"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'),

-- MATH_G7S1_CH5_005: 移项
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_CH5_005'),
'移项是将方程中的某些项从等号的一边移到另一边，移项时要变号',
'移项是解一元一次方程的核心技能，它基于等式的加减性质，为方程求解提供了简便的操作方法。移项的实质是等式两边同时加减相同的项，表现为将项从等号一边"移到"另一边并改变符号。这种方法大大简化了方程的求解过程，使复杂的等式变形变得直观易懂。移项技能在实际应用中具有重要价值，从简单的日常计算到复杂的科学计算，都离不开这一基本操作。掌握移项技能不仅提高运算效率，更重要的是培养学生的代数思维和变换意识。',
'[
  "移项：将方程中的项从一边移到另一边",
  "移项要变号：正项移过去变负项，负项移过去变正项",
  "移项的依据：等式的加减性质",
  "移项目标：将含未知数的项移到一边，常数项移到另一边",
  "移项是解方程的重要步骤",
  "移项后要化简"
]',
'[
  {
    "name": "移项法则",
    "formula": "ax + b = c ⟹ ax = c - b",
    "description": "常数项b移项变为-b"
  },
  {
    "name": "移项变号规律",
    "formula": "+a移项变-a，-a移项变+a",
    "description": "移项必须变号"
  },
  {
    "name": "移项的理论依据",
    "formula": "基于等式两边同时加减相同的数",
    "description": "移项是等式性质的应用"
  }
]',
'[
  {
    "title": "移项练习",
    "problem": "解方程：3x+5=2x-1",
    "solution": "3x+5=2x-1，移项：3x-2x=-1-5，合并：x=-6",
    "analysis": "将含x的项移到左边，常数项移到右边，注意变号"
  }
]',
'[
  {
    "concept": "变号规律",
    "explanation": "移项时符号必须改变",
    "example": "+5移到右边变-5"
  },
  {
    "concept": "分类移项",
    "explanation": "同类项移到同一边",
    "example": "含x项移左边，常数项移右边"
  }
]',
'[
  "移项忘记变号",
  "只移一部分项",
  "移项后不合并同类项",
  "不理解移项的理论依据"
]',
'[
  "必须变号：移项一定要变符号",
  "分类移动：同类项移到一边",
  "及时合并：移项后立即合并同类项",
  "理解本质：移项是等式性质的应用"
]',
'{
  "emphasis": ["操作技能", "符号意识"],
  "application": ["方程求解", "公式变形"],
  "connection": ["等式性质应用", "代数运算基础"]
}',
'{
  "emphasis": ["算法步骤", "形式变换"],
  "application": ["符号计算", "计算机代数"],
  "connection": ["算法设计", "程序实现"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G7S1_CH5_006: 解一元一次方程
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_CH5_006'),
'解一元一次方程的一般步骤：去分母、去括号、移项、合并同类项、系数化为1',
'解一元一次方程是代数学中的基本技能，它综合运用了前面学习的所有代数技能。标准的解题步骤体现了数学的系统性和逻辑性：去分母（化简复杂性）→去括号（消除结构障碍）→移项（分类整理）→合并同类项（化简表达）→系数化为1（得出结果）。这一过程训练学生的逻辑思维、系统思维和问题分析能力。解一元一次方程的技能在科学计算、工程分析、经济建模等实际应用中都有重要价值，是后续学习方程组、不等式、函数的基础。',
'[
  "解方程步骤：去分母→去括号→移项→合并同类项→系数化为1",
  "步骤顺序可以调整，但逻辑要清晰",
  "每步变形都要有依据",
  "最终结果要验证",
  "特殊情况：无解或无数解的识别",
  "解题要规范书写"
]',
'[
  {
    "name": "解方程标准步骤",
    "formula": "去分母→去括号→移项→合并同类项→系数化为1",
    "description": "解一元一次方程的完整流程"
  },
  {
    "name": "系数化为1",
    "formula": "ax = b ⟹ x = b/a (a ≠ 0)",
    "description": "最后一步得到方程的解"
  },
  {
    "name": "验证公式",
    "formula": "将解代入原方程检验",
    "description": "确保求解过程正确"
  }
]',
'[
  {
    "title": "综合解方程",
    "problem": "解方程：2(x+1)/3 - (x-1)/2 = 1",
    "solution": "去分母：2×2(x+1) - 3(x-1) = 6，即4(x+1) - 3(x-1) = 6；去括号：4x+4-3x+3=6；移项合并：x=6-4-3=-1",
    "analysis": "按标准步骤，先处理分母和括号，再移项合并"
  }
]',
'[
  {
    "concept": "步骤化思维",
    "explanation": "将复杂问题分解为简单步骤",
    "example": "复杂方程通过步骤化变为简单形式"
  },
  {
    "concept": "逻辑顺序",
    "explanation": "按合理顺序进行变形",
    "example": "先处理复杂结构，再进行基本运算"
  }
]',
'[
  "步骤顺序错乱",
  "某些步骤遗漏",
  "计算过程出错",
  "不验证最终结果"
]',
'[
  "步骤完整：按标准步骤进行",
  "逻辑清晰：每步都有理由",
  "计算仔细：避免计算错误",
  "必须验证：代入原方程检验"
]',
'{
  "emphasis": ["系统思维", "规范操作"],
  "application": ["问题系统化解决", "标准化流程"],
  "connection": ["数学方法论", "逻辑思维训练"]
}',
'{
  "emphasis": ["算法流程", "程序化思维"],
  "application": ["计算机求解", "算法设计"],
  "connection": ["程序设计", "算法优化"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'),

-- MATH_G7S1_CH5_007: 含分母的一元一次方程
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_CH5_007'),
'含分母的一元一次方程需要先去分母，方法是等式两边同时乘以各分母的最小公倍数',
'含分母的一元一次方程是方程求解中的重要类型，它在实际问题中广泛出现，如速度问题、浓度问题、工程问题等。去分母的关键是找到所有分母的最小公倍数，然后等式两边同时乘以这个数，将分数方程转化为整数方程。这一过程体现了数学中"化繁为简"的基本思想。去分母技能不仅简化计算，更重要的是为后续学习分式方程、有理方程奠定基础。掌握这一技能培养学生处理复杂问题的能力和化简意识。',
'[
  "含分母方程：方程中含有分数形式的项",
  "去分母：等式两边同时乘以各分母的最小公倍数",
  "最小公倍数：所有分母的最小公倍数",
  "去分母后化为整数方程",
  "注意：分母不能为0",
  "验证时要检查分母是否为0"
]',
'[
  {
    "name": "去分母法则",
    "formula": "等式两边同时乘以所有分母的最小公倍数",
    "description": "将分数方程转化为整数方程"
  },
  {
    "name": "最小公倍数求法",
    "formula": "LCM(a,b,c) = 最小公倍数",
    "description": "计算多个数的最小公倍数"
  },
  {
    "name": "分母限制",
    "formula": "分母 ≠ 0",
    "description": "分母不能为零的限制条件"
  }
]',
'[
  {
    "title": "解含分母方程",
    "problem": "解方程：x/2 + (x-1)/3 = 1",
    "solution": "最小公倍数是6，两边同时乘6：3x + 2(x-1) = 6，去括号：3x + 2x - 2 = 6，合并：5x = 8，所以x = 8/5",
    "analysis": "先求最小公倍数，去分母后按常规步骤求解"
  }
]',
'[
  {
    "concept": "最小公倍数",
    "explanation": "能被所有分母整除的最小正整数",
    "example": "2,3的最小公倍数是6"
  },
  {
    "concept": "化繁为简",
    "explanation": "将复杂的分数方程转化为简单的整数方程",
    "example": "去分母后计算更简便"
  }
]',
'[
  "最小公倍数计算错误",
  "去分母时漏乘某些项",
  "忘记分母不能为0的限制",
  "验证时不检查分母"
]',
'[
  "公倍数准确：正确计算最小公倍数",
  "全面去除：每一项都要乘以公倍数",
  "分母限制：检查分母不为0",
  "验证完整：检验解的有效性"
]',
'{
  "emphasis": ["计算技能", "化简思想"],
  "application": ["实际问题求解", "分数运算"],
  "connection": ["分数基础", "最小公倍数应用"]
}',
'{
  "emphasis": ["算法优化", "数值计算"],
  "application": ["计算机代数", "数值求解"],
  "connection": ["算法设计", "精度控制"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G7S1_CH5_008: 无限循环小数化分数
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_CH5_008'),
'利用一元一次方程将无限循环小数转化为分数，体现方程的实际应用价值',
'无限循环小数化分数是一元一次方程的重要应用，它展示了方程在解决数学理论问题中的价值。这一方法巧妙地利用了循环小数的周期性特征和方程的求解技能，将看似复杂的无限过程转化为有限的代数运算。这种转化不仅具有重要的理论意义，证明了有理数与无限循环小数的一一对应关系，而且在计算机科学、数值分析等实际应用中具有价值。掌握这一技能培养学生的数学建模能力、抽象思维能力和问题转化能力。',
'[
  "循环小数：小数部分从某位起重复出现的小数",
  "纯循环小数：从小数点后第一位就开始循环",
  "混循环小数：从小数点后某位开始循环",
  "方程方法：设循环小数为x，利用循环性质建立方程",
  "扩大倍数：根据循环节位数确定倍数",
  "化简分数：将得到的分数化为最简分数"
]',
'[
  {
    "name": "纯循环小数化分数",
    "formula": "设x=0.ab̄，则100x-x=ab，x=ab/99",
    "description": "纯循环小数的转化方法"
  },
  {
    "name": "混循环小数化分数",
    "formula": "设x=0.abc̄，则1000x-10x=abc-ab，x=(abc-ab)/990",
    "description": "混循环小数的转化方法"
  },
  {
    "name": "循环节位数规律",
    "formula": "n位循环节对应10ⁿ倍数",
    "description": "扩大倍数的选择规律"
  }
]',
'[
  {
    "title": "循环小数化分数",
    "problem": "将0.3̄化为分数",
    "solution": "设x=0.3̄=0.333...，则10x=3.333...，所以10x-x=3，9x=3，x=1/3",
    "analysis": "利用循环性质建立方程，通过移项求解"
  }
]',
'[
  {
    "concept": "循环性质",
    "explanation": "循环小数的周期性特征",
    "example": "0.3̄表示0.333...无限重复"
  },
  {
    "concept": "方程建模",
    "explanation": "用方程表达循环小数的性质",
    "example": "设x=循环小数，建立等式关系"
  }
]',
'[
  "扩大倍数选择错误",
  "方程建立不正确",
  "计算过程出错",
  "分数不化简到最简形式"
]',
'[
  "倍数对应：循环节位数对应扩大倍数",
  "方程准确：正确建立等式关系",
  "计算仔细：避免运算错误",
  "结果化简：分数要化为最简形式"
]',
'{
  "emphasis": ["数学建模", "抽象思维"],
  "application": ["数论研究", "理论证明"],
  "connection": ["有理数理论", "数学分析基础"]
}',
'{
  "emphasis": ["算法设计", "数值表示"],
  "application": ["计算机科学", "数值计算"],
  "connection": ["数值分析", "计算机数学"]
}',
'1.0', true, 'zh-CN', 4.7, 'approved'),

-- ============================================
-- 5.3 实际问题与一元一次方程部分
-- ============================================

-- MATH_G7S1_CH5_009: 方程解决实际问题的步骤
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_CH5_009'),
'用方程解决实际问题的一般步骤：审题、设未知数、列方程、解方程、检验答案',
'用方程解决实际问题是数学应用的核心技能，它将抽象的数学工具与具体的现实问题相结合。这一过程体现了数学建模的基本思想：将实际问题数学化、数学问题形式化、形式问题算法化、算法问题程序化。标准的解题步骤确保了问题求解的系统性和完整性。这种方法在工程设计、经济分析、科学研究等领域都有广泛应用。掌握这一技能不仅能解决具体的数学问题，更重要的是培养学生的问题分析能力、逻辑思维能力和数学建模意识。',
'[
  "审题：理解题意，找出已知条件和所求问题",
  "设未知数：根据所求设定合适的未知数",
  "列方程：根据等量关系列出方程",
  "解方程：运用解方程技能求出未知数的值",
  "检验答案：检验解是否符合实际意义",
  "答题规范：完整回答原问题"
]',
'[
  {
    "name": "解题步骤",
    "formula": "审题 → 设未知数 → 列方程 → 解方程 → 检验答案",
    "description": "用方程解决实际问题的标准流程"
  },
  {
    "name": "等量关系",
    "formula": "根据题意找出数量间的相等关系",
    "description": "列方程的关键环节"
  },
  {
    "name": "实际检验",
    "formula": "解要符合实际问题的意义",
    "description": "确保答案的合理性"
  }
]',
'[
  {
    "title": "实际问题求解",
    "problem": "一件商品原价100元，打8折后比原价少多少元？",
    "solution": "设打8折后少x元。等量关系：原价-少的钱=现价，即100-x=100×0.8，解得x=20。答：比原价少20元。",
    "analysis": "关键是找到等量关系：原价-减少量=现价"
  }
]',
'[
  {
    "concept": "数学建模",
    "explanation": "将实际问题转化为数学问题",
    "example": "用数学语言描述现实情况"
  },
  {
    "concept": "等量关系",
    "explanation": "题目中隐含的相等关系",
    "example": "总量=部分1+部分2"
  }
]',
'[
  "审题不仔细，理解题意错误",
  "未知数设定不合理",
  "等量关系找错",
  "不检验答案的实际意义"
]',
'[
  "仔细审题：多读几遍，理解题意",
  "合理设元：选择合适的未知数",
  "找准关系：准确找出等量关系",
  "必须检验：确保答案符合实际"
]',
'{
  "emphasis": ["问题分析", "建模思维"],
  "application": ["生活问题求解", "实际应用"],
  "connection": ["数学建模", "实践应用"]
}',
'{
  "emphasis": ["系统化方法", "算法思维"],
  "application": ["计算机建模", "系统分析"],
  "connection": ["软件工程", "系统设计"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G7S1_CH5_010: 配套问题
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_CH5_010'),
'配套问题是关于两种或多种物品按一定比例搭配的实际问题',
'配套问题在生活和生产中非常常见，如服装搭配、零件配套、原料配比等。这类问题的核心是理解"配套"的含义：按照固定的比例关系进行搭配，使得各部分协调统一。配套问题培养学生的比例思维、系统思维和资源优化意识。在现代制造业中，配套问题对应着生产计划、库存管理、供应链优化等重要应用。解决配套问题不仅锻炼数学运算能力，更重要的是培养学生的逻辑分析能力和实际问题求解能力。',
'[
  "配套：按固定比例搭配使用",
  "关键信息：配套比例、现有数量、求配套套数",
  "基本思路：用最少的那种物品确定配套数",
  "常见类型：求最多配套数、求还需要多少",
  "等量关系：各物品数量与配套数的关系",
  "实际意义：配套数必须是整数"
]',
'[
  {
    "name": "配套基本关系",
    "formula": "配套数 = 某物品数量 ÷ 该物品在每套中的数量",
    "description": "计算最大配套数的公式"
  },
  {
    "name": "最少原则",
    "formula": "最大配套数 = min{各物品可配套数}",
    "description": "由最少的那种物品决定总配套数"
  }
]',
'[
  {
    "title": "配套问题求解",
    "problem": "做校服，上衣和裤子按1:1配套。现有布料可做上衣30件，裤子25条，最多能配成多少套校服？",
    "solution": "设最多配成x套校服。每套需要上衣1件，裤子1条。上衣够配30套，裤子够配25套，所以最多配25套。",
    "analysis": "配套数由最少的物品数量决定"
  }
]',
'[
  {
    "concept": "比例配套",
    "explanation": "各物品按固定比例进行搭配",
    "example": "上衣:裤子=1:1的配套关系"
  },
  {
    "concept": "资源限制",
    "explanation": "受最稀缺资源的限制",
    "example": "最少的那种物品决定总数"
  }
]',
'[
  "不理解配套的比例关系",
  "忽视最少物品的限制作用",
  "配套数不是整数",
  "没有检验答案的合理性"
]',
'[
  "比例理解：明确各物品的配套比例",
  "最少限制：找出最稀缺的物品",
  "整数要求：配套数必须是正整数",
  "实际检验：验证答案是否合理"
]',
'{
  "emphasis": ["比例思维", "资源优化"],
  "application": ["生产计划", "资源配置"],
  "connection": ["比例关系", "优化思想"]
}',
'{
  "emphasis": ["约束优化", "系统分析"],
  "application": ["运筹学", "供应链管理"],
  "connection": ["线性规划", "资源分配"]
}',
'1.0', true, 'zh-CN', 4.7, 'approved'),

-- MATH_G7S1_CH5_011: 工程问题
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_CH5_011'),
'工程问题是关于工作效率、工作时间和工作总量关系的实际问题',
'工程问题是数学应用中的重要类型，它反映了现实生活中的工作效率和时间安排问题。这类问题的核心是理解工作总量、工作效率、工作时间三者之间的关系：工作总量=工作效率×工作时间。工程问题培养学生的效率意识、时间观念和合理规划能力。在现代社会中，工程问题对应着项目管理、人力资源配置、生产效率优化等重要应用。掌握工程问题的解法不仅提高数学应用能力，更重要的是培养学生的逻辑分析能力和实际问题求解思维。',
'[
  "基本关系：工作总量=工作效率×工作时间",
  "工作总量常设为1（整个工程）",
  "工作效率=1/完成工程所需时间",
  "多人合作：总效率=各人效率之和",
  "关键信息：各人单独完成时间、合作时间",
  "常见类型：求合作时间、求单独完成时间"
]',
'[
  {
    "name": "工程基本关系",
    "formula": "工作总量 = 工作效率 × 工作时间",
    "description": "工程问题的基本数量关系"
  },
  {
    "name": "工作效率",
    "formula": "工作效率 = 1/单独完成时间",
    "description": "效率与时间的反比关系"
  },
  {
    "name": "合作效率",
    "formula": "总效率 = 效率₁ + 效率₂ + ... + 效率ₙ",
    "description": "多人合作时效率相加"
  }
]',
'[
  {
    "title": "工程问题求解",
    "problem": "甲单独完成某工程需要12天，乙单独完成需要18天，两人合作需要多少天？",
    "solution": "设工程总量为1。甲的效率=1/12，乙的效率=1/18，合作效率=1/12+1/18=3/36+2/36=5/36。合作时间=1÷(5/36)=36/5=7.2天。",
    "analysis": "先求各自效率，再求合作效率，最后求合作时间"
  }
]',
'[
  {
    "concept": "效率概念",
    "explanation": "单位时间内完成的工作量",
    "example": "甲12天完成，效率为1/12"
  },
  {
    "concept": "合作效果",
    "explanation": "多人合作效率为各人效率之和",
    "example": "效率相加，时间缩短"
  }
]',
'[
  "不理解效率与时间的关系",
  "合作效率计算错误",
  "工作总量设定不当",
  "单位换算错误"
]',
'[
  "关系理解：明确总量、效率、时间关系",
  "效率相加：合作时效率要相加",
  "总量设1：通常设工作总量为1",
  "单位统一：注意时间单位的统一"
]',
'{
  "emphasis": ["效率思维", "时间管理"],
  "application": ["项目管理", "工作安排"],
  "connection": ["效率概念", "时间规划"]
}',
'{
  "emphasis": ["优化算法", "资源配置"],
  "application": ["项目调度", "人力资源"],
  "connection": ["运筹学", "管理科学"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G7S1_CH5_012: 行程问题
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_CH5_012'),
'行程问题是关于路程、速度和时间关系的实际问题',
'行程问题是数学应用中最经典的问题类型之一，它直接反映了物理学中匀速直线运动的基本规律。这类问题的核心是理解路程、速度、时间三者之间的关系：路程=速度×时间。行程问题不仅是数学知识的应用，更是物理概念的体现，培养学生的时空观念和运动思维。在现代交通、物流、导航等领域，行程问题有着广泛的实际应用。掌握行程问题的解法培养学生的逻辑分析能力、空间想象能力和实际问题求解思维。',
'[
  "基本关系：路程=速度×时间",
  "相遇问题：两物体相向而行",
  "追及问题：两物体同向而行",
  "环形问题：在环形路线上运动",
  "关键信息：速度、时间、距离",
  "常见类型：求相遇时间、追及时间、行程距离"
]',
'[
  {
    "name": "行程基本关系",
    "formula": "路程 = 速度 × 时间",
    "description": "行程问题的基本数量关系"
  },
  {
    "name": "相遇问题",
    "formula": "相遇时间 = 初始距离 ÷ (速度₁ + 速度₂)",
    "description": "相向运动的相遇时间公式"
  },
  {
    "name": "追及问题",
    "formula": "追及时间 = 初始距离差 ÷ (快速度 - 慢速度)",
    "description": "同向运动的追及时间公式"
  }
]',
'[
  {
    "title": "行程问题求解",
    "problem": "甲乙两地相距300千米，两车同时从两地相向开出，甲车速度60千米/时，乙车速度40千米/时，多少小时后相遇？",
    "solution": "设x小时后相遇。甲车行程60x，乙车行程40x，相遇时两车行程之和等于总距离：60x+40x=300，解得x=3小时。",
    "analysis": "相向运动，速度相加，路程相加等于总距离"
  }
]',
'[
  {
    "concept": "运动方向",
    "explanation": "区分相向运动和同向运动",
    "example": "相向时速度相加，同向时速度相减"
  },
  {
    "concept": "时间统一",
    "explanation": "所有物体运动时间相同",
    "example": "同时出发，相遇时运动时间相等"
  }
]',
'[
  "不区分相向和同向运动",
  "速度和时间单位不统一",
  "等量关系建立错误",
  "不理解相遇和追及的含义"
]',
'[
  "方向明确：分清相向还是同向",
  "单位统一：速度和时间单位要一致",
  "关系准确：正确建立等量关系",
  "概念理解：明确相遇、追及的含义"
]',
'{
  "emphasis": ["运动思维", "时空观念"],
  "application": ["交通规划", "物流安排"],
  "connection": ["物理运动", "实际应用"]
}',
'{
  "emphasis": ["算法模型", "优化计算"],
  "application": ["导航系统", "路径规划"],
  "connection": ["计算机科学", "人工智能"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G7S1_CH5_013: 数字问题
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_CH5_013'),
'数字问题是关于数的性质和数位特征的实际问题',
'数字问题是数学中富有趣味性和挑战性的问题类型，它深入探讨数的本质特征和规律。这类问题涉及数位概念、进位制原理、数的性质等基础数学知识。数字问题不仅锻炼计算能力，更重要的是培养学生的数感、逻辑推理能力和抽象思维。在密码学、信息科学、计算机科学等现代技术领域，数字问题的思想方法有着重要应用。掌握数字问题的解法有助于深化对数系的理解，提高数学思维的严密性和灵活性。',
'[
  "两位数表示：10a+b（a为十位数字，b为个位数字）",
  "三位数表示：100a+10b+c（a为百位，b为十位，c为个位）",
  "数位概念：个位、十位、百位等",
  "数字范围：各数位数字为0-9，首位不为0",
  "常见类型：数字交换、数字和差、数字倍数",
  "等量关系：基于数位表示建立方程"
]',
'[
  {
    "name": "两位数表示",
    "formula": "两位数 = 10 × 十位数字 + 个位数字",
    "description": "两位数的数位表示法"
  },
  {
    "name": "三位数表示",
    "formula": "三位数 = 100 × 百位数字 + 10 × 十位数字 + 个位数字",
    "description": "三位数的数位表示法"
  },
  {
    "name": "数字交换",
    "formula": "原数10a+b，交换后10b+a",
    "description": "两位数十位个位交换的表示"
  }
]',
'[
  {
    "title": "数字问题求解",
    "problem": "一个两位数，十位数字比个位数字大2，如果把十位数字和个位数字交换，得到的新数比原数小18，求原数。",
    "solution": "设个位数字为x，则十位数字为x+2。原数=10(x+2)+x=11x+20，新数=10x+(x+2)=11x+2。根据题意：(11x+20)-(11x+2)=18，解得18=18恒成立。再由数字范围：x+2≤9，x≥0，得x≤7。验证：当x=6时，原数=86，新数=68，差为18。",
    "analysis": "用数位表示法建立等量关系，注意数字的取值范围"
  }
]',
'[
  {
    "concept": "数位表示",
    "explanation": "用位置值表示数的组成",
    "example": "23=2×10+3"
  },
  {
    "concept": "数字范围",
    "explanation": "各数位数字的取值限制",
    "example": "十位数字1-9，个位数字0-9"
  }
]',
'[
  "数位表示错误",
  "忽略数字取值范围",
  "等量关系建立不当",
  "不验证答案的合理性"
]',
'[
  "数位准确：正确使用数位表示法",
  "范围注意：考虑数字的取值范围",
  "关系清晰：准确建立等量关系",
  "验证答案：检查结果是否合理"
]',
'{
  "emphasis": ["数位概念", "逻辑推理"],
  "application": ["数字游戏", "智力题目"],
  "connection": ["进位制", "数的性质"]
}',
'{
  "emphasis": ["数值表示", "算法思维"],
  "application": ["密码学", "信息编码"],
  "connection": ["计算机科学", "数字系统"]
}',
'1.0', true, 'zh-CN', 4.7, 'approved'),

-- MATH_G7S1_CH5_014: 初步认识数学模型
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_CH5_014'),
'数学模型是用数学语言描述现实世界现象和规律的数学结构',
'数学建模是现代数学最重要的应用方向，它将现实世界的复杂问题转化为数学问题进行研究。一元一次方程是最基本的数学模型，它能够描述许多线性关系和比例关系。数学建模过程体现了从具体到抽象、从实际到理论的思维转换，培养学生的抽象思维、逻辑推理和创新能力。在现代科学技术发展中，数学建模已成为解决实际问题的重要手段，广泛应用于工程技术、经济管理、生物医学、环境科学等各个领域。掌握数学建模思想为学生今后的学习和工作奠定重要基础。',
'[
  "数学模型：用数学方法描述现实问题的数学结构",
  "建模过程：现实问题→数学问题→数学解→现实解",
  "模型特点：抽象性、简化性、近似性",
  "一元一次方程模型：描述线性关系的基本模型",
  "模型验证：检验模型的合理性和适用性",
  "模型应用：用模型解决实际问题"
]',
'[
  {
    "name": "数学建模过程",
    "formula": "现实问题 → 数学模型 → 数学解 → 现实解答",
    "description": "数学建模的基本流程"
  },
  {
    "name": "线性模型",
    "formula": "y = ax + b (一元一次方程模型)",
    "description": "描述线性关系的数学模型"
  },
  {
    "name": "模型检验",
    "formula": "理论结果与实际情况的对比验证",
    "description": "验证模型有效性的方法"
  }
]',
'[
  {
    "title": "建立数学模型",
    "problem": "某出租车收费标准：起步价8元（3公里内），超过3公里每公里2元。乘坐x公里(x>3)需要多少元？",
    "solution": "建立模型：总费用=起步价+超出部分费用，即y=8+2(x-3)=2x+2。这是一个一元一次方程模型，描述了费用与距离的线性关系。",
    "analysis": "将实际收费规则转化为数学表达式，建立线性模型"
  }
]',
'[
  {
    "concept": "抽象化",
    "explanation": "从具体现象中提取数学关系",
    "example": "将收费规则抽象为数学公式"
  },
  {
    "concept": "简化假设",
    "explanation": "忽略次要因素，突出主要关系",
    "example": "假设匀速行驶，忽略交通堵塞"
  }
]',
'[
  "过度简化，忽略重要因素",
  "模型过于复杂，脱离实际",
  "不验证模型的适用性",
  "混淆数学解与实际解"
]',
'[
  "合理抽象：既要简化又要保留本质",
  "假设明确：明确模型的假设条件",
  "验证重要：检验模型的合理性",
  "实际回归：数学解要回到实际问题"
]',
'{
  "emphasis": ["建模思维", "抽象能力"],
  "application": ["科学研究", "工程设计"],
  "connection": ["数学应用", "科学方法"]
}',
'{
  "emphasis": ["系统分析", "模型设计"],
  "application": ["计算机建模", "人工智能"],
  "connection": ["系统工程", "算法设计"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved');

-- ============================================
-- 数据插入结束标志
-- ============================================
