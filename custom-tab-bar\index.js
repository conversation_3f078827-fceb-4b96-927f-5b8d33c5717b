Component({
  /**
   * 组件的属性列表
   */
  properties: {
    activeTab: {
      type: String,
      value: 'history' // 默认激活的tab
    },
    isIPhoneX: {
      type: Boolean,
      value: false
    },
    isIPad: {
      type: Boolean,
      value: false
    },
    safeAreaBottom: {
      type: Number,
      value: 0
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 底部导航项配置
    tabs: [
      { id: 'history', text: '对话', icon: 'icon-history' },
      { id: 'class', text: '班级', icon: 'icon-class' },
      { id: 'ai', text: 'AI推荐', icon: 'icon-ai' },
      { id: 'notebook', text: '错题本', icon: 'icon-notebook' },
      { id: 'profile', text: '我的', icon: 'icon-profile' }
    ]
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 点击底部导航项
     */
    onTabTap(e) {
      const type = e.currentTarget.dataset.type;
      
      // 如果点击的就是当前tab，不做处理
      if (type === this.properties.activeTab) {
        return;
      }
      
      // 触发自定义事件，通知父组件
      this.triggerEvent('tabchange', { type });
      
      // 根据类型进行页面跳转
      switch(type) {
        case 'history':
          wx.switchTab({
            url: '/pages/history/history'
          });
          break;
        case 'class':
          wx.switchTab({
            url: '/pages/class/index'
          });
          break;
        case 'ai':
          wx.switchTab({
            url: '/pages/ai/index'
          });
          break;
        case 'notebook':
          wx.switchTab({
            url: '/pages/notebook/index'
          });
          break;
        case 'profile':
          wx.switchTab({
            url: '/pages/profile/index'
          });
          break;
      }
    }
  }
}) 