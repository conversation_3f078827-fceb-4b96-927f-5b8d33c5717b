Component({
  properties: {
    percent: {
      type: Number,
      value: 0,
      observer(newVal) {
        this.drawProgressBg();
        this.drawProgressBar(newVal);
      }
    },
    size: {
      type: Number,
      value: 120
    },
    lineWidth: {
      type: Number,
      value: 8
    },
    lineColor: {
      type: String,
      value: '#3E7BFA'
    },
    bgColor: {
      type: String,
      value: '#E5E5E5'
    },
    gradientColors: {
      type: Array,
      value: []
    },
    showText: {
      type: Boolean,
      value: true
    },
    textColor: {
      type: String,
      value: '#333333'
    },
    textSize: {
      type: Number,
      value: 30
    },
    textFormat: {
      type: String,
      value: '{value}%'
    },
    animated: {
      type: Boolean,
      value: true
    },
    animDuration: {
      type: Number,
      value: 1000
    }
  },
  
  data: {
    ctx: null,
    bgCtx: null,
    displayPercent: 0
  },

  ready() {
    const query = this.createSelectorQuery();
    query.select('#progress-bg').fields({ node: true, size: true }).exec((res) => {
      const canvas = res[0].node;
      const ctx = canvas.getContext('2d');
      const dpr = wx.getSystemInfoSync().pixelRatio;
      canvas.width = this.properties.size * dpr;
      canvas.height = this.properties.size * dpr;
      ctx.scale(dpr, dpr);
      this.setData({ bgCtx: ctx });
      this.drawProgressBg();
    });

    query.select('#progress-bar').fields({ node: true, size: true }).exec((res) => {
      const canvas = res[0].node;
      const ctx = canvas.getContext('2d');
      const dpr = wx.getSystemInfoSync().pixelRatio;
      canvas.width = this.properties.size * dpr;
      canvas.height = this.properties.size * dpr;
      ctx.scale(dpr, dpr);
      this.setData({ ctx: ctx });
      this.drawProgressBar(this.properties.percent);
    });
  },

  methods: {
    drawProgressBg() {
      const { bgCtx, bgColor, size, lineWidth } = this.data;
      if (!bgCtx) return;
      
      bgCtx.clearRect(0, 0, size, size);
      bgCtx.lineWidth = lineWidth;
      bgCtx.strokeStyle = bgColor;
      bgCtx.lineCap = 'round';
      bgCtx.beginPath();
      bgCtx.arc(size / 2, size / 2, size / 2 - lineWidth / 2, 0, 2 * Math.PI);
      bgCtx.stroke();
    },

    drawProgressBar(percent) {
      const { ctx, size, lineWidth, lineColor, gradientColors, animated, animDuration } = this.data;
      if (!ctx) return;

      const radius = size / 2 - lineWidth / 2;
      const startAngle = -Math.PI / 2;
      
      if (animated) {
        const startPercent = this.data.displayPercent;
        const diff = percent - startPercent;
        const startTime = Date.now();
        
        const animate = () => {
          const now = Date.now();
          const elapsed = now - startTime;
          const progress = Math.min(elapsed / animDuration, 1);
          const currentPercent = startPercent + diff * progress;
          
          this.setData({ displayPercent: currentPercent });
          
          ctx.clearRect(0, 0, size, size);
          ctx.lineWidth = lineWidth;
          ctx.lineCap = 'round';
          
          if (gradientColors && gradientColors.length > 1) {
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradientColors.forEach((color, index) => {
              gradient.addColorStop(index / (gradientColors.length - 1), color);
            });
            ctx.strokeStyle = gradient;
          } else {
            ctx.strokeStyle = lineColor;
          }
          
          ctx.beginPath();
          ctx.arc(size / 2, size / 2, radius, startAngle, startAngle + (currentPercent / 100) * 2 * Math.PI);
          ctx.stroke();
          
          if (progress < 1) {
            wx.nextTick(animate);
          }
        };
        
        animate();
      } else {
        this.setData({ displayPercent: percent });
        
        ctx.clearRect(0, 0, size, size);
        ctx.lineWidth = lineWidth;
        ctx.lineCap = 'round';
        
        if (gradientColors && gradientColors.length > 1) {
          const gradient = ctx.createLinearGradient(0, 0, size, size);
          gradientColors.forEach((color, index) => {
            gradient.addColorStop(index / (gradientColors.length - 1), color);
          });
          ctx.strokeStyle = gradient;
        } else {
          ctx.strokeStyle = lineColor;
        }
        
        ctx.beginPath();
        ctx.arc(size / 2, size / 2, radius, startAngle, startAngle + (percent / 100) * 2 * Math.PI);
        ctx.stroke();
      }
    }
  }
}); 