{"description": "项目配置文件", "packOptions": {"ignore": [{"value": "/minitest", "type": "folder"}, {"value": "/database_scripts", "type": "folder"}, {"value": "/grade_scripts", "type": "folder"}, {"value": "/scripts", "type": "folder"}, {"value": "/docs", "type": "folder"}], "include": []}, "miniprogramRoot": "", "compileType": "miniprogram", "projectname": "K12数学学习助手", "setting": {"useCompilerPlugins": ["typescript", "less"], "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "es6": true, "enhance": true, "postcss": true, "minified": true, "uglifyFileName": true, "checkInvalidKey": true, "uploadWithSourceMap": true, "packNpmManually": true, "packNpmRelationList": [], "minifyWXSS": true, "minifyWXML": true, "useStaticServer": true, "showES6CompileOption": false, "checkSiteMap": true, "disableUseStrict": false, "ignoreDevUnusedFiles": false, "ignoreUploadUnusedFiles": false, "compileWorklet": false, "localPlugins": false, "condition": false, "swc": false, "disableSWC": true}, "condition": {}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}, "libVersion": "3.8.0", "appid": "wx5d736153377de6df", "testRoot": "minitest/", "cloudfunctionRoot": "cloudfunctions/", "cloudfunctionTemplateRoot": "cloudfunctionTemplate/", "simulatorPluginLibVersion": {}, "cloudbaseRoot": "cloudbase/", "cloudBaseConfig": {"envId": "cloud1-0g3pg16f43f63333"}}