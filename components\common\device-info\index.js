Component({
  /**
   * 组件的属性列表
   */
  properties: {
    show: {
      type: Boolean,
      value: false
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    deviceInfo: {},
    screenInfo: {},
    safeArea: {},
    isIPX: false,
    isIPad: false,
    showMore: false
  },

  lifetimes: {
    attached() {
      this.getDeviceInfo();
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 获取设备信息
    getDeviceInfo() {
      try {
        const deviceUtils = require('../../../utils/device-info.js');
        const systemInfo = deviceUtils.getSystemInfoCompat();
        
        // 基本信息
        const deviceInfo = {
          brand: systemInfo.brand || '未知',
          model: systemInfo.model || '未知',
          system: systemInfo.system || '未知',
          platform: systemInfo.platform || '未知',
          SDKVersion: systemInfo.SDKVersion || '未知',
          version: systemInfo.version || '未知',
          language: systemInfo.language || '未知',
          batteryLevel: systemInfo.batteryLevel ? systemInfo.batteryLevel + '%' : '未知'
        };

        // 屏幕信息
        const screenInfo = {
          width: systemInfo.windowWidth || 0,
          height: systemInfo.windowHeight || 0,
          screenWidth: systemInfo.screenWidth || 0,
          screenHeight: systemInfo.screenHeight || 0,
          pixelRatio: systemInfo.pixelRatio || 1
        };

        // 安全区域
        const safeArea = systemInfo.safeArea || {
          top: 0,
          right: 0,
          bottom: 0,
          left: 0,
          width: 0,
          height: 0
        };

        // 判断是否为iPhone X系列
        const isIPX = this.isIphoneX(systemInfo);
        
        // 判断是否为iPad
        const isIPad = systemInfo.model && systemInfo.model.indexOf('iPad') > -1;

        this.setData({
          deviceInfo,
          screenInfo,
          safeArea,
          isIPX,
          isIPad
        });
      } catch (e) {
        console.error('获取设备信息失败', e);
        wx.showToast({
          title: '获取设备信息失败',
          icon: 'none'
        });
      }
    },

    // 判断是否为iPhone X系列
    isIphoneX(systemInfo) {
      if (systemInfo.platform !== 'ios') return false;
      
      // iPhone X/XS: 375 x 812
      // iPhone XR/XS Max: 414 x 896
      // iPhone 12 Mini: 360 x 780
      // iPhone 12/12 Pro: 390 x 844
      // iPhone 12 Pro Max: 428 x 926
      const screenHeight = systemInfo.screenHeight;
      const safeArea = systemInfo.safeArea;
      
      return safeArea && 
             ((safeArea.top >= 44 && safeArea.bottom < screenHeight) || 
              systemInfo.model.indexOf('iPhone X') > -1 ||
              systemInfo.model.indexOf('iPhone 11') > -1 ||
              systemInfo.model.indexOf('iPhone 12') > -1 ||
              systemInfo.model.indexOf('iPhone 13') > -1 ||
              systemInfo.model.indexOf('iPhone 14') > -1 ||
              systemInfo.model.indexOf('iPhone 15') > -1);
    },

    // 关闭面板
    handleClose() {
      this.setData({
        show: false
      });
      this.triggerEvent('close');
    },

    // 复制信息
    handleCopy() {
      const systemInfo = wx.getSystemInfoSync();
      let infoText = `设备信息:\n`;
      infoText += `品牌: ${this.data.deviceInfo.brand}\n`;
      infoText += `型号: ${this.data.deviceInfo.model}\n`;
      infoText += `系统: ${this.data.deviceInfo.system}\n`;
      infoText += `平台: ${this.data.deviceInfo.platform}\n`;
      infoText += `屏幕宽度: ${this.data.screenInfo.width}px\n`;
      infoText += `屏幕高度: ${this.data.screenInfo.height}px\n`;
      infoText += `设备像素比: ${this.data.screenInfo.pixelRatio}\n`;
      infoText += `状态栏高度: ${systemInfo.statusBarHeight}px\n`;
      infoText += `安全区域: ${JSON.stringify(this.data.safeArea)}\n`;
      infoText += `微信版本: ${this.data.deviceInfo.version}\n`;
      infoText += `SDK版本: ${this.data.deviceInfo.SDKVersion}\n`;

      wx.setClipboardData({
        data: infoText,
        success: () => {
          wx.showToast({
            title: '设备信息已复制',
            icon: 'success'
          });
        },
        fail: () => {
          wx.showToast({
            title: '复制失败',
            icon: 'none'
          });
        }
      });
    },

    // 切换展示更多信息
    toggleMore() {
      this.setData({
        showMore: !this.data.showMore
      });
    }
  }
}) 