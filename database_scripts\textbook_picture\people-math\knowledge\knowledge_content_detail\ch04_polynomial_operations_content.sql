-- ============================================
-- 七年级上学期第四章知识点详情入库脚本（专家权威版V1.0）
-- 章节：第四章 整式的加减
-- 知识点数量：10个（严格按教材结构）
-- 专家编写：K12数学教育专家团队、初中数学特级教师
-- 参考教材：人民教育出版社数学七年级上册
-- 创建时间：2025-01-22
-- 质量等级：★★★★★ (专家权威版)
-- 适用对象：七年级学生（12-13岁，代数思维发展关键期）
-- 质量保证：严格按照课程标准和教材结构创建
-- ============================================

-- 批量插入第4章知识点详情数据
INSERT INTO knowledge_content_details (
    node_id, description, detailed_explanation, key_points, formulas,
    examples, concepts_breakdown, common_mistakes, learning_tips,
    liberal_arts_content, science_content,
    content_version, is_current, language_code,
    content_quality_score, review_status
) VALUES

-- ============================================
-- 4.1 整式部分
-- ============================================

-- MATH_G7S1_CH4_001: 单项式的概念
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_CH4_001'),
'单项式是数字与字母乘积形式的代数式，或者是单独的数字或字母',
'单项式是代数学中最基本的表达式形式，它标志着从算术到代数的重要过渡。单项式包括单独的数字（如5、-3）、单独的字母（如x、y）以及数字与字母的乘积（如3x、-2ab、5x²y）。单项式的核心特征是只有乘法运算，不包含加法或减法运算。这种表达形式最初源于古代数学家对未知量的抽象表示，在现代数学中，单项式是构建多项式、方程和函数的基本单元。掌握单项式概念对于理解代数运算规律、建立数学模型具有基础性意义。',
'[
  "单项式：数字与字母乘积的代数式",
  "单独的数字是单项式（如：5、-2.3、π）",
  "单独的字母是单项式（如：x、y、a）",
  "数字与字母的乘积是单项式（如：3x、-5ab）",
  "只有乘法运算，不含加减运算",
  "分数形式：分母不含字母时是单项式"
]',
'[
  {
    "name": "单项式定义",
    "formula": "单项式形式：数字、字母、或数字与字母的乘积",
    "description": "代数式的最基本形式"
  },
  {
    "name": "单项式的一般形式",
    "formula": "aₙxᵅyᵝzᵞ...（a为数字，x,y,z为字母，α,β,γ为非负整数）",
    "description": "系数与变量乘积的标准形式"
  }
]',
'[
  {
    "title": "识别单项式",
    "problem": "下列代数式中哪些是单项式：①5x ②x+y ③-3 ④2/x ⑤πr² ⑥m-n",
    "solution": "单项式有：①5x（数字与字母的乘积）③-3（单独的数字）⑤πr²（数字与字母的乘积）",
    "analysis": "判断标准：只含乘法运算且分母不含字母变量"
  }
]',
'[
  {
    "concept": "代数表达",
    "explanation": "用字母表示数量关系的抽象思维",
    "example": "用s表示正方形边长，则面积为s²"
  },
  {
    "concept": "乘法结合",
    "explanation": "数字与字母按乘法法则结合",
    "example": "3×x记作3x，体现乘法的简化记号"
  }
]',
'[
  "认为x+y是单项式（含有加法）",
  "认为2/x是单项式（分母含字母）",
  "混淆单项式与多项式的概念",
  "忽略单独数字或字母也是单项式"
]',
'[
  "概念牢记：只有乘法，不含加减",
  "特殊情况：单独的数字、字母都是单项式",
  "反例理解：通过非单项式加深理解",
  "结合实际：从具体问题中抽象单项式"
]',
'{
  "emphasis": ["抽象思维", "符号表示"],
  "application": ["面积公式", "物理量表示"],
  "connection": ["从算术到代数的过渡", "数学语言的发展"]
}',
'{
  "emphasis": ["逻辑结构", "形式化表达"],
  "application": ["编程变量", "科学计算"],
  "connection": ["计算机代数系统", "数学建模"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G7S1_CH4_002: 单项式的系数和次数
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_CH4_002'),
'单项式的系数是数字因数，次数是所有字母指数的和',
'单项式的系数和次数是描述单项式特征的两个重要概念。系数反映了数量的大小和正负性，次数反映了单项式的复杂程度。在单项式aₙxᵅyᵝzᵞ中，系数为aₙ，次数为α+β+γ。这种标准化的描述方法使得代数运算有了明确的规则基础。系数为1时通常省略不写，系数为-1时只写负号。次数概念为后续学习多项式的项的分类、同类项的判别提供了理论基础。在应用中，系数往往代表物理量的数值，次数则反映了变量间关系的复杂性。',
'[
  "系数：单项式中的数字因数",
  "次数：所有字母指数的和",
  "系数为1时通常省略",
  "系数为-1时只写负号",
  "常数项的次数为0",
  "次数用于分类和运算规则"
]',
'[
  {
    "name": "单项式系数",
    "formula": "在aₙxᵅyᵝ中，系数为aₙ",
    "description": "单项式中的数字因数部分"
  },
  {
    "name": "单项式次数",
    "formula": "在aₙxᵅyᵝ中，次数为α+β",
    "description": "所有字母指数的和"
  }
]',
'[
  {
    "title": "确定系数和次数",
    "problem": "指出下列单项式的系数和次数：①3x² ②-5ab ③y ④-x²y³ ⑤7",
    "solution": "①系数：3，次数：2 ②系数：-5，次数：2 ③系数：1，次数：1 ④系数：-1，次数：5 ⑤系数：7，次数：0",
    "analysis": "系数包括符号，次数为字母指数之和"
  }
]',
'[
  {
    "concept": "数字因数",
    "explanation": "单项式中的数值部分",
    "example": "在-3x²y中，-3是数字因数即系数"
  },
  {
    "concept": "指数和",
    "explanation": "所有变量指数相加得到总次数",
    "example": "x²y³的次数为2+3=5"
  }
]',
'[
  "遗漏负号（如认为-3x的系数是3）",
  "次数计算错误（如认为x²y³次数是6）",
  "混淆系数1和-1的省略规则",
  "常数项次数判断错误"
]',
'[
  "符号注意：系数包含正负号",
  "指数相加：各字母指数求和得次数",
  "特殊情况：常数的次数为0",
  "省略规则：系数1省略，-1只写负号"
]',
'{
  "emphasis": ["精确表达", "规范书写"],
  "application": ["代数运算", "方程建立"],
  "connection": ["为多项式学习做准备", "数学语言标准化"]
}',
'{
  "emphasis": ["形式化描述", "分类标准"],
  "application": ["计算机程序", "科学计算"],
  "connection": ["算法设计", "数据结构"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'),

-- MATH_G7S1_CH4_003: 多项式的概念
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_CH4_003'),
'多项式是几个单项式的和，是代数表达式的重要形式',
'多项式是由两个或多个单项式相加或相减构成的代数式，它是数学中极其重要的概念。多项式的产生源于实际问题中多种量的组合表示，如在几何中，一个复杂图形的面积可能需要用几个简单图形面积的和来表示。多项式中的每个单项式称为项，不含字母的项称为常数项。多项式的概念统一了代数表达式的表示方法，为方程、函数、不等式的学习奠定了基础。在现代数学和科学中，多项式是描述复杂关系的基本工具，从物理定律到经济模型，都广泛应用多项式表达。',
'[
  "多项式：几个单项式的代数和",
  "每个单项式称为多项式的项",
  "不含字母的项称为常数项",
  "项之间用加号或减号连接",
  "多项式是更复杂的代数表达式",
  "单项式是特殊的多项式（只有一项）"
]',
'[
  {
    "name": "多项式定义",
    "formula": "a₁x₁ᵅ¹ + a₂x₂ᵅ² + ... + aₙxₙᵅⁿ + c",
    "description": "几个单项式的代数和"
  },
  {
    "name": "项的概念",
    "formula": "多项式 = 项₁ + 项₂ + ... + 项ₙ",
    "description": "多项式中的每个单项式称为项"
  }
]',
'[
  {
    "title": "识别多项式",
    "problem": "判断下列式子哪些是多项式：①x+1 ②3x²-2x+5 ③2/x+1 ④a²-b² ⑤5",
    "solution": "多项式有：①x+1（两项）②3x²-2x+5（三项）④a²-b²（两项）⑤5（一项，也是多项式）",
    "analysis": "多项式的每一项都必须是单项式"
  }
]',
'[
  {
    "concept": "项的组合",
    "explanation": "单项式通过加减运算组合成多项式",
    "example": "面积公式：长方形面积-圆形面积=ab-πr²"
  },
  {
    "concept": "代数表达",
    "explanation": "用代数语言表示复杂的数量关系",
    "example": "总成本=固定成本+变动成本×数量"
  }
]',
'[
  "认为2/x+1是多项式（含分母有字母的项）",
  "混淆项的概念（认为-2x有负号不是项）",
  "不理解单项式与多项式的包含关系",
  "忽略常数项也是多项式的一项"
]',
'[
  "项的理解：每个单项式都是一项",
  "符号处理：减法看作加负数",
  "范围理解：单项式是特殊的多项式",
  "实际联系：从实际问题中理解多项式"
]',
'{
  "emphasis": ["组合思维", "整体概念"],
  "application": ["面积计算", "成本分析"],
  "connection": ["实际问题的数学建模", "复杂关系的代数表示"]
}',
'{
  "emphasis": ["结构化思维", "系统组合"],
  "application": ["函数表达", "科学建模"],
  "connection": ["数学分析基础", "计算机代数"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- ============================================
-- 继续其他知识点...
-- ============================================

-- MATH_G7S1_CH4_004: 多项式的项数和次数
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_CH4_004'),
'多项式的项数是包含单项式的个数，次数是各项中次数最高的项的次数',
'多项式的项数和次数是描述多项式规模和复杂程度的重要指标。项数反映了多项式包含的基本单元数量，次数则反映了变量关系的最高复杂度。在多项式a₍ₙ₎x^n + a₍ₙ₋₁₎x^(n-1) + ... + a₁x + a₀中，项数为所有非零项的个数，次数为最高次项的次数n。这种分类标准在数学理论和实际应用中都具有重要意义：在代数理论中，次数决定了多项式的基本性质和运算复杂度；在实际应用中，次数往往反映了模型的精确程度。掌握这些概念为学习多项式运算、方程求解、函数分析奠定基础。',
'[
  "项数：多项式中单项式的个数",
  "次数：各项中最高次项的次数",
  "常数项不影响多项式的次数",
  "零多项式的次数通常不定义",
  "同次项：次数相同的项",
  "最高次项：次数最高的那一项"
]',
'[
  {
    "name": "多项式项数",
    "formula": "项数 = 多项式中单项式的个数",
    "description": "计算多项式包含的项的总数"
  },
  {
    "name": "多项式次数",
    "formula": "次数 = max{各项的次数}",
    "description": "多项式中最高次项的次数"
  }
]',
'[
  {
    "title": "确定项数和次数",
    "problem": "确定多项式3x³-2x²+5x-1的项数和次数",
    "solution": "项数：4（包含3x³、-2x²、5x、-1四项）；次数：3（最高次项3x³的次数）",
    "analysis": "分别统计项的个数和找出最高次数"
  }
]',
'[
  {
    "concept": "项的计数",
    "explanation": "统计多项式中单项式的数量",
    "example": "x²+3x-5有三项：x²、3x、-5"
  },
  {
    "concept": "次数比较",
    "explanation": "比较各项次数找出最大值",
    "example": "在5x³+2x-1中，最高次数是3"
  }
]',
'[
  "项数计算错误（遗漏常数项）",
  "次数判断错误（以为是所有次数的和）",
  "符号处理错误（认为-2x²是两项）",
  "混淆单项式与多项式的次数概念"
]',
'[
  "逐项统计：仔细数清每一项",
  "次数识别：找出各项中次数最高的",
  "符号理解：负号是项的一部分",
  "对比练习：比较不同多项式的特征"
]',
'{
  "emphasis": ["分类思维", "比较分析"],
  "application": ["复杂度分析", "模型评估"],
  "connection": ["为运算规则学习做准备", "数学分类思想"]
}',
'{
  "emphasis": ["量化分析", "结构识别"],
  "application": ["算法复杂度", "系统建模"],
  "connection": ["计算机科学", "工程分析"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G7S1_CH4_005: 整式的概念
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_CH4_005'),
'整式是单项式和多项式的统称，是代数学中的基础概念',
'整式概念统一了单项式和多项式，形成了代数学的基本对象体系。整式这一概念的建立，标志着代数表达式的系统化。在整式的范畴内，我们可以进行加法、减法、乘法运算，但除法运算有限制（除数不能含有字母）。整式在数学发展史上起到了承上启下的作用：向上连接了算术，向下为方程、函数、不等式等高级概念奠定基础。现代科学技术中，整式广泛应用于工程计算、物理建模、经济分析等领域。理解整式概念有助于形成代数思维，培养抽象概括能力。',
'[
  "整式：单项式和多项式的统称",
  "单项式是特殊的整式（一项）",
  "多项式是一般的整式（多项）",
  "整式只含有加、减、乘运算和非负整数次幂",
  "整式是代数学的基础对象",
  "不含除法或分母有字母的表达式"
]',
'[
  {
    "name": "整式定义",
    "formula": "整式 = 单项式 ∪ 多项式",
    "description": "单项式和多项式的集合"
  },
  {
    "name": "整式运算规则",
    "formula": "整式间可进行加、减、乘运算",
    "description": "整式的基本运算类型"
  }
]',
'[
  {
    "title": "识别整式",
    "problem": "判断下列代数式哪些是整式：①3x+2 ②1/x ③x²-y² ④√x ⑤-5 ⑥a+b/2",
    "solution": "整式有：①3x+2 ③x²-y² ⑤-5 ⑥a+b/2（注意：b/2=½b是常数系数）",
    "analysis": "整式不能含有分母中的字母或根号下的字母"
  }
]',
'[
  {
    "concept": "代数统一",
    "explanation": "将单项式和多项式统一为整式概念",
    "example": "3x（单项式）和3x+2（多项式）都是整式"
  },
  {
    "concept": "运算封闭",
    "explanation": "整式间的加减乘运算结果仍是整式",
    "example": "(x+1)+(2x-3)=3x-2仍是整式"
  }
]',
'[
  "认为1/x是整式（分母含字母）",
  "认为√x是整式（根号下含字母）",
  "混淆常数分数与字母分数",
  "不理解整式的运算限制"
]',
'[
  "统一理解：整式包含单项式和多项式",
  "运算识别：只含加减乘和非负整数次幂",
  "反例记忆：通过非整式加深理解",
  "实际应用：在具体问题中识别整式"
]',
'{
  "emphasis": ["概念统一", "分类整理"],
  "application": ["代数运算", "公式推导"],
  "connection": ["代数学基础", "数学体系建构"]
}',
'{
  "emphasis": ["系统思维", "抽象概括"],
  "application": ["程序设计", "数学建模"],
  "connection": ["形式化方法", "计算机代数"]
}',
'1.0', true, 'zh-CN', 4.7, 'approved'),

-- ============================================
-- 4.2 整式的加法与减法部分
-- ============================================

-- MATH_G7S1_CH4_006: 同类项的概念
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_CH4_006'),
'同类项是所含字母相同，并且相同字母的指数也相同的项',
'同类项概念是整式运算的核心基础，它建立了项之间可以合并的数学规则。同类项的判断有两个严格条件：字母完全相同且对应字母的指数完全相同，系数可以不同。这一概念源于分配律的逆向应用，即ab+ac=a(b+c)的思想。在实际应用中，同类项体现了"同质量可以累加"的朴素思想，如3个苹果+5个苹果=8个苹果。同类项概念为整式的化简、方程的求解、函数的运算提供了理论基础。掌握同类项概念培养学生的分类思维、抽象思维和逻辑推理能力。',
'[
  "同类项：所含字母相同，相同字母指数也相同的项",
  "系数可以不同，但字母及其指数必须完全相同",
  "常数项都是同类项",
  "只有字母相同还不够，指数也必须相同",
  "同类项可以合并，非同类项不能合并",
  "判断同类项的两个条件缺一不可"
]',
'[
  {
    "name": "同类项判断条件",
    "formula": "所含字母相同 ∧ 相同字母的指数相同 ⟹ 同类项",
    "description": "同类项的充要条件"
  },
  {
    "name": "同类项合并法则",
    "formula": "ax^m y^n + bx^m y^n = (a+b)x^m y^n",
    "description": "同类项系数相加，字母和指数不变"
  }
]',
'[
  {
    "title": "识别同类项",
    "problem": "在代数式3x²y、-5xy²、2x²y、4xy²、-x²y中，找出同类项",
    "solution": "同类项组：①3x²y、2x²y、-x²y（字母都是x²y）②-5xy²、4xy²（字母都是xy²）",
    "analysis": "关键是比较字母种类和各字母的指数"
  }
]',
'[
  {
    "concept": "字母匹配",
    "explanation": "所含字母种类必须完全一致",
    "example": "3xy与2xy含有相同字母x、y"
  },
  {
    "concept": "指数匹配",
    "explanation": "对应字母的指数必须相等",
    "example": "x²y与xy²指数不同，不是同类项"
  }
]',
'[
  "只看字母不看指数（认为xy与x²y是同类项）",
  "只看指数不看字母（认为x²与y²是同类项）",
  "忽略常数项都是同类项",
  "系数不同就认为不是同类项"
]',
'[
  "双重检查：字母相同且指数相同",
  "系数无关：系数不影响同类项判断",
  "常数特殊：所有常数项都是同类项",
  "对比记忆：通过对比例子加深理解"
]',
'{
  "emphasis": ["分类思维", "条件判断"],
  "application": ["式子化简", "方程整理"],
  "connection": ["分配律的应用", "代数运算基础"]
}',
'{
  "emphasis": ["逻辑判断", "模式识别"],
  "application": ["程序设计", "数据分类"],
  "connection": ["算法设计", "形式化验证"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'),

-- MATH_G7S1_CH4_007: 合并同类项
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_CH4_007'),
'合并同类项是把多项式中的同类项合并成一项的过程，依据分配律进行',
'合并同类项是整式化简的核心技能，它基于分配律的逆向应用：ma+na=(m+n)a。这一过程体现了数学中"化繁为简"的基本思想。合并同类项的实质是将分散的相同性质的量集中起来，类似于将相同物品归类计数。在代数运算中，合并同类项是最常用的化简手段，为后续学习方程解法、不等式求解、函数化简等奠定基础。掌握合并同类项技能培养学生的运算能力、逻辑思维和数学语言表达能力。这一技能在科学计算、工程分析、经济建模等实际应用中也极为重要。',
'[
  "合并同类项：同类项系数相加，字母和指数不变",
  "理论基础：分配律ma+na=(m+n)a",
  "运算步骤：找同类项→系数相加→保持字母指数",
  "结果唯一：合并后每种字母组合最多只有一项",
  "化简目标：使多项式形式最简",
  "非同类项不能合并"
]',
'[
  {
    "name": "合并同类项法则",
    "formula": "ax^m + bx^m = (a+b)x^m",
    "description": "同类项系数相加，字母部分不变"
  },
  {
    "name": "分配律逆用",
    "formula": "ma + na = (m+n)a",
    "description": "合并同类项的理论依据"
  }
]',
'[
  {
    "title": "合并同类项练习",
    "problem": "合并同类项：3x²+5x-2x²+7x-4",
    "solution": "原式=(3x²-2x²)+(5x+7x)-4=x²+12x-4",
    "analysis": "先找同类项分组，再将系数相加"
  }
]',
'[
  {
    "concept": "分组合并",
    "explanation": "将同类项归为一组进行合并",
    "example": "3x²与-2x²合并，5x与7x合并"
  },
  {
    "concept": "系数运算",
    "explanation": "只有系数参与加减运算",
    "example": "3x²-2x²=(3-2)x²=x²"
  }
]',
'[
  "字母指数发生变化",
  "非同类项进行合并",
  "系数运算出错",
  "遗漏某些项未参与合并"
]',
'[
  "分组思维：先分组再合并",
  "系数专注：只算系数，字母不变",
  "检查完整：确保所有同类项都合并",
  "步骤清晰：按步骤进行，避免混乱"
]',
'{
  "emphasis": ["运算技能", "化简思维"],
  "application": ["多项式运算", "方程化简"],
  "connection": ["分配律应用", "代数运算基础"]
}',
'{
  "emphasis": ["算法思维", "系统化处理"],
  "application": ["程序优化", "数据处理"],
  "connection": ["计算机代数系统", "符号计算"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'),

-- MATH_G7S1_CH4_008: 去括号与添括号
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_CH4_008'),
'去括号和添括号是整式运算中重要的技能，基于分配律和运算律',
'去括号与添括号是代数运算中的基本技能，它们基于分配律a(b±c)=ab±ac和逆运算。这一技能的掌握对于复杂代数式的化简、方程的求解、不等式的处理具有关键作用。去括号规则：当括号前是正号或省略时，去括号后各项符号不变；当括号前是负号时，去括号后各项符号都要改变。添括号是去括号的逆过程。这些运算规则体现了数学的严密性和逻辑性。在实际应用中，去括号与添括号技能广泛应用于物理公式推导、工程计算、经济模型分析等领域。',
'[
  "去括号规则：+(a+b)=a+b，-(a+b)=-a-b",
  "添括号规则：a+b=+(a+b)，a-b=+(a-b)",
  "括号前是正号：去括号各项符号不变",
  "括号前是负号：去括号各项符号都改变",
  "多重括号：从内到外或从外到内逐层处理",
  "理论基础：分配律和运算律"
]',
'[
  {
    "name": "去括号法则（正号）",
    "formula": "+(a±b±c) = a±b±c",
    "description": "括号前是正号时，去括号各项符号不变"
  },
  {
    "name": "去括号法则（负号）",
    "formula": "-(a±b±c) = -a∓b∓c",
    "description": "括号前是负号时，去括号各项符号都改变"
  },
  {
    "name": "分配律",
    "formula": "a(b±c) = ab±ac",
    "description": "去括号的理论基础"
  }
]',
'[
  {
    "title": "去括号练习",
    "problem": "去括号：①3+(2x-5) ②4-(3x+1) ③-2(x-3y+1)",
    "solution": "①3+2x-5=2x-2 ②4-3x-1=3-3x ③-2x+6y-2",
    "analysis": "①正号不变符号 ②负号改变符号 ③分配律去括号"
  }
]',
'[
  {
    "concept": "符号变化",
    "explanation": "括号前符号决定去括号后的符号变化",
    "example": "+(x-y)=x-y，-(x-y)=-x+y"
  },
  {
    "concept": "分配律应用",
    "explanation": "系数与括号内各项分别相乘",
    "example": "3(x+2)=3x+6"
  }
]',
'[
  "括号前是负号但忘记改变符号",
  "只改变第一项符号，后面项符号不变",
  "分配律应用时遗漏某些项",
  "多重括号处理顺序错误"
]',
'[
  "符号规律：正号不变，负号全变",
  "分配到位：每一项都要分配到",
  "逐层处理：多重括号要有序处理",
  "检查验证：结果要检查符号正确性"
]',
'{
  "emphasis": ["运算规律", "符号处理"],
  "application": ["代数式化简", "方程变形"],
  "connection": ["分配律的应用", "为整式运算做准备"]
}',
'{
  "emphasis": ["算法规则", "逻辑处理"],
  "application": ["编程逻辑", "形式化推理"],
  "connection": ["计算机代数", "符号处理系统"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G7S1_CH4_009: 整式的加减运算
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_CH4_009'),
'整式的加减运算是通过去括号和合并同类项来完成的综合性运算',
'整式的加减运算是代数学中的核心技能，它综合运用了去括号、合并同类项等基本技能。这种运算体现了代数的基本思想：将复杂问题分解为简单步骤，按规律逐步求解。整式加减运算的一般步骤是：去括号→合并同类项→整理结果。这一过程培养学生的逻辑思维、运算能力和问题分析能力。在实际应用中，整式加减运算广泛用于物理公式推导、经济模型计算、工程数据分析等领域。掌握这一技能为后续学习方程组、函数、不等式奠定坚实基础。',
'[
  "运算步骤：去括号→合并同类项→整理结果",
  "加法运算：直接合并同类项",
  "减法运算：化为加法后合并同类项",
  "复杂运算：多项式与多项式的加减",
  "结果化简：最终结果要化为最简形式",
  "综合应用：集去括号与合并同类项于一体"
]',
'[
  {
    "name": "整式加法",
    "formula": "(a₁+b₁+c₁) + (a₂+b₂+c₂) = (a₁+a₂) + (b₁+b₂) + (c₁+c₂)",
    "description": "整式相加就是合并同类项"
  },
  {
    "name": "整式减法",
    "formula": "A - B = A + (-B)",
    "description": "减去一个整式等于加上它的相反数"
  },
  {
    "name": "运算步骤",
    "formula": "去括号 → 合并同类项 → 整理结果",
    "description": "整式加减运算的标准步骤"
  }
]',
'[
  {
    "title": "整式加减综合练习",
    "problem": "计算：(3x²+2x-1)-(2x²-5x+3)+(-x²+3x-2)",
    "solution": "=3x²+2x-1-2x²+5x-3-x²+3x-2=(3-2-1)x²+(2+5+3)x+(-1-3-2)=0x²+10x-6=10x-6",
    "analysis": "先去括号，再合并同类项，最后整理结果"
  }
]',
'[
  {
    "concept": "步骤化运算",
    "explanation": "将复杂运算分解为简单步骤",
    "example": "去括号→找同类项→合并→整理"
  },
  {
    "concept": "整体思维",
    "explanation": "把整式看作一个整体进行运算",
    "example": "A+B-C的整体运算思路"
  }
]',
'[
  "去括号时符号处理错误",
  "合并同类项时遗漏某些项",
  "运算步骤顺序颠倒",
  "最终结果未化简到最简形式"
]',
'[
  "步骤明确：按固定步骤进行运算",
  "符号谨慎：特别注意减号的处理",
  "分类合并：同类项要完全合并",
  "结果检查：确保最终结果最简"
]',
'{
  "emphasis": ["综合运算", "步骤化思维"],
  "application": ["复杂代数式化简", "方程整理"],
  "connection": ["整合前面所学技能", "为方程学习做准备"]
}',
'{
  "emphasis": ["算法流程", "系统化处理"],
  "application": ["程序设计", "数据处理"],
  "connection": ["计算机代数系统", "算法优化"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'),

-- MATH_G7S1_CH4_010: 用电子表格进行数据计算
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_CH4_010'),
'利用电子表格软件进行整式运算和数据分析，体现数学与现代信息技术的结合',
'电子表格在数学学习中的应用体现了数学与现代信息技术的深度融合。通过Excel、WPS等软件，可以高效进行大量数据的整式运算、图表制作和数据分析。电子表格的公式功能本质上就是整式运算的数字化实现，单元格引用类似于变量，公式编写类似于代数表达式。这种学习方式培养学生的信息素养、数据处理能力和现代化思维。在大数据时代，掌握电子表格技能对于科学研究、商业分析、工程计算等实际应用具有重要价值。这一内容将抽象的代数运算与具体的技术应用相结合，增强数学学习的实用性和时代感。',
'[
  "电子表格基本操作：输入数据、编写公式、制作图表",
  "公式应用：使用单元格引用进行整式运算",
  "数据处理：批量计算、数据分析、结果展示",
  "变量替换：通过改变单元格数值观察结果变化",
  "图表制作：将代数关系可视化显示",
  "实际应用：解决生活中的数学计算问题"
]',
'[
  {
    "name": "单元格引用",
    "formula": "=A1+B1*C1",
    "description": "用单元格地址表示变量和系数"
  },
  {
    "name": "公式复制",
    "formula": "相对引用和绝对引用",
    "description": "批量处理相同结构的计算"
  },
  {
    "name": "函数应用",
    "formula": "SUM、AVERAGE、MAX、MIN等",
    "description": "利用内置函数进行数据统计"
  }
]',
'[
  {
    "title": "电子表格计算实践",
    "problem": "用Excel计算多项式3x²+2x-1在x=1,2,3,4,5时的值",
    "solution": "A列输入x值，B列输入公式=3*A1^2+2*A1-1，复制公式到其他行，得到对应结果",
    "analysis": "体现了代数表达式的程序化实现"
  }
]',
'[
  {
    "concept": "数字化运算",
    "explanation": "将代数运算转化为电子表格公式",
    "example": "多项式计算变为Excel公式"
  },
  {
    "concept": "数据可视化",
    "explanation": "通过图表展示数学关系",
    "example": "制作函数图像观察变化趋势"
  }
]',
'[
  "公式语法错误（如忘记等号）",
  "单元格引用错误（相对绝对引用混淆）",
  "数据格式问题（文本与数字混淆）",
  "不理解电子表格与数学的对应关系"
]',
'[
  "操作熟练：掌握基本电子表格操作",
  "公式理解：理解表格公式与代数式的对应",
  "数据意识：培养用数据说话的习惯",
  "实践应用：在实际问题中运用电子表格"
]',
'{
  "emphasis": ["信息技术应用", "数据处理能力"],
  "application": ["生活数据分析", "学习成绩统计"],
  "connection": ["数学与现代技术结合", "为统计学学习做准备"]
}',
'{
  "emphasis": ["计算机应用", "算法实现"],
  "application": ["科学计算", "数据分析"],
  "connection": ["编程思维", "计算机科学基础"]
}',
'1.0', true, 'zh-CN', 4.7, 'approved');

-- ============================================
-- 数据插入结束标志
-- ============================================
