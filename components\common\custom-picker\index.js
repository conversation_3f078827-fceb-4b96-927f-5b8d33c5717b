Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 是否显示选择器
    show: {
      type: Boolean,
      value: false
    },
    // 选择器标题
    title: {
      type: String,
      value: '请选择'
    },
    // 选项列表
    options: {
      type: Array,
      value: []
    },
    // 当前选择索引
    value: {
      type: Number,
      value: -1
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 临时选择索引
    tempIndex: -1
  },

  /**
   * 数据监听器
   */
  observers: {
    'show': function(show) {
      if (show) {
        // 显示选择器时初始化临时索引
        this.setData({
          tempIndex: this.properties.value
        });
      }
    },
    'value': function(value) {
      // 外部value变化时更新临时索引
      this.setData({
        tempIndex: value
      });
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 关闭选择器
    close() {
      this.triggerEvent('cancel');
    },
    
    // 选择某一项
    selectItem(e) {
      const index = e.currentTarget.dataset.index;
      this.setData({
        tempIndex: index
      });
    },
    
    // 确认选择
    confirm() {
      const { tempIndex, options } = this.data;
      if (tempIndex !== -1) {
        this.triggerEvent('confirm', {
          value: tempIndex,
          item: options[tempIndex]
        });
      } else {
        this.triggerEvent('cancel');
      }
    },
    
    // 阻止冒泡
    preventBubble() {
      // 防止点击内容区域关闭弹窗
      return false;
    }
  }
}) 