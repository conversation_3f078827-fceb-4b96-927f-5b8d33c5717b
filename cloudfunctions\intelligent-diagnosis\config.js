// 智能诊断系统统一配置管理
const CONFIG = {
  // 系统基础配置
  system: {
    version: '3.0.0',
    timeout: 30000, // 30秒超时
    maxRetries: 3,
    logLevel: process.env.LOG_LEVEL || 'info',
    enableMetrics: true,
    enableProfiling: false
  },

  // 缓存配置
  cache: {
    enabled: true,
    ttl: 5 * 60 * 1000, // 5分钟
    maxSize: 100,
    cleanupInterval: 10 * 60 * 1000, // 10分钟清理一次
    compressionEnabled: true
  },

  // 诊断引擎配置
  diagnosis: {
    // AI分析配置
    ai: {
      temperature: 0.1,
      maxTokens: 2048,
      topP: 0.9,
      enableAdvancedAnalysis: true,
      confidenceThreshold: 0.7
    },
    
    // 薄弱点识别配置
    weakness: {
      masteryThreshold: 0.7, // 掌握度阈值
      severityLevels: {
        critical: 0.3,
        high: 0.5,
        medium: 0.7
      },
      maxWeaknessCount: 10
    },

    // 学习路径配置
    path: {
      maxPathLength: 20,
      defaultDifficulty: 'moderate',
      adaptationRate: 0.1,
      checkpointInterval: 5
    },

    // 预测模型配置
    prediction: {
      horizons: {
        short: 14, // 2周
        medium: 90, // 3个月
        long: 365 // 1年
      },
      confidenceThreshold: 0.6,
      enableUncertaintyQuantification: true
    }
  },

  // 性能监控配置
  monitoring: {
    enableMetrics: true,
    metricsRetentionDays: 30,
    alertThresholds: {
      errorRate: 0.05, // 5%
      responseTime: 5000, // 5秒
      memoryUsage: 0.8 // 80%
    },
    enableProfiling: false,
    profilingInterval: 60000 // 1分钟
  },

  // 数据库配置
  database: {
    host: 'localhost',
    port: 5432,
    database: 'k12',
    user: 'postgres',
    password: '123456',
    ssl: false,
    timeout: 10000, // 10秒
    retryAttempts: 3,
    retryDelay: 1000, // 1秒
    maxConnections: 10,
    enableQueryLogging: false
  },

  // 报告生成配置
  report: {
    maxReportSize: 50 * 1024, // 50KB
    enableVisualization: true,
    defaultFormat: 'comprehensive',
    includeTechnicalDetails: false,
    enableExport: true
  },

  // 安全配置
  security: {
    enableInputValidation: true,
    enableOutputSanitization: true,
    maxInputSize: 10 * 1024, // 10KB
    enableRateLimiting: true,
    rateLimitWindow: 60000, // 1分钟
    rateLimitMax: 100 // 每分钟最多100请求
  },

  // 教育特定配置
  education: {
    gradeRange: {
      min: 1,
      max: 12
    },
    subjects: ['mathematics'],
    difficultyLevels: ['basic', 'intermediate', 'advanced', 'expert'],
    learningStyles: ['visual', 'auditory', 'kinesthetic', 'reading'],
    assessmentTypes: ['formative', 'summative', 'diagnostic']
  }
};

/**
 * 获取配置值
 * @param {string} path - 配置路径，如 'diagnosis.ai.temperature'
 * @param {*} defaultValue - 默认值
 * @returns {*} 配置值
 */
function getConfig(path, defaultValue = null) {
  const keys = path.split('.');
  let value = CONFIG;
  
  for (const key of keys) {
    if (value && typeof value === 'object' && key in value) {
      value = value[key];
    } else {
      return defaultValue;
    }
  }
  
  return value;
}

/**
 * 设置配置值
 * @param {string} path - 配置路径
 * @param {*} value - 配置值
 */
function setConfig(path, value) {
  const keys = path.split('.');
  const lastKey = keys.pop();
  let current = CONFIG;
  
  for (const key of keys) {
    if (!(key in current) || typeof current[key] !== 'object') {
      current[key] = {};
    }
    current = current[key];
  }
  
  current[lastKey] = value;
}

/**
 * 验证配置完整性
 * @returns {Object} 验证结果
 */
function validateConfig() {
  const errors = [];
  const warnings = [];
  
  // 验证必要配置
  if (!CONFIG.system.timeout || CONFIG.system.timeout < 1000) {
    errors.push('系统超时时间配置无效');
  }
  
  if (!CONFIG.cache.ttl || CONFIG.cache.ttl < 60000) {
    warnings.push('缓存TTL配置过短，可能影响性能');
  }
  
  if (CONFIG.diagnosis.weakness.masteryThreshold < 0 || CONFIG.diagnosis.weakness.masteryThreshold > 1) {
    errors.push('掌握度阈值必须在0-1之间');
  }
  
  if (CONFIG.education.gradeRange.min < 1 || CONFIG.education.gradeRange.max > 12) {
    errors.push('年级范围配置无效');
  }
  
  return {
    valid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * 根据环境调整配置
 * @param {string} env - 环境名称 ('development', 'staging', 'production')
 */
function adjustConfigForEnvironment(env) {
  switch (env) {
    case 'development':
      CONFIG.system.logLevel = 'debug';
      CONFIG.system.enableProfiling = true;
      CONFIG.cache.ttl = 30000; // 30秒缓存
      CONFIG.monitoring.enableProfiling = true;
      CONFIG.database.enableQueryLogging = true;
      CONFIG.report.includeTechnicalDetails = true;
      break;
      
    case 'staging':
      CONFIG.system.logLevel = 'info';
      CONFIG.monitoring.alertThresholds.errorRate = 0.1; // 更宽松的错误率
      CONFIG.cache.ttl = 2 * 60 * 1000; // 2分钟缓存
      break;
      
    case 'production':
      CONFIG.system.logLevel = 'warn';
      CONFIG.system.enableProfiling = false;
      CONFIG.cache.ttl = 10 * 60 * 1000; // 10分钟缓存
      CONFIG.monitoring.enableProfiling = false;
      CONFIG.database.enableQueryLogging = false;
      CONFIG.security.enableRateLimiting = true;
      break;
  }
}

/**
 * 获取性能相关配置
 * @returns {Object} 性能配置
 */
function getPerformanceConfig() {
  return {
    timeout: CONFIG.system.timeout,
    cacheEnabled: CONFIG.cache.enabled,
    cacheTTL: CONFIG.cache.ttl,
    maxRetries: CONFIG.system.maxRetries,
    enableMetrics: CONFIG.monitoring.enableMetrics,
    enableProfiling: CONFIG.monitoring.enableProfiling
  };
}

/**
 * 获取安全相关配置
 * @returns {Object} 安全配置
 */
function getSecurityConfig() {
  return {
    enableInputValidation: CONFIG.security.enableInputValidation,
    enableOutputSanitization: CONFIG.security.enableOutputSanitization,
    maxInputSize: CONFIG.security.maxInputSize,
    enableRateLimiting: CONFIG.security.enableRateLimiting,
    rateLimitWindow: CONFIG.security.rateLimitWindow,
    rateLimitMax: CONFIG.security.rateLimitMax
  };
}

/**
 * 获取教育领域配置
 * @returns {Object} 教育配置
 */
function getEducationConfig() {
  return {
    gradeRange: CONFIG.education.gradeRange,
    subjects: CONFIG.education.subjects,
    difficultyLevels: CONFIG.education.difficultyLevels,
    learningStyles: CONFIG.education.learningStyles,
    assessmentTypes: CONFIG.education.assessmentTypes
  };
}

/**
 * 动态更新配置
 * @param {Object} updates - 配置更新
 */
function updateConfig(updates) {
  function deepMerge(target, source) {
    for (const key in source) {
      if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
        if (!target[key]) target[key] = {};
        deepMerge(target[key], source[key]);
      } else {
        target[key] = source[key];
      }
    }
  }
  
  deepMerge(CONFIG, updates);
}

// 初始化时根据环境调整配置
const currentEnv = process.env.NODE_ENV || 'development';
adjustConfigForEnvironment(currentEnv);

// 验证配置
const configValidation = validateConfig();
if (!configValidation.valid) {
  console.error('配置验证失败:', configValidation.errors);
}

if (configValidation.warnings.length > 0) {
  console.warn('配置警告:', configValidation.warnings);
}

module.exports = {
  CONFIG,
  getConfig,
  setConfig,
  validateConfig,
  adjustConfigForEnvironment,
  getPerformanceConfig,
  getSecurityConfig,
  getEducationConfig,
  updateConfig
}; 