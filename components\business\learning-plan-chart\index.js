Component({
  properties: {
    // 计划完成率（0-100）
    completion: {
      type: Number,
      value: 0
    },
    // 任务完成情况
    completedTasks: {
      type: Number,
      value: 0
    },
    // 总任务数
    totalTasks: {
      type: Number,
      value: 0
    },
    // 已学习天数
    completedDays: {
      type: Number,
      value: 0
    },
    // 计划总天数
    totalDays: {
      type: Number,
      value: 0
    },
    // 每日学习时间（分钟）
    dailyTime: {
      type: Number,
      value: 0
    },
    // 今日学习时间（分钟）
    todayTime: {
      type: Number,
      value: 0
    },
    // 是否使用卡片样式
    useCard: {
      type: Boolean,
      value: true
    },
    // 主题色
    primaryColor: {
      type: String,
      value: '#3E7BFA'
    },
    // 起始渐变色
    startColor: {
      type: String,
      value: '#7B72FB'
    },
    // 结束渐变色
    endColor: {
      type: String,
      value: '#5D54FA'
    },
    // 是否使用渐变
    useGradient: {
      type: Boolean,
      value: true
    },
    // 圆环大小
    progressSize: {
      type: Number,
      value: 160
    },
    // 标题
    title: {
      type: String,
      value: ''
    },
    // 是否显示日期
    showDate: {
      type: Boolean,
      value: false
    },
    // 日期
    date: {
      type: String,
      value: ''
    }
  },

  data: {
    // 格式化后的学习天数
    studyDays: '',
    // 日期格式化
    dateFormatted: ''
  },

  lifetimes: {
    attached: function() {
      this.formatData();
    }
  },

  observers: {
    'completedDays, totalDays, date': function() {
      this.formatData();
    }
  },

  methods: {
    // 格式化数据
    formatData: function() {
      // 格式化学习天数
      const studyDays = `${this.data.completedDays}/${this.data.totalDays}`;
      
      // 格式化日期
      let dateFormatted = this.data.date;
      if (dateFormatted && dateFormatted.length > 0) {
        // 如果是YYYY-MM-DD格式，转换为MM月DD日
        const dateParts = dateFormatted.split('-');
        if (dateParts.length === 3) {
          dateFormatted = `${parseInt(dateParts[1])}月${parseInt(dateParts[2])}日`;
        }
      }
      
      this.setData({
        studyDays,
        dateFormatted
      });
    }
  }
}); 