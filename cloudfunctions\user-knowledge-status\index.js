// 云函数入口文件
var cloud = require('wx-server-sdk')
cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV })
var db = cloud.database()
var _ = db.command

// 状态类型常量
var STATUS_TYPES = {
  MASTERED: 'mastered',       // 已掌握
  LEARNING: 'learning',       // 学习中
  WEAK: 'weak',               // 薄弱
  NOT_MASTERED: 'not-mastered', // 未掌握
  NOT_STARTED: 'not-started'    // 未开始
};

// 云函数入口函数
exports.main = async (event, context) => {
  var wxContext = cloud.getWXContext()
  var openId = wxContext.OPENID

  var { action, data } = event

  switch (action) {
    case 'initUserKnowledgeStatus':
      return await initUserKnowledgeStatus(openId, data.knowledgeIds)
    case 'getUserKnowledgeStatus':
      return await getUserKnowledgeStatus(openId)
    case 'updateKnowledgeStatus':
      return await updateKnowledgeStatus(openId, data.knowledgeId, data.status)
    case 'updateBatchKnowledgeStatus':
      return await updateBatchKnowledgeStatus(openId, data.statusMap)
    case 'getKnowledgeStatusByIds':
      return await getKnowledgeStatusByIds(openId, data.knowledgeIds)
    default:
      return {
        code: -1,
        msg: '未知操作'
      }
  }
}

// 初始化用户知识点状态
async function initUserKnowledgeStatus(userId, knowledgeIds) {
  try {
    // 查询用户是否已有知识点状态记录
    var userRecord = await db.collection('user_knowledge_status')
      .where({
        userId: userId
      })
      .get()

    if (userRecord.data.length > 0) {
      // 用户已有记录，仅初始化不存在的知识点
      var existingData = userRecord.data[0]
      var statusMap = existingData.statusMap || {}
      
      var hasUpdates = false
      knowledgeIds.forEach(id => {
        if (!statusMap[id]) {
          statusMap[id] = STATUS_TYPES.NOT_STARTED
          hasUpdates = true
        }
      })

      if (hasUpdates) {
        await db.collection('user_knowledge_status')
          .doc(existingData._id)
          .update({
            data: {
              statusMap: statusMap,
              updateTime: db.serverDate()
            }
          })
      }
      
      return {
        code: 0,
        msg: '用户知识点状态初始化成功',
        data: { statusMap }
      }
    } else {
      // 用户无记录，创建新记录
      var statusMap = {}
      knowledgeIds.forEach(id => {
        statusMap[id] = STATUS_TYPES.NOT_STARTED
      })
      
      await db.collection('user_knowledge_status').add({
        data: {
          userId: userId,
          statusMap: statusMap,
          createTime: db.serverDate(),
          updateTime: db.serverDate()
        }
      })

      return {
        code: 0,
        msg: '用户知识点状态创建成功',
        data: { statusMap }
      }
    }
  } catch (err) {
    return {
      code: -1,
      msg: '初始化用户知识点状态失败',
      error: err
    }
  }
}

// 获取用户全部知识点状态
async function getUserKnowledgeStatus(userId) {
  try {
    var result = await db.collection('user_knowledge_status')
      .where({
        userId: userId
      })
      .get()

    if (result.data.length > 0) {
      return {
        code: 0,
        msg: '获取用户知识点状态成功',
        data: result.data[0]
      }
    } else {
      return {
        code: 0,
        msg: '用户尚未有知识点状态记录',
        data: { statusMap: {} }
      }
    }
  } catch (err) {
    return {
      code: -1,
      msg: '获取用户知识点状态失败',
      error: err
    }
  }
}

// 更新单个知识点状态
async function updateKnowledgeStatus(userId, knowledgeId, status) {
  // 验证状态值
  if (!Object.values(STATUS_TYPES).includes(status)) {
    return {
      code: -1,
      msg: '无效的状态值'
    }
  }

  try {
    var result = await db.collection('user_knowledge_status')
      .where({
        userId: userId
      })
      .get()

    if (result.data.length > 0) {
      // 更新现有记录
      var docId = result.data[0]._id
      var statusMapKey = `statusMap.${knowledgeId}`
      var updateData = {}
      updateData[statusMapKey] = status
      
      await db.collection('user_knowledge_status')
        .doc(docId)
        .update({
          data: {
            ...updateData,
            updateTime: db.serverDate()
          }
        })
    } else {
      // 创建新记录
      var statusMap = {}
      statusMap[knowledgeId] = status
      
      await db.collection('user_knowledge_status').add({
        data: {
          userId: userId,
          statusMap: statusMap,
          createTime: db.serverDate(),
          updateTime: db.serverDate()
        }
      })
    }

    return {
      code: 0,
      msg: '更新知识点状态成功'
    }
  } catch (err) {
    return {
      code: -1,
      msg: '更新知识点状态失败',
      error: err
    }
  }
}

// 批量更新知识点状态
async function updateBatchKnowledgeStatus(userId, statusMap) {
  // 验证所有状态值
  for (var status of Object.values(statusMap)) {
    if (!Object.values(STATUS_TYPES).includes(status)) {
      return {
        code: -1,
        msg: `无效的状态值: ${status}`
      }
    }
  }

  try {
    var result = await db.collection('user_knowledge_status')
      .where({
        userId: userId
      })
      .get()

    if (result.data.length > 0) {
      // 更新现有记录
      var docId = result.data[0]._id
      var currentStatusMap = result.data[0].statusMap || {}
      var newStatusMap = { ...currentStatusMap, ...statusMap }
      
      await db.collection('user_knowledge_status')
        .doc(docId)
        .update({
          data: {
            statusMap: newStatusMap,
            updateTime: db.serverDate()
          }
        })
    } else {
      // 创建新记录
      await db.collection('user_knowledge_status').add({
        data: {
          userId: userId,
          statusMap: statusMap,
          createTime: db.serverDate(),
          updateTime: db.serverDate()
        }
      })
    }

    return {
      code: 0,
      msg: '批量更新知识点状态成功'
    }
  } catch (err) {
    return {
      code: -1,
      msg: '批量更新知识点状态失败',
      error: err
    }
  }
}

// 获取特定知识点的状态
async function getKnowledgeStatusByIds(userId, knowledgeIds) {
  try {
    var result = await db.collection('user_knowledge_status')
      .where({
        userId: userId
      })
      .get()

    if (result.data.length > 0) {
      var statusMap = result.data[0].statusMap || {}
      var filteredStatusMap = {}
      
      knowledgeIds.forEach(id => {
        filteredStatusMap[id] = statusMap[id] || STATUS_TYPES.NOT_STARTED
      })
      
      return {
        code: 0,
        msg: '获取知识点状态成功',
        data: { statusMap: filteredStatusMap }
      }
    } else {
      // 用户尚无记录，返回所有请求的知识点为未开始状态
      var statusMap = {}
      knowledgeIds.forEach(id => {
        statusMap[id] = STATUS_TYPES.NOT_STARTED
      })
      
      return {
        code: 0,
        msg: '用户尚未有知识点状态记录',
        data: { statusMap }
      }
    }
  } catch (err) {
    return {
      code: -1,
      msg: '获取知识点状态失败',
      error: err
    }
  }
} 