Component({
  properties: {
    show: {
      type: Boolean,
      value: false
    },
    content: {
      type: String,
      value: ''
    },
    duration: {
      type: Number,
      value: 1500
    },
    position: {
      type: String,
      value: 'center' // 可选: 'top', 'center', 'bottom'
    },
    icon: {
      type: String,
      value: '' // 图标类名，例如 'icon-success'
    }
  },
  
  data: {
    animationData: {},
    timer: null
  },
  
  observers: {
    'show': function(value) {
      if (value) {
        this.showToast();
      }
    }
  },
  
  methods: {
    showToast() {
      // 清除可能存在的定时器
      if (this.data.timer) {
        clearTimeout(this.data.timer);
      }
      
      const animation = wx.createAnimation({
        duration: 200,
        timingFunction: 'ease'
      });
      
      animation.opacity(1).step();
      
      this.setData({
        animationData: animation.export()
      });
      
      // 设置自动关闭
      this.data.timer = setTimeout(() => {
        this.hideToast();
      }, this.data.duration);
    },
    
    hideToast() {
      const animation = wx.createAnimation({
        duration: 200,
        timingFunction: 'ease'
      });
      
      animation.opacity(0).step();
      
      this.setData({
        animationData: animation.export()
      });
      
      setTimeout(() => {
        this.setData({
          show: false
        });
        this.triggerEvent('onHide');
      }, 200);
    }
  }
}) 