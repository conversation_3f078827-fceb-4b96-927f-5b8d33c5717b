---
description: 
globs: 
alwaysApply: true
---
# 项目代码标准规范

本文档汇总了项目中所有开发相关的代码标准和规范，以确保代码质量和一致性。

## 目录
1. [编码规范](mdc:#编码规范)
2. [组件开发规范](mdc:#组件开发规范)
3. [图标使用规范](mdc:#图标使用规范)
4. [样式命名规范](mdc:#样式命名规范)
5. [文件组织规范](mdc:#文件组织规范)
6. [代码修改指南](mdc:#代码修改指南)

## 编码规范

### 命名规范

#### 文件与文件夹命名
- 使用小写字母和连字符命名文件和文件夹，例如：`study-plan`、`problem-card`
- 页面文件与文件夹名保持一致
- 组件文件与文件夹名保持一致

#### 变量命名
- 使用驼峰命名法，例如：`userName`、`problemList`
- 布尔类型变量使用 `is`、`has`、`can` 等前缀，例如：`isLoggedIn`、`hasPermission`
- 私有变量以下划线开头，例如：`_privateMethod`

#### 常量命名
- 使用全大写字母和下划线，例如：`API_BASE_URL`、`MAX_RETRY_COUNT`

### 代码风格

#### JavaScript
- 使用 ES6+ 语法
- 使用 2 个空格缩进
- 语句末尾使用分号
- 优先使用箭头函数
- 优先使用 `const` 和 `let`
- 使用解构赋值简化代码
- 避免使用全局变量
- 使用模块化管理代码

#### WXML
- 使用 2 个空格缩进
- 标签属性使用双引号
- 属性值使用单引号
- 属性名使用小写字母和连字符
- 标签必须闭合
- 复杂视图使用模板拆分

#### WXSS
- 使用 2 个空格缩进
- 选择器命名使用小写字母和连字符（kebab-case）
- 声明块的左花括号前添加一个空格
- 每个声明独占一行
- 所有声明语句以分号结尾
- 使用变量管理颜色和尺寸
- 避免过深的选择器嵌套

## 组件开发规范

### 组件分类

1. **通用组件 (`components/common/`)**
   - 与业务逻辑无关的基础UI组件
   - 例如：导航栏、加载动画、轻提示等

2. **业务组件 (`components/business/`)**
   - 与特定业务场景相关的组件
   - 例如：数学公式渲染、题目卡片、知识点节点等

3. **表单组件 (`components/form/`)**
   - 与表单输入相关的组件
   - 例如：文本输入框、数学公式输入器等

### 组件开发规范

- 组件必须在对应分类目录下创建独立文件夹
- 组件名称采用小写字母和连字符命名（kebab-case）
- 每个组件应包含四个同名文件（.js/.json/.wxml/.wxss）
- 组件应具有明确的功能边界和接口定义
- 复杂组件应考虑拆分为更小的子组件

### 组件复用原则

- 开发新功能前，先检查是否有可复用的现有组件
- 通用组件应保持高度可配置性，避免耦合业务逻辑
- 相似功能的组件应考虑合并或抽象为基础组件+扩展模式
- 组件应尽可能减少对外部状态的依赖

## 图标使用规范

### 图标管理原则

1. **集中管理原则**
   - 所有图标样式必须统一放在 [styles/icons.wxss](mdc:styles/icons.wxss) 文件中
   - 禁止在其他样式文件中定义图标样式
   - 禁止在组件内部样式中重复定义图标

2. **图标来源标准**
   - 优先使用[iconfont.cn](mdc:https:/www.iconfont.cn)平台的开源图标
   - 项目已创建iconfont图标项目集，统一管理项目所需图标
   - 禁止使用本地图片图标文件

3. **实现方式标准**
   - 统一使用SVG背景图实现
   - 使用CSS变量控制颜色
   - 使用尺寸类控制大小

### 图标命名规范

- 使用语义化命名，反映图标用途而非外观
- 使用小写字母和连字符(-)，不使用下划线或空格
- 命名模式：
  - 功能型图标：`icon-{功能}-{状态}`，例如：`icon-search`
  - 对象型图标：`icon-{对象}`，例如：`icon-user`
  - 方向型图标：`icon-arrow-{方向}`，例如：`icon-arrow-down`

### 图标尺寸规范

- `.icon-xs`: 24rpx - 用于辅助提示、标签等小型场景
- `.icon-sm`: 32rpx - 用于列表项、按钮内的图标
- `.icon-md`: 40rpx - 用于主导航、功能入口
- `.icon-lg`: 48rpx - 用于强调性图标、特殊功能按钮
- `.icon-xl`: 64rpx - 用于页面级图标、空状态提示

### 图标使用示例

```html
<!-- 基础使用 -->
<view class="icon icon-home"></view>

<!-- 配合尺寸类 -->
<view class="icon icon-home icon-md"></view>

<!-- 配合颜色类 -->
<view class="icon icon-home icon-primary"></view>
```

## 样式命名规范

### BEM命名规范

- 块（Block）：独立实体，有意义的独立元素，例如：`.card`、`.button`
- 元素（Element）：块的一部分，无法独立使用，例如：`.card__title`、`.button__icon`
- 修饰符（Modifier）：改变块或元素的外观或状态，例如：`.button--primary`、`.card--featured`

### 通用样式类

- 使用功能性命名而非表现性命名，例如：`.text-primary` 而非 `.text-blue`
- 常用通用类：
  - 布局类：`.container`、`.flex`、`.grid`
  - 间距类：`.margin-top-sm`、`.padding-lg`
  - 颜色类：`.bg-primary`、`.text-secondary`
  - 字体类：`.font-bold`、`.text-center`

### 响应式设计类

- 统一使用移动优先策略
- 使用媒体查询处理不同设备尺寸
- 断点类命名：`.sm-only`、`.md-up`、`.lg-down`

## 文件组织规范

### 目录结构

- 页面文件放在 `pages/` 目录下
- 分包页面放在对应的分包目录下，如 `packageHome/pages/`
- 组件文件放在 `components/` 目录下，按类型分类
- 工具函数放在 `utils/` 目录下
- 样式文件放在 `styles/` 目录下
- 数据模型放在 `models/` 目录下

### 模块化原则

- 相关功能应该放在同一个目录下
- 公共功能应该提取到共享目录
- 每个文件应该只有一个主要职责
- 避免创建过大的文件（超过300行）

### 引用规范

- 使用相对路径引用同一模块内的文件
- 使用绝对路径引用跨模块文件
- 避免循环依赖
- 合理使用别名简化路径

## 代码修改指南

### 修改范围限制

- **仅修改明确要求的内容**：只修改用户明确要求更改的代码部分，不扩展到其他区域
- **保留现有功能**：确保修改不会影响或破坏现有功能的正常运行
- **遵循样式一致性**：任何修改都应遵循项目既有的代码风格和命名约定

### 修改核对清单

1. 确认修改范围是否明确定义
2. 核对修改是否仅限于指定内容
3. 验证修改不会影响其他功能
4. 确保修改后的代码与项目风格保持一致

### 修改案例

**正确做法:**
```javascript
// 用户要求：将按钮颜色改为蓝色
.button {
  // ...existing code...
  color: #3E7BFA; // 只修改颜色属性
  // ...existing code...
}
```

**错误做法:**
```javascript
// 用户要求：将按钮颜色改为蓝色
.button {
  // ...existing code...
  color: #3E7BFA;
  padding: 10px; // 错误：修改了未要求更改的内容
  border-radius: 4px; // 错误：添加了未要求的新属性
  // ...existing code...
}
```

### 关键文件修改注意事项

以下是项目中的一些关键文件，修改时需特别注意：

- [app.wxss](mdc:app.wxss) - 全局样式文件
- [styles/theme.wxss](mdc:styles/theme.wxss) - 主题定义文件
- [styles/icons.wxss](mdc:styles/icons.wxss) - 图标样式文件
