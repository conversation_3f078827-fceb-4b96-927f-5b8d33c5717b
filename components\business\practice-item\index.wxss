/* 练习项组件样式 */
@import "../../../styles/icons.wxss";

.practice-item {
  display: flex;
  flex-direction: column;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

/* 头部样式 */
.practice-item-header {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.practice-item-icon {
  margin-right: 16rpx;
  color: var(--primary-color, #3E7BFA);
}

.practice-item-title {
  font-size: 32rpx;
  font-weight: 500;
  flex: 1;
  color: #333333;
}

.practice-item-status {
  font-size: 24rpx;
  color: #999999;
  padding: 6rpx 12rpx;
  background-color: #F5F7FA;
  border-radius: 8rpx;
}

.status-completed {
  color: #09BB07;
  background-color: rgba(9, 187, 7, 0.1);
}

.status-in-progress {
  color: #FF9800;
  background-color: rgba(255, 152, 0, 0.1);
}

/* 难度星级 */
.practice-item-difficulty {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.difficulty-label {
  font-size: 24rpx;
  color: #666666;
  margin-right: 12rpx;
}

.difficulty-stars {
  display: flex;
}

.star {
  width: 24rpx;
  height: 24rpx;
  margin-right: 4rpx;
}

.star-filled {
  color: #FFBE00;
}

.star-empty {
  color: #E0E0E0;
}

/* 描述样式 */
.practice-item-description {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 16rpx;
  line-height: 1.5;
}

/* 统计信息 */
.practice-item-stats {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.stat-item {
  display: flex;
  align-items: center;
  margin-right: 32rpx;
}

.stat-icon {
  font-size: 24rpx;
  margin-right: 8rpx;
  color: #999999;
}

.stat-value {
  font-size: 24rpx;
  color: #666666;
}

/* 完成进度 */
.practice-item-completion {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.completion-percent {
  font-size: 24rpx;
  color: #3E7BFA;
  margin-right: 16rpx;
  min-width: 60rpx;
}

.completion-progress {
  flex: 1;
  height: 12rpx;
  background-color: #F5F7FA;
  border-radius: 6rpx;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background-color: #3E7BFA;
  border-radius: 6rpx;
  transition: width 0.3s ease;
}

/* 底部样式 */
.practice-item-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16rpx;
}

.practice-item-tags {
  display: flex;
  flex-wrap: wrap;
  flex: 1;
}

.tag {
  font-size: 22rpx;
  color: #666666;
  background-color: #F5F7FA;
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
  margin-right: 12rpx;
  margin-bottom: 8rpx;
}

.knowledge-tag {
  color: #3E7BFA;
  background-color: rgba(62, 123, 250, 0.1);
}

/* 操作按钮 */
.practice-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12rpx 24rpx;
  background-color: #3E7BFA;
  color: white;
  border-radius: 8rpx;
  font-size: 28rpx;
}

.practice-button-icon {
  margin-right: 8rpx;
  font-size: 24rpx;
}