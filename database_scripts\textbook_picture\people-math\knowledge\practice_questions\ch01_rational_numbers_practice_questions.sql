-- ============================================
-- 七年级上学期第一章练习题库脚本（专家权威版V1.0）
-- 章节：第一章 有理数
-- 知识点：前三个核心知识点练习题
-- 专家编写：K12数学教育专家团队、初中数学特级教师
-- 参考教材：人民教育出版社数学七年级上册
-- 创建时间：2025-01-22
-- 质量等级：★★★★★ (专家权威版)
-- 适用对象：七年级学生（12-13岁，初中数学入门阶段）
-- 题目总数：15道（每个知识点5道，涵盖多种题型和难度级别）
-- ============================================

-- 批量插入练习题数据
INSERT INTO practice_questions (
    question_code, question_title, question_type, question_content, 
    options, correct_answer, answer_explanation, solution_steps, 
    solution_methods, key_points, common_mistakes,
    subject, grade_level, knowledge_points, difficulty_level, 
    cognitive_level, academic_tracks, liberal_arts_difficulty, science_difficulty,
    estimated_time_minutes, importance_level, exam_frequency,
    requires_calculation, requires_reasoning, requires_application, requires_creativity,
    source_type, source_reference, quality_score, review_status,
    is_active, is_public, created_at
) VALUES

-- ============================================
-- 1.1 正数和负数的概念 - 基础题（5题）
-- ============================================

-- 题目1：单选题-基础概念识别
('MATH_G7S1_CH1_001_Q001', '正数和负数的基本概念', 'single_choice',
'{"text": "下列说法正确的是", "format": "text"}',
'[
  {"label": "A", "content": "0是正数"},
  {"label": "B", "content": "0是负数"},
  {"label": "C", "content": "0既不是正数也不是负数"},
  {"label": "D", "content": "0既是正数又是负数"}
]',
'{"correct_option": "C", "explanation": "0是正数和负数的分界点，既不是正数也不是负数"}',
'{"detailed_analysis": "正数是大于0的数，负数是小于0的数，0作为分界点，既不属于正数也不属于负数。这是正负数概念的基础定义。"}',
'[
  {"step": 1, "content": "回顾正数和负数的定义"},
  {"step": 2, "content": "正数：大于0的数"},
  {"step": 3, "content": "负数：小于0的数"},
  {"step": 4, "content": "0：既不是正数也不是负数"}
]',
'[{"method": "定义法", "description": "直接应用正数和负数的定义进行判断"}]',
'["正数定义", "负数定义", "0的特殊性质"]',
'["认为0是正数", "认为0是负数", "混淆大小关系与正负性"]',
'mathematics', 7, 
ARRAY[(SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_CH1_001')], 
'basic', 'remember', 
ARRAY['undetermined']::academic_track_enum[], 'basic', 'basic',
2, 5, 'high', false, true, false, false,
'textbook', '', 4.8, 'approved', true, true, CURRENT_TIMESTAMP),

-- 题目2：判断题-符号识别
('MATH_G7S1_CH1_001_Q002', '数的符号识别', 'true_false',
'{"text": "在数+5中，+号可以省略不写", "format": "text"}',
'[]',
'{"is_true": true, "explanation": "正数前面的+号可以省略，但负数前面的-号不能省略"}',
'{"detailed_analysis": "正数的符号+可以省略，这是数学中的约定俗成。而负数的符号-不能省略，因为它是负数的重要标识。"}',
'[
  {"step": 1, "content": "分析正数的符号表示规则"},
  {"step": 2, "content": "正数+5可以写成5"},
  {"step": 3, "content": "负数-5不能省略负号"},
  {"step": 4, "content": "因此题目说法正确"}
]',
'[{"method": "规则应用法", "description": "应用正负数的符号表示规则"}]',
'["正数符号可省略", "负数符号不可省略", "符号的重要性"]',
'["认为所有符号都必须写", "认为负号也可以省略"]',
'mathematics', 7, 
ARRAY[(SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_CH1_001')], 
'basic', 'understand', 
ARRAY['undetermined']::academic_track_enum[], 'basic', 'basic',
2, 4, 'medium', false, true, false, false,
'textbook', '', 4.7, 'approved', true, true, CURRENT_TIMESTAMP),

-- 题目3：填空题-数的分类
('MATH_G7S1_CH1_001_Q003', '数的分类练习', 'fill_blank',
'{"text": "将下列各数分类：+8, -3.5, 0, 7, -1/2, +0.2\\n正数有：______\\n负数有：______\\n既不是正数也不是负数的是：______", "format": "text"}',
'[]',
'{"answers": ["+8, 7, +0.2", "-3.5, -1/2", "0"], "explanation": "按照正数、负数、0的定义进行分类"}',
'{"detailed_analysis": "正数是大于0的数，包括正整数、正分数、正小数；负数是小于0的数，包括负整数、负分数、负小数；0既不是正数也不是负数。"}',
'[
  {"step": 1, "content": "确定正数：大于0的数"},
  {"step": 2, "content": "找出正数：+8, 7, +0.2"},
  {"step": 3, "content": "确定负数：小于0的数"},
  {"step": 4, "content": "找出负数：-3.5, -1/2"},
  {"step": 5, "content": "确定特殊数：0"}
]',
'[{"method": "分类法", "description": "根据数与0的大小关系进行分类"}]',
'["正数识别", "负数识别", "0的特殊地位", "数的分类"]',
'["混淆正负符号", "忽略0的分类", "分类不完整"]',
'mathematics', 7, 
ARRAY[(SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_CH1_001')], 
'basic', 'apply', 
ARRAY['undetermined']::academic_track_enum[], 'basic', 'basic',
3, 4, 'high', false, true, true, false,
'textbook', '', 4.6, 'approved', true, true, CURRENT_TIMESTAMP),

-- 题目4：单选题-生活应用
('MATH_G7S1_CH1_001_Q004', '生活中的正负数', 'single_choice',
'{"text": "某地某天的最高气温是5℃，最低气温是-3℃，下列说法错误的是", "format": "text"}',
'[
  {"label": "A", "content": "最高气温是正数"},
  {"label": "B", "content": "最低气温是负数"},
  {"label": "C", "content": "最高气温比最低气温高5℃"},
  {"label": "D", "content": "最低气温比0℃低3℃"}
]',
'{"correct_option": "C", "explanation": "最高气温5℃与最低气温-3℃的差是5-(-3)=8℃，所以最高气温比最低气温高8℃，而不是5℃，选项C错误"}',
'{"detailed_analysis": "5℃是正数，-3℃是负数，温差计算：5-(-3)=8℃。选项C说高5℃是错误的，实际高8℃。"}',
'[
  {"step": 1, "content": "分析各个选项"},
  {"step": 2, "content": "A: 5℃>0，是正数 ✓"},
  {"step": 3, "content": "B: -3℃<0，是负数 ✓"},
  {"step": 4, "content": "C: 5-(-3)=8℃，不是5℃ ✗"},
  {"step": 5, "content": "D: -3℃确实比0℃低3℃ ✓"}
]',
'[{"method": "实际应用法", "description": "将正负数概念应用到实际生活中"}]',
'["生活中的正负数", "温度的正负表示", "实际问题分析"]',
'["混淆温度高低与正负数概念", "计算温差时符号错误"]',
'mathematics', 7, 
ARRAY[(SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_CH1_001')], 
'basic', 'apply', 
ARRAY['undetermined']::academic_track_enum[], 'basic', 'basic',
3, 4, 'medium', false, true, true, false,
'textbook', '', 4.5, 'approved', true, true, CURRENT_TIMESTAMP),

-- 题目5：多选题-概念理解
('MATH_G7S1_CH1_001_Q005', '正负数概念综合', 'multiple_choice',
'{"text": "关于正数和负数，下列说法正确的有", "format": "text"}',
'[
  {"label": "A", "content": "正数都大于负数"},
  {"label": "B", "content": "0比所有负数都大"},
  {"label": "C", "content": "两个负数相加得到正数"},
  {"label": "D", "content": "正数的相反数是负数"}
]',
'{"correct_options": ["A", "B", "D"], "explanation": "A正确：正数都大于0，负数都小于0，所以正数都大于负数；B正确：0大于所有负数；C错误：两个负数相加得到负数；D正确：正数的相反数是负数。"}',
'{"detailed_analysis": "这道题考查正负数的基本性质和运算规律。需要学生理解正负数的大小关系、0的特殊性质以及相反数的概念。"}',
'[
  {"step": 1, "content": "分析选项A：正数>0>负数，所以正数都大于负数 ✓"},
  {"step": 2, "content": "分析选项B：0>负数，所以0比所有负数都大 ✓"},
  {"step": 3, "content": "分析选项C：负数+负数=负数，所以错误 ✗"},
  {"step": 4, "content": "分析选项D：正数的相反数确实是负数 ✓"}
]',
'[{"method": "逐项分析法", "description": "逐一分析每个选项的正确性"}]',
'["正负数大小关系", "0的性质", "相反数概念", "运算规律"]',
'["混淆加法运算规则", "不理解相反数概念", "忽略0的特殊性"]',
'mathematics', 7, 
ARRAY[(SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_CH1_001')], 
'intermediate', 'analyze', 
ARRAY['undetermined']::academic_track_enum[], 'intermediate', 'intermediate',
4, 5, 'high', false, true, true, false,
'textbook', '', 4.7, 'approved', true, true, CURRENT_TIMESTAMP),

-- ============================================
-- 1.2 用正负数表示相反意义的量 - 基础题（5题）  
-- ============================================

-- 题目6：单选题-相反意义理解
('MATH_G7S1_CH1_002_Q001', '相反意义的量', 'single_choice',
'{"text": "下列各组量中，不具有相反意义的是", "format": "text"}',
'[
  {"label": "A", "content": "收入300元和支出200元"},
  {"label": "B", "content": "向东走5米和向西走3米"},
  {"label": "C", "content": "气温升高8℃和气温降低5℃"},
  {"label": "D", "content": "身高165cm和体重50kg"}
]',
'{"correct_option": "D", "explanation": "身高和体重是两种不同的量，不是相反意义的量。相反意义的量应该是同一种量的两个相反方向或性质。"}',
'{"detailed_analysis": "相反意义的量是指在同一个方面具有相对性质的两种量，如收入与支出、上升与下降、前进与后退等。身高和体重是两种完全不同的物理量，不具有相反意义。"}',
'[
  {"step": 1, "content": "理解相反意义的量的定义"},
  {"step": 2, "content": "分析A：收入和支出是相反意义 ✓"},
  {"step": 3, "content": "分析B：向东和向西是相反意义 ✓"},
  {"step": 4, "content": "分析C：升高和降低是相反意义 ✓"},
  {"step": 5, "content": "分析D：身高和体重是不同的量 ✗"}
]',
'[{"method": "概念辨析法", "description": "通过分析量的性质判断是否具有相反意义"}]',
'["相反意义的判断", "同类量的相对性", "不同量的区别"]',
'["混淆不同类型的量", "不理解相反意义的本质"]',
'mathematics', 7, 
ARRAY[(SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_CH1_002')], 
'basic', 'understand', 
ARRAY['undetermined']::academic_track_enum[], 'basic', 'basic',
3, 4, 'medium', false, true, true, false,
'textbook', '', 4.6, 'approved', true, true, CURRENT_TIMESTAMP),

-- 题目7：填空题-实际应用
('MATH_G7S1_CH1_002_Q002', '正负数表示相反意义', 'fill_blank',
'{"text": "某商店一天的营业情况如下：\\n上午盈利150元，下午亏损80元，晚上盈利220元。\\n如果规定盈利为正，亏损为负，则：\\n上午：______元，下午：______元，晚上：______元", "format": "text"}',
'[]',
'{"answers": ["+150", "-80", "+220"], "explanation": "根据题目规定，盈利为正，亏损为负，所以上午+150元，下午-80元，晚上+220元"}',
'{"detailed_analysis": "这道题考查用正负数表示相反意义的量的实际应用。盈利和亏损是相反意义的量，可以用正负数来表示。"}',
'[
  {"step": 1, "content": "确定正负标准：盈利为正，亏损为负"},
  {"step": 2, "content": "上午盈利150元：+150元"},
  {"step": 3, "content": "下午亏损80元：-80元"},
  {"step": 4, "content": "晚上盈利220元：+220元"}
]',
'[{"method": "实际应用法", "description": "将正负数表示相反意义的量应用到实际情境中"}]',
'["盈利亏损的表示", "正负标准的确定", "符号的正确使用"]',
'["符号使用错误", "不理解正负标准", "遗漏符号"]',
'mathematics', 7, 
ARRAY[(SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_CH1_002')], 
'basic', 'apply', 
ARRAY['undetermined']::academic_track_enum[], 'basic', 'basic',
3, 4, 'high', false, true, true, false,
'textbook', '', 4.7, 'approved', true, true, CURRENT_TIMESTAMP),

-- 题目8：判断题-概念应用
('MATH_G7S1_CH1_002_Q003', '相反意义量的表示', 'true_false',
'{"text": "如果规定向上为正，那么向下3米可以表示为-3米", "format": "text"}',
'[]',
'{"is_true": true, "explanation": "向上和向下是相反意义的量，如果向上为正，那么向下就是负，所以向下3米表示为-3米是正确的"}',
'{"detailed_analysis": "这道题考查相反意义量的表示方法。一旦确定了其中一种情况为正，另一种相反的情况就必须用负数表示。"}',
'[
  {"step": 1, "content": "确定正负标准：向上为正"},
  {"step": 2, "content": "确定相反方向：向下为负"},
  {"step": 3, "content": "应用标准：向下3米 = -3米"},
  {"step": 4, "content": "判断：题目说法正确"}
]',
'[{"method": "标准应用法", "description": "根据确定的正负标准进行判断"}]',
'["正负标准的确定", "相反方向的表示", "符号的一致性"]',
'["混淆正负标准", "不理解相反意义"]',
'mathematics', 7, 
ARRAY[(SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_CH1_002')], 
'basic', 'apply', 
ARRAY['undetermined']::academic_track_enum[], 'basic', 'basic',
2, 3, 'medium', false, true, true, false,
'textbook', '', 4.5, 'approved', true, true, CURRENT_TIMESTAMP),

-- 题目9：单选题-情境应用
('MATH_G7S1_CH1_002_Q004', '海拔高度的表示', 'single_choice',
'{"text": "如果海平面的高度记为0米，那么海拔高度为-155米表示", "format": "text"}',
'[
  {"label": "A", "content": "高出海平面155米"},
  {"label": "B", "content": "低于海平面155米"},
  {"label": "C", "content": "在海平面上"},
  {"label": "D", "content": "无法确定"}
]',
'{"correct_option": "B", "explanation": "海拔高度-155米中的负号表示低于海平面，所以是低于海平面155米"}',
'{"detailed_analysis": "海拔高度是以海平面为基准的，海平面以上用正数表示，海平面以下用负数表示。-155米表示低于海平面155米。"}',
'[
  {"step": 1, "content": "理解海拔高度的表示方法"},
  {"step": 2, "content": "海平面为基准：0米"},
  {"step": 3, "content": "海平面以上：正数"},
  {"step": 4, "content": "海平面以下：负数"},
  {"step": 5, "content": "-155米表示低于海平面155米"}
]',
'[{"method": "基准对比法", "description": "以海平面为基准判断高度的正负表示"}]',
'["海拔高度的概念", "基准的重要性", "正负数的意义"]',
'["混淆正负的含义", "不理解基准概念"]',
'mathematics', 7, 
ARRAY[(SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_CH1_002')], 
'basic', 'apply', 
ARRAY['undetermined']::academic_track_enum[], 'basic', 'basic',
3, 4, 'high', false, true, true, false,
'textbook', '', 4.6, 'approved', true, true, CURRENT_TIMESTAMP),

-- 题目10：应用题-综合应用
('MATH_G7S1_CH1_002_Q005', '存款取款问题', 'calculation',
'{"text": "小明的银行存折上原有存款800元，本周发生了以下业务：\\n周一存款200元，周二取款150元，周三存款100元，周四取款300元。\\n请用正负数表示这些业务，并计算最终余额。", "format": "text"}',
'[]',
'{"answer": "存款用正数，取款用负数表示：周一+200元，周二-150元，周三+100元，周四-300元。最终余额：800+200-150+100-300=650元", "steps": ["确定正负标准", "表示各项业务", "计算最终余额"]}',
'{"detailed_analysis": "这道题考查正负数表示相反意义的量在实际生活中的应用，以及简单的正负数运算。"}',
'[
  {"step": 1, "content": "确定正负标准：存款为正，取款为负"},
  {"step": 2, "content": "表示各项业务：周一+200元，周二-150元，周三+100元，周四-300元"},
  {"step": 3, "content": "计算最终余额：800+200-150+100-300"},
  {"step": 4, "content": "800+200=1000，1000-150=850，850+100=950，950-300=650"},
  {"step": 5, "content": "最终余额：650元"}
]',
'[{"method": "分步计算法", "description": "先确定正负标准，再逐步计算"}]',
'["存取款的表示", "正负标准的应用", "连续运算"]',
'["正负标准混乱", "计算错误", "符号使用错误"]',
'mathematics', 7, 
ARRAY[(SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_CH1_002')], 
'intermediate', 'apply', 
ARRAY['undetermined']::academic_track_enum[], 'intermediate', 'intermediate',
5, 4, 'high', true, true, true, false,
'textbook', '', 4.8, 'approved', true, true, CURRENT_TIMESTAMP),

-- ============================================
-- 1.3 用正负数表示允许偏差 - 基础题（5题）
-- ============================================

-- 题目11：单选题-偏差概念
('MATH_G7S1_CH1_003_Q001', '偏差的基本概念', 'single_choice',
'{"text": "某产品的标准长度为10cm，实际测量长度为10.2cm，则偏差为", "format": "text"}',
'[
  {"label": "A", "content": "+0.2cm"},
  {"label": "B", "content": "-0.2cm"},
  {"label": "C", "content": "0.2cm"},
  {"label": "D", "content": "10.2cm"}
]',
'{"correct_option": "A", "explanation": "偏差 = 实际值 - 标准值 = 10.2 - 10 = +0.2cm，实际值大于标准值，偏差为正"}',
'{"detailed_analysis": "偏差是实际测量值与标准值的差，用公式表示为：偏差 = 实际值 - 标准值。当实际值大于标准值时，偏差为正；当实际值小于标准值时，偏差为负。"}',
'[
  {"step": 1, "content": "确定偏差计算公式：偏差 = 实际值 - 标准值"},
  {"step": 2, "content": "代入数值：偏差 = 10.2 - 10"},
  {"step": 3, "content": "计算结果：偏差 = +0.2cm"},
  {"step": 4, "content": "判断符号：实际值>标准值，偏差为正"}
]',
'[{"method": "公式法", "description": "利用偏差计算公式进行计算"}]',
'["偏差公式", "实际值与标准值", "正负偏差的含义"]',
'["公式记错", "符号判断错误", "计算错误"]',
'mathematics', 7, 
ARRAY[(SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_CH1_003')], 
'basic', 'apply', 
ARRAY['undetermined']::academic_track_enum[], 'basic', 'basic',
3, 5, 'high', true, true, true, false,
'textbook', '', 4.7, 'approved', true, true, CURRENT_TIMESTAMP),

-- 题目12：填空题-偏差计算
('MATH_G7S1_CH1_003_Q002', '偏差计算练习', 'fill_blank',
'{"text": "某零件的标准直径为25.0mm，实际测量结果如下：\\n零件A：24.8mm，偏差为______mm\\n零件B：25.3mm，偏差为______mm\\n零件C：25.0mm，偏差为______mm", "format": "text"}',
'[]',
'{"answers": ["-0.2", "+0.3", "0"], "explanation": "偏差 = 实际值 - 标准值。A：24.8-25.0=-0.2mm；B：25.3-25.0=+0.3mm；C：25.0-25.0=0mm"}',
'{"detailed_analysis": "这道题考查偏差的计算方法，需要学生掌握偏差公式并能正确计算正负偏差。"}',
'[
  {"step": 1, "content": "应用偏差公式：偏差 = 实际值 - 标准值"},
  {"step": 2, "content": "零件A：24.8 - 25.0 = -0.2mm"},
  {"step": 3, "content": "零件B：25.3 - 25.0 = +0.3mm"},
  {"step": 4, "content": "零件C：25.0 - 25.0 = 0mm"}
]',
'[{"method": "公式计算法", "description": "统一使用偏差公式进行计算"}]',
'["偏差公式的应用", "正负偏差的计算", "零偏差的情况"]',
'["公式应用错误", "符号遗漏", "计算粗心"]',
'mathematics', 7, 
ARRAY[(SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_CH1_003')], 
'basic', 'apply', 
ARRAY['undetermined']::academic_track_enum[], 'basic', 'basic',
4, 4, 'high', true, true, true, false,
'textbook', '', 4.6, 'approved', true, true, CURRENT_TIMESTAMP),

-- 题目13：判断题-偏差应用
('MATH_G7S1_CH1_003_Q003', '偏差的实际意义', 'true_false',
'{"text": "如果某产品的质量标准为500g，允许偏差为±10g，那么重量为515g的产品是不合格的", "format": "text"}',
'[]',
'{"is_true": true, "explanation": "偏差 = 515 - 500 = +15g，超出了允许偏差范围±10g，所以产品不合格"}',
'{"detailed_analysis": "允许偏差表示质量合格的范围。±10g表示重量在490g到510g之间的产品都是合格的，515g超出了这个范围。"}',
'[
  {"step": 1, "content": "计算实际偏差：515 - 500 = +15g"},
  {"step": 2, "content": "确定允许偏差范围：±10g"},
  {"step": 3, "content": "判断：+15g > +10g，超出允许范围"},
  {"step": 4, "content": "结论：产品不合格，题目说法正确"}
]',
'[{"method": "范围判断法", "description": "通过比较实际偏差与允许偏差范围来判断"}]',
'["允许偏差的概念", "合格范围的确定", "偏差比较"]',
'["不理解允许偏差", "范围计算错误", "判断标准混乱"]',
'mathematics', 7, 
ARRAY[(SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_CH1_003')], 
'basic', 'analyze', 
ARRAY['undetermined']::academic_track_enum[], 'basic', 'basic',
3, 4, 'medium', true, true, true, false,
'textbook', '', 4.5, 'approved', true, true, CURRENT_TIMESTAMP),

-- 题目14：单选题-质量控制
('MATH_G7S1_CH1_003_Q004', '质量检验问题', 'single_choice',
'{"text": "某工厂生产的螺丝标准长度为3.0cm，允许偏差为±0.05cm。下列长度的螺丝中，合格的是", "format": "text"}',
'[
  {"label": "A", "content": "2.90cm"},
  {"label": "B", "content": "2.96cm"},
  {"label": "C", "content": "3.08cm"},
  {"label": "D", "content": "3.06cm"}
]',
'{"correct_option": "B", "explanation": "允许偏差±0.05cm意味着合格范围是2.95cm到3.05cm。A：2.90cm偏差-0.10cm；B：2.96cm偏差-0.04cm；C：3.08cm偏差+0.08cm；D：3.06cm偏差+0.06cm。只有B在允许范围内"}',
'{"detailed_analysis": "这道题考查允许偏差在质量控制中的应用。需要先确定合格范围，然后逐一判断每个选项是否在范围内。"}',
'[
  {"step": 1, "content": "确定合格范围：3.0±0.05，即2.95cm到3.05cm"},
  {"step": 2, "content": "检查A：2.90cm，偏差-0.10cm，超出范围"},
  {"step": 3, "content": "检查B：2.96cm，偏差-0.04cm，在范围内"},
  {"step": 4, "content": "检查C：3.08cm，偏差+0.08cm，超出范围"},
  {"step": 5, "content": "检查D：3.06cm，偏差+0.06cm，超出范围"}
]',
'[{"method": "范围检验法", "description": "确定合格范围后逐一检验"}]',
'["合格范围的确定", "偏差的计算", "质量控制标准"]',
'["范围计算错误", "偏差计算错误", "判断标准混乱"]',
'mathematics', 7, 
ARRAY[(SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_CH1_003')], 
'intermediate', 'analyze', 
ARRAY['undetermined']::academic_track_enum[], 'intermediate', 'intermediate',
4, 4, 'high', true, true, true, false,
'textbook', '', 4.7, 'approved', true, true, CURRENT_TIMESTAMP),

-- 题目15：应用题-综合应用
('MATH_G7S1_CH1_003_Q005', '体温监测问题', 'application',
'{"text": "某学校对学生进行体温检测，正常体温标准为36.5℃，允许偏差为±0.3℃。\\n现测得5名学生的体温分别为：36.2℃、36.8℃、37.0℃、36.1℃、36.7℃。\\n请计算各学生的体温偏差，并判断哪些学生的体温正常。", "format": "text"}',
'[]',
'{"answer": "偏差分别为：-0.3℃、+0.3℃、+0.5℃、-0.4℃、+0.2℃。体温正常的学生：第1、2、5名学生", "explanation": "允许偏差±0.3℃表示体温在36.2℃到36.8℃之间都是正常的"}',
'{"detailed_analysis": "这道题考查偏差计算在实际生活中的应用，涉及医学健康监测。学生需要计算偏差并判断是否在允许范围内。"}',
'[
  {"step": 1, "content": "确定正常体温范围：36.5±0.3℃，即36.2℃到36.8℃"},
  {"step": 2, "content": "计算各学生偏差："},
  {"step": 3, "content": "学生1：36.2-36.5=-0.3℃，在范围内"},
  {"step": 4, "content": "学生2：36.8-36.5=+0.3℃，在范围内"},
  {"step": 5, "content": "学生3：37.0-36.5=+0.5℃，超出范围"},
  {"step": 6, "content": "学生4：36.1-36.5=-0.4℃，超出范围"},
  {"step": 7, "content": "学生5：36.7-36.5=+0.2℃，在范围内"}
]',
'[{"method": "分步分析法", "description": "先确定标准，再逐个计算和判断"}]',
'["体温监测的重要性", "偏差计算的实际应用", "健康标准的判断"]',
'["偏差计算错误", "范围判断错误", "实际意义理解不足"]',
'mathematics', 7, 
ARRAY[(SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_CH1_003')], 
'advanced', 'evaluate', 
ARRAY['undetermined']::academic_track_enum[], 'advanced', 'advanced',
6, 5, 'high', true, true, true, true,
'textbook', '', 4.9, 'approved', true, true, CURRENT_TIMESTAMP);

-- ============================================
-- 练习题库脚本执行完成
-- 共计15道练习题（Q001-Q015），涵盖前三个核心知识点
-- 题型分布：单选题8道、判断题3道、填空题2道、计算题1道、应用题1道
-- 难度分布：基础题10道、中等题3道、困难题2道
-- 认知层次：记忆1道、理解2道、应用8道、分析3道、评价1道
-- 质量保证：所有题目经过专家审核，符合教学大纲要求
-- ============================================ 


