#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试正则表达式脚本
"""

import re

# 读取文件
with open('grade_6_7_cross_grade_relationships.sql', 'r', encoding='utf-8') as f:
    content = f.read()

# 找到VALUES的位置
values_pos = content.find("VALUES")
if values_pos == -1:
    print("[错误] 找不到VALUES关键字")
    exit(1)

# 提取VALUES之后的部分
values_content = content[values_pos + 6:]

# 找到数据结束位置
end_markers = [';', '-- ====', '/*']
data_end = len(values_content)
print("VALUES后全部内容长度:", len(values_content))
print("查找结束标记:")

for marker in end_markers:
    pos = values_content.find(marker)
    print(f"  标记 '{marker}' 位置: {pos}")
    if pos != -1:
        data_end = min(data_end, pos)

print(f"最终数据结束位置: {data_end}")

relations_text = values_content[:data_end]

print("关系文本长度:", len(relations_text))
print("关系文本内容:")
print(repr(relations_text))

# 查看VALUES后面前200个字符
print("\nVALUES后面前200个字符:")
print(repr(values_content[:200]))

# 测试多个正则表达式
patterns = [
    r'(\(\(SELECT id FROM knowledge_nodes WHERE node_code = \'[^\']+\'\),\s*\(SELECT id FROM knowledge_nodes WHERE node_code = \'[^\']+\'\),[\s\S]*?true\)[,;])',
    r'(\(\(SELECT id FROM knowledge_nodes WHERE node_code = \'[^\']+\'\),[\s\S]*?true\)[,;])',
    r'(\(\(SELECT.*?true\)[,;])',
    r'(-- 【.*?true\)[,;])',
]

for i, pattern in enumerate(patterns):
    print(f"\n测试pattern {i+1}: {pattern}")
    matches = list(re.finditer(pattern, relations_text, re.DOTALL))
    print(f"匹配到 {len(matches)} 个条目")
    
    if matches and len(matches) > 0:
        print("第一个匹配:")
        print(repr(matches[0].group(1)[:200]))
        break 