#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检测同年级关联关系脚本
用于识别文件中的同年级关系（应该删除的关系）
只保留二年级→三年级的跨年级关系
"""

import re
import sys

def extract_grade_info(node_code):
    """提取知识点的年级和学期信息"""
    if node_code.startswith('MATH_G8S1_'):
        return '8', '1'  # 八年级上学期
    elif node_code.startswith('MATH_G8S2_'):
        return '8', '2'  # 八年级下学期
    elif node_code.startswith('MATH_G9S1_'):
        return '9', '1'  # 九年级上学期
    elif node_code.startswith('MATH_G9S2_'):
        return '9', '2'  # 九年级下学期
    else:
        return None, None

def check_same_grade_relationships(filename):
    """检查同年级关系"""
    
    with open(filename, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 提取所有关系
    pattern = r"\(\(SELECT id FROM knowledge_nodes WHERE node_code = '([^']+)'\),\s*\(SELECT id FROM knowledge_nodes WHERE node_code = '([^']+)'\),\s*'([^']+)'[^;)]*\)"
    matches = list(re.finditer(pattern, content, re.DOTALL))
    
    print(f"[分析] 检查文件: {filename}")
    print("=" * 80)
    print(f"[统计] 总关系数量: {len(matches)}")
    
    same_grade_relationships = []
    cross_grade_relationships = []
    invalid_relationships = []
    
    for i, match in enumerate(matches):
        source_code, target_code, rel_type = match.groups()
        position = i + 1
        
        source_grade, source_semester = extract_grade_info(source_code)
        target_grade, target_semester = extract_grade_info(target_code)
        
        if source_grade is None or target_grade is None:
            invalid_relationships.append((position, source_code, target_code, rel_type))
            continue
        
        if source_grade == target_grade:
            # 同年级关系
            same_grade_relationships.append((position, source_code, target_code, rel_type, source_grade))
        elif source_grade == '8' and target_grade == '9':
            # 正确的跨年级关系（八年级→九年级）
            cross_grade_relationships.append((position, source_code, target_code, rel_type))
        else:
            # 其他错误关系（比如九年级→八年级）
            invalid_relationships.append((position, source_code, target_code, rel_type))
    
    # 报告结果
    if same_grade_relationships:
        print(f"[错误] 发现 {len(same_grade_relationships)} 个同年级关系 (需要删除)")
        print()
        print("[详情] 同年级关系详情:")
        print()
        
        grade_8_count = 0
        grade_9_count = 0
        
        for position, source, target, rel_type, grade in same_grade_relationships:
            print(f"{position:3d}. {source} → {target} ({rel_type}) - 第{grade}年级内部关系")
            if grade == '8':
                grade_8_count += 1
            else:
                grade_9_count += 1
        
        print()
        print(f"[分布] 八年级内部关系: {grade_8_count} 个")
        print(f"[分布] 九年级内部关系: {grade_9_count} 个")
    else:
        print("[成功] 未发现同年级关系")
    
    if invalid_relationships:
        print(f"[错误] 发现 {len(invalid_relationships)} 个其他错误关系")
        for position, source, target, rel_type in invalid_relationships:
            print(f"{position:3d}. {source} → {target} ({rel_type}) - 无效关系")
    
    print()
    print(f"[统计] 正确的跨年级关系: {len(cross_grade_relationships)} 个")
    print(f"[统计] 同年级关系: {len(same_grade_relationships)} 个")
    print(f"[统计] 其他错误关系: {len(invalid_relationships)} 个")
    
    return same_grade_relationships, invalid_relationships

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("用法: python check_same_grade.py <文件名>")
        sys.exit(1)
    
    filename = sys.argv[1]
    same_grade, invalid = check_same_grade_relationships(filename)
    
    if same_grade or invalid:
        print()
        print("=" * 80)
        print("[建议] 需要删除同年级关系和其他错误关系")
        print("=" * 80)
        sys.exit(1)
    else:
        print()
        print("=" * 80) 
        print("[成功] 文件中只包含正确的跨年级关系")
        print("=" * 80) 