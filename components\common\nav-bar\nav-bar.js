/**
 * 通用导航栏组件
 */

const app = getApp();
const deviceUtils = require('../../../utils/device-info.js');

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 标题
    title: {
      type: String,
      value: ''
    },
    // 是否固定在顶部
    fixed: {
      type: Boolean,
      value: true
    },
    // 是否显示返回按钮
    showBack: {
      type: Boolean,
      value: true
    },
    // 是否显示首页按钮
    showHome: {
      type: Boolean,
      value: false
    },
    // 返回按钮文本
    backText: {
      type: String,
      value: ''
    },
    // 首页按钮文本
    homeText: {
      type: String,
      value: ''
    },
    // 背景色
    bgColor: {
      type: String,
      value: '#ffffff'
    },
    // 文本颜色
    textColor: {
      type: String,
      value: '#333333'
    },
    // 是否显示阴影
    shadow: {
      type: Boolean,
      value: true
    },
    // 是否透明背景
    transparent: {
      type: Boolean,
      value: false
    },
    // 自定义返回事件
    customBack: {
      type: Boolean,
      value: false
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    statusBarHeight: 20,
    navBarHeight: 46,
    capsuleHeight: 32
  },

  lifetimes: {
    attached() {
      this.init();
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 初始化导航栏高度等信息
    init() {
      const systemInfo = deviceUtils.getSystemInfoCompat();
      const menuButtonInfo = wx.getMenuButtonBoundingClientRect();
      
      // 状态栏高度
      const statusBarHeight = systemInfo.statusBarHeight;
      
      // 导航栏高度 = 胶囊按钮上下边距 + 胶囊按钮高度
      const navBarHeight = (menuButtonInfo.top - statusBarHeight) * 2 + menuButtonInfo.height;
      
      this.setData({
        statusBarHeight,
        navBarHeight,
        capsuleHeight: menuButtonInfo.height
      });
    },

    // 返回上一页
    handleBack() {
      if (this.data.customBack) {
        this.triggerEvent('back');
      } else {
        const pages = getCurrentPages();
        if (pages.length > 1) {
          wx.navigateBack();
        } else {
          wx.reLaunch({
            url: '/pages/index/index'
          });
        }
      }
    },

    // 返回首页
    handleHome() {
      this.triggerEvent('home');
      wx.reLaunch({
        url: '/pages/index/index'
      });
    }
  }
}) 