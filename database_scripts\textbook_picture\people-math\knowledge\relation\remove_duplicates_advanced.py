#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级重复关系清理工具
基于find_duplicates1.py的检测结果，精确删除重复关系
保留第一个出现的关系，删除后续重复项
"""

import re
import sys
import os
from collections import defaultdict

def extract_relationships(filename):
    """提取所有关系及其位置信息"""
    with open(filename, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 多行SQL关系模式，使用DOTALL标志处理跨行匹配
    pattern = r"\(\(SELECT id FROM knowledge_nodes WHERE node_code = '([^']+)'\),\s*\(SELECT id FROM knowledge_nodes WHERE node_code = '([^']+)'\),\s*'([^']+)'[^;)]*\)"
    
    relationships = []
    
    # 在整个文件中查找关系
    matches = list(re.finditer(pattern, content, re.DOTALL))
    lines = content.split('\n')
    
    for match in matches:
        source_code = match.group(1)
        target_code = match.group(2)
        rel_type = match.group(3)
        
        key = f"{source_code}|{target_code}|{rel_type}"
        
        # 确定行号
        start_pos = match.start()
        line_num = content[:start_pos].count('\n') + 1
        
        relationships.append({
            'line_num': line_num,
            'source': source_code,
            'target': target_code,
            'type': rel_type,
            'key': key,
            'match_start': match.start(),
            'match_end': match.end(),
            'match_text': match.group(0)
        })
    
    return relationships, lines, content

def find_duplicates(relationships):
    """查找重复关系"""
    seen = defaultdict(list)
    
    for rel in relationships:
        seen[rel['key']].append(rel)
    
    duplicates = {k: v for k, v in seen.items() if len(v) > 1}
    return duplicates

def remove_duplicates(filename, output_filename=None):
    """删除重复关系，保留第一个"""
    if output_filename is None:
        output_filename = filename
    
    print(f"[处理] 分析文件: {filename}")
    
    # 提取所有关系
    relationships, lines = extract_relationships(filename)
    print(f"[统计] 找到 {len(relationships)} 个关系")
    
    # 查找重复
    duplicates = find_duplicates(relationships)
    
    if not duplicates:
        print("[成功] 未发现重复关系")
        return True
    
    print(f"[发现] {len(duplicates)} 组重复关系：")
    
    # 记录需要删除的行号
    lines_to_remove = set()
    
    for i, (key, rels) in enumerate(duplicates.items(), 1):
        source, target, rel_type = key.split('|')
        print(f"\n{i}. {source} → {target} ({rel_type})")
        print(f"   出现次数: {len(rels)}")
        
        # 保留第一个，删除其余
        for j, rel in enumerate(rels):
            if j == 0:
                print(f"   保留: 第{rel['line_num']}行")
            else:
                print(f"   删除: 第{rel['line_num']}行")
                lines_to_remove.add(rel['line_num'] - 1)  # 转为0索引
    
    # 创建新内容（删除重复行）
    new_lines = []
    removed_count = 0
    
    for i, line in enumerate(lines):
        if i in lines_to_remove:
            removed_count += 1
            print(f"[删除] 第{i+1}行: {line[:100]}...")
        else:
            new_lines.append(line)
    
    # 写入文件
    with open(output_filename, 'w', encoding='utf-8') as f:
        f.write('\n'.join(new_lines))
    
    print(f"\n[完成] 删除了 {removed_count} 个重复关系")
    print(f"[保存] 结果保存到: {output_filename}")
    print(f"[统计] 原始关系: {len(relationships)}")
    print(f"[统计] 清理后关系: {len(relationships) - removed_count}")
    
    return True

def main():
    if len(sys.argv) < 2:
        print("用法: python remove_duplicates_advanced.py <input_file> [output_file]")
        sys.exit(1)
    
    input_file = sys.argv[1]
    output_file = sys.argv[2] if len(sys.argv) > 2 else None
    
    if not os.path.exists(input_file):
        print(f"[错误] 文件不存在: {input_file}")
        sys.exit(1)
    
    try:
        remove_duplicates(input_file, output_file)
        print("\n[成功] 重复关系清理完成")
    except Exception as e:
        print(f"[错误] 处理过程中出现错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main() 