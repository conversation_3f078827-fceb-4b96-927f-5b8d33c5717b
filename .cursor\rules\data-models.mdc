---
description: 
globs: 
alwaysApply: false
---
# 数据模型规范

本项目采用清晰的数据模型结构，确保数据的一致性和可维护性。

## 核心数据模型

### 用户模型

```javascript
// models/user.js
{
  id: String,          // 用户唯一标识
  name: String,        // 用户名称
  grade: Number,       // 年级
  learningProgress: {  // 学习进度
    currentChapter: String,
    completedTopics: Array
  },
  preferences: {       // 学习偏好
    difficulty: String,
    learningStyle: String
  }
}
```

### 知识点模型

```javascript
// models/knowledge-point.js
{
  id: String,           // 知识点ID
  name: String,         // 知识点名称
  grade: Number,        // 适用年级
  chapter: String,      // 所属章节
  prerequisites: Array, // 前置知识点
  content: {           // 知识点内容
    explanation: String,
    examples: Array,
    exercises: Array
  }
}
```

### 题目模型

```javascript
// models/question.js
{
  id: String,          // 题目ID
  type: String,        // 题目类型
  difficulty: Number,  // 难度等级
  content: {          // 题目内容
    text: String,
    images: Array,
    options: Array    // 选择题选项
  },
  solution: {         // 解题方案
    steps: Array,
    explanation: String
  },
  tags: Array         // 相关知识点标签
}
```

## 数据关系

- 用户 -> 学习进度 -> 知识点
- 知识点 -> 题目集合
- 题目 -> 知识点标签

## 数据验证

- 所有模型必须进行数据验证
- 使用统一的验证工具函数
- 在存储前进行数据清理
- 保持数据类型的一致性

## 实现参考

- 用户模型：[models/user.js](mdc:models/user.js)
- 知识点模型：[models/knowledge-point.js](mdc:models/knowledge-point.js)
- 题目模型：[models/question.js](mdc:models/question.js)
- 数据验证工具：[utils/validator.js](mdc:utils/validator.js)
