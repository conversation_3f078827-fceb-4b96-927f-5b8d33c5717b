/**
 * 数学公式编辑器组件
 * 基于KaTeX实现的LaTeX公式编辑和渲染组件
 */

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 初始公式内容
    initialFormula: {
      type: String,
      value: ''
    },
    // 是否显示编辑器
    visible: {
      type: Boolean,
      value: false,
      observer: function(newVal) {
        if (newVal) {
          this._showEditor();
        } else {
          this._hideEditor();
        }
      }
    },
    show: {
      type: Boolean,
      value: false,
      observer: '_onShowChange'
    },
    value: {
      type: String,
      value: ''
    },
    placeholder: {
      type: String,
      value: '请输入LaTeX格式的数学公式'
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 编辑器显示状态
    show: false,
    // 当前公式内容
    formula: '',
    // 是否显示预览
    showPreview: true,
    // 当前选中的符号类别标签
    currentTab: 'basic',
    // 是否有语法错误
    hasError: false,
    // 错误信息
    errorMsg: '',
    // 符号分类
    tabs: [
      { id: 'basic', name: '基础' },
      { id: 'algebra', name: '代数' },
      { id: 'geometry', name: '几何' },
      { id: 'calculus', name: '微积分' }
    ],
    // 符号库
    symbols: {
      basic: [
        {
          title: '算术运算',
          items: [
            { text: '+', desc: '加号' },
            { text: '-', desc: '减号' },
            { text: '\\times', desc: '乘号' },
            { text: '\\div', desc: '除号' },
            { text: '=', desc: '等于' },
            { text: '\\neq', desc: '不等于' },
            { text: '\\approx', desc: '约等于' },
            { text: '\\pm', desc: '正负号' }
          ]
        },
        {
          title: '上下标',
          items: [
            { text: '^{?}', desc: '上标', placeholder: true },
            { text: '_{?}', desc: '下标', placeholder: true },
            { text: '^{?}_{?}', desc: '上下标', placeholder: true },
            { text: '\\frac{?}{?}', desc: '分数', placeholder: true }
          ]
        }
      ],
      algebra: [
        {
          title: '代数符号',
          items: [
            { text: '\\sqrt{?}', desc: '平方根', placeholder: true },
            { text: '\\sqrt[?]{?}', desc: 'n次方根', placeholder: true },
            { text: '\\sum_{?}^{?}', desc: '求和', placeholder: true },
            { text: '\\prod_{?}^{?}', desc: '求积', placeholder: true }
          ]
        },
        {
          title: '关系运算',
          items: [
            { text: '<', desc: '小于' },
            { text: '>', desc: '大于' },
            { text: '\\leq', desc: '小于等于' },
            { text: '\\geq', desc: '大于等于' },
            { text: '\\in', desc: '属于' },
            { text: '\\notin', desc: '不属于' },
            { text: '\\subset', desc: '子集' },
            { text: '\\supset', desc: '超集' }
          ]
        }
      ],
      geometry: [
        {
          title: '几何符号',
          items: [
            { text: '\\angle', desc: '角' },
            { text: '\\triangle', desc: '三角形' },
            { text: '\\square', desc: '正方形' },
            { text: '\\cong', desc: '全等' },
            { text: '\\sim', desc: '相似' },
            { text: '\\parallel', desc: '平行' },
            { text: '\\perp', desc: '垂直' },
            { text: '\\circ', desc: '度' }
          ]
        },
        {
          title: '坐标几何',
          items: [
            { text: '(?, ?)', desc: '坐标点', placeholder: true },
            { text: '\\overrightarrow{??}', desc: '向量', placeholder: true },
            { text: '\\overleftrightarrow{??}', desc: '直线', placeholder: true },
            { text: '\\overarc{??}', desc: '弧', placeholder: true }
          ]
        }
      ],
      calculus: [
        {
          title: '微积分符号',
          items: [
            { text: '\\lim_{? \\to ?}', desc: '极限', placeholder: true },
            { text: '\\frac{d?}{d?}', desc: '导数', placeholder: true },
            { text: '\\frac{\\partial ?}{\\partial ?}', desc: '偏导数', placeholder: true },
            { text: '\\int_{?}^{?}', desc: '定积分', placeholder: true },
            { text: '\\int', desc: '不定积分' },
            { text: '\\sum', desc: '求和' },
            { text: '\\prod', desc: '求积' },
            { text: '\\infty', desc: '无穷' }
          ]
        }
      ]
    },
    inputValue: '',
    error: '',
    isValid: true,
    symbolCategories: [
      { id: 'basic', name: '基础' },
      { id: 'algebra', name: '代数' },
      { id: 'geometry', name: '几何' },
      { id: 'calculus', name: '微积分' },
      { id: 'sets', name: '集合' },
      { id: 'logic', name: '逻辑' }
    ],
    basicSymbols: [
      { symbol: '+', latex: '+', desc: '加号' },
      { symbol: '-', latex: '-', desc: '减号' },
      { symbol: '×', latex: '\\times', desc: '乘号' },
      { symbol: '÷', latex: '\\div', desc: '除号' },
      { symbol: '=', latex: '=', desc: '等于' },
      { symbol: '≠', latex: '\\neq', desc: '不等于' },
      { symbol: '≈', latex: '\\approx', desc: '约等于' },
      { symbol: '<', latex: '<', desc: '小于' },
      { symbol: '>', latex: '>', desc: '大于' },
      { symbol: '≤', latex: '\\leq', desc: '小于等于' },
      { symbol: '≥', latex: '\\geq', desc: '大于等于' },
      { symbol: 'π', latex: '\\pi', desc: '圆周率' },
      { symbol: '∞', latex: '\\infty', desc: '无穷' },
      { symbol: '%', latex: '\\%', desc: '百分比' },
      { symbol: '°', latex: '^{\\circ}', desc: '度' },
      { symbol: '±', latex: '\\pm', desc: '正负号' }
    ],
    algebraSymbols: [
      { symbol: 'x²', latex: 'x^2', desc: '平方' },
      { symbol: 'x³', latex: 'x^3', desc: '立方' },
      { symbol: 'xⁿ', latex: 'x^n', desc: 'n次方' },
      { symbol: '√', latex: '\\sqrt{x}', desc: '平方根' },
      { symbol: '∛', latex: '\\sqrt[3]{x}', desc: '立方根' },
      { symbol: 'ⁿ√', latex: '\\sqrt[n]{x}', desc: 'n次根' },
      { symbol: 'log', latex: '\\log_{a}{x}', desc: '对数' },
      { symbol: 'ln', latex: '\\ln{x}', desc: '自然对数' },
      { symbol: '|x|', latex: '|x|', desc: '绝对值' },
      { symbol: 'a/b', latex: '\\frac{a}{b}', desc: '分数' },
      { symbol: 'a⁄b', latex: '{a} \\over {b}', desc: '分数(另一种)' },
      { symbol: 'Σ', latex: '\\sum_{i=1}^{n}', desc: '求和' },
      { symbol: 'Π', latex: '\\prod_{i=1}^{n}', desc: '求积' },
      { symbol: 'x̄', latex: '\\bar{x}', desc: '平均值' },
      { symbol: 'â', latex: '\\hat{a}', desc: '估计值' },
      { symbol: 'ẋ', latex: '\\dot{x}', desc: '导数' },
      { symbol: 'x_i', latex: 'x_{i}', desc: '下标' }
    ],
    geometrySymbols: [
      { symbol: '△', latex: '\\triangle', desc: '三角形' },
      { symbol: '□', latex: '\\square', desc: '正方形' },
      { symbol: '∠', latex: '\\angle', desc: '角' },
      { symbol: '⊥', latex: '\\perp', desc: '垂直' },
      { symbol: '∥', latex: '\\parallel', desc: '平行' },
      { symbol: '≅', latex: '\\cong', desc: '全等' },
      { symbol: '∼', latex: '\\sim', desc: '相似' },
      { symbol: '∩', latex: '\\cap', desc: '交集' },
      { symbol: '∪', latex: '\\cup', desc: '并集' },
      { symbol: '⊂', latex: '\\subset', desc: '子集' },
      { symbol: '⊆', latex: '\\subseteq', desc: '子集或等于' },
      { symbol: '⊃', latex: '\\supset', desc: '超集' },
      { symbol: '∈', latex: '\\in', desc: '属于' },
      { symbol: '∉', latex: '\\notin', desc: '不属于' },
      { symbol: '∅', latex: '\\emptyset', desc: '空集' },
      { symbol: '⊕', latex: '\\oplus', desc: '异或' }
    ],
    calculusSymbols: [
      { symbol: '∫', latex: '\\int', desc: '积分' },
      { symbol: '∬', latex: '\\iint', desc: '二重积分' },
      { symbol: '∭', latex: '\\iiint', desc: '三重积分' },
      { symbol: '∮', latex: '\\oint', desc: '闭合曲线积分' },
      { symbol: '∂', latex: '\\partial', desc: '偏导数' },
      { symbol: 'dx', latex: '\\mathrm{d}x', desc: '微分' },
      { symbol: '∇', latex: '\\nabla', desc: '梯度' },
      { symbol: 'lim', latex: '\\lim_{x \\to a}', desc: '极限' },
      { symbol: '→', latex: '\\rightarrow', desc: '右箭头' },
      { symbol: '↑', latex: '\\uparrow', desc: '上箭头' },
      { symbol: '↓', latex: '\\downarrow', desc: '下箭头' },
      { symbol: '←', latex: '\\leftarrow', desc: '左箭头' },
      { symbol: '↔', latex: '\\leftrightarrow', desc: '双向箭头' },
      { symbol: '⇒', latex: '\\Rightarrow', desc: '推导' },
      { symbol: '⇐', latex: '\\Leftarrow', desc: '被推导' },
      { symbol: '⇔', latex: '\\Leftrightarrow', desc: '等价' }
    ],
    setsSymbols: [
      { symbol: '∩', latex: '\\cap', desc: '交集' },
      { symbol: '∪', latex: '\\cup', desc: '并集' },
      { symbol: '∖', latex: '\\setminus', desc: '差集' },
      { symbol: '⊂', latex: '\\subset', desc: '真子集' },
      { symbol: '⊆', latex: '\\subseteq', desc: '子集' },
      { symbol: '⊃', latex: '\\supset', desc: '真超集' },
      { symbol: '⊇', latex: '\\supseteq', desc: '超集' },
      { symbol: '∈', latex: '\\in', desc: '属于' },
      { symbol: '∉', latex: '\\notin', desc: '不属于' },
      { symbol: '∅', latex: '\\emptyset', desc: '空集' },
      { symbol: 'ℕ', latex: '\\mathbb{N}', desc: '自然数集' },
      { symbol: 'ℤ', latex: '\\mathbb{Z}', desc: '整数集' },
      { symbol: 'ℚ', latex: '\\mathbb{Q}', desc: '有理数集' },
      { symbol: 'ℝ', latex: '\\mathbb{R}', desc: '实数集' },
      { symbol: 'ℂ', latex: '\\mathbb{C}', desc: '复数集' },
      { symbol: '∀', latex: '\\forall', desc: '任意' }
    ],
    logicSymbols: [
      { symbol: '∧', latex: '\\land', desc: '且' },
      { symbol: '∨', latex: '\\lor', desc: '或' },
      { symbol: '¬', latex: '\\neg', desc: '非' },
      { symbol: '⊕', latex: '\\oplus', desc: '异或' },
      { symbol: '→', latex: '\\rightarrow', desc: '蕴含' },
      { symbol: '↔', latex: '\\leftrightarrow', desc: '等价' },
      { symbol: '⊢', latex: '\\vdash', desc: '推出' },
      { symbol: '⊨', latex: '\\models', desc: '语义推出' },
      { symbol: '∃', latex: '\\exists', desc: '存在' },
      { symbol: '∄', latex: '\\nexists', desc: '不存在' },
      { symbol: '∀', latex: '\\forall', desc: '任意' },
      { symbol: '□', latex: '\\Box', desc: '必然' },
      { symbol: '◇', latex: '\\Diamond', desc: '可能' },
      { symbol: '⊤', latex: '\\top', desc: '恒真' },
      { symbol: '⊥', latex: '\\bot', desc: '恒假' },
      { symbol: '≡', latex: '\\equiv', desc: '逻辑等价' }
    ]
  },

  /**
   * 组件的生命周期
   */
  lifetimes: {
    attached() {
      this.setData({
        inputValue: this.properties.value
      });
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 显示编辑器
    _showEditor() {
      this.setData({
        formula: this.properties.initialFormula || '',
        show: true
      });
      
      // 延迟动画执行
      setTimeout(() => {
        this.setData({
          'show': true
        });
      }, 50);
    },
    
    // 隐藏编辑器
    _hideEditor() {
      this.setData({
        show: false
      });
      
      // 延迟关闭，等待动画完成
      setTimeout(() => {
        this.setData({
          visible: false
        });
      }, 300);
    },
    
    // 切换预览模式
    togglePreview() {
      this.setData({
        showPreview: !this.data.showPreview
      });
    },
    
    // 清空公式
    clearFormula() {
      this.setData({
        formula: '',
        hasError: false,
        errorMsg: ''
      });
    },
    
    // 切换符号标签
    switchTab(e) {
      const tabId = e.currentTarget.dataset.id;
      this.setData({
        currentTab: tabId
      });
    },
    
    // 插入符号
    insertSymbol(e) {
      const latex = e.currentTarget.dataset.latex;
      if (!latex) return;
      
      const current = this.data.inputValue || '';
      const newValue = current + latex;
      
      this.setData({
        inputValue: newValue
      });
      
      this._validateFormula(newValue);
    },
    
    // 处理输入变化
    handleInput(e) {
      const value = e.detail.value;
      this.setData({
        inputValue: value
      });
      
      this._validateFormula(value);
    },
    
    // 清空输入内容
    clearInput() {
      this.setData({
        inputValue: '',
        error: '',
        isValid: true
      });
    },
    
    // 取消操作
    handleCancel() {
      this.triggerEvent('cancel');
    },
    
    // 确认操作
    handleConfirm() {
      const { inputValue, isValid } = this.data;
      
      if (!isValid) {
        wx.showToast({
          title: '公式格式有误，请修正',
          icon: 'none'
        });
        return;
      }
      
      this.triggerEvent('confirm', { value: inputValue });
    },
    
    // 验证公式
    _validateFormula(formula) {
      try {
        // 简单的公式验证：括号是否匹配等
        const openBrackets = (formula.match(/\{/g) || []).length;
        const closeBrackets = (formula.match(/\}/g) || []).length;
        
        const openSquare = (formula.match(/\[/g) || []).length;
        const closeSquare = (formula.match(/\]/g) || []).length;
        
        const openParen = (formula.match(/\(/g) || []).length;
        const closeParen = (formula.match(/\)/g) || []).length;
        
        let isValid = true;
        let errorMsg = '';
        
        if (openBrackets !== closeBrackets) {
          isValid = false;
          errorMsg = '花括号 { } 不匹配';
        } else if (openSquare !== closeSquare) {
          isValid = false;
          errorMsg = '方括号 [ ] 不匹配';
        } else if (openParen !== closeParen) {
          isValid = false;
          errorMsg = '圆括号 ( ) 不匹配';
        }
        
        this.setData({
          isValid,
          error: errorMsg
        });
        
        return isValid;
      } catch (error) {
        console.error('Formula validation error:', error);
        this.setData({
          isValid: false,
          error: '公式格式错误'
        });
        return false;
      }
    },
    
    // 阻止遮罩层点击穿透
    preventTap() {
      return false;
    },

    _onShowChange(newVal) {
      if (newVal) {
        this.setData({
          inputValue: this.properties.value,
          error: '',
          isValid: true,
          currentTab: 'basic'
        });
      }
    },

    // 处理输入变化
    handleInput(e) {
      const value = e.detail.value;
      this.setData({
        inputValue: value,
        error: '',
        isValid: true
      });
      
      this._validateFormula(value);
    },

    // 验证公式
    _validateFormula(formula) {
      try {
        // 简单的公式验证：括号是否匹配等
        const openBrackets = (formula.match(/\{/g) || []).length;
        const closeBrackets = (formula.match(/\}/g) || []).length;
        
        const openSquare = (formula.match(/\[/g) || []).length;
        const closeSquare = (formula.match(/\]/g) || []).length;
        
        const openParen = (formula.match(/\(/g) || []).length;
        const closeParen = (formula.match(/\)/g) || []).length;
        
        let isValid = true;
        let errorMsg = '';
        
        if (openBrackets !== closeBrackets) {
          isValid = false;
          errorMsg = '花括号 { } 不匹配';
        } else if (openSquare !== closeSquare) {
          isValid = false;
          errorMsg = '方括号 [ ] 不匹配';
        } else if (openParen !== closeParen) {
          isValid = false;
          errorMsg = '圆括号 ( ) 不匹配';
        }
        
        // 检查基本的LaTeX命令语法
        const commandRegex = /\\[a-zA-Z]+(?:\{[^{}]*\})*|\\\[|\\\]/g;
        const matches = formula.match(commandRegex);
        
        // 更复杂的验证可以在这里添加
        
        this.setData({
          isValid,
          error: errorMsg
        });
        
        return isValid;
      } catch (error) {
        console.error('Formula validation error:', error);
        this.setData({
          isValid: false,
          error: '公式格式错误'
        });
        return false;
      }
    },

    // 取消操作
    handleCancel() {
      this.triggerEvent('cancel');
    },

    // 确认操作
    handleConfirm() {
      const { inputValue, isValid } = this.data;
      
      if (!isValid) {
        wx.showToast({
          title: '公式格式有误，请修正',
          icon: 'none'
        });
        return;
      }
      
      this.triggerEvent('confirm', { value: inputValue });
    },

    // 阻止遮罩层点击穿透
    preventTap() {
      return false;
    }
  }
}) 