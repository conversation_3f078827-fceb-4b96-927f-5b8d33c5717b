-- ============================================
-- 七年级上学期第二章知识点详情入库脚本（专家权威版V1.0）
-- 章节：第二章 有理数的运算
-- 知识点数量：16个（包含2个综合实践）
-- 专家编写：K12数学教育专家团队、初中数学特级教师
-- 参考教材：人民教育出版社数学七年级上册
-- 创建时间：2025-01-22
-- 质量等级：★★★★★ (专家权威版)
-- 适用对象：七年级学生（12-13岁，有理数运算基础建设阶段）
-- 质量保证：严格按照教材页码24-67内容结构创建
-- ============================================

-- 批量插入第2章知识点详情数据
INSERT INTO knowledge_content_details (
    node_id, description, detailed_explanation, key_points, formulas,
    examples, concepts_breakdown, common_mistakes, learning_tips,
    liberal_arts_content, science_content,
    content_version, is_current, language_code,
    content_quality_score, review_status
) VALUES

-- ============================================
-- 2.1 有理数的加法与减法部分
-- ============================================

-- MATH_G7S1_CH2_001: 有理数加法的法则
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_CH2_001'),
'有理数加法遵循同号相加绝对值相加、异号相加大减小、符号跟着绝对值大的法则，是有理数运算体系的核心基础',
'有理数加法法则是整个有理数运算体系的基石，它统一了正数、负数、零的加法运算规律。法则可以概括为：①同号两数相加，取相同符号，并把绝对值相加；②异号两数相加，绝对值相等时和为0，绝对值不等时取绝对值较大加数的符号，并用较大的绝对值减去较小的绝对值；③一个数与0相加，仍得这个数。这套法则不仅解决了负数引入后的加法运算问题，更体现了数学的统一性和完备性。从数轴角度理解，加法运算就是在数轴上的移动：加正数向右移，加负数向左移，运算结果就是最终位置。掌握有理数加法法则为后续学习减法转化、乘除运算、代数式运算等奠定坚实基础。',
'[
  "同号相加法则：同号两数相加，取相同符号，绝对值相加",
  "异号相加法则：异号两数相加，取绝对值大的符号，大减小",
  "零的性质：任何数与0相加等于原数",
  "数轴理解：加法是数轴上的向量移动",
  "绝对值作用：决定运算的具体数值大小",
  "符号确定：由绝对值大小关系决定结果符号",
  "运算顺序：先确定符号，再计算绝对值"
]',
'[
  {
    "name": "同号加法法则",
    "formula": "(+a) + (+b) = +(a+b); (-a) + (-b) = -(a+b)",
    "description": "同号两数相加，取相同符号，绝对值相加"
  },
  {
    "name": "异号加法法则",
    "formula": "(+a) + (-b) = +(a-b) (当a>b); (+a) + (-b) = -(b-a) (当a<b)",
    "description": "异号两数相加，取绝对值大的符号，用大减小"
  },
  {
    "name": "零的加法性质",
    "formula": "a + 0 = 0 + a = a",
    "description": "任何数与零相加等于原数"
  },
  {
    "name": "数轴加法解释",
    "formula": "加正数→向右移动；加负数→向左移动",
    "description": "数轴上的加法运算几何意义"
  }
]',
'[
  {
    "title": "同号加法运算",
    "problem": "计算：①(+7) + (+5)；②(-8) + (-3)；③(-1.5) + (-2.7)",
    "solution": "①(+7) + (+5) = +(7+5) = +12；②(-8) + (-3) = -(8+3) = -11；③(-1.5) + (-2.7) = -(1.5+2.7) = -4.2",
    "analysis": "同号相加取相同符号，绝对值直接相加，符号保持不变"
  },
  {
    "title": "异号加法运算",
    "problem": "计算：①(+9) + (-5)；②(-7) + (+3)；③(+2.6) + (-2.6)",
    "solution": "①(+9) + (-5) = +(9-5) = +4；②(-7) + (+3) = -(7-3) = -4；③(+2.6) + (-2.6) = 0",
    "analysis": "异号相加比较绝对值大小，取大的符号，用大减小，相等时结果为0"
  },
  {
    "title": "数轴上的加法理解",
    "problem": "在数轴上表示(-3) + (+5)的运算过程",
    "solution": "从-3的位置出发，向右移动5个单位，到达+2的位置，所以(-3) + (+5) = +2",
    "analysis": "数轴为加法运算提供直观的几何解释，加正数右移，加负数左移"
  },
  {
    "title": "复杂有理数加法",
    "problem": "计算：①(-3/4) + (+1/2)；②(-2.25) + (+3.75)；③0 + (-8)",
    "solution": "①(-3/4) + (+1/2) = (-3/4) + (+2/4) = -(3-2)/4 = -1/4；②(-2.25) + (+3.75) = +(3.75-2.25) = +1.5；③0 + (-8) = -8",
    "analysis": "分数和小数的加法同样遵循有理数加法法则，注意通分和小数对齐"
  }
]',
'[
  {
    "concept": "符号统一性",
    "explanation": "有理数加法将正负数运算统一在同一法则下",
    "example": "正数、负数、零都遵循相同的加法规律"
  },
  {
    "concept": "绝对值的作用",
    "explanation": "绝对值决定运算的数值大小和结果符号",
    "example": "比较|+5|和|-3|决定(+5)+(-3)的符号"
  },
  {
    "concept": "数轴几何意义",
    "explanation": "加法运算对应数轴上的向量移动",
    "example": "加法的几何直观帮助理解抽象运算"
  },
  {
    "concept": "运算的封闭性",
    "explanation": "有理数集合在加法运算下封闭",
    "example": "任意两个有理数相加结果仍是有理数"
  },
  {
    "concept": "法则的普遍性",
    "explanation": "加法法则适用于所有有理数形式",
    "example": "整数、分数、小数都遵循相同法则"
  }
]',
'[
  "同号相加时符号搞错（如正数相加得负数）",
  "异号相加时不比较绝对值大小",
  "异号相加时符号跟随错误（跟绝对值小的）",
  "忘记零的特殊性质",
  "混淆加法与减法的运算规则",
  "数轴移动方向理解错误"
]',
'[
  "口诀记忆：同号相加取同号，异号相加大减小",
  "符号先定：先确定结果符号，再计算绝对值",
  "数轴辅助：用数轴移动理解加法运算",
  "分类练习：分别练习同号、异号、含零的加法",
  "检验习惯：用数轴或实际意义检验结果合理性"
]',
'{
  "emphasis": ["法则理解", "符号判断", "数轴应用"],
  "application": ["温度变化", "盈亏计算", "位移合成", "账目统计"],
  "connection": ["为减法学习做准备", "与小学加法的联系和扩展"]
}',
'{
  "emphasis": ["运算法则", "数系完善", "抽象思维"],
  "application": ["代数运算", "数值计算", "算法设计"],
  "connection": ["群论基础", "向量运算", "复数运算"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'),

-- MATH_G7S1_CH2_002: 有理数加法的运算律
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_CH2_002'),
'有理数加法满足交换律和结合律，即a+b=b+a和(a+b)+c=a+(b+c)，这些运算律是简化计算和代数变形的重要工具',
'有理数加法运算律继承并扩展了小学数学中的加法运算律，在引入负数后仍然成立，体现了数学的一致性和美感。加法交换律a+b=b+a表明加数的顺序不影响和，这在实际计算中允许我们灵活调整运算顺序。加法结合律(a+b)+c=a+(b+c)表明多个数相加时，可以任意改变运算的组合方式。这两个运算律的应用使得多项有理数的加法运算变得灵活高效，特别是在处理正负数混合运算时，可以将同号数分组运算，大大简化计算过程。运算律的掌握不仅提高计算效率，更为后续学习代数式变形、方程求解、不等式处理等提供基础工具。',
'[
  "加法交换律：a + b = b + a（加数交换位置，和不变）",
  "加法结合律：(a + b) + c = a + (b + c)（加数重新组合，和不变）",
  "运算律的普遍性：对所有有理数都成立",
  "简化计算：通过运算律优化计算过程",
  "同号分组：利用运算律将同号数分组运算",
  "灵活变形：为代数变形提供理论基础",
  "计算策略：选择合适的运算顺序提高效率"
]',
'[
  {
    "name": "加法交换律",
    "formula": "a + b = b + a",
    "description": "两个数相加，交换加数的位置，和不变"
  },
  {
    "name": "加法结合律",
    "formula": "(a + b) + c = a + (b + c)",
    "description": "三个数相加，先把前两个数相加，或先把后两个数相加，和不变"
  },
  {
    "name": "运算律组合应用",
    "formula": "a + b + c = (a + c) + b = (b + c) + a",
    "description": "交换律和结合律的综合运用"
  },
  {
    "name": "同号数分组",
    "formula": "(+a) + (-b) + (+c) + (-d) = [(+a) + (+c)] + [(-b) + (-d)]",
    "description": "利用运算律将同号数分组简化运算"
  }
]',
'[
  {
    "title": "交换律的应用",
    "problem": "计算：(-25) + (+37) + (-15)，观察如何应用交换律简化计算",
    "solution": "原式 = (-25) + (-15) + (+37) = [(-25) + (-15)] + (+37) = (-40) + (+37) = -3",
    "analysis": "利用交换律将同号数(-25)和(-15)放在一起，先算同号加法，再算异号加法"
  },
  {
    "title": "结合律的应用",
    "problem": "计算：[(-18) + (+25)] + (-7)和(-18) + [(+25) + (-7)]",
    "solution": "[(-18) + (+25)] + (-7) = (+7) + (-7) = 0；(-18) + [(+25) + (-7)] = (-18) + (+18) = 0，两种方法结果相同",
    "analysis": "结合律允许改变运算的组合方式，选择合适的组合可以简化计算"
  },
  {
    "title": "运算律综合应用",
    "problem": "计算：(-3.5) + (+2.8) + (-6.5) + (+7.2) + (-1.8)",
    "solution": "分组：[(-3.5) + (-6.5) + (-1.8)] + [(+2.8) + (+7.2)] = (-11.8) + (+10) = -1.8",
    "analysis": "将负数分一组，正数分一组，分别计算后再相加，计算更简便"
  },
  {
    "title": "特殊数值的运算律应用",
    "problem": "计算：(-999) + (+1000) + (-1)，寻找最优计算路径",
    "solution": "方法一：(-999) + (-1) + (+1000) = (-1000) + (+1000) = 0；方法二：(-999) + (+1000) + (-1) = (+1) + (-1) = 0",
    "analysis": "运算律为我们提供了多种计算路径，选择能够产生整数或相消的组合"
  }
]',
'[
  {
    "concept": "运算的不变性",
    "explanation": "运算律保证不同计算路径得到相同结果",
    "example": "无论如何组合，多个数的和总是不变的"
  },
  {
    "concept": "计算策略优化",
    "explanation": "运算律为选择最优计算方法提供理论依据",
    "example": "寻找能够简化计算的数的组合"
  },
  {
    "concept": "代数思维培养",
    "explanation": "运算律是从算术向代数过渡的重要桥梁",
    "example": "为代数式变形提供基础工具"
  },
  {
    "concept": "数学的一致性",
    "explanation": "运算律在数系扩展中保持不变",
    "example": "从自然数到有理数，运算律始终成立"
  },
  {
    "concept": "灵活性原则",
    "explanation": "运算律提供计算的灵活性和多样性",
    "example": "同一个问题可以有多种等价的解法"
  }
]',
'[
  "认为运算律只适用于正数",
  "在应用运算律时改变了符号",
  "误认为运算律会改变运算结果",
  "不知道何时使用运算律简化计算",
  "混淆交换律和结合律的作用",
  "在复杂运算中忘记应用运算律"
]',
'[
  "理解本质：运算律是计算工具，不改变结果",
  "寻找规律：观察数的特点，选择合适的组合",
  "分组策略：同号数分组、互为相反数分组",
  "验证习惯：用不同方法验证计算结果",
  "灵活运用：根据题目特点选择最优算法"
]',
'{
  "emphasis": ["灵活计算", "策略选择", "效率提升"],
  "application": ["快速心算", "简化计算", "数据处理", "统计分析"],
  "connection": ["为代数变形做准备", "与算术运算律的联系"]
}',
'{
  "emphasis": ["代数思维", "运算优化", "算法设计"],
  "application": ["程序设计", "算法优化", "数值计算", "符号运算"],
  "connection": ["代数结构", "群论基础", "计算复杂度"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G7S1_CH2_003: 有理数减法的法则
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_CH2_003'),
'有理数减法法则将减法转化为加法：减去一个数等于加上这个数的相反数，即a-b=a+(-b)，统一了有理数的运算体系',
'有理数减法法则是数学史上的重要突破，它巧妙地将减法运算转化为加法运算，使得有理数运算体系更加统一和完善。法则的核心思想是：减去一个数等于加上这个数的相反数，即a-b=a+(-b)。这个转化不仅解决了负数相减的技术问题，更体现了数学的简洁性和统一性。通过这个法则，所有的减法运算都可以转化为已经掌握的加法运算，大大简化了运算规则的记忆和应用。从数轴角度理解，减法就是反向加法：减正数相当于向左移动，减负数相当于向右移动。这种转化思想在数学中具有重要地位，为后续学习代数式运算、方程求解等提供了基础方法。',
'[
  "减法转加法：a - b = a + (-b)",
  "相反数概念：-b是b的相反数",
  "符号变化：减号变加号，被减数变相反数",
  "运算统一：所有减法都转化为加法",
  "数轴理解：减正数左移，减负数右移",
  "双重变号：减负数等于加正数",
  "运算简化：减法变加法后应用加法法则"
]',
'[
  {
    "name": "减法基本法则",
    "formula": "a - b = a + (-b)",
    "description": "减去一个数等于加上这个数的相反数"
  },
  {
    "name": "减正数的运算",
    "formula": "a - (+b) = a + (-b)",
    "description": "减去正数等于加上负数"
  },
  {
    "name": "减负数的运算",
    "formula": "a - (-b) = a + (+b) = a + b",
    "description": "减去负数等于加上正数"
  },
  {
    "name": "数轴上的减法",
    "formula": "减正数→向左移；减负数→向右移",
    "description": "减法在数轴上的几何意义"
  }
]',
'[
  {
    "title": "减正数的运算",
    "problem": "计算：①(+5) - (+3)；②(-7) - (+2)；③0 - (+4)",
    "solution": "①(+5) - (+3) = (+5) + (-3) = +2；②(-7) - (+2) = (-7) + (-2) = -9；③0 - (+4) = 0 + (-4) = -4",
    "analysis": "减正数转化为加负数，然后按加法法则计算"
  },
  {
    "title": "减负数的运算",
    "problem": "计算：①(+3) - (-5)；②(-8) - (-3)；③(-1) - (-1)",
    "solution": "①(+3) - (-5) = (+3) + (+5) = +8；②(-8) - (-3) = (-8) + (+3) = -5；③(-1) - (-1) = (-1) + (+1) = 0",
    "analysis": "减负数转化为加正数，注意双重变号的处理"
  },
  {
    "title": "数轴上的减法理解",
    "problem": "在数轴上表示(-2) - (+3)和(-2) - (-3)的运算过程",
    "solution": "(-2) - (+3)：从-2出发向左移3个单位到-5；(-2) - (-3)：从-2出发向右移3个单位到+1",
    "analysis": "数轴为减法提供直观解释：减正数左移，减负数右移"
  },
  {
    "title": "复杂减法运算",
    "problem": "计算：①(-2.5) - (+1.5) - (-3.5)；②(-3/4) - (-1/2) - (+1/4)",
    "solution": "①(-2.5) - (+1.5) - (-3.5) = (-2.5) + (-1.5) + (+3.5) = -0.5；②(-3/4) - (-1/2) - (+1/4) = (-3/4) + (+2/4) + (-1/4) = -2/4 = -1/2",
    "analysis": "连续减法全部转化为加法，然后应用加法运算律简化计算"
  }
]',
'[
  {
    "concept": "运算转化思想",
    "explanation": "将新运算转化为已知运算是数学的重要方法",
    "example": "减法转化为加法体现了转化思想的威力"
  },
  {
    "concept": "相反数的作用",
    "explanation": "相反数概念是连接加法和减法的桥梁",
    "example": "通过相反数实现减法到加法的转化"
  },
  {
    "concept": "符号的双重性",
    "explanation": "减号既表示运算又表示数的性质",
    "example": "-(-3)中两个负号意义不同"
  },
  {
    "concept": "几何直观理解",
    "explanation": "数轴上的移动为减法提供几何解释",
    "example": "减法对应数轴上的反向移动"
  },
  {
    "concept": "运算的统一性",
    "explanation": "减法法则统一了有理数运算体系",
    "example": "所有运算最终都可以转化为加法"
  }
]',
'[
  "减法转化时符号处理错误",
  "双重变号时符号搞混（--变+）",
  "数轴移动方向判断错误",
  "减负数时忘记变号",
  "连续减法时转化不彻底",
  "混淆减号的不同含义"
]',
'[
  "转化原则：见减法，想加法",
  "符号关注：特别注意双重变号",
  "数轴辅助：用数轴理解减法的方向",
  "分步计算：复杂运算分步转化",
  "检验方法：用数轴或实际意义检验"
]',
'{
  "emphasis": ["转化思想", "符号处理", "数轴应用"],
  "application": ["温度差计算", "时间间隔", "位置变化", "盈亏分析"],
  "connection": ["与加法的联系", "为混合运算做准备"]
}',
'{
  "emphasis": ["运算转化", "抽象思维", "算法优化"],
  "application": ["算法设计", "符号运算", "数值计算"],
  "connection": ["代数变形", "方程求解", "不等式处理"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'),

-- MATH_G7S1_CH2_004: 有理数加减混合运算
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_CH2_004'),
'有理数加减混合运算通过统一转化为加法运算，结合运算律灵活计算，是有理数运算技能的综合体现',
'有理数加减混合运算是前面所学加法法则、减法法则和运算律的综合应用，代表着学生从单一运算向复合运算的重要过渡。其核心策略是：首先将所有减法转化为加法，得到若干个正数和负数的连加；然后灵活运用加法交换律和结合律，将同号数分组或寻找相互抵消的数对，实现计算的简化。这种运算不仅锻炼了计算技能，更培养了整体观察、分类处理、策略选择等数学思维能力。在实际应用中，加减混合运算广泛出现在温度变化、账目管理、位移计算等生活场景中。掌握这项技能为后续学习含有字母的代数式运算、一元一次方程求解等代数内容奠定坚实基础。',
'[
  "统一转化：所有减法转化为加法运算",
  "符号统一：省略正号，突出负号",
  "分组计算：将同号数分组运算",
  "寻找抵消：找出相互抵消的数对",
  "运算律应用：灵活运用交换律和结合律",
  "整体观察：从整体把握运算特点",
  "策略优化：选择最简便的计算路径"
]',
'[
  {
    "name": "减法转加法",
    "formula": "a - b + c - d = a + (-b) + c + (-d)",
    "description": "将所有减法转化为加法的统一形式"
  },
  {
    "name": "符号省略约定",
    "formula": "a + (-b) + c + (-d) = a - b + c - d",
    "description": "省略加号和正号的简化写法"
  },
  {
    "name": "同号分组",
    "formula": "(a + c) + (-b - d) = (a + c) - (b + d)",
    "description": "将同号数分组进行计算"
  },
  {
    "name": "相消配对",
    "formula": "a + (-a) + b = b",
    "description": "寻找相互抵消的数对简化运算"
  }
]',
'[
  {
    "title": "基础加减混合运算",
    "problem": "计算：8 - 15 + 6 - 9 + 13",
    "solution": "方法一：8 + (-15) + 6 + (-9) + 13 = (8 + 6 + 13) + (-15 - 9) = 27 + (-24) = 3",
    "analysis": "将正数和负数分别分组，先算同号数相加，再算异号数相加"
  },
  {
    "title": "含分数的混合运算",
    "problem": "计算：-3/4 + 1/2 - 1/3 + 2/3 - 3/4",
    "solution": "重新排列：(-3/4 - 3/4) + (1/2) + (-1/3 + 2/3) = (-6/4) + (1/2) + (1/3) = -3/2 + 1/2 + 1/3 = -1 + 1/3 = -2/3",
    "analysis": "寻找相同分母的数进行配对，或寻找相互抵消的数对"
  },
  {
    "title": "含小数的混合运算",
    "problem": "计算：-12.7 + 5.4 - 8.6 + 12.7 - 1.4",
    "solution": "观察发现-12.7和+12.7相消：(-12.7 + 12.7) + (5.4 - 8.6 - 1.4) = 0 + (-4.6) = -4.6",
    "analysis": "优先寻找相互抵消的数对，简化运算过程"
  },
  {
    "title": "复杂混合运算策略",
    "problem": "计算：-99 + 156 - 201 + 99 - 56 + 1",
    "solution": "配对：(-99 + 99) + (156 - 56) + (-201 + 1) = 0 + 100 + (-200) = -100",
    "analysis": "通过巧妙配对实现计算简化，体现数学运算的技巧性"
  }
]',
'[
  {
    "concept": "运算策略思维",
    "explanation": "根据题目特点选择最优的运算路径",
    "example": "分组、配对、抵消等不同策略的灵活运用"
  },
  {
    "concept": "整体观察能力",
    "explanation": "从整体把握算式特点，发现运算规律",
    "example": "一眼看出相互抵消的数对或便于计算的组合"
  },
  {
    "concept": "数感培养",
    "explanation": "通过大量练习培养对数的敏感性",
    "example": "快速识别运算中的特殊数值关系"
  },
  {
    "concept": "代数预备思维",
    "explanation": "为代数式运算奠定思维基础",
    "example": "同类项合并的思维雏形"
  },
  {
    "concept": "计算技能综合",
    "explanation": "加法、减法、运算律的综合运用",
    "example": "多种运算技能的有机结合"
  }
]',
'[
  "减法转化不彻底，部分未转化",
  "符号处理混乱，正负号搞错",
  "不会观察数的特点选择策略",
  "分组时忽略符号的处理",
  "计算过程中途改变策略导致错误",
  "不验证结果的合理性"
]',
'[
  "两步策略：先转化为加法，再选择计算方法",
  "符号重点：转化后重点关注负号",
  "观察优先：计算前先观察数的特点",
  "分组技巧：同号分组、配对抵消、凑整配对",
  "验证习惯：用估算或逆运算验证结果"
]',
'{
  "emphasis": ["策略选择", "技巧运用", "综合能力"],
  "application": ["财务统计", "成绩分析", "温度监测", "库存管理"],
  "connection": ["为代数式运算做准备", "提升计算技能"]
}',
'{
  "emphasis": ["算法优化", "策略思维", "效率提升"],
  "application": ["数据处理", "算法设计", "程序优化"],
  "connection": ["代数式化简", "方程求解技巧", "算法复杂度"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G7S1_CH2_005: 正负术（古代加减法则）
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_CH2_005'),
'中国古代数学经典《九章算术》中的"正负术"是世界上最早的完整有理数加减运算体系，体现了中华数学文明的卓越智慧',
'正负术是中国古代数学的辉煌成就，出现在东汉时期的《九章算术》第八章"方程"中，比西方掌握类似理论早了1500多年。正负术的核心内容是："同名相益，异名相除，正负相消"。其中"同名相益"指同号数相加取和，"异名相除"指异号数相加取差，"正负相消"指相反数相加得零。这套理论不仅解决了当时复杂的多元线性方程组求解问题，更建立了完整的有理数运算体系。古代数学家用红色算筹表示正数，黑色算筹表示负数，这种颜色对比的表示方法直观明了，便于操作。正负术的思想方法与现代有理数运算法则完全一致，展现了中国古代数学家的深刻洞察力和抽象思维能力，为世界数学发展做出了开创性贡献。',
'[
  "历史地位：世界最早的完整有理数运算体系",
  "核心法则：同名相益，异名相除，正负相消",
  "表示方法：红算筹表示正数，黑算筹表示负数",
  "理论完整：包含加法、减法的统一运算法则",
  "实际应用：解决复杂的多元线性方程组",
  "文化价值：体现中华数学文明的智慧",
  "现代意义：与现代有理数运算完全一致",
  "影响深远：为后世代数学发展奠定基础"
]',
'[
  {
    "name": "同名相益",
    "formula": "正正得正，负负得负",
    "description": "同号数相加，取相同符号，数值相加"
  },
  {
    "name": "异名相除",
    "formula": "正负相加取大减小",
    "description": "异号数相加，取绝对值大的符号，大减小"
  },
  {
    "name": "正负相消",
    "formula": "正负数值相等时和为零",
    "description": "相反数相加结果为零"
  },
  {
    "name": "算筹表示",
    "formula": "红筹为正，黑筹为负",
    "description": "用不同颜色算筹表示正负数"
  }
]',
'[
  {
    "title": "正负术的基本运算",
    "problem": "用正负术计算：正五与正三相加，负七与负二相加，正六与负四相加",
    "solution": "①正五+正三：同名相益，得正八；②负七+负二：同名相益，得负九；③正六+负四：异名相除，六大于四，得正二",
    "analysis": "正负术的运算法则与现代有理数加法法则完全一致"
  },
  {
    "title": "算筹表示法的应用",
    "problem": "如何用红黑算筹表示(-5) + (+3) + (-2)的运算过程？",
    "solution": "用5根黑筹表示-5，3根红筹表示+3，2根黑筹表示-2。先算黑筹：5+2=7根黑筹，再与3根红筹相除：7-3=4根黑筹，结果为-4",
    "analysis": "算筹的颜色对比使正负数运算直观化，便于理解和操作"
  },
  {
    "title": "正负术与现代运算的对比",
    "problem": "比较正负术法则与现代有理数加法法则的异同",
    "solution": "相同点：①运算结果完全一致；②都强调符号的重要性；③都有分类讨论思想。不同点：①表述方式古今有异；②现代更注重符号化表示；③古代更注重实际操作",
    "analysis": "正负术体现了数学真理的永恒性和中国古代数学的先进性"
  },
  {
    "title": "正负术的历史意义探究",
    "problem": "正负术的创立对中国古代数学和世界数学发展有何重要意义？",
    "solution": "①建立了世界最早的负数理论体系；②为求解复杂方程组提供了工具；③体现了中国数学注重实用的特点；④比西方早1500年掌握了完整的有理数运算；⑤为后世代数学发展奠定了基础",
    "analysis": "正负术是中华数学文明对世界的重要贡献，展现了古代数学家的卓越智慧"
  }
]',
'[
  {
    "concept": "数学文化传承",
    "explanation": "正负术体现了深厚的中华数学文化底蕴",
    "example": "从古代正负术到现代有理数运算的历史传承"
  },
  {
    "concept": "直观表示方法",
    "explanation": "红黑算筹提供了正负数的直观表示",
    "example": "颜色对比使抽象的正负概念具体化"
  },
  {
    "concept": "理论与实践结合",
    "explanation": "正负术既有理论高度又有实用价值",
    "example": "既解决理论问题又服务于实际计算"
  },
  {
    "concept": "数学的普遍性",
    "explanation": "数学真理超越时空，古今一致",
    "example": "古代正负术与现代运算法则的一致性"
  },
  {
    "concept": "文明比较价值",
    "explanation": "正负术体现了中华文明在数学领域的领先地位",
    "example": "比西方早1500年的理论创新"
  },
  {
    "concept": "教育启迪意义",
    "explanation": "学习正负术有助于增强文化自信",
    "example": "从历史中汲取数学智慧和民族自豪感"
  }
]',
'[
  "不了解正负术的历史价值和重要地位",
  "认为正负术与现代数学毫无关系",
  "忽视中国古代数学的世界贡献",
  "不理解算筹表示法的直观优势",
  "轻视数学史学习的文化意义",
  "缺乏对数学文化传承的认识"
]',
'[
  "历史认知：了解正负术的历史地位和价值",
  "文化自信：认识中华数学文明的伟大贡献",
  "对比学习：比较古今运算方法的异同",
  "直观理解：体验算筹表示法的直观性",
  "传承意识：理解数学知识的历史传承",
  "应用联系：将历史智慧与现代学习联系"
]',
'{
  "emphasis": ["文化传承", "历史价值", "民族自豪"],
  "application": ["数学史教育", "文化传播", "爱国教育"],
  "connection": ["中华优秀传统文化", "数学文化素养", "文化自信"]
}',
'{
  "emphasis": ["数学史学", "比较研究", "文明史观"],
  "application": ["学术研究", "教育理论", "文化研究"],
  "connection": ["数学哲学", "科学史", "比较文明学"]
}',
'1.0', true, 'zh-CN', 4.7, 'approved'),

-- ============================================
-- 2.2 有理数的乘法与除法部分
-- ============================================

-- MATH_G7S1_CH2_006: 有理数乘法的法则
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_CH2_006'),
'有理数乘法遵循符号法则：同号得正，异号得负，绝对值相乘，零乘任何数得零',
'有理数乘法法则将乘法运算扩展到负数领域，建立了完整的有理数乘法体系。法则核心为：①同号两数相乘，积为正数，绝对值相乘；②异号两数相乘，积为负数，绝对值相乘；③任何数与零相乘都得零。这个法则不仅保持了运算的一致性，还体现了数学的对称美。从数学本质看，负数乘法可以理解为"相反方向的重复"，如(-3)×4表示向负方向重复4次3个单位。乘法法则为除法、乘方等后续运算奠定基础，在解决实际问题中广泛应用。',
'[
  "同号相乘得正：(+a)×(+b)=+(ab), (-a)×(-b)=+(ab)",
  "异号相乘得负：(+a)×(-b)=-(ab), (-a)×(+b)=-(ab)",
  "零的乘法性质：任何数与0相乘都得0",
  "符号优先原则：先确定积的符号，再计算绝对值",
  "几何意义：负数乘法表示反向重复",
  "运算的封闭性：有理数乘法结果仍为有理数"
]',
'[
  {
    "name": "同号乘法法则",
    "formula": "(+a) × (+b) = +(a×b); (-a) × (-b) = +(a×b)",
    "description": "同号两数相乘得正数"
  },
  {
    "name": "异号乘法法则",
    "formula": "(+a) × (-b) = -(a×b); (-a) × (+b) = -(a×b)",
    "description": "异号两数相乘得负数"
  },
  {
    "name": "零的乘法性质",
    "formula": "a × 0 = 0 × a = 0",
    "description": "任何数与零相乘都等于零"
  }
]',
'[
  {
    "title": "同号乘法运算",
    "problem": "计算：①(+5) × (+3)；②(-4) × (-6)；③(-1.5) × (-2)",
    "solution": "①(+5) × (+3) = +(5×3) = +15；②(-4) × (-6) = +(4×6) = +24；③(-1.5) × (-2) = +(1.5×2) = +3",
    "analysis": "同号相乘积为正，先确定符号为正，再计算绝对值的乘积"
  },
  {
    "title": "异号乘法运算",
    "problem": "计算：①(+7) × (-2)；②(-5) × (+8)；③(+3/4) × (-2/3)",
    "solution": "①(+7) × (-2) = -(7×2) = -14；②(-5) × (+8) = -(5×8) = -40；③(+3/4) × (-2/3) = -(3×2)/(4×3) = -6/12 = -1/2",
    "analysis": "异号相乘积为负，先确定符号为负，再计算绝对值的乘积"
  }
]',
'[
  {
    "concept": "符号运算规律",
    "explanation": "乘法符号遵循固定规律：同号得正，异号得负",
    "example": "这个规律适用于所有有理数乘法"
  }
]',
'[
  "符号判断错误，特别是负负得正",
  "零的乘法性质运用错误"
]',
'[
  "符号口诀：同号得正，异号得负",
  "两步计算：先定符号，再算数值"
]',
'{
  "emphasis": ["符号规律", "法则应用"],
  "application": ["面积计算", "速度问题", "经济分析"],
  "connection": ["为除法做准备", "几何意义理解"]
}',
'{
  "emphasis": ["运算法则", "数系扩展"],
  "application": ["代数运算", "函数性质"],
  "connection": ["群论结构", "复数乘法"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'),

-- MATH_G7S1_CH2_007: 有理数乘法的运算律
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_CH2_007'),
'有理数乘法满足交换律、结合律和分配律，为简化计算和代数变形提供强大工具',
'有理数乘法运算律是数学运算的重要性质，包括：①交换律a×b=b×a；②结合律(a×b)×c=a×(b×c)；③分配律a×(b+c)=a×b+a×c。这些运算律在负数引入后依然成立，体现了数学的一致性。运算律的应用使复杂运算变得简单，特别是分配律，它连接了乘法与加法，为后续代数式展开、因式分解等奠定基础。掌握运算律不仅提高计算效率，更培养代数思维。',
'[
  "乘法交换律：a × b = b × a",
  "乘法结合律：(a × b) × c = a × (b × c)",
  "分配律：a × (b + c) = a × b + a × c",
  "运算律的普遍性：对所有有理数成立",
  "简化计算：合理运用律简化运算过程",
  "代数预备：为代数式运算奠定基础"
]',
'[
  {
    "name": "乘法交换律",
    "formula": "a × b = b × a",
    "description": "两数相乘，交换因数位置，积不变"
  },
  {
    "name": "乘法结合律",
    "formula": "(a × b) × c = a × (b × c)",
    "description": "三数相乘，改变运算顺序，积不变"
  },
  {
    "name": "乘法分配律",
    "formula": "a × (b + c) = a × b + a × c",
    "description": "一个数乘以两数之和，等于分别相乘再相加"
  }
]',
'[
  {
    "title": "分配律的应用",
    "problem": "计算：(-5) × (3 + 7)和(-5) × 3 + (-5) × 7",
    "solution": "方法一：(-5) × (3 + 7) = (-5) × 10 = -50；方法二：(-5) × 3 + (-5) × 7 = -15 + (-35) = -50",
    "analysis": "分配律将乘法运算转化为加法运算，提供不同的计算路径"
  }
]',
'[
  {
    "concept": "运算律的一致性",
    "explanation": "运算律在数系扩展中保持不变",
    "example": "从自然数到有理数，运算律始终成立"
  }
]',
'[
  "分配律使用时符号处理错误",
  "混淆不同运算律的适用条件"
]',
'[
  "理解本质：运算律反映运算的本质性质",
  "灵活应用：根据题目特点选择合适的运算律"
]',
'{
  "emphasis": ["运算律应用", "计算简化"],
  "application": ["快速计算", "代数预备"],
  "connection": ["代数式展开", "因式分解基础"]
}',
'{
  "emphasis": ["代数结构", "运算性质"],
  "application": ["抽象代数", "数学证明"],
  "connection": ["环论基础", "代数系统"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G7S1_CH2_008: 有理数除法的法则
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_CH2_008'),
'有理数除法转化为乘法：除以一个数等于乘以这个数的倒数，符号法则与乘法相同',
'有理数除法法则通过转化思想将除法归结为乘法，体现了数学的统一性。法则为：a÷b=a×(1/b)，其中b≠0。这种转化不仅简化了运算规则，还统一了符号处理：同号得正，异号得负。除法的几何意义是"等分"或"包含"，在有理数范围内得到完善体现。除法法则为后续学习分式运算、方程求解提供基础。',
'[
  "除法转乘法：a ÷ b = a × (1/b) (b≠0)",
  "倒数概念：1/b是b的倒数",
  "符号法则：同号得正，异号得负",
  "零的除法：0除以任何非零数得0，任何数不能除以0",
  "运算统一：除法通过转化纳入乘法体系"
]',
'[
  {
    "name": "除法基本法则",
    "formula": "a ÷ b = a × (1/b) (b≠0)",
    "description": "除以一个数等于乘以这个数的倒数"
  },
  {
    "name": "除法符号法则",
    "formula": "同号得正，异号得负",
    "description": "有理数除法的符号规律"
  }
]',
'[
  {
    "title": "有理数除法运算",
    "problem": "计算：①(+12) ÷ (+3)；②(-15) ÷ (-5)；③(+8) ÷ (-4)",
    "solution": "①(+12) ÷ (+3) = (+12) × (1/3) = +4；②(-15) ÷ (-5) = (-15) × (-1/5) = +3；③(+8) ÷ (-4) = (+8) × (-1/4) = -2",
    "analysis": "除法转化为乘法后，按乘法法则计算"
  }
]',
'[
  {
    "concept": "转化思想",
    "explanation": "将除法转化为乘法体现了数学的转化思想",
    "example": "通过转化将新运算归结为已知运算"
  }
]',
'[
  "除法转化时倒数求错",
  "符号处理与乘法不一致"
]',
'[
  "转化原则：见除法，想乘法",
  "倒数重点：准确求出倒数"
]',
'{
  "emphasis": ["转化思想", "倒数概念"],
  "application": ["比例问题", "速度计算"],
  "connection": ["分式运算", "方程求解"]
}',
'{
  "emphasis": ["运算转化", "数系完善"],
  "application": ["数值计算", "算法设计"],
  "connection": ["域论基础", "代数结构"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'),

-- MATH_G7S1_CH2_009: 有理数乘除混合运算
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_CH2_009'),
'有理数乘除混合运算统一转化为乘法运算，灵活运用运算律简化计算过程',
'有理数乘除混合运算是乘法、除法法则和运算律的综合应用。核心策略是将所有除法转化为乘法，得到连乘形式，然后运用乘法运算律简化计算。计算步骤：①统一转化为乘法；②确定积的符号；③计算绝对值的积。这种运算培养学生的整体思维和转化能力，为代数式运算奠定基础。',
'[
  "统一转化：所有除法转化为乘法",
  "符号确定：奇数个负因数得负，偶数个得正",
  "运算律应用：灵活运用交换律、结合律",
  "计算简化：寻找互为倒数的数对",
  "整体观察：从整体把握运算特点"
]',
'[
  {
    "name": "乘除混合转化",
    "formula": "a × b ÷ c × d = a × b × (1/c) × d",
    "description": "将除法统一转化为乘法"
  },
  {
    "name": "符号确定法则",
    "formula": "负因数个数为奇数得负，偶数得正",
    "description": "多个数相乘时符号的确定方法"
  }
]',
'[
  {
    "title": "乘除混合运算",
    "problem": "计算：(-2) × 3 ÷ (-4) × (-5)",
    "solution": "转化：(-2) × 3 × (-1/4) × (-5) = (-2) × 3 × (-1/4) × (-5)。符号：3个负数，积为负。数值：2×3×(1/4)×5 = 30/4 = 7.5。结果：-7.5",
    "analysis": "先转化为乘法，确定符号，再计算绝对值"
  }
]',
'[
  {
    "concept": "符号统计方法",
    "explanation": "通过统计负因数个数确定积的符号",
    "example": "奇数个负数得负，偶数个负数得正"
  }
]',
'[
  "除法转化不彻底",
  "符号统计错误"
]',
'[
  "两步策略：先转化，再计算",
  "符号统计：数清负因数的个数"
]',
'{
  "emphasis": ["转化统一", "符号判断"],
  "application": ["复合计算", "实际问题"],
  "connection": ["代数式运算", "分式计算"]
}',
'{
  "emphasis": ["算法思维", "运算优化"],
  "application": ["程序设计", "数值分析"],
  "connection": ["算法复杂度", "计算效率"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G7S1_CH2_010: 数系扩充看乘法法则
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_CH2_010'),
'从数系扩充角度理解有理数乘法法则的必然性，体现数学的逻辑严密性和内在统一性',
'数系扩充的视角揭示了有理数乘法法则的深层逻辑。从自然数到整数再到有理数，每次扩充都要保持原有运算律不变。负数乘法法则特别是(-1)×(-1)=+1，不是人为规定，而是为保持分配律等运算律成立的必然结果。这种必然性体现了数学的内在逻辑和美感，展现了数学概念发展的严密性。理解这一点有助于培养学生的数学思维和对数学本质的认识。',
'[
  "数系发展：自然数→整数→有理数的扩充过程",
  "运算律保持：扩充过程中运算律必须保持不变",
  "逻辑必然：负负得正是逻辑推导的必然结果",
  "分配律推导：通过0×a=0推导(-1)×(-1)=1",
  "数学美感：法则体现了数学的内在和谐",
  "思维培养：理解数学概念的发展逻辑"
]',
'[
  {
    "name": "数系扩充原则",
    "formula": "扩充后保持原有运算律",
    "description": "数系扩充的基本要求"
  },
  {
    "name": "分配律推导",
    "formula": "0 = 0×1 = 0×[1+(-1)] = 0×1 + 0×(-1) = 0 + 0×(-1)",
    "description": "利用分配律推导负数乘法"
  }
]',
'[
  {
    "title": "负负得正的推导",
    "problem": "用分配律证明(-1)×(-1)=1",
    "solution": "设(-1)×(-1)=x，由分配律：(-1)×[1+(-1)] = (-1)×1 + (-1)×(-1) = -1 + x。而(-1)×[1+(-1)] = (-1)×0 = 0，所以-1+x=0，因此x=1",
    "analysis": "通过严格的逻辑推导证明负负得正的必然性"
  }
]',
'[
  {
    "concept": "数学的逻辑性",
    "explanation": "数学概念和法则都有严密的逻辑基础",
    "example": "负数乘法法则是逻辑推导的结果，不是任意规定"
  }
]',
'[
  "认为数学法则是任意规定的",
  "不理解数系扩充的逻辑性"
]',
'[
  "逻辑思维：理解法则背后的逻辑",
  "推导练习：尝试自己推导法则"
]',
'{
  "emphasis": ["逻辑推理", "数学本质"],
  "application": ["数学证明", "逻辑思维"],
  "connection": ["数学哲学", "抽象代数"]
}',
'{
  "emphasis": ["数学基础", "逻辑体系"],
  "application": ["公理化方法", "数学基础"],
  "connection": ["集合论", "数学逻辑"]
}',
'1.0', true, 'zh-CN', 4.7, 'approved'),

-- ============================================
-- 2.3 有理数的乘方部分
-- ============================================

-- MATH_G7S1_CH2_011: 有理数乘方的概念
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_CH2_011'),
'有理数乘方是相同因数连乘的简便表示，记作aⁿ，其中a是底数，n是指数，体现了数学的简洁性',
'有理数乘方是乘法运算的特殊形式，表示n个相同的有理数a相乘，记作aⁿ，读作"a的n次方"或"a的n次幂"。其中a叫做底数，n叫做指数，aⁿ叫做幂。乘方运算将重复的乘法用简洁的形式表示，体现了数学的简化美。特别地，任何非零数的0次方等于1，这是为保持指数运算律而规定的。乘方概念的引入为后续学习科学记数法、指数函数等奠定基础，在实际生活中广泛应用于面积、体积、增长率等计算。',
'[
  "乘方定义：aⁿ = a × a × ... × a (n个a相乘)",
  "基本要素：底数a、指数n、幂aⁿ",
  "读法规范：aⁿ读作a的n次方或a的n次幂",
  "特殊情况：a¹ = a，a⁰ = 1 (a≠0)",
  "符号处理：底数为负数时注意括号的使用",
  "简化表示：用乘方简化重复乘法的书写"
]',
'[
  {
    "name": "乘方的定义",
    "formula": "aⁿ = a × a × ... × a (n个a)",
    "description": "n个相同因数a的乘积"
  },
  {
    "name": "指数为1的情况",
    "formula": "a¹ = a",
    "description": "任何数的1次方等于它本身"
  },
  {
    "name": "指数为0的情况",
    "formula": "a⁰ = 1 (a≠0)",
    "description": "任何非零数的0次方等于1"
  },
  {
    "name": "负数乘方的符号",
    "formula": "(-a)ⁿ: n为偶数时结果为正，n为奇数时结果为负",
    "description": "负数乘方的符号规律"
  }
]',
'[
  {
    "title": "基本乘方运算",
    "problem": "计算：①2³；②(-3)²；③(-2)⁴；④5¹；⑤3⁰",
    "solution": "①2³ = 2×2×2 = 8；②(-3)² = (-3)×(-3) = 9；③(-2)⁴ = (-2)×(-2)×(-2)×(-2) = 16；④5¹ = 5；⑤3⁰ = 1",
    "analysis": "按照乘方定义展开计算，注意负数乘方的符号变化"
  },
  {
    "title": "负数乘方的符号规律",
    "problem": "不计算，判断下列各式的符号：①(-2)¹⁰；②(-3)⁷；③(-1)²⁰⁰⁸；④(-5)⁹⁹",
    "solution": "①(-2)¹⁰：指数10为偶数，结果为正；②(-3)⁷：指数7为奇数，结果为负；③(-1)²⁰⁰⁸：指数2008为偶数，结果为正；④(-5)⁹⁹：指数99为奇数，结果为负",
    "analysis": "负数的偶次方为正，奇次方为负，这是判断符号的快速方法"
  },
  {
    "title": "乘方在实际问题中的应用",
    "problem": "一张纸的厚度是0.1mm，对折1次后厚度是多少？对折n次后厚度是多少？",
    "solution": "对折1次：0.1×2¹ = 0.2mm；对折2次：0.1×2² = 0.4mm；对折n次：0.1×2ⁿ mm",
    "analysis": "乘方在表示重复倍增过程中具有简洁明了的优势"
  }
]',
'[
  {
    "concept": "简化表示思想",
    "explanation": "乘方用简洁形式表示复杂的重复乘法",
    "example": "2¹⁰比2×2×2×2×2×2×2×2×2×2简洁得多"
  },
  {
    "concept": "指数的意义",
    "explanation": "指数表示底数作为因数的个数",
    "example": "a⁵表示5个a相乘"
  },
  {
    "concept": "符号变化规律",
    "explanation": "负数乘方的符号遵循偶正奇负的规律",
    "example": "(-1)²ⁿ = 1, (-1)²ⁿ⁺¹ = -1"
  },
  {
    "concept": "数学的统一性",
    "explanation": "a⁰ = 1的规定保持了指数运算的统一性",
    "example": "aᵐ⁺⁰ = aᵐ × a⁰ = aᵐ × 1 = aᵐ"
  }
]',
'[
  "混淆底数和指数的作用",
  "负数乘方时括号使用错误",
  "不理解a⁰ = 1的规定",
  "符号判断时奇偶性搞错"
]',
'[
  "概念清晰：明确底数、指数、幂的含义",
  "符号口诀：负数偶次方为正，奇次方为负",
  "括号重要：负数做底数时必须加括号",
  "特殊记忆：任何非零数的0次方等于1"
]',
'{
  "emphasis": ["概念理解", "符号规律", "实际应用"],
  "application": ["面积体积计算", "增长问题", "科学计数"],
  "connection": ["为科学记数法做准备", "指数函数预备"]
}',
'{
  "emphasis": ["指数运算", "数学归纳", "函数思想"],
  "application": ["指数函数", "数列问题", "算法复杂度"],
  "connection": ["对数概念", "幂函数", "数学建模"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'),

-- MATH_G7S1_CH2_012: 有理数乘方的运算
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_CH2_012'),
'有理数乘方运算掌握底数符号处理、分数乘方计算、混合运算顺序等关键技能',
'有理数乘方运算是乘方概念的具体应用，需要熟练掌握各种情况的计算方法。关键技能包括：①正负数乘方的符号确定；②分数乘方的计算方法；③小数乘方的处理技巧；④带括号和不带括号的区别。特别要注意-aⁿ与(-a)ⁿ的区别：前者是a的n次方的相反数，后者是-a的n次方。乘方运算为后续的混合运算、科学记数法等提供计算基础。',
'[
  "正数乘方：结果恒为正数",
  "负数乘方：偶次方为正，奇次方为负",
  "分数乘方：分子分母分别乘方",
  "小数乘方：按整数乘方规律计算",
  "符号区别：-aⁿ ≠ (-a)ⁿ",
  "0的乘方：0ⁿ = 0 (n>0)",
  "1的乘方：1ⁿ = 1"
]',
'[
  {
    "name": "分数的乘方",
    "formula": "(a/b)ⁿ = aⁿ/bⁿ (b≠0)",
    "description": "分数的乘方等于分子分母分别乘方"
  },
  {
    "name": "负号位置的影响",
    "formula": "-aⁿ = -(aⁿ); (-a)ⁿ取决于n的奇偶性",
    "description": "负号在括号内外的不同效果"
  },
  {
    "name": "特殊数的乘方",
    "formula": "0ⁿ = 0 (n>0); 1ⁿ = 1; (-1)ⁿ = ±1",
    "description": "特殊数字的乘方规律"
  }
]',
'[
  {
    "title": "分数乘方计算",
    "problem": "计算：①(2/3)³；②(-1/2)⁴；③(1.5)²",
    "solution": "①(2/3)³ = 2³/3³ = 8/27；②(-1/2)⁴ = (-1)⁴/2⁴ = 1/16；③(1.5)² = (3/2)² = 9/4 = 2.25",
    "analysis": "分数乘方时分子分母分别乘方，注意符号处理"
  },
  {
    "title": "符号差异辨析",
    "problem": "比较-3²和(-3)²的值",
    "solution": "-3² = -(3²) = -9；(-3)² = (-3)×(-3) = 9",
    "analysis": "负号的位置决定了运算结果，括号的作用至关重要"
  },
  {
    "title": "特殊数乘方",
    "problem": "计算：①0⁵；②1²⁰；③(-1)⁶；④(-1)⁷",
    "solution": "①0⁵ = 0；②1²⁰ = 1；③(-1)⁶ = 1；④(-1)⁷ = -1",
    "analysis": "掌握特殊数的乘方规律可以快速得出结果"
  }
]',
'[
  {
    "concept": "括号的重要性",
    "explanation": "括号决定了负号的作用范围",
    "example": "-2³ = -8, 而(-2)³ = -8但原因不同"
  },
  {
    "concept": "运算的一致性",
    "explanation": "乘方运算遵循统一的计算规则",
    "example": "无论底数是什么形式，乘方法则都适用"
  }
]',
'[
  "混淆-aⁿ与(-a)ⁿ",
  "分数乘方时只对分子乘方",
  "0的乘方与0⁰混淆"
]',
'[
  "括号敏感：注意负号是否在括号内",
  "分别乘方：分数乘方分子分母都要乘方",
  "特殊记忆：掌握0、1、-1的乘方规律"
]',
'{
  "emphasis": ["计算技能", "符号处理", "分数运算"],
  "application": ["几何计算", "物理公式", "实际问题"],
  "connection": ["混合运算", "代数式计算"]
}',
'{
  "emphasis": ["精确计算", "算法思维"],
  "application": ["数值计算", "程序设计"],
  "connection": ["计算机算法", "数值分析"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G7S1_CH2_013: 有理数混合运算的顺序
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_CH2_013'),
'有理数混合运算遵循先乘方，再乘除，后加减，有括号先算括号内的运算顺序',
'有理数混合运算是各种运算的综合应用，必须严格按照运算顺序进行：①有括号的先算括号内；②乘方运算优先级最高；③乘法和除法同级，从左到右；④加法和减法同级，从左到右；⑤同级运算按从左到右的顺序。这个顺序保证了运算结果的唯一性和正确性。混合运算不仅检验基本运算技能，更培养逻辑思维和程序化思维，为代数式运算和方程求解奠定基础。',
'[
  "运算顺序：括号→乘方→乘除→加减",
  "同级运算：从左到右依次计算",
  "括号优先：各种括号都要优先计算",
  "符号统一：先将减法转化为加法",
  "策略优化：观察特点选择简便算法",
  "逐步计算：复杂运算分步骤进行"
]',
'[
  {
    "name": "运算优先级",
    "formula": "括号 > 乘方 > 乘除 > 加减",
    "description": "有理数混合运算的优先级顺序"
  },
  {
    "name": "同级运算规则",
    "formula": "同级运算从左到右依次进行",
    "description": "相同优先级运算的处理顺序"
  },
  {
    "name": "括号类型",
    "formula": "() > [] > {}",
    "description": "不同括号的优先级顺序"
  }
]',
'[
  {
    "title": "基本混合运算",
    "problem": "计算：-2³ + 3 × (-4) - (-5)",
    "solution": "按顺序：-2³ = -8；3 × (-4) = -12；-(-5) = 5。所以原式 = -8 + (-12) + 5 = -15",
    "analysis": "先算乘方，再算乘法，最后统一为加法运算"
  },
  {
    "title": "含括号的混合运算",
    "problem": "计算：(-2)² × [3 - (-1)²]",
    "solution": "先算括号内：(-1)² = 1，3 - 1 = 2；再算乘方：(-2)² = 4；最后：4 × 2 = 8",
    "analysis": "有括号先算括号，注意括号内也要按运算顺序"
  },
  {
    "title": "复杂混合运算",
    "problem": "计算：-1⁴ - (1 - 0.5) × (1/3)² + (-2)³",
    "solution": "分步：-1⁴ = -1；(1 - 0.5) = 0.5；(1/3)² = 1/9；(-2)³ = -8。所以原式 = -1 - 0.5 × (1/9) + (-8) = -1 - 1/18 - 8 = -9 - 1/18 = -163/18",
    "analysis": "复杂运算要仔细分步，每步都要检查符号和运算顺序"
  }
]',
'[
  {
    "concept": "程序化思维",
    "explanation": "按固定顺序执行运算体现程序化思维",
    "example": "计算机执行运算也遵循相同的优先级规则"
  },
  {
    "concept": "逻辑严密性",
    "explanation": "运算顺序保证结果的唯一性",
    "example": "没有运算顺序，同一式子可能有不同结果"
  }
]',
'[
  "运算顺序搞错",
  "同级运算顺序错误",
  "括号内运算顺序混乱"
]',
'[
  "顺序牢记：熟记运算优先级",
  "分步计算：复杂运算分步骤",
  "检查习惯：每步都要检查符号"
]',
'{
  "emphasis": ["运算顺序", "逻辑思维", "计算技能"],
  "application": ["数学公式计算", "程序设计", "逻辑推理"],
  "connection": ["代数式运算", "方程求解", "函数计算"]
}',
'{
  "emphasis": ["算法思维", "程序逻辑"],
  "application": ["计算机编程", "算法设计", "自动化计算"],
  "connection": ["编译原理", "表达式求值", "递归算法"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'),

-- MATH_G7S1_CH2_014: 科学记数法
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_CH2_014'),
'科学记数法用a×10ⁿ的形式表示数，其中1≤|a|<10，n为整数，简化了极大极小数的表示和运算',
'科学记数法是表示很大或很小数的简便方法，形式为a×10ⁿ，其中a称为尾数(1≤|a|<10)，n为指数(整数)。当原数≥10时，n为正数；当0<原数<1时，n为负数。科学记数法不仅简化了数的书写，更便于比较大小和进行运算。在科学技术中广泛应用，如天体距离、原子尺度、科学计算等。掌握科学记数法培养数感，提高数学素养。',
'[
  "标准形式：a × 10ⁿ (1≤|a|<10，n为整数)",
  "尾数要求：绝对值在1到10之间",
  "指数含义：n表示小数点移动的位数",
  "正数表示：大数(≥10)指数为正",
  "小数表示：小数(0<原数<1)指数为负",
  "应用广泛：科学技术中的大小数表示"
]',
'[
  {
    "name": "科学记数法标准形式",
    "formula": "a × 10ⁿ (1≤|a|<10，n∈Z)",
    "description": "科学记数法的标准表示形式"
  },
  {
    "name": "指数确定方法",
    "formula": "n = 原数化为标准形式时小数点移动的位数",
    "description": "指数n的确定方法"
  },
  {
    "name": "指数正负规律",
    "formula": "原数≥10时n>0；0<原数<1时n<0",
    "description": "指数正负的判断规律"
  }
]',
'[
  {
    "title": "大数的科学记数法",
    "problem": "用科学记数法表示：①3000000；②186000；③5.02×10⁷",
    "solution": "①3000000 = 3×10⁶；②186000 = 1.86×10⁵；③5.02×10⁷ = 50200000",
    "analysis": "大数的科学记数法指数为正，等于小数点左移的位数"
  },
  {
    "title": "小数的科学记数法",
    "problem": "用科学记数法表示：①0.000052；②0.00309；③2.7×10⁻⁴",
    "solution": "①0.000052 = 5.2×10⁻⁵；②0.00309 = 3.09×10⁻³；③2.7×10⁻⁴ = 0.00027",
    "analysis": "小数的科学记数法指数为负，等于小数点右移的位数"
  },
  {
    "title": "科学记数法的应用",
    "problem": "地球到太阳的距离约为1.5×10⁸千米，光的速度约为3×10⁵千米/秒，计算阳光到达地球需要多长时间？",
    "solution": "时间 = 距离÷速度 = (1.5×10⁸)÷(3×10⁵) = (1.5÷3)×(10⁸÷10⁵) = 0.5×10³ = 500秒",
    "analysis": "科学记数法便于进行大数的运算，分别计算系数和指数部分"
  }
]',
'[
  {
    "concept": "数的简化表示",
    "explanation": "科学记数法将复杂的大小数用简洁形式表示",
    "example": "602200000000000000000000用科学记数法为6.022×10²³"
  },
  {
    "concept": "数量级概念",
    "explanation": "指数反映了数的数量级大小",
    "example": "10⁶是百万级，10⁹是十亿级"
  }
]',
'[
  "尾数不在1到10之间",
  "指数正负判断错误",
  "小数点移动位数计算错误"
]',
'[
  "尾数标准：保证1≤|a|<10",
  "移动计数：仔细计算小数点移动位数",
  "正负规律：大数正指数，小数负指数"
]',
'{
  "emphasis": ["数的表示", "实际应用", "数量级"],
  "application": ["天文数据", "微观世界", "科学计算"],
  "connection": ["指数概念应用", "实际问题解决"]
}',
'{
  "emphasis": ["数值表示", "计算效率"],
  "application": ["科学计算", "工程技术", "数据分析"],
  "connection": ["浮点数表示", "数值计算", "计算机科学"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- ============================================
-- 综合与实践1：进位制的认识与探究部分
-- ============================================

-- MATH_G7S1_PRACTICE_001: 进位制的概念
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_PRACTICE_001'),
'进位制是表示数的方法，不同进位制使用不同的基数，其中十进制、二进制在数学和计算机科学中具有重要地位',
'进位制是人类表示和处理数的重要方法，体现了数学的抽象性和实用性。进位制的本质是按位计数，每一位都有固定的权值。十进制是日常最常用的进位制，以10为基数；二进制以2为基数，是计算机的基础；八进制、十六进制在计算机科学中也有应用。不同进位制本质上表示相同的数量，只是表示形式不同。学习进位制有助于理解数的本质，培养抽象思维，为计算机科学学习奠定基础，同时体现数学与现代科技的紧密联系。',
'[
  "进位制定义：按某个固定基数进行计数的方法",
  "基数概念：进位制的基础数字个数",
  "位权思想：每一位都有对应的权值",
  "常见进位制：二进制、八进制、十进制、十六进制",
  "表示方法：用下标表示进位制类型",
  "数值相等：不同进位制可表示相同数量",
  "现代应用：计算机科学的基础"
]',
'[
  {
    "name": "十进制表示",
    "formula": "abcd₁₀ = a×10³ + b×10² + c×10¹ + d×10⁰",
    "description": "十进制数的位权展开"
  },
  {
    "name": "二进制表示",
    "formula": "abcd₂ = a×2³ + b×2² + c×2¹ + d×2⁰",
    "description": "二进制数的位权展开"
  },
  {
    "name": "一般进位制",
    "formula": "aₙ...a₂a₁a₀ᵣ = aₙ×rⁿ + ... + a₂×r² + a₁×r¹ + a₀×r⁰",
    "description": "r进制数的位权展开公式"
  },
  {
    "name": "进位规律",
    "formula": "满r进1，不足r时在本位表示",
    "description": "进位制的基本计数规律"
  }
]',
'[
  {
    "title": "十进制的位权理解",
    "problem": "分析1357₁₀各位数字的含义",
    "solution": "1357₁₀ = 1×10³ + 3×10² + 5×10¹ + 7×10⁰ = 1000 + 300 + 50 + 7 = 1357",
    "analysis": "十进制中每一位的权值是10的幂，从右到左依次增大"
  },
  {
    "title": "二进制的位权理解",
    "problem": "分析1101₂各位数字的含义",
    "solution": "1101₂ = 1×2³ + 1×2² + 0×2¹ + 1×2⁰ = 8 + 4 + 0 + 1 = 13₁₀",
    "analysis": "二进制中每一位的权值是2的幂，只能用0和1表示"
  },
  {
    "title": "不同进位制比较",
    "problem": "比较13₁₀、1101₂、15₈表示的数量",
    "solution": "13₁₀ = 13；1101₂ = 8+4+0+1 = 13；15₈ = 1×8¹ + 5×8⁰ = 8+5 = 13。三者表示相同数量",
    "analysis": "不同进位制可以表示相同的数量，体现数学的统一性"
  },
  {
    "title": "进位制的实际应用",
    "problem": "为什么计算机使用二进制而不是十进制？",
    "solution": "①二进制只有0和1，对应电路的断开和闭合；②逻辑简单，便于实现；③运算规则简单；④抗干扰能力强；⑤符合布尔代数原理",
    "analysis": "进位制的选择与实际应用场景密切相关，体现数学的实用价值"
  }
]',
'[
  {
    "concept": "位权思想",
    "explanation": "每一位都有固定的权值，体现了数的结构性",
    "example": "无论哪种进位制，都遵循位权原理"
  },
  {
    "concept": "抽象与具体",
    "explanation": "进位制是抽象的数学概念在具体情境中的应用",
    "example": "二进制抽象概念在计算机中的具体实现"
  },
  {
    "concept": "数学的统一性",
    "explanation": "不同进位制表示相同数量，体现数学本质的统一",
    "example": "数的本质不因表示方法而改变"
  },
  {
    "concept": "科技联系",
    "explanation": "进位制连接了数学理论与现代科技",
    "example": "二进制是计算机科学的基础"
  }
]',
'[
  "混淆不同进位制的计数规律",
  "不理解位权的作用",
  "认为不同进位制表示不同数量",
  "忽视进位制与实际应用的联系"
]',
'[
  "位权理解：重点理解每一位的权值作用",
  "规律掌握：熟记各进位制的计数规律",
  "联系实际：了解不同进位制的应用场景",
  "对比学习：通过对比加深理解"
]',
'{
  "emphasis": ["概念理解", "实际应用", "科技联系"],
  "application": ["计算机原理", "数字电路", "信息技术"],
  "connection": ["为计算机学习做准备", "现代科技基础"]
}',
'{
  "emphasis": ["抽象思维", "逻辑推理", "系统思维"],
  "application": ["计算机科学", "信息论", "数字信号处理"],
  "connection": ["编程基础", "算法设计", "数据结构"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G7S1_PRACTICE_002: 二进制与十进制的转换
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_PRACTICE_002'),
'二进制与十进制转换是不同进位制间的数值转换，掌握转换方法对理解计算机原理和数字信息处理具有重要意义',
'二进制与十进制的转换是进位制学习的核心技能，体现了数学转化思想的应用。从二进制转十进制使用位权展开法：将二进制数按位权展开后相加。从十进制转二进制使用除2取余法：反复除以2，将余数倒序排列。这种转换不仅是数学技能，更是理解计算机工作原理的基础。在信息时代，数据都以二进制形式存储和处理，掌握进制转换有助于理解数字化原理。转换过程培养逻辑思维、算法思维和系统性思维，为程序设计和计算机科学学习奠定基础。',
'[
  "转换方向：二进制⇌十进制双向转换",
  "二转十方法：位权展开法（按权相加）",
  "十转二方法：除2取余法（余数倒排）",
  "验证重要：转换后要验证结果正确性",
  "实际意义：理解计算机数据处理原理",
  "算法思维：体现系统性的转换步骤"
]',
'[
  {
    "name": "二进制转十进制",
    "formula": "aₙ...a₂a₁a₀₂ = aₙ×2ⁿ + ... + a₂×2² + a₁×2¹ + a₀×2⁰",
    "description": "按位权展开后相加"
  },
  {
    "name": "十进制转二进制",
    "formula": "反复除以2，余数倒序排列",
    "description": "除2取余法的操作步骤"
  },
  {
    "name": "验证公式",
    "formula": "转换结果再反向转换应得原数",
    "description": "检验转换正确性的方法"
  }
]',
'[
  {
    "title": "二进制转十进制",
    "problem": "将1011₂转换为十进制",
    "solution": "1011₂ = 1×2³ + 0×2² + 1×2¹ + 1×2⁰ = 8 + 0 + 2 + 1 = 11₁₀",
    "analysis": "从右到左按位权展开，2的幂次从0开始递增"
  },
  {
    "title": "十进制转二进制",
    "problem": "将19₁₀转换为二进制",
    "solution": "19÷2=9...1；9÷2=4...1；4÷2=2...0；2÷2=1...0；1÷2=0...1。余数倒排：10011₂",
    "analysis": "连续除以2，记录余数，最后将余数倒序排列得到结果"
  },
  {
    "title": "转换验证",
    "problem": "验证10011₂ = 19₁₀",
    "solution": "10011₂ = 1×2⁴ + 0×2³ + 0×2² + 1×2¹ + 1×2⁰ = 16 + 0 + 0 + 2 + 1 = 19₁₀ ✓",
    "analysis": "通过反向转换验证结果的正确性，培养检验习惯"
  },
  {
    "title": "实际应用场景",
    "problem": "计算机中用8位二进制表示数字，求能表示的最大十进制数",
    "solution": "8位二进制最大值为11111111₂ = 1×2⁷ + 1×2⁶ + ... + 1×2⁰ = 2⁸ - 1 = 255₁₀",
    "analysis": "理解二进制在计算机中的实际应用，认识位数的限制"
  }
]',
'[
  {
    "concept": "转化思想",
    "explanation": "进制转换体现了数学中重要的转化思想",
    "example": "将未知问题转化为已知问题求解"
  },
  {
    "concept": "算法思维",
    "explanation": "转换过程体现了系统性的算法步骤",
    "example": "除2取余法是一个标准的算法流程"
  },
  {
    "concept": "逆向验证",
    "explanation": "通过反向操作验证结果的正确性",
    "example": "转换后再反向转换检验答案"
  },
  {
    "concept": "计算机原理",
    "explanation": "理解计算机内部数据表示和处理方式",
    "example": "所有数据在计算机中都以二进制形式存储"
  }
]',
'[
  "位权计算时幂次搞错",
  "除2取余时余数顺序颠倒",
  "忘记验证转换结果",
  "不理解转换的实际意义"
]',
'[
  "步骤规范：严格按照转换步骤操作",
  "幂次注意：位权计算时仔细确定幂次",
  "顺序重要：除2取余法中余数要倒排",
  "验证习惯：转换后一定要验证结果",
  "联系实际：理解转换在信息技术中的应用"
]',
'{
  "emphasis": ["转换技能", "计算机原理", "实际应用"],
  "application": ["程序设计", "数字电路", "信息技术", "计算机操作"],
  "connection": ["为编程学习做准备", "理解数字化世界"]
}',
'{
  "emphasis": ["算法思维", "逻辑推理", "计算机科学"],
  "application": ["编程语言", "数据结构", "算法设计", "系统原理"],
  "connection": ["计算机体系结构", "程序设计基础", "数字逻辑"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'); 