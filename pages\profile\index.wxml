<view class="container device-container {{isIPad ? 'ipad-mode ' + ipadLayoutMode : ''}} {{isIPhoneX ? 'iphonex-mode' : ''}} {{isAndroid ? 'android-mode' : ''}} {{isEyeCareModeEnabled ? 'eye-care-mode' : ''}}">
  <!-- 顶部导航栏组件 -->
  <nav-bar 
    title="个人中心" 
    fixed="{{true}}" 
    showBack="{{false}}" 
    bgColor="#ffffff"
    textColor="#333333"> 
  </nav-bar>
  
  <!-- 使用用户资料卡组件 -->
  <user-profile-card 
    isLoggedIn="{{isLoggedIn}}"
    nickname="{{nickname}}"
    schoolName="{{schoolName}}"
    className="{{className}}"
    solvedCount="{{solvedCount}}"
    wrongCount="{{wrongCount}}"
    studyHours="{{studyHours}}"
    medalCount="{{userMedals.length}}"
    userLevel="{{userLevel}}"
    levelProgress="{{levelProgress}}"
    medals="{{userMedals}}"
    isIPad="{{isIPad}}"
    bind:login="editUserInfo"
    bind:edit="editUserInfo"
    bind:medalTap="onMedalTap">
  </user-profile-card>

  <!-- 常用功能 -->
  <view class="section card-base section--common">
    <view class="section-header section__header">
      <view class="section-indicator section__indicator"></view>
      <view class="section-title section__title">常用功能</view>
    </view>
    <view class="function-grid section__grid">
      <view class="function-item section__item" bindtap="onModuleItemTap" data-id="knowledge-graph">
        <view class="function-icon function-icon-purple section__icon">
          <view class="icon icon-knowledge-white icon-md"></view>
        </view>
        <view class="function-name section__name">知识图谱</view>
      </view>
      <view class="function-item section__item" bindtap="onModuleItemTap" data-id="performance">
        <view class="function-icon function-icon-purple section__icon">
          <view class="icon icon-chart-white icon-md"></view>
        </view>
        <view class="function-name section__name">能力分析</view>
      </view>
      <view class="function-item section__item" bindtap="onModuleItemTap" data-id="exam-bank">
        <view class="function-icon function-icon-purple section__icon">
          <view class="icon icon-book-white icon-md"></view>
        </view>
        <view class="function-name section__name">试卷库</view>
      </view>
      <view class="function-item section__item" bindtap="onModuleItemTap" data-id="learning-path">
        <view class="function-icon function-icon-purple section__icon">
          <view class="icon icon-calendar-white icon-md"></view>
        </view>
        <view class="function-name section__name">学习计划</view>
      </view>
    </view>
  </view>

  <!-- 学习数据 -->
  <view class="section card-base section--data">
    <view class="section-header section__header">
      <view class="section-indicator section__indicator"></view>
      <view class="section-title section__title">学习数据</view>
    </view>
    <view class="function-grid section__grid">
      <view class="function-item section__item" bindtap="onModuleItemTap" data-id="study-report">
        <view class="function-icon function-icon-blue section__icon">
          <view class="icon icon-document-white icon-md"></view>
        </view>
        <view class="function-name section__name">学习报告</view>
      </view>
      <view class="function-item section__item" bindtap="onModuleItemTap" data-id="weak-points">
        <view class="function-icon function-icon-blue section__icon">
          <view class="icon icon-warning-white icon-md"></view>
        </view>
        <view class="function-name section__name">薄弱知识点</view>
      </view>
      <view class="function-item section__item" bindtap="onModuleItemTap" data-id="study-record">
        <view class="function-icon function-icon-blue section__icon">
          <view class="icon icon-history-white icon-md"></view>
        </view>
        <view class="function-name section__name">学习记录</view>
      </view>
      <view class="function-item section__item" bindtap="onModuleItemTap" data-id="collection-hub">
        <view class="function-icon function-icon-blue section__icon">
          <view class="icon icon-star-white icon-md"></view>
        </view>
        <view class="function-name section__name">我的收藏</view>
      </view>
    </view>
  </view> 
  
  <!-- 底部安全区域占位 -->
  <view class="safe-area-inset-bottom"></view>

  <!-- 底部导航栏 -->
  <tab-bar activeTab="profile" isIPhoneX="{{isIPhoneX}}" isIPad="{{isIPad}}" safeAreaBottom="{{safeAreaBottom}}" bind:tabchange="onTabChange"></tab-bar>
</view>
