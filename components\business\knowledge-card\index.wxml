<view class="knowledge-card {{isExpanded ? 'expanded' : ''}}">
  <!-- 知识点卡片头部 -->
  <view class="knowledge-header" bindtap="toggleExpand">
    <view class="header-left">
      <text class="knowledge-title">{{knowledgeData.title}}</text>
      <text class="knowledge-grade" wx:if="{{knowledgeData.grade}}">{{knowledgeData.grade}}</text>
    </view>
    <view class="header-right">
      <view class="collect-icon {{isCollected ? 'collected' : ''}}" catchtap="toggleCollect" wx:if="{{expandable}}">
        <text class="icon icon-star"></text>
      </view>
      <view class="expand-icon" wx:if="{{expandable}}">
        <text class="icon {{isExpanded ? 'icon-arrow-up' : 'icon-arrow-down'}}"></text>
      </view>
    </view>
  </view>

  <!-- 知识点卡片内容 -->
  <view class="knowledge-content" wx:if="{{isExpanded || !expandable}}">
    <!-- 知识点描述 -->
    <view class="description" wx:if="{{knowledgeData.description}}">
      <text>{{knowledgeData.description}}</text>
    </view>

    <!-- 知识点标签 -->
    <view class="labels" wx:if="{{knowledgeData.labels && knowledgeData.labels.length > 0}}">
      <view class="label-item" wx:for="{{knowledgeData.labels}}" wx:key="index" catchtap="onLabelTap" data-label="{{item}}">
        <text>{{item}}</text>
      </view>
    </view>

    <!-- 知识点例题 -->
    <view class="examples" wx:if="{{knowledgeData.examples && knowledgeData.examples.length > 0}}">
      <view class="section-title">例题</view>
      <view class="example-item" wx:for="{{knowledgeData.examples}}" wx:key="index">
        <text class="example-title">例{{index + 1}}：</text>
        <text class="example-content">{{item.content}}</text>
      </view>
    </view>

    <!-- 相关知识点 -->
    <view class="related" wx:if="{{knowledgeData.related && knowledgeData.related.length > 0}}">
      <view class="section-title">相关知识点</view>
      <view class="related-list">
        <view class="related-item" 
              wx:for="{{knowledgeData.related}}" 
              wx:key="id" 
              data-id="{{item.id}}" 
              catchtap="onRelatedKnowledge">
          <text>{{item.title}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 知识点卡片底部 -->
  <view class="knowledge-footer" wx:if="{{isExpanded || !expandable}}" catchtap="preventBubble">
    <view class="action-btn detail-btn" catchtap="onDetail">
      <view class="icon icon-detail"></view>
      <text>详情</text>
    </view>
    <view class="action-btn practice-btn" catchtap="onPractice">
      <view class="icon icon-practice"></view>
      <text>练习</text>
    </view>
    <view class="action-btn share-btn" catchtap="onShare">
      <view class="icon icon-share"></view>
      <text>分享</text>
    </view>
  </view>
</view> 