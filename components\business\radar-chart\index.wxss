.radar-chart-wrapper {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.radar-chart-title {
  font-size: 16px;
  font-weight: 500;
  color: #333333;
  margin-bottom: 12px;
  text-align: center;
}

.radar-chart-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.radar-canvas {
  width: 100%;
  height: 100%;
}

.svg-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: visible;
  display: flex;
  justify-content: center;
  align-items: center;
}

.svg-wrapper {
  width: 100%;
  height: 100%;
}

.svg-image {
  width: 100%;
  height: 100%;
  display: block;
}

/* SVG样式 */
.radar-grid {
  stroke: #EEEEEE;
  stroke-width: 1;
  fill: none;
}

.radar-axis {
  stroke: #DDDDDD;
  stroke-width: 1;
}

.radar-shape-primary {
  fill-opacity: 0.4;
  stroke-width: 2;
}

.radar-shape-secondary {
  fill-opacity: 0.2;
  stroke-width: 2;
}

.radar-point {
  fill: white;
  stroke-width: 2;
}

.radar-label {
  font-size: 12px;
  fill: #333333;
  pointer-events: none;
}

.no-data-tip {
  font-size: 14px;
  fill: #999;
  text-anchor: middle;
}

/* 图例样式 */
.chart-legend {
  display: flex;
  justify-content: center;
  padding: 16rpx 0;
  flex-wrap: wrap;
  gap: 32rpx;
}

.legend-item {
  display: flex;
  align-items: center;
}

.legend-color {
  width: 24rpx;
  height: 24rpx;
  border-radius: 50%;
  margin-right: 8rpx;
}

.legend-text {
  font-size: 24rpx;
  color: #666666;
}

/* 兼容不同设备 */
@media (min-width: 768px) {
  .radar-chart-container {
    max-width: 600rpx;
    margin: 0 auto;
  }
}

/* 加载动画 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  min-height: 150rpx;
}

.loading-spinner {
  width: 48rpx;
  height: 48rpx;
  border: 4rpx solid rgba(62, 123, 250, 0.1);
  border-left-color: #3E7BFA;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
} 