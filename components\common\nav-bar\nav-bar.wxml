<!-- 导航栏组件 -->
<view class="nav-bar {{fixed ? 'nav-bar-fixed' : ''}} {{shadow ? 'nav-bar-shadow' : ''}}" style="background-color: {{transparent ? 'transparent' : bgColor}}; color: {{textColor}};">
  <!-- 状态栏 -->
  <view class="nav-bar-status" style="height: {{statusBarHeight}}px;"></view>
  
  <!-- 导航栏内容区 -->
  <view class="nav-bar-content" style="height: {{navBarHeight}}px;">
    <!-- 左侧区域 -->
    <view class="nav-bar-left">
      <block wx:if="{{showBack}}">
        <view class="nav-bar-button" bindtap="handleBack">
          <view class="nav-bar-icon nav-bar-back-icon"></view>
          <text wx:if="{{backText}}" class="nav-bar-button-text">{{backText}}</text>
        </view>
      </block>
      <block wx:if="{{showHome}}">
        <view class="nav-bar-button" bindtap="handleHome">
          <view class="nav-bar-icon nav-bar-home-icon"></view>
          <text wx:if="{{homeText}}" class="nav-bar-button-text">{{homeText}}</text>
        </view>
      </block>
      <!-- 自定义左侧内容插槽 -->
      <slot name="left"></slot>
    </view>
    
    <!-- 中间标题区域 -->
    <view class="nav-bar-center">
      <block wx:if="{{title}}">
        <text class="nav-bar-title">{{title}}</text>
      </block>
      <!-- 自定义中间内容插槽 -->
      <slot name="center"></slot>
    </view>
    
    <!-- 右侧区域 -->
    <view class="nav-bar-right">
      <!-- 自定义右侧内容插槽 -->
      <slot name="right"></slot>
    </view>
  </view>
</view>

<!-- 占位元素，用于固定定位时占位 -->
<view wx:if="{{fixed}}" style="height: {{statusBarHeight + navBarHeight}}px;"></view>

<!-- 占位区域，fixed模式下保持内容不被导航栏遮挡 -->
<view wx:if="{{fixed && needCapsule}}" style="height: {{capsuleHeight}}px;"></view> 