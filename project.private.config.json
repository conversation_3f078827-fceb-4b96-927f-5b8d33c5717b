{"description": "项目私有配置文件。此文件中的内容将覆盖 project.config.json 中的相同字段。项目的改动优先同步到此文件中。详见文档：https://developers.weixin.qq.com/miniprogram/dev/devtools/projectconfig.html", "projectname": "K12%E6%95%B0%E5%AD%A6%E5%AD%A6%E4%B9%A0%E5%8A%A9%E6%89%8B", "setting": {"compileHotReLoad": true, "ignoreDevUnusedFiles": false, "urlCheck": true, "useStaticServer": true, "coverView": true, "showShadowRootInWxmlPanel": true, "lazyloadPlaceholderEnable": false, "skylineRenderEnable": false, "preloadBackgroundData": false, "autoAudits": false, "useApiHook": true, "useApiHostProcess": true, "useLanDebug": false, "showES6CompileOption": false, "checkInvalidKey": true, "bigPackageSizeSupport": false}, "libVersion": "3.8.9", "condition": {}}