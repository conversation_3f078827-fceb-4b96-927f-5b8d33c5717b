/* 知识点详情页 - 极简重新设计 */
@import "../../../styles/common.wxss";
@import "../../../styles/icons.wxss";

/* 页面容器 */
.page-container {
  background: #fafbfc;
  min-height: 100vh;
}

/* 简洁导航栏 */
.simple-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background-color: #fff;
  border-bottom: 1rpx solid #eee;
}

.navbar-content {
  display: flex;
  align-items: center;
  width: 100%;
  min-height: 44px;
  position: relative;
  box-sizing: border-box;
}

.back-btn {
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding-left: 8px;
}

.nav-actions {
  position: absolute;
  right: 8px;
  display: flex;
  gap: 8rpx;
}

.nav-btn {
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-title {
  flex: 1;
  font-size: 16px;
  font-weight: 500;
  text-align: center;
  color: #333;
  margin-right: 44px; /* 为了标题居中，右侧留出与左侧按钮相同宽度 */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 主要内容区域 */
.main-content {
  padding-top: 140rpx;
  padding-bottom: 32rpx;
}

/* 知识点标题卡片 */
.title-card {
  background: #ffffff;
  margin: 0 24rpx 24rpx;
  border-radius: 24rpx;
  padding: 40rpx;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.03);
}

.knowledge-title {
  font-size: 48rpx;
  font-weight: 700;
  color: #1f2937;
  line-height: 1.3;
  margin-top: 8rpx;
  margin-bottom: 16rpx;
  display: flex;
  align-items: baseline;
  flex-wrap: wrap;
  gap: 16rpx;
}

.grade-badge {
  display: inline-block;
  background: #f0f9ff;
  color: #0284c7;
  font-size: 24rpx;
  font-weight: 600;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  margin-left: 16rpx;
}

.knowledge-desc {
  font-size: 28rpx;
  color: #6b7280;
  line-height: 1.5;
  margin-bottom: 32rpx;
}

/* 知识掌握进度 */
.mastery-progress {
  background: #f8fafc;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 32rpx;
  border: 1rpx solid #e2e8f0;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.progress-label {
  font-size: 26rpx;
  font-weight: 600;
  color: #374151;
}

.progress-percentage {
  font-size: 32rpx;
  font-weight: 700;
  color: #3b82f6;
}

.progress-bar {
  height: 12rpx;
  background: #e5e7eb;
  border-radius: 6rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3b82f6 0%, #1d4ed8 100%);
  border-radius: 6rpx;
  transition: width 0.3s ease;
}

.progress-details {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.progress-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.progress-icon {
  font-size: 28rpx;
  margin-bottom: 8rpx;
}

.progress-item.mastered .progress-icon {
  color: #10b981;
}

.progress-item.learning .progress-icon {
  color: #f59e0b;
}

.progress-item.not-mastered .progress-icon {
  color: #ef4444;
}

.progress-text {
  font-size: 22rpx;
  color: #6b7280;
  text-align: center;
}

.progress-prediction {
  font-size: 30rpx;
  color: #6366f1;
  text-align: center;
  font-weight: 600;
}

/* 登录提示样式 */
.login-prompt {
  background: #f8fafc;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 32rpx;
  border: 1rpx solid #e2e8f0;
  text-align: center;
}

.login-prompt-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
}

.prompt-icon {
  font-size: 48rpx;
  color: #6b7280;
}

.prompt-text {
  font-size: 26rpx;
  color: #6b7280;
  margin-bottom: 8rpx;
}

.prompt-button {
  background: #3b82f6;
  color: white;
  padding: 12rpx 32rpx;
  border-radius: 24rpx;
  font-size: 26rpx;
  font-weight: 600;
  transition: all 0.2s ease;
}

.prompt-button:active {
  transform: scale(0.95);
  background: #2563eb;
}

/* 重要程度和考试频次样式 */
.importance-stars {
  color: #f59e0b;
  font-size: 28rpx;
}

.exam-frequency {
  color: #dc2626;
  font-weight: 600;
}

/* 快速信息 */
.quick-info {
  display: flex;
  gap: 24rpx;
}

.info-item {
  flex: 1;
  text-align: center;
}

.info-label {
  font-size: 24rpx;
  color: #9ca3af;
  margin-bottom: 8rpx;
}

.info-value {
  font-size: 28rpx;
  font-weight: 600;
  color: #1f2937;
}

.difficulty-easy {
  color: #059669;
}

.difficulty-medium {
  color: #d97706;
}

.difficulty-hard {
  color: #dc2626;
}

/* AI助手 */
.ai-assistant {
  background: #ffffff;
  margin: 0 24rpx 24rpx;
  border-radius: 24rpx;
  padding: 32rpx;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.03);
}

.assistant-title {
  display: flex;
  align-items: center;
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 24rpx;
}

.assistant-title .icon {
  margin-right: 12rpx;
  color: #6366f1;
}

.assistant-actions {
  display: flex;
  gap: 12rpx;
}

.action-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 8rpx;
  border-radius: 16rpx;
  background: #f8fafc;
  transition: all 0.2s ease;
}

.action-item:active {
  transform: scale(0.98);
  background: #e2e8f0;
}

.action-icon {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  background: #6366f1;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8rpx;
}

.action-icon .icon {
  color: #ffffff;
  filter: brightness(0) invert(1);
}

.action-icon.playing {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.action-text {
  font-size: 22rpx;
  color: #374151;
  font-weight: 500;
  text-align: center;
  line-height: 1.2;
}

/* 学习进度提示 */
.learning-status {
  margin-top: 20rpx;
  padding: 20rpx 24rpx;
  background: linear-gradient(135deg, #f0f9ff 0%, #dbeafe 100%);
  border-radius: 16rpx;
  border: 2rpx solid #3b82f6;
  text-align: center;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.15);
}

.status-text {
  font-size: 28rpx;
  color: #1e40af;
  font-weight: 600;
  letter-spacing: 0.5rpx;
}

/* 内容标签页 */
.content-tabs {
  background: #ffffff;
  margin: 0 24rpx 24rpx;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.03);
}

.tab-header {
  display: flex;
  background: #f8fafc;
  padding: 8rpx;
  gap: 8rpx;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 20rpx 16rpx;
  font-size: 28rpx;
  font-weight: 500;
  color: #6b7280;
  border-radius: 12rpx;
  transition: all 0.2s ease;
}

.tab-item.active {
  background: #ffffff;
  color: #6366f1;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.tab-content {
  padding: 32rpx;
}

/* 内容区域 */
.content-section {
  min-height: 400rpx;
}

/* 5分钟搞定样式 */
.quick-master {
  space-y: 32rpx;
}

.section-title {
  display: flex;
  align-items: center;
  font-size: 30rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 20rpx;
}

.title-icon {
  margin-right: 12rpx;
  font-size: 32rpx;
}

.core-points {
  margin-bottom: 32rpx;
}

.point-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 24rpx;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f3f4f6;
}

.point-item:last-child {
  border-bottom: none;
}

.point-marker {
  width: 40rpx;
  height: 40rpx;
  background: #3b82f6;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: 600;
  margin-right: 16rpx;
  flex-shrink: 0;
}

.point-content {
  flex: 1;
}

.point-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 8rpx;
}

.point-text {
  font-size: 26rpx;
  color: #6b7280;
  line-height: 1.6;
}

.memory-tips {
  margin-top: 32rpx;
}

.tip-card {
  background: linear-gradient(135deg, #fef3c7 0%, #fed7aa 100%);
  border: 1rpx solid #f59e0b;
  border-radius: 12rpx;
  padding: 20rpx;
  text-align: center;
}

.tip-text {
  font-size: 28rpx;
  color: #92400e;
  font-weight: 600;
  font-style: italic;
}

/* 深度理解样式 */
.deep-understanding {
  space-y: 32rpx;
}

.principle-content {
  background: #f8fafc;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 32rpx;
}

.principle-text {
  font-size: 26rpx;
  color: #374151;
  line-height: 1.6;
}

/* 常见误区样式 */
.common-misconceptions {
  margin-top: 32rpx;
  margin-bottom: 32rpx;
}

.misconception-item {
  background: #fef2f2;
  border: 1rpx solid #fecaca;
  border-radius: 8rpx;
  padding: 12rpx 16rpx;
  margin-bottom: 8rpx;
}

.misconception-text {
  font-size: 24rpx;
  color: #dc2626;
  line-height: 1.5;
}

.derivation-process {
  margin-top: 32rpx;
}

.step-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20rpx;
  padding: 16rpx;
  background: #ffffff;
  border-radius: 8rpx;
  border: 1rpx solid #e5e7eb;
}

.step-number {
  background: #6366f1;
  color: white;
  padding: 6rpx 12rpx;
  border-radius: 16rpx;
  font-size: 22rpx;
  font-weight: 600;
  margin-right: 16rpx;
  white-space: nowrap;
}

.step-content {
  flex: 1;
  font-size: 26rpx;
  color: #374151;
  line-height: 1.5;
}

/* 推导步骤标题和描述 */
.step-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 8rpx;
}

.step-desc {
  font-size: 24rpx;
  color: #6b7280;
  line-height: 1.6;
  margin-bottom: 6rpx;
  font-family: 'Monaco', 'Menlo', 'Courier New', monospace;
}

/* 实战练习样式 */
.practice-levels {
  space-y: 24rpx;
}

.level-item {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
  border: 1rpx solid #e5e7eb;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.level-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.level-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #1f2937;
}

.level-count {
  font-size: 24rpx;
  color: #6366f1;
  font-weight: 600;
  background: #f0f9ff;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

.level-description {
  font-size: 24rpx;
  color: #6b7280;
  margin-bottom: 20rpx;
  line-height: 1.5;
}

.level-button {
  background: #6366f1;
  color: white;
  text-align: center;
  padding: 16rpx 32rpx;
  border-radius: 12rpx;
  font-size: 26rpx;
  font-weight: 600;
  transition: all 0.2s ease;
}

.level-button:active {
  transform: scale(0.98);
  background: #4f46e5;
}

/* 拓展延伸样式 */
.extension-section {
  space-y: 32rpx;
}

.application-scenarios {
  margin-bottom: 32rpx;
}

.scenario-item {
  background: #f8fafc;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 16rpx;
  border-left: 4rpx solid #3b82f6;
}

.scenario-title {
  font-size: 26rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 8rpx;
}

.scenario-desc {
  font-size: 24rpx;
  color: #6b7280;
  line-height: 1.5;
}

.thinking-expansion {
  margin-top: 32rpx;
}

.thinking-question {
  background: linear-gradient(135deg, #fef7ff 0%, #f3e8ff 100%);
  border: 1rpx solid #c084fc;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
}

.question-text {
  font-size: 26rpx;
  color: #7c2d12;
  line-height: 1.6;
  font-weight: 500;
  display: block;
  margin-bottom: 12rpx;
}

.thinking-hint {
  margin-top: 16rpx;
  padding: 12rpx 16rpx;
  background: rgba(139, 92, 246, 0.1);
  border-radius: 8rpx;
  border-left: 3rpx solid #8b5cf6;
}

.thinking-hint text {
  font-size: 22rpx;
  color: #6b46c1;
  line-height: 1.5;
}

/* 例题样式 */
.example-item {
  background: #ffffff;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-top: 16rpx;
  border: 1rpx solid #e5e7eb;
}

.example-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #374151;
  margin-bottom: 16rpx;
}

.example-solution {
  background: #f9fafb;
  border-radius: 8rpx;
  padding: 16rpx;
}

.solution-step {
  font-size: 26rpx;
  color: #6b7280;
  line-height: 1.5;
  margin-bottom: 8rpx;
  font-family: 'Courier New', monospace;
}

.solution-step:last-child {
  margin-bottom: 0;
  font-weight: 600;
  color: #6366f1;
}

/* 练习区域 */
.practice-section {
  space-y: 24rpx;
}

.practice-header {
  text-align: center;
  margin-bottom: 32rpx;
}

.practice-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 8rpx;
}

.practice-subtitle {
  font-size: 24rpx;
  color: #6b7280;
}

.practice-preview {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  margin-bottom: 32rpx;
}

.preview-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 24rpx;
  background: #f8fafc;
  border-radius: 12rpx;
}

.preview-difficulty {
  font-size: 22rpx;
  font-weight: 600;
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
  background: #e0e7ff;
  color: #6366f1;
}

.preview-title {
  flex: 1;
  font-size: 26rpx;
  color: #374151;
  margin: 0 16rpx;
}

.preview-count {
  font-size: 24rpx;
  color: #9ca3af;
}

.start-practice-btn {
  background: #6366f1;
  color: #ffffff;
  text-align: center;
  padding: 24rpx;
  border-radius: 16rpx;
  font-size: 28rpx;
  font-weight: 600;
  transition: all 0.2s ease;
}

.start-practice-btn:active {
  transform: scale(0.98);
  background: #4f46e5;
}

/* 知识关联 */
.knowledge-relation {
  background: #ffffff;
  margin: 0 16rpx 32rpx;
  border-radius: 24rpx;
  padding: 32rpx 24rpx;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.03);
}

/* 关系标题区域 - 简洁设计，无图标 */
.relation-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #e5e7eb;
}

.relation-title {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.relation-title .icon {
  color: #3b82f6;
}

.relation-title-text {
  font-size: 32rpx;
  font-weight: 700;
  color: #1f2937;
}

.overall-progress {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8rpx;
}

.progress-text {
  font-size: 24rpx;
  color: #6b7280;
  font-weight: 500;
}

.mini-progress-bar {
  width: 120rpx;
  height: 8rpx;
  background: #e5e7eb;
  border-radius: 4rpx;
  overflow: hidden;
}

.mini-progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3b82f6 0%, #1d4ed8 100%);
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

/* AI智能推荐区域 - 与上半部分AI助手风格一致 */
.smart-recommendations {
  background: #f8fafc;
  border-radius: 16rpx;
  padding: 24rpx 20rpx;
  margin-bottom: 40rpx;
  border: 1rpx solid #e2e8f0;
}

.recommendation-header {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}

.rec-icon-wrapper {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: #6366f1;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
}

.rec-icon-wrapper .icon {
  color: #ffffff;
  filter: brightness(0) invert(1);
}

.recommendation-title-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
}

.recommendation-cards {
  display: flex;
  gap: 16rpx;
}

.rec-card {
  flex: 1;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16rpx;
  padding: 24rpx 16rpx;
  color: white;
  min-height: 120rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
}

.rec-header {
  margin-bottom: 16rpx;
  text-align: center;
  width: 100%;
}

.rec-title {
  font-size: 28rpx;
  font-weight: 700;
  margin-bottom: 6rpx;
  text-align: center;
}

.rec-subtitle {
  font-size: 24rpx;
  font-weight: 500;
  margin-bottom: 10rpx;
  opacity: 0.9;
  text-align: center;
}

.rec-desc {
  font-size: 22rpx;
  opacity: 0.8;
  line-height: 1.4;
  text-align: center;
}

/* 知识关联Tab - 与上半部分标签页风格一致 */
.knowledge-tabs {
  margin-top: 32rpx;
}

.knowledge-tab-header {
  display: flex;
  background: #f8fafc;
  padding: 6rpx;
  border-radius: 16rpx;
  margin-bottom: 32rpx;
  gap: 6rpx;
}

.knowledge-tab-item {
  flex: 1;
  text-align: center;
  padding: 16rpx 8rpx;
  border-radius: 12rpx;
  transition: all 0.2s ease;
  position: relative;
}

.knowledge-tab-item.active {
  background: #ffffff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.knowledge-tab-title {
  font-size: 24rpx;
  font-weight: 600;
  color: #6b7280;
  margin-bottom: 6rpx;
}

.knowledge-tab-item.active .knowledge-tab-title {
  color: #3b82f6;
}

.knowledge-tab-count {
  font-size: 20rpx;
  color: #9ca3af;
  background: #f3f4f6;
  padding: 4rpx 8rpx;
  border-radius: 10rpx;
  min-width: 32rpx;
  text-align: center;
  display: inline-block;
}

.knowledge-tab-item.active .knowledge-tab-count {
  background: #e0f2fe;
  color: #0284c7;
}

.knowledge-tab-content-wrapper {
  min-height: 500rpx;
}

/* Tab内容区域 */
.knowledge-tab-content {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Tab统计信息 - 与上半部分快速信息风格一致 */
.tab-summary {
  display: flex;
  gap: 32rpx;
  margin-bottom: 32rpx;
  padding: 20rpx 24rpx;
  background: #f8fafc;
  border-radius: 16rpx;
  border: 1rpx solid #e2e8f0;
}

.summary-item {
  flex: 1;
  text-align: center;
}

.summary-label {
  font-size: 24rpx;
  color: #6b7280;
  display: block;
  margin-bottom: 10rpx;
}

.summary-value {
  font-size: 28rpx;
  font-weight: 600;
  color: #1f2937;
}

.summary-value.success {
  color: #059669;
}

.summary-value.warning {
  color: #d97706;
}

.summary-value.info {
  color: #0284c7;
}

.summary-value.danger {
  color: #dc2626;
}

/* 知识点列表 */
.knowledge-items {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.knowledge-item {
  background: #ffffff;
  border-radius: 16rpx;
  border: 1rpx solid #f1f5f9;
  overflow: hidden;
  transition: all 0.3s ease;
}

.knowledge-item.expanded {
  border-color: #d1d5db;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.knowledge-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 20rpx;
  cursor: pointer;
}

.knowledge-content:active {
  background-color: #f8fafc;
}

.knowledge-main {
  flex: 1;
  margin-right: 16rpx;
}

.knowledge-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #1f2937;
  line-height: 1.4;
  margin-bottom: 10rpx;
}

.knowledge-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
  align-items: center;
}

/* 标签样式统一 */
.tag {
  font-size: 20rpx;
  font-weight: 500;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  white-space: nowrap;
}

.tag-grade {
  background: #f0f9ff;
  color: #0284c7;
  border: 1rpx solid #bfdbfe;
}

.tag-chapter {
  background: #f8fafc;
  color: #64748b;
  border: 1rpx solid #cbd5e1;
}

/* 知识点统计区域 */
.knowledge-stats {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

/* 掌握度圆圈 */
.progress-circle {
  width: 56rpx;
  height: 56rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 3rpx solid #e5e7eb;
  background: #ffffff;
  flex-shrink: 0;
}

.progress-number {
  font-size: 20rpx;
  font-weight: 600;
  color: #374151;
}

/* 详情区域 */
.knowledge-detail {
  border-top: 1rpx solid #f3f4f6;
  padding: 24rpx 20rpx;
  background: #fafbfc;
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.detail-content {
  margin-bottom: 24rpx;
}

/* 详情分区 */
.detail-section {
  margin-bottom: 20rpx;
}

.detail-section:last-child {
  margin-bottom: 0;
}

.detail-label {
  font-size: 24rpx;
  font-weight: 600;
  color: #374151;
  display: block;
  margin-bottom: 10rpx;
}

.detail-suggestion {
  font-size: 26rpx;
  color: #6b7280;
  line-height: 1.6;
}

/* 关键要点 */
.key-points {
  display: flex;
  flex-direction: column;
  gap: 6rpx;
}

.key-point-item {
  font-size: 24rpx;
  color: #6b7280;
  line-height: 1.5;
  padding-left: 6rpx;
}

/* 掌握技巧 */
.mastery-tips {
  font-size: 24rpx;
  color: #059669;
  line-height: 1.6;
  font-style: italic;
  background: #f0fdf4;
  padding: 12rpx;
  border-radius: 8rpx;
  border-left: 3rpx solid #10b981;
}

/* 元数据 */
.detail-meta {
  display: flex;
  gap: 20rpx;
  margin-top: 16rpx;
  padding: 12rpx;
  background: #ffffff;
  border-radius: 8rpx;
  border: 1rpx solid #e5e7eb;
}

.meta-item {
  flex: 1;
  text-align: center;
}

.meta-label {
  font-size: 20rpx;
  color: #9ca3af;
  display: block;
  margin-bottom: 4rpx;
}

.meta-value {
  font-size: 22rpx;
  font-weight: 600;
  color: #374151;
}

/* 操作按钮 */
.detail-actions {
  display: flex;
  gap: 12rpx;
}

.action-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16rpx 20rpx;
  border-radius: 10rpx;
  font-size: 26rpx;
  font-weight: 500;
  transition: all 0.2s ease;
  border: none;
  outline: none;
}

.action-btn.primary {
  background: #3b82f6;
  color: #ffffff;
}

.action-btn.primary:active {
  background: #2563eb;
  transform: scale(0.98);
}

.action-btn.secondary {
  background: #f3f4f6;
  color: #374151;
  border: 1rpx solid #d1d5db;
}

.action-btn.secondary:active {
  background: #e5e7eb;
  transform: scale(0.98);
}

/* 拓展延伸样式 */
.extension-section {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}

/* 学习技巧区域 */
.learning-tips-section {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.tip-item {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
  margin-bottom: 20rpx;
  padding: 16rpx;
  background: #f8fafc;
  border-radius: 12rpx;
  border-left: 4rpx solid #3b82f6;
}

.tip-item:last-child {
  margin-bottom: 0;
}

.tip-marker {
  width: 32rpx;
  height: 32rpx;
  background: #3b82f6;
  color: #ffffff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  font-weight: 600;
  flex-shrink: 0;
}

.tip-content {
  flex: 1;
}

.tip-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 8rpx;
}

.tip-description {
  font-size: 26rpx;
  color: #6b7280;
  line-height: 1.6;
}

/* 常见错误区域 */
.common-mistakes-section {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.mistake-item {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
  margin-bottom: 20rpx;
  padding: 16rpx;
  background: #fef2f2;
  border-radius: 12rpx;
  border-left: 4rpx solid #ef4444;
}

.mistake-item:last-child {
  margin-bottom: 0;
}

.mistake-marker {
  font-size: 24rpx;
  flex-shrink: 0;
}

.mistake-content {
  flex: 1;
}

.mistake-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #dc2626;
  margin-bottom: 8rpx;
}

.mistake-reason {
  font-size: 24rpx;
  color: #7f1d1d;
  margin-bottom: 6rpx;
}

.mistake-correction {
  font-size: 24rpx;
  color: #059669;
  font-weight: 500;
  background: #ecfdf5;
  padding: 8rpx 12rpx;
  border-radius: 6rpx;
}

.mistake-text {
  font-size: 26rpx;
  color: #6b7280;
  line-height: 1.6;
}

/* 多媒体资源区域 */
.multimedia-resources {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.resource-group {
  margin-bottom: 24rpx;
}

.resource-group:last-child {
  margin-bottom: 0;
}

.resource-subtitle {
  font-size: 28rpx;
  font-weight: 600;
  color: #374151;
  margin-bottom: 16rpx;
  padding-left: 8rpx;
  border-left: 4rpx solid #8b5cf6;
}

.resource-list {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.resource-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 16rpx;
  background: #f8fafc;
  border-radius: 12rpx;
  border: 1rpx solid #e2e8f0;
}

.resource-icon {
  font-size: 28rpx;
  flex-shrink: 0;
}

.resource-info {
  flex: 1;
}

.resource-title {
  font-size: 26rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4rpx;
}

.resource-desc {
  font-size: 22rpx;
  color: #6b7280;
  line-height: 1.4;
}

.resource-action {
  background: #3b82f6;
  color: #ffffff;
  font-size: 24rpx;
  font-weight: 500;
  padding: 12rpx 20rpx;
  border-radius: 8rpx;
  border: none;
  cursor: pointer;
  transition: background 0.2s ease;
}

.resource-action:active {
  background: #2563eb;
}

/* 参考资料区域 */
.reference-materials {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.reference-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.reference-item {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
  padding: 16rpx;
  background: #f8fafc;
  border-radius: 12rpx;
  border: 1rpx solid #e2e8f0;
}

.reference-icon {
  font-size: 28rpx;
  color: #8b5cf6;
  flex-shrink: 0;
  margin-top: 4rpx;
}

.reference-content {
  flex: 1;
}

.reference-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 6rpx;
}

.reference-author {
  font-size: 22rpx;
  color: #6b7280;
  margin-bottom: 8rpx;
}

.reference-desc {
  font-size: 24rpx;
  color: #6b7280;
  line-height: 1.5;
  margin-bottom: 10rpx;
}

.reference-link {
  font-size: 22rpx;
  color: #3b82f6;
  text-decoration: underline;
  cursor: pointer;
}

/* 练习资源区域 */
.practice-resources {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.practice-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.practice-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  background: #f8fafc;
  border-radius: 12rpx;
  border: 1rpx solid #e2e8f0;
}

.practice-info {
  flex: 1;
}

.practice-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 8rpx;
}

.practice-difficulty {
  font-size: 22rpx;
  color: #6b7280;
  margin-bottom: 4rpx;
}

.practice-count {
  font-size: 22rpx;
  color: #6b7280;
}

.practice-action {
  background: #10b981;
  color: #ffffff;
  font-size: 24rpx;
  font-weight: 500;
  padding: 12rpx 20rpx;
  border-radius: 8rpx;
  border: none;
  cursor: pointer;
  transition: background 0.2s ease;
}

.practice-action:active {
  background: #059669;
}

/* 文理科差异化内容区域 */
.track-specific-content {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.track-content {
  margin-bottom: 24rpx;
}

.track-content:last-child {
  margin-bottom: 0;
}

.track-subtitle {
  font-size: 28rpx;
  font-weight: 600;
  color: #374151;
  margin-bottom: 16rpx;
  padding-left: 8rpx;
  border-left: 4rpx solid #f59e0b;
}

.track-applications {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.application-item {
  padding: 16rpx;
  background: #f8fafc;
  border-radius: 10rpx;
  border: 1rpx solid #e2e8f0;
}

.app-title {
  font-size: 26rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 8rpx;
}

.app-desc {
  font-size: 24rpx;
  color: #6b7280;
  line-height: 1.5;
}

/* 默认内容区域 */
.default-extension {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 40rpx;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.default-content {
  color: #9ca3af;
}

.default-text {
  font-size: 26rpx;
  line-height: 1.6;
}

/* 适配iPad等大屏设备 */
@media screen and (min-width: 768px) {
  .recommendation-cards {
    gap: 24rpx;
  }
  
  .rec-card {
    padding: 24rpx 16rpx;
  }
  
  .knowledge-header {
    padding: 28rpx 32rpx;
  }
  
  .knowledge-detail {
    padding: 32rpx;
  }
  
  .extension-section {
    gap: 40rpx;
  }
  
  .resource-list {
    gap: 16rpx;
  }
  
  .practice-list {
    gap: 20rpx;
  }
}