Component({
  properties: {
    text: {
      type: String,
      value: '',
      observer: function(newVal, oldVal) {
        if (newVal !== oldVal && this.data.autoStart) {
          this.startAnimation();
        }
      }
    },
    speed: {
      type: Number,
      value: 100 // 默认打字速度（毫秒）
    },
    cursorChar: {
      type: String,
      value: '|' // 默认光标字符
    },
    typingMode: {
      type: String,
      value: 'standard' // 默认使用标准打字模式，可选值：standard, highlight, fade
    },
    autoStart: {
      type: Boolean,
      value: true // 是否在文本变化后自动开始动画
    },
    tapToComplete: {
      type: Boolean,
      value: false // 点击时是否直接完成打字
    },
    mode: {
      type: String,
      value: 'type' // type, highlight, fadeIn
    },
    duration: {
      type: Number,
      value: 30 // 每个字符的打印时间（毫秒）
    },
    delay: {
      type: Number,
      value: 50 // 首次开始的延迟（毫秒）
    },
    showCursor: {
      type: Boolean,
      value: true // 是否显示光标
    },
    scrollFrequency: {
      type: Number,
      value: 15 // 每打印多少个字符通知滚动一次
    }
  },

  data: {
    displayText: '', // 当前显示的文本
    isTyping: false, // 是否正在打字
    timeoutId: null, // 计时器ID
    isComplete: false, // 是否已完成
    timing: 0, // 当前计时
    charCount: 0 // 当前已打印的字符数量
  },

  lifetimes: {
    attached: function() {
      if (this.data.text && this.data.autoStart) {
        this.startAnimation();
      }
    },
    
    detached: function() {
      // 组件销毁时清除计时器
      this.clearTimers();
    }
  },

  observers: {
    'text': function(newText) {
      if (newText !== this.data.displayText && this.data.isComplete) {
        // 文本改变，重置状态
        this.setData({
          displayText: '',
          isTyping: false,
          isComplete: false
        });
        
        if (this.data.autoStart) {
          this.startAnimation();
        }
      }
    }
  },

  methods: {
    // 开始打字动画
    startAnimation: function() {
      // 重置状态
      this.clearTimers();
      
      this.setData({
        displayText: '',
        isTyping: true,
        isComplete: false,
        charCount: 0
      });
      
      // 延迟启动打字效果
      this.data.timeoutId = setTimeout(() => {
        this.typeNextChar(0);
      }, this.data.delay);
      
      // 触发动画开始事件
      this.triggerEvent('start');
    },
    
    // 递归打印每个字符
    typeNextChar: function(index) {
      const { text, duration, scrollFrequency } = this.data;
      
      if (index < text.length) {
        // 统计当前字符
        const currentChar = text.charAt(index);
        const newCharCount = this.data.charCount + 1;
        
        // 显示当前位置的文本
        this.setData({
          displayText: text.substring(0, index + 1),
          timing: (index + 1) / text.length,
          charCount: newCharCount
        });
        
        // 如果遇到换行符或每隔一定数量的字符，触发滚动事件
        if (currentChar === '\n' || newCharCount % scrollFrequency === 0) {
          this.triggerEvent('typing', { progress: newCharCount / text.length });
        }
        
        // 定时打印下一个字符，可以根据字符类型动态调整速度
        let nextDuration = duration;
        // 如果是标点符号，稍微延长一点时间
        if (/[，。！？、；：""（）,.!?;:"()]/.test(currentChar)) {
          nextDuration = duration * 3;
        }
        // 如果是换行符，延长更多时间
        else if (currentChar === '\n') {
          nextDuration = duration * 5;
        }
        
        this.data.timeoutId = setTimeout(() => {
          this.typeNextChar(index + 1);
        }, nextDuration);
      } else {
        // 打字完成
        this.setData({
          isTyping: false,
          isComplete: true
        });
        
        // 触发完成事件
        this.triggerEvent('complete');
      }
    },
    
    // 清除所有计时器
    clearTimers: function() {
      if (this.data.timeoutId) {
        clearTimeout(this.data.timeoutId);
        this.data.timeoutId = null;
      }
      if (this.data.timer) {
        clearTimeout(this.data.timer);
        this.data.timer = null;
      }
    },
    
    // 完成打字，直接显示全部文本
    completeTyping: function() {
      this.clearTimers();
      
      this.setData({
        displayText: this.data.text,
        isTyping: false,
        isComplete: true,
        timing: 1
      });
      
      // 触发完成事件
      this.triggerEvent('complete');
    },
    
    // 重新开始打字
    restart: function() {
      this.startAnimation();
    },
    
    // 组件点击事件 - 如果正在打字，则完成打字
    onTap: function() {
      if (this.data.isTyping) {
        this.completeTyping();
        return;
      }
      
      // 如果已完成，可以重新开始
      if (this.data.isComplete) {
        this.triggerEvent('tap');
      }
    },
    
    // 兼容旧方法名
    handleTap: function() {
      this.onTap();
    }
  }
})