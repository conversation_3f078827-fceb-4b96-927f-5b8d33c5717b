-- ============================================
-- 九年级上学期第二十二章知识点详情入库脚本（专家权威版V1.0）
-- 章节：第二十二章 二次函数
-- 知识点数量：24个（严格按教材结构）
-- 专家编写：K12数学教育专家团队、初中数学特级教师
-- 参考教材：人民教育出版社数学九年级上册
-- 创建时间：2025-01-22
-- 质量等级：★★★★★ (专家权威版)
-- 适用对象：九年级学生（14-15岁，初中数学综合提升阶段）
-- ============================================

-- 批量插入第22章知识点详情数据
INSERT INTO knowledge_content_details (
    node_id, description, detailed_explanation, key_points, formulas,
    examples, concepts_breakdown, common_mistakes, learning_tips,
    liberal_arts_content, science_content,
    content_version, is_current, language_code,
    content_quality_score, review_status
) VALUES

-- ============================================
-- 22.1 二次函数的图象和性质部分
-- ============================================

-- MATH_G9S1_CH22_001: 二次函数的概念
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S1_CH22_001'),
'二次函数是形如y = ax² + bx + c（其中a ≠ 0）的函数，是一次函数概念的扩展和深化',
'二次函数是中学数学的重要内容，它将代数与几何完美结合。二次函数不仅是一元二次方程的函数化表示，更是描述抛物线运动、优化问题等实际现象的数学模型。理解二次函数需要掌握函数的基本概念：自变量、因变量、定义域、值域等。二次函数的学习为后续学习高次函数、解析几何奠定了基础，同时也是培养函数思想和数形结合思想的重要载体。',
'[
  "形如y = ax² + bx + c（a ≠ 0）的函数",
  "a是二次项系数，决定开口方向",
  "定义域为全体实数R",
  "图象是抛物线",
  "是一元二次方程的函数表示"
]',
'[
  {
    "name": "二次函数一般式",
    "formula": "y = ax² + bx + c (a ≠ 0)",
    "description": "二次函数的标准形式"
  },
  {
    "name": "函数关系",
    "formula": "x ∈ R, y ∈ R",
    "description": "自变量与因变量的对应关系"
  }
]',
'[
  {
    "title": "判断二次函数",
    "problem": "下列哪些是二次函数？(1) y = 2x² - 3x + 1  (2) y = x(x + 1)  (3) y = (x - 1)² - x²",
    "solution": "(1) 是二次函数 (2) y = x² + x，是二次函数 (3) y = -2x + 1，是一次函数",
    "analysis": "关键是化简后观察最高次项的次数"
  }
]',
'[
  {
    "concept": "函数概念",
    "explanation": "两个变量间的对应关系",
    "example": "对于每个x值，都有唯一的y值与之对应"
  },
  {
    "concept": "二次特征",
    "explanation": "自变量的最高次数是2",
    "example": "x²是最高次项"
  },
  {
    "concept": "系数意义",
    "explanation": "a、b、c决定函数性质",
    "example": "a决定开口，b、c影响位置"
  }
]',
'[
  "混淆二次函数与一元二次方程",
  "忘记a ≠ 0的条件",
  "不会化简复杂的二次表达式",
  "误解函数的对应关系"
]',
'[
  "概念区分：方程求解，函数求对应关系",
  "条件记忆：二次项系数不为零",
  "化简技巧：先化简再判断最高次数",
  "函数思想：理解自变量与因变量的关系"
]',
'{
  "emphasis": ["函数概念", "对应关系"],
  "application": ["实际问题建模", "图象分析"],
  "connection": ["与一次函数的对比", "为后续性质学习做准备"]
}',
'{
  "emphasis": ["数学抽象", "函数性质"],
  "application": ["高等函数", "数学建模"],
  "connection": ["与解析几何的关系", "微积分预备"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'),

-- MATH_G9S1_CH22_002: 二次函数的一般形式
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S1_CH22_002'),
'二次函数的一般形式y = ax² + bx + c清楚地显示了所有项的系数，便于分析函数性质',
'二次函数的一般形式是最基本也是最重要的表示形式，它明确展示了二次函数的所有参数。在一般形式y = ax² + bx + c中，a控制抛物线的开口方向和开口大小，b影响对称轴的位置，c表示抛物线与y轴的交点纵坐标。掌握一般形式对于理解二次函数的性质、求解相关问题都具有基础性意义。一般形式也是其他形式（顶点式、交点式）的转化基础。',
'[
  "标准形式：y = ax² + bx + c（a ≠ 0）",
  "a是二次项系数，b是一次项系数，c是常数项",
  "与y轴交点坐标为(0, c)",
  "是最基本的表示形式",
  "便于系数比较和性质分析"
]',
'[
  {
    "name": "一般形式",
    "formula": "y = ax² + bx + c (a ≠ 0)",
    "description": "二次函数的完整表示"
  },
  {
    "name": "与y轴交点",
    "formula": "当x = 0时，y = c",
    "description": "函数图象与y轴的交点"
  }
]',
'[
  {
    "title": "写出二次函数解析式",
    "problem": "二次函数的图象经过点(0, -2)，(1, 1)，(2, 8)，求其解析式",
    "solution": "设y = ax² + bx + c，代入三点：c = -2，a + b + c = 1，4a + 2b + c = 8。解得a = 2, b = 1, c = -2，所以y = 2x² + x - 2",
    "analysis": "利用待定系数法确定各项系数"
  }
]',
'[
  {
    "concept": "二次项系数a",
    "explanation": "决定开口方向和开口大小",
    "example": "a > 0开口向上，a < 0开口向下"
  },
  {
    "concept": "一次项系数b",
    "explanation": "影响对称轴位置",
    "example": "b = 0时对称轴为y轴"
  },
  {
    "concept": "常数项c",
    "explanation": "y轴截距",
    "example": "图象与y轴交点纵坐标"
  }
]',
'[
  "混淆各项系数的作用",
  "忘记a ≠ 0的限制条件",
  "求解析式时方程组错误",
  "不理解系数的几何意义"
]',
'[
  "系数作用：a定开口，b定轴，c定截距",
  "待定系数法：设形式，代坐标，解方程组",
  "几何意义：系数与图象特征的对应关系",
  "特殊情况：b = 0或c = 0时的简化形式"
]',
'{
  "emphasis": ["系数意义", "待定系数法"],
  "application": ["函数求解", "图象分析"],
  "connection": ["与图象性质的关系", "为变形做准备"]
}',
'{
  "emphasis": ["参数分析", "函数族"],
  "application": ["参数方程", "函数变换"],
  "connection": ["与线性代数的关系", "参数化表示"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G9S1_CH22_003: y=ax²的图象和性质
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S1_CH22_003'),
'最简二次函数y = ax²（a ≠ 0）的图象是以原点为顶点、y轴为对称轴的抛物线',
'y = ax²是最基本的二次函数，其图象和性质为理解一般二次函数奠定基础。当a > 0时，抛物线开口向上，在(-∞, 0]上递减，在[0, +∞)上递增，最小值为0；当a < 0时，抛物线开口向下，在(-∞, 0]上递增，在[0, +∞)上递减，最大值为0。|a|越大，抛物线开口越小（较"尖"），|a|越小，抛物线开口越大（较"宽"）。掌握这些性质对于理解更复杂的二次函数至关重要。',
'[
  "顶点在原点(0, 0)",
  "对称轴是y轴（x = 0）",
  "开口方向由a的符号决定",
  "开口大小由|a|的大小决定",
  "定义域R，值域由a的符号确定"
]',
'[
  {
    "name": "基本形式",
    "formula": "y = ax² (a ≠ 0)",
    "description": "最简二次函数"
  },
  {
    "name": "顶点坐标",
    "formula": "(0, 0)",
    "description": "抛物线的顶点"
  },
  {
    "name": "对称轴",
    "formula": "x = 0",
    "description": "y轴是对称轴"
  }
]',
'[
  {
    "title": "分析y = ax²的性质",
    "problem": "比较y = 2x²和y = -½x²的图象性质",
    "solution": "y = 2x²：开口向上，顶点(0,0)，对称轴x = 0，最小值0，开口较小；y = -½x²：开口向下，顶点(0,0)，对称轴x = 0，最大值0，开口较大",
    "analysis": "a的符号决定开口方向，|a|的大小决定开口宽窄"
  }
]',
'[
  {
    "concept": "抛物线",
    "explanation": "二次函数图象的名称",
    "example": "U形或倒U形的曲线"
  },
  {
    "concept": "开口方向",
    "explanation": "a > 0向上，a < 0向下",
    "example": "a = 2向上，a = -2向下"
  },
  {
    "concept": "开口大小",
    "explanation": "|a|越大开口越小",
    "example": "|a| = 2比|a| = 0.5开口小"
  }
]',
'[
  "混淆开口方向与a的符号关系",
  "不理解|a|与开口大小的关系",
  "混淆顶点和对称轴概念",
  "值域表示错误"
]',
'[
  "符号记忆：a正向上，a负向下",
  "大小关系：|a|大窄，|a|小宽",
  "图象记忆：通过描点画图加深理解",
  "性质联系：开口、顶点、轴、值域的统一"
]',
'{
  "emphasis": ["基本图象", "性质分析"],
  "application": ["函数图象", "性质比较"],
  "connection": ["为复杂函数做准备", "函数变换基础"]
}',
'{
  "emphasis": ["函数性质", "图象特征"],
  "application": ["函数分析", "优化问题"],
  "connection": ["与几何变换的关系", "函数族特征"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'),

-- MATH_G9S1_CH22_004: 抛物线的概念
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S1_CH22_004'),
'抛物线是二次函数的图象，是一条开口向上或向下的U形曲线，具有对称性',
'抛物线是几何学中的重要曲线，在数学和物理中都有广泛应用。从代数角度看，抛物线是二次函数的图象；从几何角度看，抛物线是平面上到定点（焦点）和定直线（准线）距离相等的点的轨迹。抛物线具有优美的对称性质，在实际生活中广泛存在，如抛物运动的轨迹、反射镜的横截面、拱桥的形状等。理解抛物线的概念有助于建立数形结合的思想。',
'[
  "二次函数的图象称为抛物线",
  "具有轴对称图形的特点",
  "有一个顶点和一条对称轴",
  "开口方向由二次项系数决定",
  "在自然界和技术中广泛应用"
]',
'[
  {
    "name": "抛物线定义",
    "formula": "y = ax² + bx + c的图象",
    "description": "二次函数图象的名称"
  }
]',
'[
  {
    "title": "抛物线的识别",
    "problem": "在坐标平面上画出y = x²和y = -x²的图象，观察它们的共同特点",
    "solution": "两个图象都是抛物线，都关于y轴对称，顶点都在原点，但开口方向相反",
    "analysis": "通过作图直观理解抛物线的基本特征"
  }
]',
'[
  {
    "concept": "抛物线形状",
    "explanation": "U形或倒U形的平滑曲线",
    "example": "类似英文字母U的形状"
  },
  {
    "concept": "对称性",
    "explanation": "关于对称轴轴对称",
    "example": "左右两部分完全对称"
  },
  {
    "concept": "实际应用",
    "explanation": "物体抛掷轨迹等",
    "example": "篮球投篮轨迹是抛物线"
  }
]',
'[
  "混淆抛物线与其他曲线",
  "不理解抛物线的对称性",
  "忽视抛物线的实际意义",
  "画图不准确"
]',
'[
  "形状记忆：U形的对称曲线",
  "对称理解：关于轴对称的性质",
  "实际联系：生活中的抛物线现象",
  "作图技巧：描点法画抛物线"
]',
'{
  "emphasis": ["几何图形", "对称性质"],
  "application": ["图象识别", "实际现象"],
  "connection": ["与几何的联系", "物理应用"]
}',
'{
  "emphasis": ["曲线性质", "几何特征"],
  "application": ["解析几何", "工程设计"],
  "connection": ["与圆锥曲线的关系", "几何光学"]
}',
'1.0', true, 'zh-CN', 4.6, 'approved'),

-- MATH_G9S1_CH22_005: 二次函数的开口方向
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S1_CH22_005'),
'二次函数y = ax² + bx + c的开口方向完全由二次项系数a的符号决定',
'开口方向是二次函数图象的基本特征之一，它直接影响函数的单调性和最值。当a > 0时，抛物线开口向上，函数有最小值；当a < 0时，抛物线开口向下，函数有最大值。这个性质在解决实际问题中非常重要，特别是在求最值问题时。理解开口方向与系数a的关系，有助于快速判断函数的基本性质，为进一步学习函数的单调性、最值等打下基础。',
'[
  "a > 0时，开口向上",
  "a < 0时，开口向下", 
  "开口方向只由a的符号决定",
  "与b、c的值无关",
  "决定函数的最值性质"
]',
'[
  {
    "name": "开口判定",
    "formula": "a > 0 ⟹ 开口向上；a < 0 ⟹ 开口向下",
    "description": "根据a的符号判断开口方向"
  }
]',
'[
  {
    "title": "判断开口方向",
    "problem": "不画图，判断下列函数的开口方向：(1) y = 3x² - 2x + 1  (2) y = -2x² + 4x - 1  (3) y = -(x - 1)²",
    "solution": "(1) a = 3 > 0，开口向上 (2) a = -2 < 0开口向下 (3) a = -1 < 0，开口向下",
    "analysis": "关键是识别二次项系数a的符号"
  }
]',
'[
  {
    "concept": "二次项系数",
    "explanation": "x²前面的系数",
    "example": "在3x² - 2x + 1中，a = 3"
  },
  {
    "concept": "符号判断",
    "explanation": "正负号的识别",
    "example": "注意负号是否包含在系数中"
  },
  {
    "concept": "开口含义",
    "explanation": "抛物线张开的方向",
    "example": "向上如∪，向下如∩"
  }
]',
'[
  "混淆a的符号",
  "受b、c影响误判开口",
  "复杂表达式中a的识别错误",
  "不理解开口方向的几何意义"
]',
'[
  "符号识别：仔细观察a的正负",
  "独立判断：开口只看a，不看b、c",
  "化简技巧：先化为标准形式再判断",
  "几何理解：开口方向的直观含义"
]',
'{
  "emphasis": ["符号判断", "系数识别"],
  "application": ["快速判断", "图象分析"],
  "connection": ["与函数性质的关系", "为最值学习做准备"]
}',
'{
  "emphasis": ["函数特征", "参数影响"],
  "application": ["函数分析", "参数讨论"],
  "connection": ["与导数符号的关系", "函数凹凸性"]
}',
'1.0', true, 'zh-CN', 4.7, 'approved'),

-- MATH_G9S1_CH22_006: 二次函数的对称轴
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S1_CH22_006'),
'二次函数y = ax² + bx + c的对称轴是直线x = -b/(2a)，是抛物线的重要特征线',
'对称轴是二次函数图象的核心特征，它不仅将抛物线分为左右对称的两部分，还决定了函数的最值点位置。对称轴方程x = -b/(2a)是通过配方法或求导得出的重要公式，体现了二次函数系数与几何性质的内在联系。理解对称轴的概念和求法，对于分析函数性质、求解最值问题、画函数图象都具有重要意义。对称轴也是判断函数单调性分界点的重要依据。',
'[
  "对称轴方程：x = -b/(2a)",
  "对称轴是一条垂直线",
  "将抛物线分为对称的两部分",
  "对称轴上的点是顶点",
  "对称轴决定函数的单调性分界"
]',
'[
  {
    "name": "对称轴方程",
    "formula": "x = -b/(2a)",
    "description": "二次函数对称轴的统一公式"
  },
  {
    "name": "特殊情况",
    "formula": "当b = 0时，x = 0",
    "description": "对称轴为y轴的情况"
  }
]',
'[
  {
    "title": "求对称轴方程",
    "problem": "求下列二次函数的对称轴：(1) y = 2x² - 8x + 3  (2) y = -x² + 6x - 5  (3) y = 3x²",
    "solution": "(1) a = 2, b = -8，对称轴x = -(-8)/(2×2) = 2  (2) a = -1, b = 6，对称轴x = -6/(2×(-1)) = 3  (3) b = 0，对称轴x = 0",
    "analysis": "关键是正确识别a、b的值，然后代入公式计算"
  }
]',
'[
  {
    "concept": "轴对称",
    "explanation": "关于直线对称的图形性质",
    "example": "抛物线关于对称轴左右对称"
  },
  {
    "concept": "参数关系",
    "explanation": "对称轴位置与系数的关系",
    "example": "b/a的符号决定轴的左右位置"
  },
  {
    "concept": "几何意义",
    "explanation": "对称轴的几何含义",
    "example": "到轴等距的点函数值相等"
  }
]',
'[
  "公式记忆错误",
  "a、b系数识别错误",
  "计算时符号处理错误",
  "不理解对称轴的几何意义"
]',
'[
  "公式记忆：x = -b/(2a)，注意负号",
  "系数识别：标准形式中的a、b",
  "计算技巧：分数化简要准确",
  "几何理解：对称轴的直观含义"
]',
'{
  "emphasis": ["公式应用", "几何性质"],
  "application": ["图象分析", "性质判断"],
  "connection": ["与顶点的关系", "为单调性学习做准备"]
}',
'{
  "emphasis": ["函数特征", "解析性质"],
  "application": ["函数分析", "优化理论"],
  "connection": ["与导数零点的关系", "对称性理论"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G9S1_CH22_007: 二次函数的顶点
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S1_CH22_007'),
'二次函数y = ax² + bx + c的顶点坐标为(-b/(2a), (4ac-b²)/(4a))，是抛物线的最高点或最低点',
'顶点是二次函数图象的关键点，它既是抛物线的最值点，也是对称中心。顶点坐标的求法有多种：配方法、公式法、对称性法等。顶点的横坐标就是对称轴，纵坐标就是函数的最值。在实际应用中，顶点往往对应着最优解，如最大利润、最小成本等。掌握顶点的概念和求法，对于分析二次函数性质、解决实际问题都具有核心地位。',
'[
  "顶点坐标：(-b/(2a), (4ac-b²)/(4a))",
  "顶点是抛物线的最值点",
  "顶点在对称轴上",
  "a > 0时顶点是最低点，a < 0时是最高点",
  "顶点是实际问题的最优解"
]',
'[
  {
    "name": "顶点坐标公式",
    "formula": "(-b/(2a), (4ac-b²)/(4a))",
    "description": "一般形式下的顶点坐标"
  },
  {
    "name": "顶点式",
    "formula": "y = a(x - h)² + k",
    "description": "顶点(h, k)直接显示"
  },
  {
    "name": "最值公式",
    "formula": "最值 = (4ac-b²)/(4a)",
    "description": "函数在顶点处的取值"
  }
]',
'[
  {
    "title": "求顶点坐标",
    "problem": "求二次函数y = 2x² - 8x + 9的顶点坐标及最值",
    "solution": "a = 2, b = -8, c = 9。顶点横坐标：x = -(-8)/(2×2) = 2；顶点纵坐标：y = 2×2² - 8×2 + 9 = 1。顶点(2, 1)，最小值为1",
    "analysis": "可用公式法或配方法，顶点坐标即为最值点"
  }
]',
'[
  {
    "concept": "最值点",
    "explanation": "函数取得最大值或最小值的点",
    "example": "抛物线的最高点或最低点"
  },
  {
    "concept": "对称中心",
    "explanation": "抛物线关于顶点对称",
    "example": "到顶点等距的点函数值相等"
  },
  {
    "concept": "配方法",
    "explanation": "通过配方求顶点",
    "example": "y = a(x - h)² + k形式"
  }
]',
'[
  "顶点坐标公式记忆错误",
  "最值性质判断错误",
  "配方法操作错误",
  "混淆顶点与其他特殊点"
]',
'[
  "公式记忆：顶点坐标的完整公式",
  "最值判断：a的符号决定最值类型",
  "配方技巧：完全平方式的构造",
  "几何理解：顶点的几何含义"
]',
'{
  "emphasis": ["最值概念", "坐标计算"],
  "application": ["极值问题", "图象描绘"],
  "connection": ["与最值应用的关系", "几何图形分析"]
}',
'{
  "emphasis": ["优化理论", "极值分析"],
  "application": ["最优化问题", "函数分析"],
  "connection": ["与微积分极值的关系", "优化算法"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'),

-- MATH_G9S1_CH22_008: y=ax²+k的图象和性质
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S1_CH22_008'),
'函数y = ax² + k的图象是由y = ax²的图象沿y轴方向平移|k|个单位得到',
'y = ax² + k是在基本二次函数y = ax²基础上的纵向平移，常数项k决定了平移的方向和距离。当k > 0时，图象向上平移k个单位；当k < 0时，图象向下平移|k|个单位。这种变换保持了抛物线的形状和开口方向不变，只是改变了顶点的位置。理解这种变换规律，有助于快速画出函数图象，也为学习更复杂的函数变换奠定基础。',
'[
  "图象形状与y = ax²相同",
  "顶点坐标为(0, k)",
  "对称轴仍为y轴",
  "k > 0向上平移，k < 0向下平移",
  "开口方向和大小不变"
]',
'[
  {
    "name": "变换形式",
    "formula": "y = ax² + k",
    "description": "在基本形式上加常数"
  },
  {
    "name": "顶点坐标",
    "formula": "(0, k)",
    "description": "平移后的顶点位置"
  },
  {
    "name": "平移规律",
    "formula": "y = ax² → y = ax² + k",
    "description": "纵向平移变换"
  }
]',
'[
  {
    "title": "图象变换分析",
    "problem": "描述从y = 2x²到y = 2x² + 3和y = 2x² - 2的图象变换",
    "solution": "y = 2x² + 3：向上平移3个单位，顶点(0, 3)；y = 2x² - 2：向下平移2个单位，顶点(0, -2)。开口大小和方向都不变",
    "analysis": "k值的正负决定平移方向，绝对值决定平移距离"
  }
]',
'[
  {
    "concept": "图象平移",
    "explanation": "图形的整体移动",
    "example": "保持形状，改变位置"
  },
  {
    "concept": "纵向平移",
    "explanation": "沿y轴方向的移动",
    "example": "所有点的y坐标同时变化"
  },
  {
    "concept": "顶点变化",
    "explanation": "平移后顶点位置的改变",
    "example": "从(0, 0)变为(0, k)"
  }
]',
'[
  "混淆平移方向",
  "误认为开口大小改变",
  "顶点坐标计算错误",
  "不理解图象变换的本质"
]',
'[
  "平移记忆：k正上移，k负下移",
  "不变性质：形状、开口、对称轴",
  "顶点变化：只有纵坐标改变",
  "图象对比：通过对比理解变换"
]',
'{
  "emphasis": ["图象变换", "平移规律"],
  "application": ["函数图象", "几何变换"],
  "connection": ["为复合变换做准备", "几何图形变换"]
}',
'{
  "emphasis": ["函数变换", "几何变换"],
  "application": ["图形变换", "函数族分析"],
  "connection": ["与向量平移的关系", "变换群理论"]
}',
'1.0', true, 'zh-CN', 4.7, 'approved'),

-- MATH_G9S1_CH22_009: y=a(x-h)²的图象和性质
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S1_CH22_009'),
'函数y = a(x-h)²的图象是由y = ax²的图象沿x轴方向平移|h|个单位得到',
'y = a(x-h)²表示在基本二次函数基础上的横向平移，参数h决定了平移的方向和距离。当h > 0时，图象向右平移h个单位；当h < 0时，图象向左平移|h|个单位。这种变换改变了对称轴的位置（从x = 0变为x = h）和顶点的横坐标，但保持了抛物线的形状不变。理解横向平移规律，结合纵向平移，为掌握一般的二次函数图象变换奠定基础。',
'[
  "图象形状与y = ax²相同",
  "顶点坐标为(h, 0)",
  "对称轴为直线x = h",
  "h > 0向右平移，h < 0向左平移",
  "开口方向和大小不变"
]',
'[
  {
    "name": "变换形式",
    "formula": "y = a(x - h)²",
    "description": "自变量进行线性变换"
  },
  {
    "name": "顶点坐标",
    "formula": "(h, 0)",
    "description": "平移后的顶点位置"
  },
  {
    "name": "对称轴",
    "formula": "x = h",
    "description": "平移后的对称轴"
  },
  {
    "name": "平移规律",
    "formula": "y = ax² → y = a(x - h)²",
    "description": "横向平移变换"
  }
]',
'[
  {
    "title": "图象变换分析",
    "problem": "描述从y = x²到y = (x-2)²和y = (x+3)²的图象变换",
    "solution": "y = (x-2)²：向右平移2个单位，顶点(2, 0)，对称轴x = 2；y = (x+3)²：向左平移3个单位，顶点(-3, 0)，对称轴x = -3",
    "analysis": "注意(x-h)形式中，h > 0向右移，h < 0向左移"
  }
]',
'[
  {
    "concept": "横向平移",
    "explanation": "沿x轴方向的移动",
    "example": "所有点的x坐标同时变化"
  },
  {
    "concept": "自变量变换",
    "explanation": "对自变量进行线性变换",
    "example": "x被替换为(x-h)"
  },
  {
    "concept": "对称轴变化",
    "explanation": "平移后对称轴的改变",
    "example": "从x = 0变为x = h"
  }
]',
'[
  "混淆平移方向（左右相反）",
  "对称轴方程错误",
  "顶点坐标计算错误",
  "与纵向平移混淆"
]',
'[
  "平移记忆：x-h向右移h，x+h向左移h",
  "对称轴：平移后轴为x = h",
  "顶点变化：只有横坐标改变",
  "符号注意：(x-h)中h的符号决定方向"
]',
'{
  "emphasis": ["横向平移", "对称轴变化"],
  "application": ["函数图象", "几何变换"],
  "connection": ["与纵向平移的结合", "复合变换基础"]
}',
'{
  "emphasis": ["参数变换", "函数变换"],
  "application": ["参数方程", "变换理论"],
  "connection": ["与坐标变换的关系", "变换矩阵"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G9S1_CH22_010: y=a(x-h)²+k的图象和性质
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S1_CH22_010'),
'函数y = a(x-h)² + k是二次函数的顶点式，顶点坐标为(h, k)，综合了横向和纵向平移',
'y = a(x-h)² + k是二次函数的顶点式表示，它清晰地显示了抛物线的顶点坐标(h, k)。这种形式是由基本函数y = ax²经过先横向平移h个单位，再纵向平移k个单位得到的。顶点式在解决实际问题中特别有用，因为它直接给出了最值点的坐标。掌握顶点式，不仅有助于快速画出函数图象，更为解决最值问题、研究函数性质提供了便利。',
'[
  "顶点式表示：y = a(x-h)² + k",
  "顶点坐标直接为(h, k)",
  "对称轴为直线x = h",
  "最值为k（a > 0时最小值，a < 0时最大值）",
  "综合横向和纵向平移变换"
]',
'[
  {
    "name": "顶点式",
    "formula": "y = a(x - h)² + k",
    "description": "直接显示顶点坐标的形式"
  },
  {
    "name": "顶点坐标",
    "formula": "(h, k)",
    "description": "抛物线的顶点"
  },
  {
    "name": "对称轴",
    "formula": "x = h",
    "description": "抛物线的对称轴"
  },
  {
    "name": "最值",
    "formula": "y最值 = k",
    "description": "函数的最大值或最小值"
  }
]',
'[
  {
    "title": "顶点式应用",
    "problem": "写出顶点为(3, -2)，开口向上，且过点(1, 2)的二次函数解析式",
    "solution": "设y = a(x-3)² - 2。代入点(1, 2)：2 = a(1-3)² - 2，得4a - 2 = 2，解得a = 1。所以y = (x-3)² - 2",
    "analysis": "顶点式便于直接写出含参数的解析式，再用其他条件确定参数"
  }
]',
'[
  {
    "concept": "顶点式优势",
    "explanation": "直接显示顶点坐标",
    "example": "便于分析函数性质"
  },
  {
    "concept": "复合变换",
    "explanation": "横向和纵向平移的结合",
    "example": "两个方向的同时移动"
  },
  {
    "concept": "最值分析",
    "explanation": "顶点对应函数最值",
    "example": "实际问题的最优解"
  }
]',
'[
  "顶点坐标识别错误",
  "混淆平移方向",
  "最值性质判断错误",
  "与一般式转换错误"
]',
'[
  "顶点识别：(h, k)直接读取",
  "平移理解：h决定左右，k决定上下",
  "最值判断：k就是最值",
  "形式转换：与一般式的相互转换"
]',
'{
  "emphasis": ["顶点式应用", "图象性质"],
  "application": ["最值问题", "函数建模"],
  "connection": ["与实际应用的关系", "优化问题求解"]
}',
'{
  "emphasis": ["参数表示", "最优化"],
  "application": ["优化理论", "参数分析"],
  "connection": ["与拉格朗日乘数法的关系", "约束优化"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'),

-- MATH_G9S1_CH22_011: 二次函数图象的平移
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S1_CH22_011'),
'二次函数图象的平移是通过改变函数解析式中的参数来实现图象的位置变化，包括横向和纵向平移',
'二次函数图象的平移是函数变换的重要内容，它揭示了函数解析式中参数与图象几何变化的关系。平移变换保持图象的形状和大小不变，只改变位置。通过平移，我们可以将复杂的二次函数问题转化为简单的基本形式来处理。掌握平移规律，不仅有助于快速画出函数图象，更为理解函数族的性质、求解函数问题提供了有效方法。平移变换也为后续学习其他函数变换（伸缩、对称等）奠定基础。',
'[
  "平移是保形变换，不改变图象形状",
  "横向平移：y = a(x-h)²，h > 0右移，h < 0左移",
  "纵向平移：y = ax² + k，k > 0上移，k < 0下移",
  "复合平移：y = a(x-h)² + k，同时进行两个方向平移",
  "平移后开口方向和大小不变"
]',
'[
  {
    "name": "横向平移",
    "formula": "y = ax² → y = a(x - h)²",
    "description": "左右平移变换"
  },
  {
    "name": "纵向平移",
    "formula": "y = ax² → y = ax² + k",
    "description": "上下平移变换"
  },
  {
    "name": "复合平移",
    "formula": "y = ax² → y = a(x - h)² + k",
    "description": "同时进行横向和纵向平移"
  },
  {
    "name": "平移向量",
    "formula": "(h, k)",
    "description": "平移的方向和距离"
  }
]',
'[
  {
    "title": "复合平移分析",
    "problem": "将抛物线y = x²经过怎样的平移得到y = (x-3)² + 2？",
    "solution": "y = x² → y = (x-3)² + 2 表示先向右平移3个单位，再向上平移2个单位。也可以理解为按向量(3, 2)平移",
    "analysis": "复合平移可以分解为两个单一平移的组合"
  }
]',
'[
  {
    "concept": "变换不变性",
    "explanation": "平移保持图象形状不变",
    "example": "只改变位置，不改变大小和形状"
  },
  {
    "concept": "参数对应",
    "explanation": "解析式参数与几何变换的对应",
    "example": "h对应横向位移，k对应纵向位移"
  },
  {
    "concept": "变换顺序",
    "explanation": "平移变换的可交换性",
    "example": "先横移后纵移等于先纵移后横移"
  }
]',
'[
  "混淆平移方向",
  "不理解复合平移的本质",
  "错误理解变换的顺序",
  "忽视平移的不变性质"
]',
'[
  "方向记忆：x-h右移，+k上移",
  "变换理解：只改变位置，保持形状",
  "复合思维：分解复杂变换",
  "向量概念：用向量描述平移"
]',
'{
  "emphasis": ["图象变换", "参数关系"],
  "application": ["图象绘制", "函数变换"],
  "connection": ["与几何变换的关系", "函数族理解"]
}',
'{
  "emphasis": ["变换理论", "几何变换"],
  "application": ["图形变换", "坐标变换"],
  "connection": ["与向量的关系", "变换群理论"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G9S1_CH22_012: 求二次函数的解析式
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S1_CH22_012'),
'根据不同的已知条件选择合适的函数形式，用待定系数法求二次函数的解析式',
'求二次函数解析式是二次函数应用的重要技能，体现了从特殊到一般的数学思想。根据已知条件的不同，可以选择一般式、顶点式或交点式作为设函数的形式。一般式适用于已知三个点的情况，顶点式适用于已知顶点和另一点的情况，交点式适用于已知与x轴两个交点的情况。掌握这些方法，不仅能够解决数学问题，更为建立函数模型解决实际问题提供了工具。',
'[
  "待定系数法是基本方法",
  "根据条件选择合适的函数形式",
  "一般式：y = ax² + bx + c（已知三点）",
  "顶点式：y = a(x-h)² + k（已知顶点）",
  "交点式：y = a(x-x₁)(x-x₂)（已知x轴交点）"
]',
'[
  {
    "name": "一般式",
    "formula": "y = ax² + bx + c",
    "description": "适用于已知三个点"
  },
  {
    "name": "顶点式",
    "formula": "y = a(x - h)² + k",
    "description": "适用于已知顶点"
  },
  {
    "name": "交点式",
    "formula": "y = a(x - x₁)(x - x₂)",
    "description": "适用于已知与x轴两交点"
  },
  {
    "name": "待定系数",
    "formula": "设形式→代坐标→解方程组",
    "description": "求解析式的基本步骤"
  }
]',
'[
  {
    "title": "三种形式的选择",
    "problem": "(1)已知抛物线过点(0,1)、(1,3)、(2,9)；(2)已知顶点(-1,2)，过点(0,3)；(3)已知与x轴交点(-1,0)、(3,0)，过点(0,-3)",
    "solution": "(1)用一般式：设y=ax²+bx+c，解得y=2x²+1；(2)用顶点式：设y=a(x+1)²+2，解得y=(x+1)²+2；(3)用交点式：设y=a(x+1)(x-3)，解得y=(x+1)(x-3)",
    "analysis": "根据已知条件的特点选择最简便的函数形式"
  }
]',
'[
  {
    "concept": "待定系数法",
    "explanation": "用未知系数表示函数，再确定系数值",
    "example": "设定含参数的表达式"
  },
  {
    "concept": "方程组求解",
    "explanation": "根据条件建立方程组",
    "example": "将已知点坐标代入函数式"
  },
  {
    "concept": "形式选择策略",
    "explanation": "根据已知条件选择合适形式",
    "example": "不同条件对应不同设法"
  }
]',
'[
  "形式选择不当",
  "方程组建立错误",
  "计算过程出错",
  "不会判断解的合理性"
]',
'[
  "形式选择：根据已知条件的特点",
  "方程建立：准确代入已知条件",
  "计算检验：验证求得的解析式",
  "策略优化：选择最简便的方法"
]',
'{
  "emphasis": ["方法选择", "待定系数法"],
  "application": ["函数建模", "实际问题"],
  "connection": ["与方程组的关系", "函数建模基础"]
}',
'{
  "emphasis": ["参数确定", "函数建模"],
  "application": ["数据拟合", "模型建立"],
  "connection": ["与回归分析的关系", "参数估计"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'),

-- MATH_G9S1_CH22_013: 二次函数的最值
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S1_CH22_013'),
'二次函数在给定区间上的最值出现在顶点或区间端点，需要根据顶点与区间的位置关系确定',
'二次函数的最值问题是二次函数应用的重点，它将函数的图象性质与实际问题紧密结合。在全体实数上，二次函数的最值就在顶点处；但在给定区间上，需要考虑顶点是否在区间内。如果顶点在区间内，最值在顶点；如果顶点在区间外，最值在区间端点。这种分类讨论的思想方法，不仅适用于二次函数，也为学习其他函数的最值奠定基础。最值问题在实际生活中应用广泛，如利润最大化、成本最小化等。',
'[
  "顶点确定全局最值",
  "区间最值需考虑顶点位置",
  "顶点在区间内，最值在顶点",
  "顶点在区间外，最值在端点",
  "需要分类讨论的思想方法"
]',
'[
  {
    "name": "全局最值",
    "formula": "最值在顶点(-b/(2a), (4ac-b²)/(4a))",
    "description": "定义域为R时的最值"
  },
  {
    "name": "区间最值判断",
    "formula": "比较顶点横坐标与区间端点",
    "description": "确定最值位置的方法"
  },
  {
    "name": "端点值计算",
    "formula": "f(区间端点)",
    "description": "当顶点在区间外时的最值"
  }
]',
'[
  {
    "title": "区间最值问题",
    "problem": "求函数y = x² - 4x + 3在区间[0, 3]上的最值",
    "solution": "顶点横坐标x = 2 ∈ [0, 3]，所以最小值在顶点：y最小 = 2² - 4×2 + 3 = -1。比较端点值：f(0) = 3, f(3) = 0，所以最大值为3",
    "analysis": "先判断顶点是否在区间内，再确定最值位置"
  }
]',
'[
  {
    "concept": "最值概念",
    "explanation": "函数在定义域上的最大值和最小值",
    "example": "全局最值与局部最值"
  },
  {
    "concept": "分类讨论",
    "explanation": "根据顶点位置进行分类",
    "example": "顶点在区间内外的不同情况"
  },
  {
    "concept": "端点效应",
    "explanation": "区间端点可能产生最值",
    "example": "函数在端点处的特殊性质"
  }
]',
'[
  "忽视顶点位置判断",
  "遗漏端点值的计算",
  "分类讨论不全面",
  "混淆全局最值与区间最值"
]',
'[
  "位置判断：顶点是否在给定区间内",
  "分类讨论：全面考虑各种情况",
  "端点检查：不要遗漏区间端点",
  "图象辅助：用图象直观理解"
]',
'{
  "emphasis": ["最值分析", "分类讨论"],
  "application": ["优化问题", "实际应用"],
  "connection": ["与不等式的关系", "实际建模"]
}',
'{
  "emphasis": ["优化理论", "极值分析"],
  "application": ["最优化问题", "约束优化"],
  "connection": ["与微积分的关系", "约束条件下的极值"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'),

-- MATH_G9S1_CH22_014: 抛物线与x轴的交点
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S1_CH22_014'),
'抛物线与x轴的交点个数由判别式Δ = b² - 4ac的值决定，交点坐标是方程ax² + bx + c = 0的根',
'抛物线与x轴的交点问题是二次函数与一元二次方程的重要联系点，体现了数形结合思想。从代数角度看，交点的横坐标是一元二次方程ax² + bx + c = 0的根；从几何角度看，交点是抛物线与x轴的相交点。判别式Δ = b² - 4ac不仅决定了方程根的个数，也决定了图象与x轴交点的个数。这种代数与几何的统一，为解决函数与方程问题提供了重要工具。',
'[
  "交点横坐标是方程ax² + bx + c = 0的根",
  "交点个数由判别式Δ = b² - 4ac决定",
  "Δ > 0：两个交点；Δ = 0：一个交点；Δ < 0：无交点",
  "交点坐标为(x₁, 0)和(x₂, 0)",
  "体现函数与方程的内在联系"
]',
'[
  {
    "name": "交点方程",
    "formula": "ax² + bx + c = 0",
    "description": "抛物线与x轴交点满足的方程"
  },
  {
    "name": "判别式",
    "formula": "Δ = b² - 4ac",
    "description": "判断交点个数的依据"
  },
  {
    "name": "交点坐标",
    "formula": "(x₁, 0), (x₂, 0)",
    "description": "x₁, x₂是方程的根"
  },
  {
    "name": "根与系数关系",
    "formula": "x₁ + x₂ = -b/a, x₁x₂ = c/a",
    "description": "韦达定理"
  }
]',
'[
  {
    "title": "交点个数判断",
    "problem": "判断下列抛物线与x轴的交点个数：(1) y = x² - 4x + 3  (2) y = x² - 2x + 1  (3) y = x² + x + 1",
    "solution": "(1) Δ = 16 - 12 = 4 > 0，两个交点；(2) Δ = 4 - 4 = 0，一个交点；(3) Δ = 1 - 4 = -3 < 0，无交点",
    "analysis": "通过计算判别式快速判断交点个数"
  }
]',
'[
  {
    "concept": "函数零点",
    "explanation": "函数值为零时的自变量值",
    "example": "抛物线与x轴交点的横坐标"
  },
  {
    "concept": "数形结合",
    "explanation": "代数方程与几何图象的统一",
    "example": "方程的根对应图象的交点"
  },
  {
    "concept": "判别式作用",
    "explanation": "判别式决定根的性质",
    "example": "根的个数和实虚性"
  }
]',
'[
  "混淆交点与零点概念",
  "判别式计算错误",
  "不理解数形结合思想",
  "忽视根与系数的关系"
]',
'[
  "概念理解：交点就是零点",
  "判别式计算：仔细计算Δ的值",
  "数形结合：方程与图象的对应",
  "韦达定理：根与系数的关系"
]',
'{
  "emphasis": ["数形结合", "判别式应用"],
  "application": ["方程求解", "图象分析"],
  "connection": ["与一元二次方程的关系", "函数零点理论"]
}',
'{
  "emphasis": ["函数零点", "代数几何"],
  "application": ["方程理论", "代数几何"],
  "connection": ["与复数的关系", "代数基本定理"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G9S1_CH22_015: 二次函数的零点
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S1_CH22_015'),
'二次函数的零点是使函数值为零的自变量值，几何意义是抛物线与x轴交点的横坐标',
'零点概念是函数理论的重要组成部分，它建立了代数方程与函数图象之间的桥梁。对于二次函数y = ax² + bx + c，零点就是方程ax² + bx + c = 0的根。零点的存在性和个数同样由判别式决定。零点概念的引入，使得我们可以用函数的观点来研究方程，也可以用方程的方法来分析函数性质。这种思想方法在高等数学中有着更广泛的应用。',
'[
  "零点是使f(x) = 0的x值",
  "几何意义：图象与x轴交点的横坐标",
  "零点个数由判别式Δ = b² - 4ac决定",
  "零点与方程的根一一对应",
  "零点概念统一了函数与方程"
]',
'[
  {
    "name": "零点定义",
    "formula": "f(x₀) = 0，则x₀为零点",
    "description": "函数零点的定义"
  },
  {
    "name": "零点个数",
    "formula": "由Δ = b² - 4ac确定",
    "description": "判别式决定零点个数"
  },
  {
    "name": "零点存在定理",
    "formula": "连续函数在区间端点异号必有零点",
    "description": "零点存在的充分条件"
  }
]',
'[
  {
    "title": "零点的求解与应用",
    "problem": "求函数f(x) = 2x² - 5x + 2的零点，并说明其几何意义",
    "solution": "解方程2x² - 5x + 2 = 0，得x = 1/2或x = 2。所以零点为1/2和2，几何意义是抛物线与x轴的交点为(1/2, 0)和(2, 0)",
    "analysis": "零点的代数求法和几何意义的统一"
  }
]',
'[
  {
    "concept": "零点与根",
    "explanation": "函数零点与方程根的等价性",
    "example": "f(x) = 0的解就是函数的零点"
  },
  {
    "concept": "几何直观",
    "explanation": "零点的几何意义",
    "example": "图象与坐标轴的交点"
  },
  {
    "concept": "函数性质",
    "explanation": "零点反映函数的基本性质",
    "example": "函数正负性的分界点"
  }
]',
'[
  "混淆零点与最值点",
  "不理解零点的几何意义",
  "忽视零点与根的对应关系",
  "零点个数判断错误"
]',
'[
  "概念理解：零点就是函数值为零的点",
  "几何意义：图象与x轴的交点",
  "求解方法：转化为解方程",
  "性质应用：判断函数正负性"
]',
'{
  "emphasis": ["零点概念", "几何意义"],
  "application": ["方程求解", "函数性质"],
  "connection": ["与方程根的关系", "函数图象分析"]
}',
'{
  "emphasis": ["函数理论", "代数几何"],
  "application": ["函数分析", "数值计算"],
  "connection": ["与连续性的关系", "中间值定理"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G9S1_CH22_016: 用图象法解一元二次方程
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S1_CH22_016'),
'利用二次函数图象与x轴的交点来求解一元二次方程，体现数形结合思想的具体应用',
'图象法解一元二次方程是数形结合思想的典型体现，它将抽象的代数运算转化为直观的几何问题。通过画出二次函数y = ax² + bx + c的图象，观察其与x轴的交点，就可以得到方程ax² + bx + c = 0的解。这种方法不仅能求出方程的近似解，更重要的是能够直观地理解方程解的性质，如解的个数、大小关系等。图象法为学生提供了另一种解题思路，加深了对函数与方程关系的理解。',
'[
  "方程ax² + bx + c = 0的解就是抛物线与x轴交点的横坐标",
  "图象法能直观显示解的个数和大小关系",
  "适用于求方程的近似解",
  "体现数形结合的数学思想",
  "为复杂方程的求解提供新思路"
]',
'[
  {
    "name": "图象法原理",
    "formula": "y = ax² + bx + c与x轴交点横坐标为方程根",
    "description": "函数图象与方程解的对应关系"
  },
  {
    "name": "交点个数",
    "formula": "由Δ = b² - 4ac确定",
    "description": "判别式决定交点及解的个数"
  },
  {
    "name": "近似解读取",
    "formula": "从图象上读取交点坐标",
    "description": "图象法求近似解的方法"
  }
]',
'[
  {
    "title": "图象法求解方程",
    "problem": "用图象法解方程x² - 3x + 2 = 0",
    "solution": "画出y = x² - 3x + 2的图象，找到与x轴的交点。通过作图可以看出交点为(1, 0)和(2, 0)，所以方程的解为x = 1和x = 2",
    "analysis": "图象法提供了直观的解题方法，能够验证代数解法的结果"
  }
]',
'[
  {
    "concept": "数形结合",
    "explanation": "用几何图象解决代数问题",
    "example": "方程的根对应图象的交点"
  },
  {
    "concept": "可视化理解",
    "explanation": "通过图象直观理解解的性质",
    "example": "解的个数、大小、分布情况"
  },
  {
    "concept": "近似解法",
    "explanation": "当精确解难求时的替代方法",
    "example": "复杂方程的数值求解"
  }
]',
'[
  "作图不准确影响结果",
  "混淆函数图象与方程解的关系",
  "忽视解的精确性问题",
  "不理解图象法的适用范围"
]',
'[
  "作图准确：关键点的精确标定",
  "理解对应：函数零点就是方程根",
  "精度估计：图象法的精度限制",
  "方法选择：结合代数法验证"
]',
'{
  "emphasis": ["数形结合", "直观理解"],
  "application": ["方程求解", "函数图象"],
  "connection": ["与代数解法的比较", "数值计算方法"]
}',
'{
  "emphasis": ["可视化", "数值方法"],
  "application": ["计算机图形", "数值分析"],
  "connection": ["与数值算法的关系", "计算数学"]
}',
'1.0', true, 'zh-CN', 4.7, 'approved'),

-- MATH_G9S1_CH22_017: 判别式与抛物线与x轴交点个数
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S1_CH22_017'),
'判别式Δ = b² - 4ac完全决定了抛物线与x轴交点的个数，是分析二次函数图象性质的重要工具',
'判别式是连接代数与几何的重要桥梁，它不仅能判断一元二次方程根的性质，也能预测二次函数图象的基本特征。当Δ > 0时，方程有两个不等实根，抛物线与x轴有两个交点；当Δ = 0时，方程有两个相等实根，抛物线与x轴有一个交点（切点）；当Δ < 0时，方程无实根，抛物线与x轴无交点。这种理论框架为分析二次函数性质、解决实际问题提供了有力工具。',
'[
  "Δ > 0：两个交点（两个不等实根）",
  "Δ = 0：一个交点（两个相等实根）",
  "Δ < 0：无交点（无实根）",
  "判别式是图象性质的代数判据",
  "建立了方程根与函数零点的联系"
]',
'[
  {
    "name": "判别式公式",
    "formula": "Δ = b² - 4ac",
    "description": "二次方程判别式"
  },
  {
    "name": "交点个数判定",
    "formula": "Δ > 0 ⇒ 2个交点；Δ = 0 ⇒ 1个交点；Δ < 0 ⇒ 0个交点",
    "description": "根据判别式判断交点个数"
  },
  {
    "name": "几何意义",
    "formula": "交点个数反映抛物线与x轴的位置关系",
    "description": "判别式的几何解释"
  }
]',
'[
  {
    "title": "综合应用判别式",
    "problem": "已知抛物线y = kx² - 2x + 1与x轴有且仅有一个交点，求k的值",
    "solution": "抛物线与x轴有且仅有一个交点，说明Δ = 0。计算：Δ = (-2)² - 4k×1 = 4 - 4k = 0，解得k = 1",
    "analysis": "利用判别式的几何意义建立代数方程"
  }
]',
'[
  {
    "concept": "判别式理论",
    "explanation": "判别式决定方程根的性质",
    "example": "实根、重根、复根的判断"
  },
  {
    "concept": "几何直观",
    "explanation": "抛物线与坐标轴的位置关系",
    "example": "相交、相切、相离三种情况"
  },
  {
    "concept": "参数讨论",
    "explanation": "含参数的二次函数性质分析",
    "example": "根据交点个数确定参数范围"
  }
]',
'[
  "判别式计算错误",
  "不理解判别式的几何意义",
  "参数讨论时分类不完整",
  "混淆判别式与其他公式"
]',
'[
  "公式记忆：Δ = b² - 4ac",
  "几何理解：交点个数的几何意义",
  "分类讨论：根据Δ的正负分类",
  "参数分析：利用判别式确定参数"
]',
'{
  "emphasis": ["判别式应用", "参数分析"],
  "application": ["函数性质", "参数讨论"],
  "connection": ["与方程理论的关系", "函数图象分析"]
}',
'{
  "emphasis": ["代数几何", "参数理论"],
  "application": ["函数族", "参数优化"],
  "connection": ["与复数的关系", "代数基本定理"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G9S1_CH22_018: 信息技术应用：探索二次函数的性质
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S1_CH22_018'),
'利用计算机软件和图形计算器探索二次函数性质，培养信息技术与数学学习的融合能力',
'信息技术为数学学习提供了强大的工具，特别是在探索函数性质方面。通过几何画板、GeoGebra、图形计算器等软件，学生可以动态地观察参数变化对二次函数图象的影响，直观地理解函数性质。这种探索式学习方式不仅提高了学习效率，更培养了学生的数学探究能力和信息素养。信息技术的应用使抽象的数学概念变得具体可感，为深度学习提供了新的途径。',
'[
  "利用软件动态展示函数图象变化",
  "通过参数调节探索函数性质规律",
  "培养信息技术与数学融合的能力",
  "提高数学探究和发现的效率",
  "为复杂问题提供可视化分析工具"
]',
'[
  {
    "name": "动态几何软件",
    "formula": "GeoGebra、几何画板等",
    "description": "专业的数学探索软件"
  },
  {
    "name": "图形计算器",
    "formula": "TI-84、卡西欧fx等",
    "description": "便携式图形分析工具"
  },
  {
    "name": "参数化探索",
    "formula": "y = a(x-h)² + k中参数的动态变化",
    "description": "观察参数对图象的影响"
  }
]',
'[
  {
    "title": "技术探索活动",
    "problem": "使用GeoGebra探索y = a(x-h)² + k中参数a、h、k对图象的影响",
    "solution": "创建滑动条控制参数a、h、k，观察：a控制开口方向和大小，h控制左右平移，k控制上下平移。通过动态变化加深理解",
    "analysis": "信息技术使抽象的参数概念变得直观可感"
  }
]',
'[
  {
    "concept": "数字化学习",
    "explanation": "利用技术工具辅助数学学习",
    "example": "动态图象、参数调节、数据分析"
  },
  {
    "concept": "探究式学习",
    "explanation": "通过观察和实验发现数学规律",
    "example": "改变参数观察图象变化"
  },
  {
    "concept": "可视化分析",
    "explanation": "用图形直观展示数学关系",
    "example": "函数图象的动态演示"
  }
]',
'[
  "过度依赖技术忽视基本概念",
  "不会将技术发现转化为数学结论",
  "技术操作不熟练影响探索效果",
  "缺乏对技术结果的批判性思考"
]',
'[
  "技术辅助：技术是工具，概念是核心",
  "观察归纳：从现象中发现数学规律",
  "操作熟练：掌握基本的软件操作",
  "批判思维：对技术结果进行理性分析"
]',
'{
  "emphasis": ["技术应用", "探究学习"],
  "application": ["数学实验", "规律发现"],
  "connection": ["与传统方法的结合", "现代数学教育"]
}',
'{
  "emphasis": ["计算机辅助", "数据可视化"],
  "application": ["科学计算", "数据分析"],
  "connection": ["与计算机科学的关系", "数字化时代技能"]
}',
'1.0', true, 'zh-CN', 4.5, 'approved'),

-- MATH_G9S1_CH22_019: 用二次函数解决最值问题
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S1_CH22_019'),
'二次函数的最值性质为解决实际生活中的最优化问题提供了有效的数学模型',
'二次函数最值问题是数学建模的重要应用，它将抽象的数学知识与具体的实际问题紧密结合。在商业、工程、农业等各个领域，许多最优化问题都可以建立二次函数模型来解决。关键在于正确理解问题情境，建立合适的函数关系，然后利用二次函数的最值性质求解。这类问题不仅考查数学知识的掌握，更重要的是培养学生的建模思维和解决实际问题的能力。',
'[
  "建立二次函数模型是关键步骤",
  "利用顶点坐标求最值",
  "注意自变量的实际意义和取值范围",
  "验证结果的实际合理性",
  "培养数学建模和应用能力"
]',
'[
  {
    "name": "建模步骤",
    "formula": "理解问题→设变量→建立函数→求最值→检验结果",
    "description": "解决实际问题的基本流程"
  },
  {
    "name": "最值求法",
    "formula": "利用顶点坐标(-b/(2a), (4ac-b²)/(4a))",
    "description": "二次函数最值的计算方法"
  },
  {
    "name": "定义域约束",
    "formula": "根据实际意义确定自变量范围",
    "description": "实际问题中的变量限制"
  }
]',
'[
  {
    "title": "面积最值问题",
    "problem": "用长为20m的篱笆围成一个矩形菜园，其中一边靠墙不需篱笆。如何围才能使面积最大？",
    "solution": "设垂直于墙的边长为x米，则平行于墙的边长为(20-2x)米。面积S = x(20-2x) = -2x² + 20x。当x = 5时，S最大 = 50平方米",
    "analysis": "关键是正确建立面积与边长的二次函数关系"
  }
]',
'[
  {
    "concept": "数学建模",
    "explanation": "用数学方法解决实际问题",
    "example": "将实际情况抽象为数学模型"
  },
  {
    "concept": "最优化思想",
    "explanation": "在约束条件下寻求最佳方案",
    "example": "成本最低、利润最大、效率最高"
  },
  {
    "concept": "实际应用",
    "explanation": "数学知识在生活中的具体运用",
    "example": "工程设计、经济决策、资源配置"
  }
]',
'[
  "建模时忽视实际约束条件",
  "计算最值时不考虑定义域",
  "结果不符合实际意义",
  "混淆数学解与实际解"
]',
'[
  "情境理解：准确把握问题的实际背景",
  "变量设定：选择合适的自变量和因变量",
  "约束考虑：注意实际问题的限制条件",
  "结果验证：检查解的实际合理性"
]',
'{
  "emphasis": ["数学建模", "实际应用"],
  "application": ["优化问题", "实际决策"],
  "connection": ["与实际生活的联系", "数学应用能力"]
}',
'{
  "emphasis": ["最优化理论", "应用数学"],
  "application": ["工程优化", "经济分析"],
  "connection": ["与运筹学的关系", "实际问题建模"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'),

-- MATH_G9S1_CH22_020: 利润最大问题
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S1_CH22_020'),
'利润最大问题是二次函数在经济领域的典型应用，体现了数学与经济生活的密切联系',
'利润最大问题是最优化理论在商业实践中的重要应用，它将经济学中的边际分析与数学中的函数极值理论完美结合。在实际商业活动中，商品价格与销量往往存在反向关系，这种关系通常可以用一次函数来描述，进而得到二次函数形式的利润函数。通过求利润函数的最大值，可以确定最优的定价策略。这类问题培养学生的经济思维和数学应用能力，体现了数学在现代社会中的重要价值。',
'[
  "利润 = 销售收入 - 总成本",
  "价格与销量通常成反比关系",
  "利润函数一般为二次函数",
  "最大利润对应顶点的纵坐标",
  "最优价格对应顶点的横坐标"
]',
'[
  {
    "name": "利润函数",
    "formula": "利润 = (售价 - 成本) × 销量",
    "description": "利润的基本计算公式"
  },
  {
    "name": "价量关系",
    "formula": "销量 = a - bp（p为价格）",
    "description": "价格与销量的线性关系"
  },
  {
    "name": "最优决策",
    "formula": "利用顶点坐标确定最优价格和最大利润",
    "description": "二次函数最值的经济意义"
  }
]',
'[
  {
    "title": "商品定价策略",
    "problem": "某商品成本20元/件，当售价30元时日销量100件，每涨价1元日销量减少5件。如何定价使利润最大？",
    "solution": "设售价为(30+x)元，销量为(100-5x)件。利润L = (30+x-20)(100-5x) = (10+x)(100-5x) = -5x² + 50x + 1000。当x = 5时利润最大，最优售价35元，最大利润1125元",
    "analysis": "通过建立二次函数模型找到最优定价策略"
  }
]',
'[
  {
    "concept": "经济模型",
    "explanation": "用数学描述经济现象",
    "example": "供需关系、成本收益分析"
  },
  {
    "concept": "边际分析",
    "explanation": "研究变量变化对结果的影响",
    "example": "边际收益、边际成本"
  },
  {
    "concept": "最优化决策",
    "explanation": "在约束条件下寻求最佳策略",
    "example": "利润最大、成本最小"
  }
]',
'[
  "忽视成本和收入的区别",
  "价量关系建立错误",
  "不理解最值的经济含义",
  "忽视实际约束条件"
]',
'[
  "概念区分：收入、成本、利润的关系",
  "关系建立：价格与销量的函数关系",
  "经济意义：数学结果的经济解释",
  "现实检验：结果的合理性分析"
]',
'{
  "emphasis": ["经济应用", "最优决策"],
  "application": ["商业策略", "经济分析"],
  "connection": ["与经济学的关系", "商业数学应用"]
}',
'{
  "emphasis": ["运筹优化", "经济数学"],
  "application": ["企业管理", "经济建模"],
  "connection": ["与管理科学的关系", "决策理论"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'),

-- MATH_G9S1_CH22_021: 抛物运动问题
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S1_CH22_021'),
'抛物运动问题是二次函数在物理学中的重要应用，展示了数学与自然科学的内在联系',
'抛物运动是物理学中的经典问题，其运动轨迹恰好是抛物线，可以用二次函数来描述。在忽略空气阻力的理想情况下，抛射物体的运动轨迹方程为二次函数形式。通过建立数学模型，可以求解射程、最大高度、飞行时间等问题。这类问题不仅体现了数学在自然科学中的应用价值，也培养学生的跨学科思维能力，加深对数学与物理统一性的理解。',
'[
  "抛物运动轨迹是抛物线",
  "高度与时间的关系为二次函数",
  "最大高度对应函数的最值",
  "落地时间对应函数的零点",
  "体现数学与物理的统一性"
]',
'[
  {
    "name": "抛物运动方程",
    "formula": "h = h₀ + v₀t - ½gt²",
    "description": "高度关于时间的二次函数"
  },
  {
    "name": "最大高度",
    "formula": "h_max = h₀ + v₀²/(2g)",
    "description": "抛物运动的最大高度"
  },
  {
    "name": "飞行时间",
    "formula": "通过解方程h = 0求得",
    "description": "物体从抛出到落地的时间"
  }
]',
'[
  {
    "title": "抛物运动分析",
    "problem": "从地面以初速度20m/s竖直向上抛出一个球，求球的最大高度和飞行时间（g = 10m/s²）",
    "solution": "高度方程：h = 20t - 5t²。最大高度：当t = 2s时，h_max = 20m。飞行时间：令h = 0，得20t - 5t² = 0，解得t = 4s",
    "analysis": "利用二次函数性质解决物理问题"
  }
]',
'[
  {
    "concept": "物理建模",
    "explanation": "用数学描述物理现象",
    "example": "运动方程、力学定律"
  },
  {
    "concept": "跨学科应用",
    "explanation": "数学在其他学科中的应用",
    "example": "物理、化学、生物中的数学模型"
  },
  {
    "concept": "理想化模型",
    "explanation": "忽略次要因素的简化模型",
    "example": "忽略空气阻力的抛物运动"
  }
]',
'[
  "混淆数学模型与实际现象",
  "不理解理想化假设的意义",
  "物理量与数学变量对应错误",
  "忽视单位换算问题"
]',
'[
  "模型理解：数学模型是现实的近似",
  "假设明确：理解模型的适用条件",
  "物理意义：数学结果的物理解释",
  "单位注意：保持单位的一致性"
]',
'{
  "emphasis": ["物理应用", "跨学科联系"],
  "application": ["物理建模", "运动分析"],
  "connection": ["与物理学的关系", "自然科学应用"]
}',
'{
  "emphasis": ["数学物理", "动力学"],
  "application": ["工程力学", "航天技术"],
  "connection": ["与微积分的关系", "经典力学"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G9S1_CH22_022: 阅读与思考：推测滑行距离与滑行时间的关系
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S1_CH22_022'),
'通过分析实际数据探索滑行距离与时间的函数关系，培养数据分析和数学建模能力',
'这是一个结合实际数据的数学探究活动，通过分析滑行距离与时间的关系，引导学生从数据中发现规律，建立数学模型。在实际的滑行过程中，由于摩擦力的作用，距离与时间的关系并非简单的线性关系，可能呈现二次函数特征。这种基于数据的探究活动，不仅培养学生的观察分析能力，更重要的是让学生体验从现象到规律、从数据到模型的科学研究过程。',
'[
  "基于实际数据的数学探究",
  "培养从数据中发现规律的能力",
  "体验数学建模的完整过程",
  "理解数学与实际问题的联系",
  "发展科学研究的思维方法"
]',
'[
  {
    "name": "数据分析",
    "formula": "观察数据→寻找规律→建立模型",
    "description": "从数据到模型的基本流程"
  },
  {
    "name": "函数拟合",
    "formula": "用函数模型拟合实验数据",
    "description": "数学建模的重要方法"
  },
  {
    "name": "模型验证",
    "formula": "用新数据检验模型的准确性",
    "description": "科学研究的重要环节"
  }
]',
'[
  {
    "title": "数据分析与建模",
    "problem": "根据滑行实验数据：时间1s距离2m，时间2s距离6m，时间3s距离12m，推测距离与时间的关系",
    "solution": "观察数据规律：s = 2t²。验证：当t=1时s=2，当t=2时s=8≠6。重新分析可能是s = t² + t的关系",
    "analysis": "通过数据分析建立合适的函数模型"
  }
]',
'[
  {
    "concept": "实验数学",
    "explanation": "通过实验数据进行数学研究",
    "example": "收集数据、分析规律、建立模型"
  },
  {
    "concept": "归纳推理",
    "explanation": "从特殊到一般的推理方法",
    "example": "从有限数据推测一般规律"
  },
  {
    "concept": "模型检验",
    "explanation": "验证模型的正确性和适用性",
    "example": "用新数据检验建立的模型"
  }
]',
'[
  "盲目套用已知函数模型",
  "不注意数据的准确性",
  "缺乏模型验证环节",
  "忽视实际条件的影响"
]',
'[
  "数据观察：仔细观察数据的变化规律",
  "模型选择：根据数据特点选择合适模型",
  "验证检验：用新数据检验模型的准确性",
  "批判思维：对结果进行合理性分析"
]',
'{
  "emphasis": ["数据分析", "数学建模"],
  "application": ["实验数学", "统计分析"],
  "connection": ["与统计学的关系", "科学研究方法"]
}',
'{
  "emphasis": ["数据科学", "计算建模"],
  "application": ["数据挖掘", "机器学习"],
  "connection": ["与人工智能的关系", "大数据分析"]
}',
'1.0', true, 'zh-CN', 4.6, 'approved'),

-- MATH_G9S1_CH22_023: 几何图形的面积问题
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S1_CH22_023'),
'运用二次函数知识解决几何图形的最大面积问题，体现数学在几何中的应用',
'几何图形的面积问题是二次函数在几何领域的重要应用。通过建立适当的坐标系或运用几何量之间的函数关系，可以将几何问题转化为二次函数的最值问题。这类问题不仅考查学生对二次函数性质的掌握，更重要的是培养数形结合的思想和几何直观。解决过程中需要合理建立函数模型，运用二次函数的最值性质，并结合几何条件进行综合分析。',
'[
  "几何问题的函数化处理",
  "建立坐标系解决几何最值问题",
  "运用二次函数性质求几何图形最大面积",
  "数形结合思想的具体体现",
  "几何直观与代数运算的结合"
]',
'[
  {
    "name": "面积函数",
    "formula": "S = f(x) (二次函数形式)",
    "description": "几何图形面积与变量的函数关系"
  },
  {
    "name": "约束条件",
    "formula": "结合几何条件确定自变量取值范围",
    "description": "几何约束对函数定义域的限制"
  },
  {
    "name": "最值求解",
    "formula": "利用二次函数最值性质求最大面积",
    "description": "在给定约束下求最优解"
  }
]',
'[
  {
    "title": "矩形最大面积问题",
    "problem": "用长为20m的篱笆围成一面靠墙的矩形菜园，墙足够长，求菜园的最大面积",
    "solution": "设垂直于墙的边长为x米，则平行于墙的边长为(20-2x)米。面积S=x(20-2x)=-2x²+20x=-2(x-5)²+50。当x=5时，面积最大值为50平方米",
    "analysis": "通过建立面积的二次函数模型求解最值问题"
  },
  {
    "title": "三角形最大面积问题", 
    "problem": "在直角坐标系中，点P在抛物线y=4-x²上，求△OPA面积的最大值(A为抛物线与x轴正半轴交点)",
    "solution": "A(2,0)，设P(t,4-t²)，则S=½×2×|4-t²|=|4-t²|。当t=0时，面积最大值为4",
    "analysis": "利用坐标表示面积，结合二次函数求最值"
  }
]',
'[
  {
    "concept": "数形结合",
    "explanation": "将几何问题转化为代数问题求解",
    "example": "建立坐标系，用函数表示几何量"
  },
  {
    "concept": "约束优化",
    "explanation": "在给定条件下求最优解",
    "example": "在周长固定的条件下求最大面积"
  },
  {
    "concept": "函数建模",
    "explanation": "用函数关系描述几何量间的关系",
    "example": "面积与边长的函数关系"
  }
]',
'[
  "忽视几何约束条件",
  "建立错误的函数关系",
  "不考虑自变量的实际意义",
  "计算错误或理解错误"
]',
'[
  "审题分析：理解几何条件和要求",
  "建模思考：建立合适的函数关系",
  "约束考虑：确定自变量的取值范围",
  "验证检查：检验结果的合理性"
]',
'{
  "emphasis": ["几何应用", "面积计算"],
  "application": ["实际测量", "优化设计"],
  "connection": ["与几何学的关系", "空间想象能力"]
}',
'{
  "emphasis": ["工程优化", "设计问题"],
  "application": ["建筑设计", "工程规划"],
  "connection": ["与工程学的关系", "实际应用能力"]
}',
'1.0', true, 'zh-CN', 4.6, 'approved'),

-- MATH_G9S1_CH22_024: 数学活动：二次函数的探索
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S1_CH22_024'),
'通过探索性数学活动，深入研究二次函数的性质，发展数学探究能力',
'这是一个综合性的数学探索活动，通过动手操作、观察猜想、理论验证等方式，让学生深入探索二次函数的各种性质。活动可以包括利用图形计算器或几何画板等工具，探索参数变化对二次函数图象的影响；通过实验观察，发现二次函数与实际现象的联系；设计小组合作活动，让学生在交流中加深对二次函数的理解。这种探索式学习有助于培养学生的创新思维和数学探究能力。',
'[
  "探索性学习方式的体验",
  "利用技术工具辅助数学学习",
  "通过实验观察发现数学规律",
  "培养数学探究和创新能力",
  "在合作中深化数学理解"
]',
'[
  {
    "name": "参数探索",
    "formula": "观察a、h、k对y=a(x-h)²+k图象的影响",
    "description": "通过改变参数探索图象变化规律"
  },
  {
    "name": "性质验证",
    "formula": "用具体例子验证二次函数的一般性质",
    "description": "从特殊到一般的探索过程"
  },
  {
    "name": "实际联系",
    "formula": "寻找生活中的二次函数现象",
    "description": "发现数学与现实的联系"
  }
]',
'[
  {
    "title": "图象变换探索",
    "problem": "利用图形工具，探索二次函数y=ax²图象如何通过平移得到y=a(x-h)²+k的图象",
    "solution": "1. 画出y=x²的图象；2. 分别画出y=x²+2、y=(x-1)²、y=(x-1)²+2的图象；3. 观察图象间的关系；4. 总结平移规律：左加右减，上加下减",
    "analysis": "通过直观观察发现图象变换的一般规律"
  },
  {
    "title": "实际现象探索",
    "problem": "收集生活中符合二次函数模型的现象，分析其数学特征",
    "solution": "收集实例：抛物运动、利润模型、面积问题等；分析共同特征：都存在最值；建立数学模型：确定函数关系式；验证模型：检验预测结果",
    "analysis": "从实际现象中抽象出数学模型"
  }
]',
'[
  {
    "concept": "探究学习",
    "explanation": "通过主动探索获得知识和能力",
    "example": "观察、猜想、验证、总结"
  },
  {
    "concept": "技术应用",
    "explanation": "利用现代技术工具辅助数学学习",
    "example": "图形计算器、几何画板、数学软件"
  },
  {
    "concept": "合作交流",
    "explanation": "在团队合作中分享思考和发现",
    "example": "小组讨论、成果展示、同伴互评"
  }
]',
'[
  "缺乏主动探索精神",
  "过分依赖技术工具",
  "不善于从现象中抽象规律",
  "缺乏合作交流意识"
]',
'[
  "主动探究：培养好奇心和探索精神",
  "工具运用：合理利用技术辅助学习",
  "观察思考：从现象中发现数学规律",
  "交流分享：在合作中完善认知"
]',
'{
  "emphasis": ["探索活动", "技术应用"],
  "application": ["研究性学习", "实践活动"],
  "connection": ["与信息技术的关系", "探究能力发展"]
}',
'{
  "emphasis": ["创新思维", "研究能力"],
  "application": ["科学研究", "技术创新"],
  "connection": ["与科学探索的关系", "创新人才培养"]
}',
'1.0', true, 'zh-CN', 4.7, 'approved');

-- ============================================
-- 批量插入语句结束
-- ============================================
