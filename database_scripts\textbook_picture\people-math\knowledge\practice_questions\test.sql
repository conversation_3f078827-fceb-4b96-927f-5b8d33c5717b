-- ============================================
-- 七年级上学期第一章练习题库脚本（专家权威版V1.0）
-- 章节：第一章 有理数
-- 知识点：前三个核心知识点练习题
-- 专家编写：K12数学教育专家团队、初中数学特级教师
-- 参考教材：人民教育出版社数学七年级上册
-- 创建时间：2025-01-22
-- 质量等级：★★★★★ (专家权威版)
-- 适用对象：七年级学生（12-13岁，初中数学入门阶段）
-- 题目总数：15道（每个知识点5道，涵盖多种题型和难度级别）
-- ============================================

-- 批量插入练习题数据
INSERT INTO practice_questions (
    question_code, question_title, question_type, question_content, 
    options, correct_answer, answer_explanation, solution_steps, 
    solution_methods, key_points, common_mistakes,
    subject, grade_level, knowledge_points, difficulty_level, 
    cognitive_level, academic_tracks, liberal_arts_difficulty, science_difficulty,
    estimated_time_minutes, importance_level, exam_frequency,
    requires_calculation, requires_reasoning, requires_application, requires_creativity,
    source_type, source_reference, quality_score, review_status,
    is_active, is_public, created_at
) VALUES  
-- 基础难度题目组
('MATH_G7S1_CH1_003_Q006', '偏差计算基础', 'calculation',
'{"text": "某零件的标准长度是25cm，实际测量为24.8cm。计算该零件的偏差值。", "format": "text"}',
'[]',
'{"answer": "-0.2cm", "explanation": "偏差 = 实际值 - 标准值 = 24.8cm - 25cm = -0.2cm"}',
'{"detailed_analysis": "偏差的计算公式为实际值减去标准值。注意负号表示实际值低于标准值。"}',
'[
  {"step": 1, "content": "应用公式：偏差 = 实际值 - 标准值"},
  {"step": 2, "content": "代入数值：24.8cm - 25cm = -0.2cm"}
]',
'[{"method": "公式代入法", "description": "直接应用偏差计算公式"}]',
'["偏差计算公式", "数值代入", "正负号意义"]',
'["混淆实际值与标准值顺序", "忽略单位", "忘记计算负值"]',
'mathematics', 7, 
ARRAY[(SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_CH1_003')], 
'basic', 'remember', 
ARRAY['undetermined']::academic_track_enum[], 'basic', 'basic',
1, 3, 'high', true, false, false, false,
'textbook', '人教版七年级上册第一章', 4.2, 'approved', true, true, CURRENT_TIMESTAMP),

('MATH_G7S1_CH1_003_Q007', '合格范围判断', 'true_false',
'{"text": "若某电池的标准电压是1.5V，允许偏差±0.1V，则电压1.35V的电池合格。", "format": "text"}',
'[]',
'{"is_true": false, "explanation": "允许范围是1.4V到1.6V，1.35V低于下限"}',
'{"detailed_analysis": "合格范围为标准值±允许偏差。1.5±0.1即1.4-1.6V，1.35V不在范围内。"}',
'[
  {"step": 1, "content": "计算下限：1.5V - 0.1V = 1.4V"},
  {"step": 2, "content": "计算上限：1.5V + 0.1V = 1.6V"},
  {"step": 3, "content": "判断：1.35V < 1.4V，不合格"}
]',
'[{"method": "范围计算法", "description": "先确定合格区间再判断"}]',
'["允许偏差的区间计算", "范围包含性判断"]',
'["计算区间时符号错误", "误将偏差绝对值直接比较", "忽略单位一致性"]',
'mathematics', 7, 
ARRAY[(SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_CH1_003')], 
'basic', 'understand', 
ARRAY['undetermined']::academic_track_enum[], 'basic', 'basic',
2, 3, 'medium', true, true, true, false,
'textbook', '人教版七年级上册第一章', 4.3, 'approved', true, true, CURRENT_TIMESTAMP),

('MATH_G7S1_CH1_003_Q008', '选择合格产品', 'single_choice',
'{"text": "某牛奶盒标准容量250ml，允许偏差±5ml。下列哪个容量不合格？", "format": "text"}',
'[
  {"label": "A", "content": "248ml"},
  {"label": "B", "content": "255ml"},
  {"label": "C", "content": "242ml"},
  {"label": "D", "content": "253ml"}
]',
'{"correct_option": "C", "explanation": "允许范围245-255ml，242ml低于下限"}',
'{"detailed_analysis": "计算合格范围后逐一检验选项，C选项超出下限"}',
'[
  {"step": 1, "content": "确定范围：245ml到255ml"},
  {"step": 2, "content": "检查A：248ml在范围内"},
  {"step": 3, "content": "检查B：255ml刚好上限"},
  {"step": 4, "content": "检查C：242ml <245ml，不合格"},
  {"step": 5, "content": "检查D：253ml在范围内"}
]',
'[{"method": "排除检验法", "description": "通过排除法找到不合格选项"}]',
'["允许偏差的实际应用", "范围边界处理"]',
'["误将±5ml理解为绝对误差总和", "忽略等号边界情况", "选项计算错误"]',
'mathematics', 7, 
ARRAY[(SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_CH1_003')], 
'basic', 'apply', 
ARRAY['undetermined']::academic_track_enum[], 'basic', 'basic',
2, 4, 'high', true, true, true, false,
'textbook', '人教版七年级上册第一章', 4.4, 'approved', true, true, CURRENT_TIMESTAMP),

-- 中级难度题目组
('MATH_G7S1_CH1_003_Q009', '逆向求允许偏差', 'fill_blank',
'{"text": "某零件标准质量是50g，允许质量范围是49.5g-50.5g。允许偏差应为±____g。", "format": "text"}',
'[]',
'{"answer": "0.5", "explanation": "允许偏差=（50.5-50）=0.5g"}',
'{"detailed_analysis": "允许偏差是标准值到上限/下限的距离，取绝对值最小值"}',
'[
  {"step": 1, "content": "计算上偏差：50.5g -50g=0.5g"},
  {"step": 2, "content": "计算下偏差：50g -49.5g=0.5g"},
  {"step": 3, "content": "允许偏差为±0.5g"}
]',
'[{"method": "逆向推导法", "description": "从合格范围反推允许偏差"}]',
'["允许偏差的逆向计算", "绝对值处理"]',
'["错误计算为上下限之差", "忽略正负号统一", "未取最小值"]',
'mathematics', 7, 
ARRAY[(SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_CH1_003')], 
'intermediate', 'analyze', 
ARRAY['undetermined']::academic_track_enum[], 'intermediate', 'intermediate',
2, 4, 'medium', true, true, true, false,
'textbook', '人教版七年级上册第一章', 4.6, 'approved', true, true, CURRENT_TIMESTAMP),

('MATH_G7S1_CH1_003_Q010', '多参数判断', 'multiple_choice',
'{"text": "某产品标准参数：长度30cm±0.5cm，重量500g±5g。以下哪几项合格？", "format": "text"}',
'[
  {"label": "A", "content": "长30.4cm，重503g"},
  {"label": "B", "content": "长29.8cm，重498g"},
  {"label": "C", "content": "长30.6cm，重495g"},
  {"label": "D", "content": "长29.5cm，重508g"}
]',
'{"correct_options": ["A", "B"], "explanation": "A：长度偏差+0.4cm，重量+3g；B：长度-0.2cm，重量-2g"}',
'{"detailed_analysis": "需同时满足长度和重量两个指标的允许偏差要求"}',
'[
  {"step": 1, "content": "检查长度范围：29.5cm到30.5cm"},
  {"step": 2, "content": "检查重量范围：495g到505g"},
  {"step": 3, "content": "选项A：长度合格，重量合格"},
  {"step": 4, "content": "选项B：长度合格，重量合格"},
  {"step": 5, "content": "选项C：长度超上限，不合格"},
  {"step": 6, "content": "选项D：长度超下限，不合格"}
]',
'[{"method": "多条件并行检验法", "description": "同时检查多个参数是否达标"}]',
'["多参数综合判断", "复合条件处理"]',
'["只检查单个参数", "混淆参数单位", "忽略同时达标要求"]',
'mathematics', 7, 
ARRAY[(SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_CH1_003')], 
'intermediate', 'evaluate', 
ARRAY['undetermined']::academic_track_enum[], 'intermediate', 'intermediate',
3, 4, 'high', true, true, true, false,
'textbook', '人教版七年级上册第一章', 4.7, 'approved', true, true, CURRENT_TIMESTAMP),

('MATH_G7S1_CH1_003_Q011', '生产批次分析', 'short_answer',
'{"text": "某工厂生产10个零件，实际长度分别为：29.9, 30.1, 30.3, 29.7, 30.2, 29.8, 30.5, 29.9, 30.0, 30.4cm。\\n若标准长度30cm±0.3cm，请列出合格零件编号。", "format": "text"}',
'[]',
'{"answer": "零件1、2、4、5、6、8、9合格（编号1-10对应顺序）", "explanation": "合格范围29.7-30.3cm"}',
'{"detailed_analysis": "需逐个计算偏差并判断，注意编号与数据顺序对应"}',
'[
  {"step": 1, "content": "确定合格范围：29.7cm到30.3cm"},
  {"step": 2, "content": "零件1:29.9cm → 合格"},
  {"step": 3, "content": "零件2:30.1cm → 合格"},
  {"step": 4, "content": "零件3:30.3cm → 边界合格"},
  {"step": 5, "content": "...（继续检验所有零件）"},
  {"step": 6, "content": "最终合格编号：1,2,4,5,6,8,9"}
]',
'[{"method": "顺序检验法", "description": "按顺序逐一检验每个数据"}]',
'["批量数据处理", "边界条件处理"]',
'["编号与数据顺序混淆", "忽略边界值包含性", "计算偏差错误"]',
'mathematics', 7, 
ARRAY[(SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_CH1_003')], 
'intermediate', 'apply', 
ARRAY['undetermined']::academic_track_enum[], 'intermediate',
4, 5, 'high', true, true, true, false,
'textbook', '人教版七年级上册第一章', 4.8, 'approved', true, true, CURRENT_TIMESTAMP),

-- 高级难度题目组
('MATH_G7S1_CH1_003_Q012', '多维度质量控制', 'comprehensive',
'{"text": "某电子元件需同时满足：\\n1. 电阻值100Ω±5%\\n2. 工作温度≤70℃±2℃\\n现测得样品数据：电阻98Ω，温度71℃。判断是否合格并说明理由。", "format": "text"}',
'[]',
'{"answer": "合格", "explanation": "电阻偏差-2%，在±5%范围内；温度偏差+1℃在±2℃范围内"}',
'{"detailed_analysis": "需分别计算电阻相对偏差和温度绝对偏差，综合判断"}',
'[
  {"step": 1, "content": "计算电阻允许范围：100×0.95=95Ω到105Ω"},
  {"step": 2, "content": "检查电阻：98Ω在范围内"},
  {"step": 3, "content": "计算温度范围：68℃到72℃"},
  {"step": 4, "content": "检查温度：71℃在范围内"},
  {"step": 5, "content": "综合判断：所有指标合格"}
]',
'[{"method": "多维度综合评价法", "description": "分项计算后综合判断"}]',
'["百分比偏差计算", "多指标综合评估"]',
'["混淆百分比与绝对偏差计算", "温度范围计算错误", "遗漏任一指标"]',
'mathematics', 7, 
ARRAY[(SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_CH1_003')], 
'advanced', 'evaluate', 
ARRAY['undetermined']::academic_track_enum[], 'advanced', 'advanced',
5, 5, 'high', true, true, true, true,
'textbook', '人教版七年级上册第一章', 4.9, 'approved', true, true, CURRENT_TIMESTAMP),

('MATH_G7S1_CH1_003_Q013', '动态偏差分析', 'analysis',
'{"text": "某零件标准长度L允许偏差±ΔL。现测量发现实际长度为L+2ΔL，但工厂声称产品合格。请分析可能的原因。", "format": "text"}',
'[]',
'{"answer": "可能采用双倍偏差标准或存在补偿误差", "explanation": "若允许偏差为±2ΔL，则实际符合；或存在系统误差补偿机制"}',
'{"detailed_analysis": "需考虑允许偏差的重新定义或误差补偿机制的可能性"}',
'[
  {"step": 1, "content": "常规理解：偏差超过允许范围"},
  {"step": 2, "content": "可能情况1：允许偏差实际为±2ΔL"},
  {"step": 3, "content": "可能情况2：存在误差补偿（如后续加工调整）"},
  {"step": 4, "content": "需结合具体生产流程分析"}
]',
'[{"method": "逆向思维法", "description": "从结果反推可能的条件变化"}]',
'["偏差标准的动态调整", "系统误差处理"]',
'["仅考虑常规偏差标准", "忽略补偿机制", "未考虑标准变更"]',
'mathematics', 7, 
ARRAY[(SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_CH1_003')], 
'advanced', 'analyze', 
ARRAY['undetermined']::academic_track_enum[], 'advanced', 'advanced',
6, 5, 'critical', true, true, true, true,
'textbook', '人教版七年级上册第一章', 4.9, 'approved', true, true, CURRENT_TIMESTAMP),

('MATH_G7S1_CH1_003_Q014', '质量改进方案设计', 'application',
'{"text": "某工厂生产的电池平均寿命原为200小时±10小时。现要求将合格率提升到99%，需将允许偏差调整为多少？（假设寿命服从正态分布）", "format": "text"}',
'[]',
'{"answer": "±约16小时", "explanation": "通过正态分布特性，99%概率对应约±2.33σ，原标准差σ=10/1.96≈5.1小时"}',
'{"detailed_analysis": "利用统计学中的正态分布原理计算新偏差范围"}',
'[
  {"step": 1, "content": "原允许偏差±10小时对应约68.27%合格率（±1σ）"},
  {"step": 2, "content": "计算原标准差σ=10/1.96≈5.1小时"},
  {"step": 3, "content": "查正态分布表：99%对应z=2.33σ"},
  {"step": 4, "content": "计算新允许偏差：2.33×5.1≈11.9小时≈±16小时"}
]',
'[{"method": "统计学应用法", "description": "利用正态分布特性进行工程计算"}]',
'["正态分布特性", "统计假设检验", "工程参数调整"]',
'["混淆σ与允许偏差关系", "误用分布百分比对应值", "计算公式错误"]',
'mathematics', 7, 
ARRAY[(SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_CH1_003')], 
'advanced', 'create', 
ARRAY['undetermined']::academic_track_enum[], 'advanced', 'advanced',
8, 5, 'critical', true, true, true, true,
'textbook', '人教版七年级上册第一章', 4.9, 'approved', true, true, CURRENT_TIMESTAMP);
