import insert
import json

# 使用正确的格式：包含```json...```代码块
correct_format_data = """{
  "text": "```json\\n[\\n  {\\n    \\"question_code\\": \\"MATH_G7S1_CH1_001_Q001_2852\\",\\n    \\"question_title\\": \\"角度单位的基本概念\\",\\n    \\"question_type\\": \\"single_choice\\",\\n    \\"question_content\\": \\"{\\\\\\\"text\\\\\\\": \\\\\\\"下列角度单位换算正确的是\\\\\\\", \\\\\\\"format\\\\\\\": \\\\\\\"text\\\\\\"}\\",\\n    \\"question_images\\": \\"[]\\",\\n    \\"question_audio\\": \\"[]\\",\\n    \\"question_video\\": \\"[]\\",\\n    \\"options\\": \\"[{\\\\\\\"label\\\\\\\": \\\\\\\"A\\\\\\\", \\\\\\\"content\\\\\\\": \\\\\\\"1°=60′\\\\\\\"}, {\\\\\\\"label\\\\\\\": \\\\\\\"B\\\\\\\", \\\\\\\"content\\\\\\\": \\\\\\\"1′=60″\\\\\\\"}, {\\\\\\\"label\\\\\\\": \\\\\\\"C\\\\\\\", \\\\\\\"content\\\\\\\": \\\\\\\"1°=100′\\\\\\\"}, {\\\\\\\"label\\\\\\\": \\\\\\\"D\\\\\\\", \\\\\\\"content\\\\\\\": \\\\\\\"1′=100″\\\\\\\"}]\\",\\n    \\"correct_answer\\": \\"{\\\\\\\"explanation\\\\\\\": \\\\\\\"角度制采用六十进制，1°=60′，1′=60″，选项A和B均正确，但A更基础\\\\\\\", \\\\\\\"correct_option\\\\\\\": \\\\\\\"A\\\\\\\"}\\",\\n    \\"answer_explanation\\": \\"{\\\\\\\"detailed_analysis\\\\\\\": \\\\\\\"角度单位采用六十进制，1度等于60分，1分等于60秒。这个换算关系来源于古巴比伦的六十进制系统。\\\\\\\"}\\",\\n    \\"solution_steps\\": \\"[{\\\\\\\"step\\\\\\\": 1, \\\\\\\"content\\\\\\\": \\\\\\\"回忆角度单位换算规则\\\\\\\"}, {\\\\\\\"step\\\\\\\": 2, \\\\\\\"content\\\\\\\": \\\\\\\"1°=60′，1′=60″\\\\\\\"}, {\\\\\\\"step\\\\\\\": 3, \\\\\\\"content\\\\\\\": \\\\\\\"验证选项A的正确性\\\\\\\"}, {\\\\\\\"step\\\\\\\": 4, \\\\\\\"content\\\\\\\": \\\\\\\"选择正确答案A\\\\\\\"}]\\",\\n    \\"solution_methods\\": \\"[{\\\\\\\"method\\\\\\\": \\\\\\\"单位换算法\\\\\\\", \\\\\\\"description\\\\\\\": \\\\\\\"应用角度制的六十进制规则进行判断\\\\\\\"}]\\",\\n    \\"key_points\\": \\"[\\\\\\\"度分秒换算\\\\\\\", \\\\\\\"六十进制概念\\\\\\\", \\\\\\\"单位符号识别\\\\\\\"]\\",\\n    \\"common_mistakes\\": \\"[\\\\\\\"混淆十进制与六十进制\\\\\\\", \\\\\\\"误记分秒关系\\\\\\\"]\\",\\n    \\"subject\\": \\"mathematics\\",\\n    \\"grade_level\\": 7,\\n    \\"knowledge_points\\": \\"[2852]\\",\\n    \\"difficulty_level\\": \\"basic\\",\\n    \\"cognitive_level\\": \\"remember\\",\\n    \\"academic_tracks\\": \\"[\\\\\\"undetermined\\\\\\"]\\",\\n    \\"liberal_arts_difficulty\\": \\"basic\\",\\n    \\"science_difficulty\\": \\"basic\\",\\n    \\"estimated_time_minutes\\": 2,\\n    \\"importance_level\\": 5,\\n    \\"exam_frequency\\": \\"high\\",\\n    \\"requires_calculation\\": false,\\n    \\"requires_reasoning\\": true,\\n    \\"requires_application\\": false,\\n    \\"requires_creativity\\": false,\\n    \\"source_type\\": \\"textbook\\",\\n    \\"source_reference\\": \\"\\",\\n    \\"quality_score\\": 4.8,\\n    \\"review_status\\": \\"approved\\",\\n    \\"reviewer_id\\": null,\\n    \\"review_notes\\": null,\\n    \\"used_count\\": 0,\\n    \\"correct_rate\\": 0.0,\\n    \\"average_time_seconds\\": 0,\\n    \\"difficulty_rating\\": 0.0,\\n    \\"ai_generated\\": false,\\n    \\"ai_difficulty_prediction\\": null,\\n    \\"ai_tags\\": \\"[]\\",\\n    \\"is_active\\": true,\\n    \\"is_public\\": true,\\n    \\"created_by\\": null,\\n    \\"created_at\\": \\"2023-10-05T08:00:00Z\\",\\n    \\"updated_at\\": \\"2023-10-05T08:00:00Z\\"\\n  },\\n  {\\n    \\"question_code\\": \\"MATH_G7S1_CH1_001_Q002_2852\\",\\n    \\"question_title\\": \\"角度表示法判断\\",\\n    \\"question_type\\": \\"true_false\\",\\n    \\"question_content\\": \\"{\\\\\\\"text\\\\\\\": \\\\\\\"用数字表示角度时，$25^\\\\\\\\\\\\\\\\circ 30'$可以写作25.5°\\\\\\\", \\\\\\\"format\\\\\\\": \\\\\\\"text\\\\\\\"}\\\",\\n    \\"question_images\\": \\"[]\\",\\n    \\"question_audio\\": \\"[]\\",\\n    \\"question_video\\": \\"[]\\",\\n    \\"options\\": \\"[]\\",\\n    \\"correct_answer\\": \\"{\\\\\\\"is_true\\\\\\\": true, \\\\\\\"explanation\\\\\\\": \\\\\\\"30′=0.5°，因此25°30′=25.5°\\\\\\\"}\\\",\\n    \\"answer_explanation\\": \\"{\\\\\\\"detailed_analysis\\\\\\\": \\\\\\\"角度的十进制表示中，分转换为度需除以60。30′=30/60=0.5°，因此25°30′=25.5°是正确的转换。\\\\\\\"}\\\",\\n    \\"solution_steps\\": \\"[{\\\\\\\"step\\\\\\\": 1, \\\\\\\"content\\\\\\\": \\\\\\\"将30′转换为度\\\\\\\"}, {\\\\\\\"step\\\\\\\": 2, \\\\\\\"content\\\\\\\": \\\\\\\"$30' = \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\frac{30}{60} = 0.5°$\\\\\\\"}, {\\\\\\\"step\\\\\\\": 3, \\\\\\\"content\\\\\\\": \\\\\\\"计算总度数25°+0.5°=25.5°\\\\\\\"}, {\\\\\\\"step\\\\\\\": 4, \\\\\\\"content\\\\\\\": \\\\\\\"判断表述正确性\\\\\\\"}]\\",\\n    \\"solution_methods\\": \\"[{\\\\\\\"method\\\\\\\": \\\\\\\"单位换算法\\\\\\\", \\\\\\\"description\\\\\\\": \\\\\\\"将分转换为度进行验证\\\\\\\"}]\\",\\n    \\"key_points\\": \\"[\\\\\\\"角度转换\\\\\\\", \\\\\\\"十进制表示\\\\\\\", \\\\\\\"分秒运算\\\\\\\"]\\",\\n    \\"common_mistakes\\": \\"[\\\\\\\"误将30′视为0.3°\\\\\\\", \\\\\\\"忽略分秒转换规则\\\\\\\"]\\",\\n    \\"subject\\": \\"mathematics\\",\\n    \\"grade_level\\": 7,\\n    \\"knowledge_points\\": \\"[2852]\\",\\n    \\"difficulty_level\\": \\"basic\\",\\n    \\"cognitive_level\\": \\"understand\\",\\n    \\"academic_tracks\\": \\"[\\\\\\"undetermined\\\\\\"]\\",\\n    \\"liberal_arts_difficulty\\": \\"basic\\",\\n    \\"science_difficulty\\": \\"basic\\",\\n    \\"estimated_time_minutes\\": 3,\\n    \\"importance_level\\": 4,\\n    \\"exam_frequency\\": \\"medium\\",\\n    \\"requires_calculation\\": true,\\n    \\"requires_reasoning\\": true,\\n    \\"requires_application\\": false,\\n    \\"requires_creativity\\": false,\\n    \\"source_type\\": \\"textbook\\",\\n    \\"source_reference\\": \\"\\",\\n    \\"quality_score\\": 4.7,\\n    \\"review_status\\": \\"approved\\",\\n    \\"reviewer_id\\": null,\\n    \\"review_notes\\": null,\\n    \\"used_count\\": 0,\\n    \\"correct_rate\\": 0.0,\\n    \\"average_time_seconds\\": 0,\\n    \\"difficulty_rating\\": 0.0,\\n    \\"ai_generated\\": false,\\n    \\"ai_difficulty_prediction\\": null,\\n    \\"ai_tags\\": \\"[]\\",\\n    \\"is_active\\": true,\\n    \\"is_public\\": true,\\n    \\"created_by\\": null,\\n    \\"created_at\\": \\"2023-10-05T08:00:00Z\\",\\n    \\"updated_at\\": \\"2023-10-05T08:00:00Z\\"\\n  },\\n  {\\n    \\"question_code\\": \\"MATH_G7S1_CH1_001_Q003_2852\\",\\n    \\"question_title\\": \\"角度运算综合应用\\",\\n    \\"question_type\\": \\"single_choice\\",\\n    \\"question_content\\": \\"{\\\\\\\"text\\\\\\\": \\\\\\\"计算$45^\\\\\\\\\\\\\\\\circ 23' + 32^\\\\\\\\\\\\\\\\circ 47'$的结果是\\\\\\\", \\\\\\\"format\\\\\\\": \\\\\\\"text\\\\\\\"}\\\",\\n    \\"question_images\\": \\"[]\\",\\n    \\"question_audio\\": \\"[]\\",\\n    \\"question_video\\": \\"[]\\",\\n    \\"options\\": \\"[{\\\\\\\"label\\\\\\\": \\\\\\\"A\\\\\\\", \\\\\\\"content\\\\\\\": \\\\\\\"78°10′\\\\\\\"}, {\\\\\\\"label\\\\\\\": \\\\\\\"B\\\\\\\", \\\\\\\"content\\\\\\\": \\\\\\\"78°70′\\\\\\\"}, {\\\\\\\"label\\\\\\\": \\\\\\\"C\\\\\\\", \\\\\\\"content\\\\\\\": \\\\\\\"79°10′\\\\\\\"}, {\\\\\\\"label\\\\\\\": \\\\\\\"D\\\\\\\", \\\\\\\"content\\\\\\\": \\\\\\\"79°70′\\\\\\\"}]\\",\\n    \\"correct_answer\\": \\"{\\\\\\\"explanation\\\\\\\": \\\\\\\"度数相加45°+32°=77°，分数相加23′+47′=70′=1°10′，总和为78°10′\\\\\\\", \\\\\\\"correct_option\\\\\\\": \\\\\\\"A\\\\\\\"}\\\",\\n    \\"answer_explanation\\": \\"{\\\\\\\"detailed_analysis\\\\\\\": \\\\\\\"角度加法需分别计算度数和分数。当分数超过60′时需向度进位，23′+47′=70′=1°10′，最终结果为78°10′。\\\\\\\"}\\\",\\n    \\"solution_steps\\": \\"[{\\\\\\\"step\\\\\\\": 1, \\\\\\\"content\\\\\\\": \\\\\\\"分离度数和分数\\\\\\\"}, {\\\\\\\"step\\\\\\\": 2, \\\\\\\"content\\\\\\\": \\\\\\\"计算度数总和45°+32°=77°\\\\\\\"}, {\\\\\\\"step\\\\\\\": 3, \\\\\\\"content\\\\\\\": \\\\\\\"计算分数总和23′+47′=70′=1°10′\\\\\\\"}, {\\\\\\\"step\\\\\\\": 4, \\\\\\\"content\\\\\\\": \\\\\\\"合并结果77°+1°10′=78°10′\\\\\\\"}]\\",\\n    \\"solution_methods\\": \\"[{\\\\\\\"method\\\\\\\": \\\\\\\"分步计算法\\\\\\\", \\\\\\\"description\\\\\\\": \\\\\\\"分别处理度数和分数的加法运算\\\\\\\"}]\\",\\n    \\"key_points\\": \\"[\\\\\\\"角度加法\\\\\\\", \\\\\\\"进位规则\\\\\\\", \\\\\\\"单位转换\\\\\\\"]\\",\\n    \\"common_mistakes\\": \\"[\\\\\\\"忽略分数进位\\\\\\\", \\\\\\\"直接相加度数和分数\\\\\\\"]\\",\\n    \\"subject\\": \\"mathematics\\",\\n    \\"grade_level\\": 7,\\n    \\"knowledge_points\\": \\"[2852]\\",\\n    \\"difficulty_level\\": \\"basic\\",\\n    \\"cognitive_level\\": \\"apply\\",\\n    \\"academic_tracks\\": \\"[\\\\\\"undetermined\\\\\\"]\\",\\n    \\"liberal_arts_difficulty\\": \\"basic\\",\\n    \\"science_difficulty\\": \\"basic\\",\\n    \\"estimated_time_minutes\\": 4,\\n    \\"importance_level\\": 5,\\n    \\"exam_frequency\\": \\"high\\",\\n    \\"requires_calculation\\": true,\\n    \\"requires_reasoning\\": true,\\n    \\"requires_application\\": true,\\n    \\"requires_creativity\\": false,\\n    \\"source_type\\": \\"textbook\\",\\n    \\"source_reference\\": \\"\\",\\n    \\"quality_score\\": 4.7,\\n    \\"review_status\\": \\"approved\\",\\n    \\"reviewer_id\\": null,\\n    \\"review_notes\\": null,\\n    \\"used_count\\": 0,\\n    \\"correct_rate\\": 0.0,\\n    \\"average_time_seconds\\": 0,\\n    \\"difficulty_rating\\": 0.0,\\n    \\"ai_generated\\": false,\\n    \\"ai_difficulty_prediction\\": null,\\n    \\"ai_tags\\": \\"[]\\",\\n    \\"is_active\\": true,\\n    \\"is_public\\": true,\\n    \\"created_by\\": null,\\n    \\"created_at\\": \\"2023-10-05T08:00:00Z\\",\\n    \\"updated_at\\": \\"2023-10-05T08:00:00Z\\"\\n  }\\n]\\n```"
}"""

print("=== 正确格式测试 ===")
print("使用 ```json...``` 代码块格式")

try:
    print("1. 测试外层JSON解析...")
    outer_data = json.loads(correct_format_data)
    print(f"   ✅ 外层JSON解析成功，包含键: {list(outer_data.keys())}")
    
    print("2. 检查text字段...")
    text_content = outer_data.get('text', '')
    print(f"   text字段长度: {len(text_content)}")
    
    print("3. 查找JSON代码块...")
    import re
    pattern = r'```json(.*?)```'
    matches = re.findall(pattern, text_content, re.DOTALL)
    print(f"   找到 {len(matches)} 个JSON代码块")
    
    if matches:
        json_content = matches[0].strip()
        print(f"   JSON代码块长度: {len(json_content)}")
        
        # 验证JSON内容可以解析
        questions = json.loads(json_content)
        print(f"   ✅ JSON代码块解析成功，题目数量: {len(questions)}")
    
    print("4. 调用insert.main函数...")
    result = insert.main(correct_format_data)
    print("   ✅ insert.main执行成功")
    
    print("5. 检查返回结果...")
    print(f"   返回键: {list(result.keys())}")
    
    if 'insert_sql' in result:
        sql = result['insert_sql']
        print(f"   ✅ 生成SQL成功，长度: {len(sql)} 字符")
        
        # 保存SQL到文件
        with open('correct_format_insert.sql', 'w', encoding='utf-8') as f:
            f.write(sql)
        print("   ✅ SQL已保存到 correct_format_insert.sql")
        
        # 显示前几行SQL
        print("\n=== SQL预览 ===")
        lines = sql.split('\n')
        for i, line in enumerate(lines[:3]):
            print(f"第{i+1}行: {line[:120]}{'...' if len(line) > 120 else ''}")
            
    else:
        print("   ❌ 没有生成SQL")
        if 'parsed_json' in result:
            parsed = result['parsed_json']
            print(f"   parsed_json类型: {type(parsed)}")
            if isinstance(parsed, list):
                print(f"   题目数量: {len(parsed)}")
            
except Exception as e:
    print(f"❌ 错误: {e}")
    import traceback
    traceback.print_exc()

print("\n=== 测试完成 ===") 