import re
import json

def main(arg1: str) -> dict:
    # 首先尝试直接解析整个内容
    try:
        full_json = json.loads(arg1)
        if 'text' in full_json:
            # 从text字段中提取```json```代码块
            text_content = full_json['text']
            pattern = r'```json(.*?)```'
            matches = re.findall(pattern, text_content, re.DOTALL)
            if matches:
                json_content = matches[0].strip()
                parsed_json = json.loads(json_content)
            else:
                return {"parsed_json": full_json}
        else:
            parsed_json = full_json
    except:
        # 如果直接解析失败，尝试原有的代码块提取方式
        pattern = r'```json(.*?)```'
        matches = re.findall(pattern, arg1, re.DOTALL)
        
        if matches:
            question = matches[0].strip()
            parsed_json = json.loads(question)
        else:
            # 直接解析JSON内容，适用于insert.json文件
            parsed_json = json.loads(arg1)
    
    jsonlen = len(parsed_json)
    
    # 获取题目数据 - 修复AttributeError错误
    if isinstance(parsed_json, dict) and "parsed_json" in parsed_json:
        questions = parsed_json["parsed_json"]
    elif isinstance(parsed_json, dict) and "arg1" in parsed_json:
        # 处理 {"arg1": "..."} 格式，从arg1中提取JSON代码块
        arg1_content = parsed_json["arg1"]
        pattern = r'```json(.*?)```'
        matches = re.findall(pattern, arg1_content, re.DOTALL)
        if matches:
            json_content = matches[0].strip()
            try:
                questions = json.loads(json_content)
            except json.JSONDecodeError:
                questions = []
        else:
            # 如果没有JSON代码块，尝试直接解析arg1的内容
            try:
                questions = json.loads(arg1_content)
            except json.JSONDecodeError:
                questions = []
    elif isinstance(parsed_json, list):
        questions = parsed_json
    else:
        questions = []
    
    if not questions:
        return {
            "parsed_json": parsed_json
        }
    
    # 生成INSERT语句
    # 字段映射和类型定义
    field_types = {
        "question_code": "text", "question_title": "text", "question_type": "text",
        "question_content": "jsonb", "question_images": "jsonb", "question_audio": "jsonb", 
        "question_video": "jsonb", "options": "jsonb", "correct_answer": "jsonb",
        "answer_explanation": "jsonb", "solution_steps": "jsonb", "solution_methods": "jsonb",
        "key_points": "jsonb", "common_mistakes": "jsonb", "subject": "enum",
        "grade_level": "smallint", "knowledge_points": "array", "difficulty_level": "enum",
        "cognitive_level": "text", "academic_tracks": "array", "liberal_arts_difficulty": "enum",
        "science_difficulty": "enum", "estimated_time_minutes": "integer", "importance_level": "smallint",
        "exam_frequency": "text", "requires_calculation": "boolean", "requires_reasoning": "boolean",
        "requires_application": "boolean", "requires_creativity": "boolean", "source_type": "text",
        "source_reference": "text", "quality_score": "numeric", "review_status": "text",
        "reviewer_id": "bigint", "review_notes": "text", "used_count": "integer",
        "correct_rate": "numeric", "average_time_seconds": "integer", "difficulty_rating": "numeric",
        "ai_generated": "boolean", "ai_difficulty_prediction": "numeric", "ai_tags": "jsonb",
        "is_active": "boolean", "is_public": "boolean", "created_by": "bigint",
        "created_at": "timestamp", "updated_at": "timestamp"
    }
    
    # 获取字段列表
    fields = [field for field in field_types.keys() if field in questions[0]]
    
    # 生成VALUES子句
    def format_value(value, field_type, field_name=""):
        if value is None or value == "" or value == "null":
            return "NULL"
        if field_type == "boolean":
            return "TRUE" if str(value).lower() in ['t', 'true', '1'] else "FALSE"
        elif field_type == "jsonb":
            if isinstance(value, str):
                # 对于已经是JSON字符串的值，尝试验证其有效性
                try:
                    # 验证是否为有效JSON
                    json.loads(value)
                    # 如果是有效JSON，使用标准PostgreSQL字符串字面值
                    # 只需要转义单引号为两个单引号，不要转义反斜杠
                    escaped_value = value.replace("'", "''")
                    return f"'{escaped_value}'"
                except json.JSONDecodeError:
                    # 如果不是有效JSON，先转换为JSON字符串
                    json_str = json.dumps(value, ensure_ascii=False)
                    escaped_value = json_str.replace("'", "''")
                    return f"'{escaped_value}'"
            else:
                # 对于对象，转换为JSON字符串
                json_str = json.dumps(value, ensure_ascii=False)
                escaped_value = json_str.replace("'", "''")
                return f"'{escaped_value}'"
        elif field_type == "array":
            # 处理数组类型
            if isinstance(value, str):
                # 处理JSON格式的数组字符串
                if value.startswith('[') and value.endswith(']'):
                    try:
                        # 解析JSON数组
                        array_data = json.loads(value)
                        if not isinstance(array_data, list):
                            # 如果解析结果不是列表，作为单个元素处理
                            array_data = [array_data]
                            
                        if not array_data:  # 空数组
                            if field_name == "knowledge_points":
                                return "ARRAY[]::BIGINT[]"
                            elif field_name == "academic_tracks":
                                return "ARRAY[]::academic_track_enum[]"
                            else:
                                return "ARRAY[]"
                        
                        # 根据字段名判断数组元素类型
                        if field_name == "knowledge_points":
                            # BIGINT数组
                            elements = [str(item) for item in array_data]
                            return f"ARRAY[{', '.join(elements)}]"
                        elif field_name == "academic_tracks":
                            # 枚举数组
                            elements = [f"'{item}'" for item in array_data]
                            return f"ARRAY[{', '.join(elements)}]::academic_track_enum[]"
                        else:
                            # 其他数组类型，假设是文本数组
                            elements = [f"'{item}'" for item in array_data]
                            return f"ARRAY[{', '.join(elements)}]"
                    except (json.JSONDecodeError, TypeError):
                        # JSON解析失败，特殊处理方括号字符串
                        content = value[1:-1].strip()  # 移除方括号
                        
                        if field_name == "academic_tracks":
                            # 对于academic_tracks，提取方括号内的内容作为枚举值
                            if content:
                                # 处理可能的多个值（逗号分隔）
                                if ',' in content:
                                    items = [item.strip().strip('"\'') for item in content.split(',')]
                                    elements = [f"'{item}'" for item in items if item]
                                    return f"ARRAY[{', '.join(elements)}]::academic_track_enum[]"
                                else:
                                    # 单个值，移除可能的引号
                                    clean_value = content.strip('"\'')
                                    return f"ARRAY['{clean_value}']::academic_track_enum[]"
                            else:
                                return "ARRAY[]::academic_track_enum[]"
                        elif field_name == "knowledge_points":
                            # 对于knowledge_points，处理数字
                            if content:
                                if ',' in content:
                                    items = [item.strip() for item in content.split(',')]
                                    return f"ARRAY[{', '.join(items)}]"
                                else:
                                    return f"ARRAY[{content}]"
                            else:
                                return "ARRAY[]::BIGINT[]"
                        else:
                            # 其他字段，作为单个元素处理
                            return f"ARRAY['{content}']" if content else "ARRAY[]"
                
                # 处理PostgreSQL格式的数组字符串
                if value.startswith('{') and value.endswith('}'):
                    content = value[1:-1]
                    if field_name == "knowledge_points":
                        return f"ARRAY[{content}]" if content else "ARRAY[]::BIGINT[]"
                    elif field_name == "academic_tracks":
                        # 对于academic_tracks，需要给内容加上引号
                        if content:
                            # 处理可能的多个值（逗号分隔）
                            if ',' in content:
                                items = [item.strip().strip('"\'') for item in content.split(',')]
                                elements = [f"'{item}'" for item in items if item]
                                return f"ARRAY[{', '.join(elements)}]::academic_track_enum[]"
                            else:
                                # 单个值，移除可能的引号后重新加上
                                clean_value = content.strip('"\'')
                                return f"ARRAY['{clean_value}']::academic_track_enum[]"
                        else:
                            return "ARRAY[]::academic_track_enum[]"
                    else:
                        return f"ARRAY[{content}]" if content else "ARRAY[]"
                
                # 如果不是以上格式，作为单个元素处理
                if field_name == "knowledge_points":
                    try:
                        # 尝试转换为数字
                        int(value)
                        return f"ARRAY[{value}]"
                    except:
                        return "ARRAY[]::BIGINT[]"
                elif field_name == "academic_tracks":
                    # 移除可能的花括号并处理引号
                    clean_value = value.strip('{}')
                    return f"ARRAY['{clean_value}']::academic_track_enum[]"
                else:
                    return f"ARRAY['{value}']"
        elif field_type == "enum":
            # 处理枚举值，只有空值或null才转换为NULL
            str_value = str(value)
            if str_value in ["null", "", "None"] or value is None:
                return "NULL"
            else:
                # 移除花括号如果存在，但保留有效的枚举值
                clean_value = str_value.strip('{}')
                return f"'{clean_value}'"
        elif field_type in ["integer", "smallint", "bigint", "numeric"]:
            # 特殊处理created_by字段，如果是字符串则设为NULL
            if field_type == "bigint" and isinstance(value, str) and not value.isdigit():
                return "NULL"
            try:
                return str(float(value)) if '.' in str(value) else str(int(value))
            except:
                return "NULL"
        elif field_type == "timestamp":
            # 对于时间戳字段，由于数据库已设置默认值，直接使用NOW()
            return "NOW()"
        else:
            # 对于普通文本字段，优先使用标准字符串字面值
            str_value = str(value)
            # 只需要转义单引号为两个单引号
            escaped_value = str_value.replace("'", "''")
            return f"'{escaped_value}'"
    
    # 构建VALUES行
    values_rows = []
    for question in questions:
        values = []
        for field in fields:
            value = question.get(field)
            field_type = field_types[field]
            values.append(format_value(value, field_type, field))
        values_rows.append(f"({', '.join(values)})")
    
    # 生成完整INSERT语句
    fields_str = ", ".join(fields)
    values_str = ",\n".join(values_rows)
    insert_sql = f"INSERT INTO practice_questions ({fields_str}) VALUES \n{values_str};"
    
    return {
        "parsed_json": parsed_json,
        "insert_sql": insert_sql
    }