// 云函数入口函数
exports.main = async (event, context) => {
  try {
    // 检查输入数据是否存在
    if (!event.data) {
      return {
        success: false,
        message: '输入数据不能为空'
      }
    }
    
    // 检查数据类型
    const dataType = typeof event.data
    if (dataType === 'undefined') {
      return {
        success: false,
        message: '数据类型不能为undefined'
      }
    }
    
    // 后续处理逻辑
    // 定义result变量并初始化
    let result = {
      processed: true,
      value: event.data
    }
    
    // 这里可以添加对event.data的实际处理代码
    
    return {
      success: true,
      data: result
    }
  } catch (error) {
    return {
      success: false,
      message: error.message || '处理失败'
    }
  }
} 