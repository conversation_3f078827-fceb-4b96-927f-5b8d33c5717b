// 综合知识数据 - 整合版
// 合并知识点详情数据和知识图谱数据，提供完整的知识学习体验

// 引入知识图谱类型定义
const {
  STATUS_TYPES,
  DIFFICULTY_LEVELS,
  SEMESTER_TYPES,
  RELATION_TYPES: RELATIONSHIP_TYPES,
  RELATION_STRENGTH,
  GRADE_LEVELS,
  SUBJECT_CATEGORIES,
  CORE_COMPETENCIES,
  COGNITIVE_LEVELS,
  TEACHING_STRATEGIES,
  ASSESSMENT_TYPES,
  DIFFICULTY_DESCRIPTIONS,
  RELATION_TYPE_DESCRIPTIONS: RELATIONSHIP_DESCRIPTIONS,
  STATUS_DESCRIPTIONS,
  SUBJECT_DESCRIPTIONS,
  COMPETENCY_DESCRIPTIONS,
  COGNITIVE_DESCRIPTIONS,
  RECOMMENDATION_CONFIG
} = require('./knowledge-graph-types.js');

/**
 * 综合知识点数据集
 * 包含完整的知识图谱信息和详细内容
 */
const comprehensiveKnowledgeData = {
  'k1': {
    // 基础标识信息
    id: 'k1',
    graph_id: 'k_quadratic_function_001',
    title: '二次函数性质与应用',
    name: '二次函数的定义与标准形式',
    description: '二次函数是指形如f(x)=ax²+bx+c(a≠0)的函数。掌握二次函数的三种表示形式，理解图像性质和实际应用。',
    
    // 教材定位信息
    educational_metadata: {
      level: 9,
      grade: '初三·数学',
      difficulty: DIFFICULTY_LEVELS.MEDIUM,
      semester: SEMESTER_TYPES.FIRST,
      textbook: '人教版数学九年级上册',
      chapter: '第二十二章 二次函数',
      section: '22.1 二次函数的图象和性质',
      chapter_number: 22,
      section_number: '22.1.1',
      estimated_time: 45,
      cognitive_level: '理解应用'
    },
    
    // 分类标签
    tags: ['二次函数', '抛物线', '极值', '对称轴', '函数性质'],
    collectDate: '2023-07-15',
    formula: 'f(x) = ax² + bx + c (a≠0)',
    
    // 难度分级信息
    difficulty_info: {
      level: DIFFICULTY_LEVELS.MEDIUM,
      display: '标准掌握',
      description: '需要熟练运用基本方法，理解函数图像性质',
      prerequisites_mastery: 0.75,
      target_mastery: 0.85,
      challenge_level: 2
    },
    
    // 知识关系网络
    knowledge_relationships: {
      prerequisites: [
        {
          id: 'k_function_basic_001',
          name: '函数的概念',
          relationship_type: RELATIONSHIP_TYPES.PREREQUISITE,
          strength: 0.9,
          description: '理解函数概念是学习二次函数的基础'
        },
        {
          id: 'k_linear_function_001',
          name: '一次函数',
          relationship_type: RELATIONSHIP_TYPES.FOUNDATION,
          strength: 0.8,
          description: '一次函数的学习为二次函数奠定基础'
        }
      ],
      related: [
        {
          id: 'k3',
          name: '一元二次方程',
          relationship_type: RELATIONSHIP_TYPES.APPLICATION,
          strength: 0.95,
          description: '二次函数与一元二次方程密切相关'
        },
        {
          id: 'k_parabola_001',
          name: '抛物线几何性质',
          relationship_type: RELATIONSHIP_TYPES.CORRELATION,
          strength: 0.85,
          description: '二次函数图像就是抛物线'
        }
      ],
      applications: [
        {
          id: 'k_optimization_001',
          name: '最值问题',
          relationship_type: RELATIONSHIP_TYPES.APPLICATION,
          strength: 0.88,
          description: '二次函数在求解最值问题中应用广泛'
        }
      ]
    },
    
    // 学习路径信息
    learning_path: {
      current_position: 'intermediate',
      recommended_next: ['k3', 'k_optimization_001'],
      prerequisite_check: true,
      mastery_requirements: {
        concept_understanding: 0.8,
        problem_solving: 0.75,
        application_ability: 0.7
      }
    },
    
    // 知识网络信息
    knowledge_network: {
      core_concepts: [
        { name: '二次函数定义', mastery: 0.85, importance: 0.95 },
        { name: '抛物线性质', mastery: 0.78, importance: 0.90 },
        { name: '对称轴与顶点', mastery: 0.82, importance: 0.88 },
        { name: '开口方向', mastery: 0.90, importance: 0.85 }
      ],
      weak_points: [
        { concept: '实际应用建模', current_level: 0.45, target_level: 0.75 },
        { concept: '函数性质综合运用', current_level: 0.52, target_level: 0.80 }
      ],
      strength_areas: [
        { concept: '基本概念理解', level: 0.88 },
        { concept: '图像识别', level: 0.85 }
      ]
    },
    
    // 详细内容
    content: {
      sections: [
        {
          id: 'quadratic_definition_001',
          title: '二次函数的定义',
          text: '二次函数是指形如f(x)=ax²+bx+c(a≠0)的函数。其中a、b、c是常数，a≠0是二次函数的必要条件。',
          detailText: '二次函数的一般形式为f(x)=ax²+bx+c，其中：<br/>• a为二次项系数，决定抛物线开口方向和开口大小<br/>• b为一次项系数，影响抛物线的对称轴位置<br/>• c为常数项，决定抛物线与y轴的交点<br/>当a>0时，抛物线开口向上；当a<0时，抛物线开口向下。',
          example: '例如：f(x)=2x²-4x+1是一个二次函数，其中a=2，b=-4，c=1。',
          keyPoints: ['二次项系数a≠0', '三个参数的几何意义', '标准形式的特点']
        },
        {
          id: 'quadratic_properties_001',
          title: '二次函数的图像性质',
          text: '二次函数的图像是一条抛物线，具有对称性、最值性等重要性质。',
          detailText: '二次函数图像的主要性质：<br/>• 对称性：以直线x=-b/(2a)为对称轴<br/>• 最值性：在顶点处取得最值<br/>• 单调性：在对称轴两侧单调性相反<br/>• 开口方向：由二次项系数a的符号决定',
          example: '对于f(x)=x²-2x+3，对称轴为x=1，顶点为(1,2)，开口向上。',
          keyPoints: ['抛物线的对称性', '顶点的意义', '单调性分析']
        },
        {
          id: 'quadratic_transformations_001',
          title: '二次函数的图像变换',
          text: '通过平移、翻折等变换，可以得到不同位置和形状的抛物线。',
          detailText: '二次函数的图像变换规律：<br/>• y=a(x-h)²+k：向右平移h个单位，向上平移k个单位<br/>• y=-ax²：关于x轴翻折<br/>• y=ax²+k：沿y轴平移k个单位<br/>• y=a(x-h)²：沿x轴平移h个单位',
          example: '从y=x²到y=2(x-1)²+3经历了：横向压缩、右移1单位、上移3单位。',
          keyPoints: ['平移变换', '翻折变换', '伸缩变换']
        }
      ],
      
      examples: [
        {
          id: 'example_001',
          title: '求二次函数的顶点坐标',
          content: '已知二次函数f(x)=2x²-8x+7，求其顶点坐标和对称轴方程。',
          solution: '方法一：利用公式法<br/>对称轴：x=-b/(2a)=-(-8)/(2×2)=2<br/>顶点纵坐标：f(2)=2×2²-8×2+7=8-16+7=-1<br/>所以顶点坐标为(2,-1)<br/><br/>方法二：配方法<br/>f(x)=2x²-8x+7=2(x²-4x)+7=2(x²-4x+4-4)+7=2(x-2)²-8+7=2(x-2)²-1<br/>所以顶点坐标为(2,-1)',
          difficulty: DIFFICULTY_LEVELS.EASY,
          knowledge_points: ['顶点公式', '配方法', '对称轴'],
          steps: [
            '识别二次函数的一般形式',
            '应用顶点坐标公式',
            '计算对称轴和顶点坐标',
            '验证结果的正确性'
          ]
        },
        {
          id: 'example_002',
          title: '二次函数的实际应用',
          content: '某商品的利润函数为P(x)=-2x²+20x-18（其中x为销售数量，单位：千件），求获得最大利润时的销售数量和最大利润。',
          solution: '这是一个二次函数最值问题。<br/>P(x)=-2x²+20x-18<br/>因为a=-2<0，所以抛物线开口向下，在顶点处取得最大值。<br/>对称轴：x=-b/(2a)=-20/(2×(-2))=5<br/>最大利润：P(5)=-2×5²+20×5-18=-50+100-18=32<br/>答：当销售数量为5千件时，获得最大利润32万元。',
          difficulty: DIFFICULTY_LEVELS.MEDIUM,
          knowledge_points: ['实际应用', '最值问题', '函数建模'],
          steps: [
            '理解实际问题的数学模型',
            '确定函数的性质',
            '求解最值',
            '解释实际意义'
          ]
        }
      ],
      
      applications: [
        {
          id: 'app_001',
          title: '抛物运动问题',
          text: '在物理学中，物体在重力作用下的抛物运动轨迹可用二次函数描述。高度h与时间t的关系为h=h₀+v₀t-½gt²。',
          realWorldExamples: [
            '篮球投篮轨迹',
            '喷泉水流形状',
            '导弹飞行路径'
          ]
        },
        {
          id: 'app_002',
          title: '经济学中的优化问题',
          text: '在经济学中，利润、成本、收入等经济指标往往可以用二次函数建模，通过求最值来解决优化问题。',
          realWorldExamples: [
            '商品定价策略',
            '生产成本优化',
            '资源配置问题'
          ]
        }
      ],
      
      related: [
        { id: 'k3', title: '一元二次方程', description: '二次函数的零点就是相应一元二次方程的解' },
        { id: 'k4', title: '三角函数', description: '都是基本初等函数，在性质研究上有相似之处' },
        { id: 'k5', title: '圆的性质', description: '在解析几何中都涉及二次方程' }
      ]
    },
    
    // 练习系统
    practice: {
      basicQuestions: [
        {
          id: 'q_basic_001',
          content: '求函数f(x)=x²-4x+3的顶点坐标。',
          answer: '对称轴x=-b/(2a)=-(-4)/(2×1)=2，f(2)=2²-4×2+3=4-8+3=-1，顶点坐标为(2,-1)。',
          difficulty: DIFFICULTY_LEVELS.EASY,
          knowledge_points: ['顶点公式', '对称轴'],
          hints: ['先求对称轴', '再代入求顶点纵坐标']
        },
        {
          id: 'q_basic_002',
          content: '判断函数f(x)=-3x²+6x-2的开口方向，并求其最值。',
          answer: '因为a=-3<0，所以抛物线开口向下。对称轴x=6/(2×3)=1，最大值f(1)=-3×1²+6×1-2=-3+6-2=1。',
          difficulty: DIFFICULTY_LEVELS.EASY,
          knowledge_points: ['开口方向', '最值'],
          hints: ['观察二次项系数的符号', '开口向下时在顶点取最大值']
        }
      ],
      
      advancedQuestions: [
        {
          id: 'q_adv_001',
          content: '已知二次函数f(x)=ax²+bx+c的图像过点(0,1)、(1,0)、(2,3)，求这个二次函数的解析式。',
          answer: '将三点坐标代入得方程组：c=1，a+b+c=0，4a+2b+c=3。解得a=2，b=-3，c=1。所以f(x)=2x²-3x+1。',
          difficulty: DIFFICULTY_LEVELS.MEDIUM,
          knowledge_points: ['待定系数法', '方程组求解'],
          hints: ['利用待定系数法', '列方程组求解']
        },
        {
          id: 'q_adv_002',
          content: '一个长方形操场，长比宽多20米，面积为2400平方米。求操场的长和宽。',
          answer: '设宽为x米，则长为(x+20)米。由面积公式：x(x+20)=2400，即x²+20x-2400=0。解得x=40或x=-60（舍去）。所以宽40米，长60米。',
          difficulty: DIFFICULTY_LEVELS.MEDIUM,
          knowledge_points: ['实际应用', '二次方程'],
          hints: ['设未知数', '建立二次方程', '求解并检验']
        }
      ],
      
      challengeQuestions: [
        {
          id: 'q_challenge_001',
          content: '已知二次函数f(x)=ax²+bx+c满足：f(0)=1，f(1)=0，且对于任意实数x，都有f(x)≥-1。求a、b、c的值。',
          answer: '由条件：c=1，a+b+1=0，即b=-a-1。又f(x)≥-1恒成立，说明最小值为-1。配方得f(x)=a(x+b/(2a))²+c-b²/(4a)。最小值为c-b²/(4a)=-1，即1-(-a-1)²/(4a)=-1。解得a=1，b=-2，c=1。',
          difficulty: DIFFICULTY_LEVELS.HARD,
          knowledge_points: ['恒成立问题', '配方法', '最值'],
          hints: ['利用给定条件', '配方求最值', '建立关于参数的方程']
        }
      ]
    },
    
    // 智能推荐数据
    intelligent_recommendations: [
      {
        id: 'k3',
        title: '一元二次方程',
        reason: '二次函数的零点对应一元二次方程的解',
        confidence: 0.95,
        difficulty_match: true
      },
      {
        id: 'k_optimization_001',
        title: '函数最值问题',
        reason: '二次函数在最值问题中应用广泛',
        confidence: 0.88,
        difficulty_match: true
      }
    ],
    
    // 学习指导信息
    learning_guidance: {
      study_tips: [
        '先理解二次函数的基本概念，再学习图像性质',
        '多练习求顶点坐标和对称轴的方法',
        '重点掌握实际应用问题的建模方法',
        '注意区分最大值和最小值的情况'
      ],
      common_mistakes: [
        '忘记二次项系数不为零的条件',
        '混淆开口方向与系数符号的关系',
        '在实际应用中建模错误',
        '计算顶点坐标时出现符号错误'
      ],
      mastery_criteria: [
        '能准确理解二次函数的定义',
        '能熟练求解顶点坐标和对称轴',
        '能解决实际应用问题',
        '能分析函数的性质'
      ]
    }
  },

  'k2': {
    id: 'k2',
    graph_id: 'k_linear_function_001',
    title: '一次函数与正比例函数',
    name: '一次函数的图像与性质',
    description: '一次函数是形如y=kx+b(k≠0)的函数，当b=0时为正比例函数。掌握一次函数的图像特征和实际应用。',
    
    educational_metadata: {
      level: 8,
      grade: '初二·数学',
      difficulty: DIFFICULTY_LEVELS.EASY,
      semester: SEMESTER_TYPES.SECOND,
      textbook: '人教版数学八年级下册',
      chapter: '第十九章 一次函数',
      section: '19.2 一次函数',
      chapter_number: 19,
      section_number: '19.2.1',
      estimated_time: 45,
      cognitive_level: '理解应用'
    },
    
    tags: ['一次函数', '正比例函数', '直线', '斜率', '截距'],
    collectDate: '',
    formula: 'y = kx + b (k≠0)',
    
    difficulty_info: {
      level: DIFFICULTY_LEVELS.EASY,
      display: '基础掌握',
      description: '掌握基本概念和图像性质即可',
      prerequisites_mastery: 0.6,
      target_mastery: 0.8,
      challenge_level: 1
    },
    
    content: {
      sections: [
        {
          id: 'linear_definition_001',
          title: '一次函数的定义',
          text: '一次函数是形如y=kx+b(k≠0)的函数，其中k叫做斜率，b叫做截距。',
          keyPoints: ['斜率k的意义', '截距b的意义', '与正比例函数的关系']
        }
      ],
      examples: [
        {
          id: 'linear_example_001',
          title: '求一次函数解析式',
          content: '已知一次函数图像过点(1,3)和(2,5)，求这个一次函数的解析式。',
          solution: '设y=kx+b，代入两点得：k+b=3，2k+b=5。解得k=2，b=1。所以y=2x+1。',
          difficulty: DIFFICULTY_LEVELS.EASY
        }
      ],
      applications: []
    },
    
    practice: {
      basicQuestions: [
        {
          id: 'q_linear_001',
          content: '求过点(0,2)和(1,4)的一次函数解析式。',
          answer: '由于过(0,2)，所以b=2。由于过(1,4)，所以k+2=4，即k=2。因此y=2x+2。'
        }
      ],
      advancedQuestions: []
    }
  },

  'k3': {
    id: 'k3',
    graph_id: 'k_quadratic_equation_001',
    title: '一元二次方程的解法',
    name: '一元二次方程',
    description: '一元二次方程是形如ax²+bx+c=0(a≠0)的方程。掌握配方法、公式法、因式分解法等解法。',
    
    educational_metadata: {
      level: 9,
      grade: '初三·数学',
      difficulty: DIFFICULTY_LEVELS.MEDIUM,
      semester: SEMESTER_TYPES.FIRST,
      textbook: '人教版数学九年级上册',
      chapter: '第二十一章 一元二次方程',
      section: '21.2 解一元二次方程',
      chapter_number: 21,
      section_number: '21.2',
      estimated_time: 90,
      cognitive_level: '掌握运用'
    },
    
    tags: ['一元二次方程', '判别式', '根与系数', '配方法', '公式法'],
    collectDate: '',
    formula: 'ax² + bx + c = 0 (a≠0)',
    
    difficulty_info: {
      level: DIFFICULTY_LEVELS.MEDIUM,
      display: '标准掌握',
      description: '需要熟练掌握多种解法和应用',
      prerequisites_mastery: 0.7,
      target_mastery: 0.85,
      challenge_level: 2
    },
    
    content: {
      sections: [
        {
          id: 'equation_methods_001',
          title: '解一元二次方程的方法',
          text: '解一元二次方程的主要方法有：直接开平方法、配方法、公式法、因式分解法。',
          keyPoints: ['各种解法的适用条件', '判别式的应用', '根与系数的关系']
        }
      ],
      examples: [
        {
          id: 'equation_example_001',
          title: '用公式法解方程',
          content: '用公式法解方程2x²-7x+3=0。',
          solution: 'a=2，b=-7，c=3。Δ=(-7)²-4×2×3=49-24=25>0。x=(7±√25)/(2×2)=(7±5)/4。所以x₁=3，x₂=1/2。',
          difficulty: DIFFICULTY_LEVELS.MEDIUM
        }
      ],
      applications: []
    },
    
    practice: {
      basicQuestions: [
        {
          id: 'q_equation_001',
          content: '解方程x²-5x+6=0。',
          answer: '因式分解：(x-2)(x-3)=0，所以x=2或x=3。'
        }
      ],
      advancedQuestions: []
    }
  },

  'k4': {
    id: 'k4',
    graph_id: 'k_trigonometry_001',
    title: '三角函数基础',
    name: '锐角三角函数',
    description: '三角函数是研究角度与边长关系的重要工具。在直角三角形中，正弦、余弦、正切表示了角与边的比值关系。',
    
    educational_metadata: {
      level: 9,
      grade: '初三·数学',
      difficulty: DIFFICULTY_LEVELS.MEDIUM,
      semester: SEMESTER_TYPES.SECOND,
      textbook: '人教版数学九年级下册',
      chapter: '第二十八章 锐角三角函数',
      section: '28.1 锐角三角函数',
      chapter_number: 28,
      section_number: '28.1',
      estimated_time: 60,
      cognitive_level: '理解应用'
    },
    
    tags: ['三角函数', '正弦', '余弦', '正切', '锐角'],
    collectDate: '',
    formula: 'sin∠A = 对边/斜边',
    
    difficulty_info: {
      level: DIFFICULTY_LEVELS.MEDIUM,
      display: '标准掌握',
      description: '需要理解概念并能解决实际问题',
      prerequisites_mastery: 0.7,
      target_mastery: 0.8,
      challenge_level: 2
    },
    
    content: {
      sections: [
        {
          id: 'trig_definition_001',
          title: '锐角三角函数的定义',
          text: '在直角三角形中，锐角A的正弦、余弦、正切分别等于对边与斜边、邻边与斜边、对边与邻边的比值。',
          keyPoints: ['三角函数的定义', '特殊角的三角函数值', '三角函数的性质']
        }
      ],
      examples: [
        {
          id: 'trig_example_001',
          title: '计算特殊角的三角函数值',
          content: '计算sin30°，cos45°，tan60°的值。',
          solution: 'sin30°=1/2，cos45°=√2/2，tan60°=√3。',
          difficulty: DIFFICULTY_LEVELS.EASY
        }
      ],
      applications: []
    },
    
    practice: {
      basicQuestions: [
        {
          id: 'q_trig_001',
          content: '计算sin30°·cos60°+cos30°·sin60°的值。',
          answer: 'sin30°=1/2，cos60°=1/2，cos30°=√3/2，sin60°=√3/2。原式=(1/2)×(1/2)+(√3/2)×(√3/2)=1/4+3/4=1。'
        }
      ],
      advancedQuestions: []
    }
  },

  'k5': {
    id: 'k5',
    graph_id: 'k_circle_properties_001',
    title: '圆的性质与定理',
    name: '圆的几何性质',
    description: '圆是平面几何中的重要图形，具有丰富的性质。圆周角定理、切线性质等是解决圆的相关问题的重要工具。',
    
    educational_metadata: {
      level: 9,
      grade: '初三·数学',
      difficulty: DIFFICULTY_LEVELS.MEDIUM,
      semester: SEMESTER_TYPES.SECOND,
      textbook: '人教版数学九年级下册',
      chapter: '第二十四章 圆',
      section: '24.1 圆的有关性质',
      chapter_number: 24,
      section_number: '24.1',
      estimated_time: 45,
      cognitive_level: '理解应用'
    },
    
    tags: ['圆', '圆周角', '切线', '弦', '几何'],
    collectDate: '',
    formula: 'S = πr²',
    
    difficulty_info: {
      level: DIFFICULTY_LEVELS.MEDIUM,
      display: '标准掌握',
      description: '需要理解圆的性质并能证明相关定理',
      prerequisites_mastery: 0.7,
      target_mastery: 0.8,
      challenge_level: 2
    },
    
    content: {
      sections: [
        {
          id: 'circle_basic_001',
          title: '圆的基本概念',
          text: '圆是平面内到定点距离等于定长的所有点的集合。定点称为圆心，定长称为半径。',
          keyPoints: ['圆的定义', '圆的基本元素', '圆的性质']
        }
      ],
      examples: [],
      applications: []
    },
    
    practice: {
      basicQuestions: [],
      advancedQuestions: []
    }
  }
};

/**
 * 知识图谱关系网络
 */
const knowledgeGraphEdges = [
  {
    id: 'edge_001',
    source: 'k_function_basic_001',
    target: 'k1',
    type: RELATIONSHIP_TYPES.PREREQUISITE,
    strength: 0.9,
    description: '函数概念是学习二次函数的基础'
  },
  {
    id: 'edge_002',
    source: 'k2',
    target: 'k1',
    type: RELATIONSHIP_TYPES.FOUNDATION,
    strength: 0.8,
    description: '一次函数为二次函数学习奠定基础'
  },
  {
    id: 'edge_003',
    source: 'k1',
    target: 'k3',
    type: RELATIONSHIP_TYPES.APPLICATION,
    strength: 0.95,
    description: '二次函数的零点对应一元二次方程的解'
  },
  {
    id: 'edge_004',
    source: 'k3',
    target: 'k4',
    type: RELATIONSHIP_TYPES.CORRELATION,
    strength: 0.6,
    description: '解三角函数方程时会用到二次方程'
  }
];

// 导出所有数据和配置
module.exports = {
  // 核心数据
  comprehensiveKnowledgeData,
  knowledgeGraphEdges,
  
  // 从knowledge-graph-types.js引入的枚举和配置
  STATUS_TYPES,
  DIFFICULTY_LEVELS,
  SEMESTER_TYPES,
  RELATIONSHIP_TYPES,
  RELATION_STRENGTH,
  GRADE_LEVELS,
  SUBJECT_CATEGORIES,
  CORE_COMPETENCIES,
  COGNITIVE_LEVELS,
  TEACHING_STRATEGIES,
  ASSESSMENT_TYPES,
  
  // 描述映射
  DIFFICULTY_DESCRIPTIONS,
  RELATIONSHIP_DESCRIPTIONS,
  STATUS_DESCRIPTIONS,
  SUBJECT_DESCRIPTIONS,
  COMPETENCY_DESCRIPTIONS,
  COGNITIVE_DESCRIPTIONS,
  
  // 推荐系统配置
  RECOMMENDATION_CONFIG
}; 