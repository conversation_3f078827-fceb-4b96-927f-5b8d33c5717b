/**
 * 知识图谱数据库服务
 * 基于 PostgreSQL 04_knowledge_graph_enhanced.sql 表结构
 * 密码：difyai123456
 */

class KnowledgeGraphService {
  
  /**
   * 调用知识图谱云函数
   * @param {string} action 操作类型
   * @param {object} params 参数
   * @returns {Promise} 返回结果
   */
  static async callFunction(action, params = {}) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'knowledge-graph-query',
        data: {
          action,
          params
        }
      });
      
      if (result.result.success) {
        return result.result.data;
      } else {
        throw new Error(result.result.error || '云函数调用失败');
      }
    } catch (error) {
      console.error(`知识图谱云函数调用失败[${action}]:`, error);
      throw error;
    }
  }

  /**
   * 获取知识点列表
   * @param {object} options 查询选项
   */
  static async getKnowledgeNodes(options = {}) {
    const {
      subject = null,
      gradeLevel = null,
      difficulty = null,
      academicTrack = null,
      searchKeyword = null,
      limit = 50,
      offset = 0
    } = options;

    return await this.callFunction('getKnowledgeNodes', {
      subject,
      gradeLevel,
      difficulty,
      academicTrack,
      searchKeyword,
      limit,
      offset
    });
  }

  /**
   * 搜索知识点
   * @param {string} keyword 搜索关键词
   * @param {object} filters 过滤条件
   */
  static async searchKnowledge(keyword, filters = {}) {
    const { subject = null, gradeLevel = null, limit = 20 } = filters;

    return await this.callFunction('searchKnowledge', {
      keyword,
      subject,
      gradeLevel,
      limit
    });
  }

  /**
   * 按年级获取知识点
   * @param {number} gradeLevel 年级
   * @param {object} options 选项
   */
  static async getKnowledgeByGrade(gradeLevel, options = {}) {
    const { subject = null, semester = null } = options;

    return await this.callFunction('getKnowledgeByGrade', {
      gradeLevel,
      subject,
      semester
    });
  }

  /**
   * 获取学生掌握状态
   * @param {number} studentId 学生ID
   * @param {object} options 选项
   */
  static async getStudentMastery(studentId, options = {}) {
    const { nodeId = null, masteryStatus = null } = options;

    return await this.callFunction('getStudentMastery', {
      studentId,
      nodeId,
      masteryStatus
    });
  }

  /**
   * 更新学生掌握状态
   * @param {number} studentId 学生ID
   * @param {number} nodeId 知识点ID
   * @param {object} masteryData 掌握数据
   */
  static async updateMasteryStatus(studentId, nodeId, masteryData = {}) {
    const {
      masteryStatus = 'learning',
      masteryPercentage = 0,
      studyTimeMinutes = 0,
      isCorrect = false
    } = masteryData;

    return await this.callFunction('updateMasteryStatus', {
      studentId,
      nodeId,
      masteryStatus,
      masteryPercentage,
      studyTimeMinutes,
      isCorrect
    });
  }

  /**
   * 获取知识点关系
   * @param {number} nodeId 知识点ID
   * @param {string} relationshipType 关系类型
   */
  static async getKnowledgeRelationships(nodeId, relationshipType = null) {
    return await this.callFunction('getKnowledgeRelationships', {
      nodeId,
      relationshipType
    });
  }

  /**
   * 获取前置知识点
   * @param {number} nodeId 知识点ID
   */
  static async getPrerequisites(nodeId) {
    return await this.callFunction('getPrerequisites', {
      nodeId
    });
  }

  /**
   * 获取学习路径
   * @param {object} options 选项
   */
  static async getLearningPath(options = {}) {
    const {
      academicTrack = null,
      gradeLevel = null,
      subject = null
    } = options;

    return await this.callFunction('getLearningPath', {
      academicTrack,
      gradeLevel,
      subject
    });
  }

  /**
   * 获取个性化推荐
   * @param {number} studentId 学生ID
   * @param {object} options 选项
   */
  static async getRecommendations(studentId, options = {}) {
    const {
      academicTrack = null,
      limit = 10
    } = options;

    return await this.callFunction('getRecommendations', {
      studentId,
      academicTrack,
      limit
    });
  }

  /**
   * 获取知识点详细内容
   * @param {number} nodeId 知识点ID
   */
  static async getKnowledgeContent(nodeId) {
    return await this.callFunction('getKnowledgeContent', {
      nodeId
    });
  }

  /**
   * 批量获取学生薄弱知识点
   * @param {number} studentId 学生ID
   * @param {object} options 选项
   */
  static async getWeakKnowledgePoints(studentId, options = {}) {
    const { subject = null, gradeLevel = null, limit = 20 } = options;

    const masteryData = await this.getStudentMastery(studentId, {
      masteryStatus: 'weak'
    });

    // 如果有额外过滤条件，在前端进行过滤
    let filteredData = masteryData;
    
    if (subject) {
      filteredData = filteredData.filter(item => item.subject === subject);
    }
    
    if (gradeLevel) {
      filteredData = filteredData.filter(item => item.grade_level === gradeLevel);
    }

    // 按重要性和掌握度排序
    filteredData.sort((a, b) => {
      if (a.importance_level !== b.importance_level) {
        return b.importance_level - a.importance_level;
      }
      return a.mastery_percentage - b.mastery_percentage;
    });

    return filteredData.slice(0, limit);
  }

  /**
   * 获取推荐学习序列
   * @param {number} studentId 学生ID
   * @param {object} options 选项
   */
  static async getRecommendedLearningSequence(studentId, options = {}) {
    const { academicTrack = null, maxCount = 10 } = options;

    // 获取推荐知识点
    const recommendations = await this.getRecommendations(studentId, {
      academicTrack,
      limit: maxCount
    });

    // 为每个推荐知识点获取前置依赖
    const enrichedRecommendations = await Promise.all(
      recommendations.map(async (rec) => {
        try {
          const prerequisites = await this.getPrerequisites(rec.id);
          return {
            ...rec,
            prerequisites: prerequisites || []
          };
        } catch (error) {
          console.warn(`获取知识点${rec.id}的前置依赖失败:`, error);
          return {
            ...rec,
            prerequisites: []
          };
        }
      })
    );

    return enrichedRecommendations;
  }

  /**
   * 获取知识点统计信息
   * @param {object} options 选项
   */
  static async getKnowledgeStatistics(options = {}) {
    const { gradeLevel = null, subject = null } = options;

    const nodes = await this.getKnowledgeNodes({
      gradeLevel,
      subject,
      limit: 1000 // 获取更多数据用于统计
    });

    // 统计分析
    const stats = {
      total: nodes.length,
      byDifficulty: {},
      byGrade: {},
      bySubject: {},
      byKnowledgeType: {}
    };

    nodes.forEach(node => {
      // 按难度统计
      stats.byDifficulty[node.difficulty] = (stats.byDifficulty[node.difficulty] || 0) + 1;
      
      // 按年级统计
      stats.byGrade[node.grade_level] = (stats.byGrade[node.grade_level] || 0) + 1;
      
      // 按学科统计
      stats.bySubject[node.subject] = (stats.bySubject[node.subject] || 0) + 1;
      
      // 按知识点类型统计
      stats.byKnowledgeType[node.knowledge_type] = (stats.byKnowledgeType[node.knowledge_type] || 0) + 1;
    });

    return stats;
  }

  /**
   * 构建知识图谱关系网络
   * @param {number} centerNodeId 中心节点ID
   * @param {number} depth 深度
   */
  static async buildKnowledgeNetwork(centerNodeId, depth = 2) {
    const visited = new Set();
    const nodes = new Map();
    const edges = [];

    async function exploreNode(nodeId, currentDepth) {
      if (currentDepth > depth || visited.has(nodeId)) {
        return;
      }

      visited.add(nodeId);

      try {
        // 获取节点详情
        const nodeContent = await KnowledgeGraphService.getKnowledgeContent(nodeId);
        if (nodeContent) {
          nodes.set(nodeId, nodeContent);
        }

        // 获取关系
        const relationships = await KnowledgeGraphService.getKnowledgeRelationships(nodeId);
        
        for (const rel of relationships) {
          edges.push(rel);
          
          // 递归探索相关节点
          const relatedNodeId = rel.source_node_id === nodeId ? rel.target_node_id : rel.source_node_id;
          if (currentDepth < depth) {
            await exploreNode(relatedNodeId, currentDepth + 1);
          }
        }
      } catch (error) {
        console.warn(`探索节点${nodeId}失败:`, error);
      }
    }

    await exploreNode(centerNodeId, 0);

    return {
      nodes: Array.from(nodes.values()),
      edges: edges,
      centerNodeId: centerNodeId
    };
  }
}

module.exports = KnowledgeGraphService; 