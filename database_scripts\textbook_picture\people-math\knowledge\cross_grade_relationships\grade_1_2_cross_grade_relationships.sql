-- ============================================
-- 一年级与二年级数学知识点跨年级关联关系脚本 - 专家权威版V1.0
-- 专家编写：K12数学教育专家组、小学数学特级教师、认知心理学专家、数学教育学专家
-- 参考教材：人民教育出版社数学一年级上下册、二年级上下册
-- 创建时间：2025-01-28
-- 参考标准：grade_11_elective_1a_internal_relationships.sql（⭐⭐⭐⭐⭐专家级标准）
-- 知识点基础：grade_1_semester_1_nodes.sql, grade_1_semester_2_nodes.sql, grade_2_semester_1_nodes.sql, grade_2_semester_2_nodes.sql
-- 编写原则：科学、严谨、全面、无冗余、可验证、符合低年级认知发展规律
-- 
-- ============================================
-- 【一年级与二年级知识点章节编号详情 - 实际验证总计162个知识点】
-- ============================================
-- 
-- 📐 一年级上学期（MATH_G1S1_，40个知识点）：
-- 数学游戏 → INTRO_001~INTRO_002（2个）
-- 第一单元：5以内数的认识和加、减法 → CH1_001~CH1_008（8个）
-- 第二单元：6～10的认识和加、减法 → CH2_001~CH2_008（8个）
-- 第三单元：认识立体图形 → CH3_001~CH3_004（4个）
-- 第四单元：11～20各数的认识 → CH4_001~CH4_004（4个）
-- 第五单元：20以内的进位加法 → CH5_001~CH5_006（6个）
-- 第六单元：复习与关联 → CH6_001~CH6_004（4个）
-- 
-- 📏 一年级下学期（MATH_G1S2_，42个知识点）：
-- 第一单元：认识平面图形 → CH1_001~CH1_005（5个）
-- 第二单元：20以内的退位减法 → CH2_001~CH2_006（6个）
-- 第三单元：100以内数的认识 → CH3_001~CH3_007（7个）
-- 第四单元：100以内的口算加、减法 → CH4_001~CH4_006（6个）
-- 第五单元：100以内的笔算加、减法 → CH5_001~CH5_004（4个）
-- 第六单元：数量间的加减关系 → CH6_001~CH6_004（4个）
-- 数学文化：欢乐购物街 → CULTURE_001~CULTURE_003（3个）
-- 第七单元：复习与关联 → CH7_001~CH7_005（5个）
-- 
-- 📊 二年级上学期（MATH_G2S1_，42个知识点）：
-- 第一单元：长度单位 → CH1_001~CH1_005（5个）
-- 第二单元：100以内的加法和减法（二） → CH2_001~CH2_007（7个）
-- 第三单元：角的初步认识 → CH3_001~CH3_004（4个）
-- 第四单元：表内乘法（一） → CH4_001~CH4_006（6个）
-- 第五单元：观察物体（一） → CH5_001~CH5_002（2个）
-- 第六单元：表内乘法（二） → CH6_001~CH6_005（5个）
-- 数学文化 → CULTURE_001~CULTURE_002（2个）
-- 第七单元：认识时间 → CH7_001~CH7_004（4个）
-- 第八单元：数学广角——搭配（一） → CH8_001~CH8_003（3个）
-- 第九单元：总复习 → CH9_001~CH9_004（4个）
-- 
-- 📈 二年级下学期（MATH_G2S2_，38个知识点）：
-- 第一单元：数据收集整理 → CH1_001~CH1_004（4个）
-- 第二单元：表内除法（一） → CH2_001~CH2_004（4个）
-- 第三单元：图形的运动（一） → CH3_001~CH3_004（4个）
-- 第四单元：表内除法（二） → CH4_001~CH4_003（3个）
-- 第五单元：混合运算 → CH5_001~CH5_003（3个）
-- 第六单元：有余数的除法 → CH6_001~CH6_004（4个）
-- 数学文化：小小设计师 → CULTURE_001（1个）
-- 第七单元：万以内数的认识 → CH7_001~CH7_009（9个）
-- 第八单元：克和千克 → CH8_001~CH8_004（4个）
-- 第九单元：数学广角——推理 → CH9_001~CH9_003（3个）
-- 总复习单元 → REVIEW_001~REVIEW_005（5个）
-- 
-- ============================================
-- 【基于认知发展规律的高质量分批编写计划 - 低年级认知科学指导】
-- ============================================
-- 
-- 🎯 低年级优化原则：
-- • 符合6-8岁儿童认知发展规律：具体运算期特征，抽象思维萌芽
-- • 强调知识的连续性和螺旋式上升：从具体到抽象，从简单到复杂
-- • 重视动手操作和感知体验：立体图形→平面图形→数量关系
-- • 突出数感培养的核心地位：数的认识→数的运算→数的应用
-- • 体现数学与生活的密切联系：时间、货币、测量、数据等实际应用
-- • 遵循低年级学习特点：记忆与理解并重，应用以生活实际为主
-- • 所有关系 grade_span = 1（一年级到二年级的跨年级关系）
-- • 重点建立纵向继承关系和横向拓展关系
-- 
-- 📋 优化后分批计划（预计240条高质量关系）：
-- 
-- 第一批：数的认识基础体系（25条）
--   范围：一年级数字认识基础 → 二年级数的拓展
--   重点：1-20数的认识 → 100以内数认识 → 万以内数认识
--   认知特点：数感从小范围向大范围的螺旋式发展
--   关系类型：主要是prerequisite和successor关系
-- 
-- 第二批：加减法计算体系（30条）
--   范围：一年级加减法基础 → 二年级加减法进阶
--   重点：5以内→10以内→20以内→100以内加减法的递进关系
--   认知特点：从具体操作到抽象计算的认知跃迁
--   关系类型：prerequisite、successor、extension关系为主
-- 
-- 第三批：乘除法启蒙体系（25条）
--   范围：一年级加法重复 → 二年级乘法概念和口诀 → 除法认识
--   重点：加法的重复→乘法认识→口诀记忆→除法认识
--   认知特点：从加法到乘法的概念飞跃，除法作为乘法的逆运算，符合8-9岁认知规律
--   关系类型：extension、related、application_of关系
-- 
-- 第四批：几何图形认知体系（20条）
--   范围：一年级立体图形 → 一年级平面图形 → 二年级图形运动
--   重点：三维空间感知→二维平面认知→图形变换理解
--   认知特点：从具体物体到抽象图形的空间认知发展
--   关系类型：prerequisite、extension、related关系
-- 
-- 第五批：测量概念体系（25条）
--   范围：一年级时间认识 → 二年级长度单位 → 二年级质量单位
--   重点：时间概念→长度测量→质量测量的量感培养
--   认知特点：从抽象时间到具体测量的量感建立
--   关系类型：related、parallel、extension关系
-- 
-- 第六批：实际应用综合体系（25条）
--   范围：一年级简单应用 → 二年级复杂应用问题
--   重点：简单计算应用→混合运算应用→实际问题解决
--   认知特点：数学知识向生活实际的迁移应用
--   关系类型：application_of、extension、related关系
-- 
-- 第七批：思维方法启蒙体系（20条）
--   范围：一年级找规律 → 二年级数据整理 → 二年级逻辑推理
--   重点：模式识别→数据处理→逻辑思维的萌芽发展
--   认知特点：从直观观察到初步的抽象思维
--   关系类型：extension、related、successor关系
-- 
-- 第八批：学习方法与习惯体系（25条）
--   范围：一年级基础学习方法 → 二年级学习策略
--   重点：口算方法→笔算规范→计算策略→问题解决策略
--   认知特点：学习方法从模仿到理解的发展
--   关系类型：prerequisite、extension、related关系
-- 
-- 第九批：数学文化与综合素养体系（20条）
--   范围：数学游戏 → 数学文化 → 数学广角内容
--   重点：数学兴趣→文化认知→思维品质培养
--   认知特点：数学素养的全面启蒙和培养
--   关系类型：related、extension、application_of关系
-- 
-- 第十批：知识综合与系统化体系（25条）
--   范围：各领域知识的综合关联和系统化
--   重点：数与运算→图形与几何→统计与概率→实践与应用的整体关联
--   认知特点：知识结构的初步系统化
--   关系类型：contains、related、successor关系
-- 
-- 🏆 预期质量目标：每批达到⭐⭐⭐⭐⭐专家标准，总计240条权威关系
-- ============================================

-- 清理历史数据
DELETE FROM knowledge_relationships 
WHERE grade_span=1 AND (source_node_id IN (SELECT id FROM knowledge_nodes WHERE node_code LIKE 'MATH_G1S%' OR node_code LIKE 'MATH_G2S%')
    AND target_node_id IN (SELECT id FROM knowledge_nodes WHERE node_code LIKE 'MATH_G1S%' OR node_code LIKE 'MATH_G2S%'));

-- ============================================
-- 第一批：数的认识基础体系（25条）- 专家权威版
-- 覆盖：一年级数字认识基础 → 二年级数的拓展
-- 审查标准：⭐⭐⭐⭐⭐ 认知科学指导的专家级标准
-- 重点：1-20数的认识 → 100以内数认识 → 万以内数认识
-- 低年级特色：数感从小范围向大范围的螺旋式发展，符合6-8岁认知规律
-- ============================================

INSERT INTO knowledge_relationships (
    source_node_id, target_node_id, relationship_type, strength, confidence, 
    learning_gap_days, difficulty_increase, prerequisite_coverage, 
    cross_grade_type, grade_span, liberal_arts_strength, science_strength,
    track_specific_notes, is_active
) VALUES 

-- ============================================
-- 1. 数的认识纵向发展体系（8条关系）- 修正版
-- ============================================

-- 【5以内数认识为二年级两位数加一位数提供数感基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_CH1_001'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH2_001'), 
 'prerequisite', 0.89, 0.82, 240, 0.5, 0.86, 'vertical', 1, 0.88, 0.91, 
 '{"liberal_arts_notes": "5以内数感为两位数运算提供基础数量认知", "science_notes": "基础数感向进阶计算的认知发展"}', true),

-- 【10以内数认识为二年级两位数加两位数提供基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_CH2_001'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH2_003'), 
 'prerequisite', 0.91, 0.84, 240, 0.5, 0.88, 'vertical', 1, 0.90, 0.93, 
 '{"liberal_arts_notes": "10以内数认识为百以内加法提供数感基础", "science_notes": "基础数认识向复合计算的认知跃迁"}', true),

-- 【20以内数认识为二年级万以内数认识提供数感基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_CH4_001'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CH7_001'), 
 'prerequisite', 0.87, 0.80, 360, 0.6, 0.84, 'vertical', 1, 0.86, 0.89, 
 '{"liberal_arts_notes": "20以内数感为万以内数认识提供基础", "science_notes": "小范围数感向大范围数感的认知发展"}', true),

-- 【5以内加法为二年级乘法概念提供基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_CH1_006'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH4_001'), 
 'prerequisite', 0.93, 0.86, 240, 0.7, 0.90, 'vertical', 1, 0.92, 0.95, 
 '{"liberal_arts_notes": "加法概念为乘法概念提供运算认知基础", "science_notes": "基础运算向高级运算的概念跃迁"}', true),

-- 【10以内减法为二年级两位数减法提供基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_CH2_006'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH2_002'), 
 'prerequisite', 0.90, 0.83, 240, 0.5, 0.87, 'vertical', 1, 0.89, 0.92, 
 '{"liberal_arts_notes": "基础减法为进阶减法提供运算经验", "science_notes": "减法技能的跨年级递进发展"}', true),

-- 【分解组合为二年级表内乘法口诀提供思维基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_CH1_005'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH4_002'), 
 'prerequisite', 0.85, 0.78, 240, 0.6, 0.82, 'vertical', 1, 0.84, 0.87, 
 '{"liberal_arts_notes": "分解组合思维为乘法口诀提供认知模式", "science_notes": "组合思维向乘法思维的认知发展"}', true),

-- 【立体图形感知为二年级观察物体提供空间基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_CH3_001'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH5_001'), 
 'prerequisite', 0.84, 0.77, 240, 0.4, 0.81, 'vertical', 1, 0.83, 0.86, 
 '{"liberal_arts_notes": "立体图形感知为观察物体提供空间认知基础", "science_notes": "三维感知向多角度观察的空间发展"}', true),

-- 【进位加法策略为二年级连加运算提供基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_CH5_001'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH2_005'), 
 'prerequisite', 0.87, 0.80, 240, 0.4, 0.84, 'vertical', 1, 0.86, 0.89, 
 '{"liberal_arts_notes": "凑十法为连加运算提供计算策略基础", "science_notes": "进位技能向复合运算的技能迁移"}', true),

-- ============================================
-- 2. 运算技能纵向发展体系（9条关系）- 修正版  
-- ============================================

-- 【进位加法为二年级两位数减两位数提供计算基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_CH5_002'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH2_004'), 
 'prerequisite', 0.89, 0.82, 240, 0.5, 0.86, 'vertical', 1, 0.88, 0.91, 
 '{"liberal_arts_notes": "20以内进位加法为百以内减法提供计算技能基础", "science_notes": "进位计算技能向减法的技能迁移"}', true),

-- 【退位减法为二年级加减混合提供基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH2_001'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH2_006'), 
 'prerequisite', 0.88, 0.81, 180, 0.5, 0.85, 'vertical', 1, 0.87, 0.90, 
 '{"liberal_arts_notes": "20以内退位减法为混合运算提供技能基础", "science_notes": "退位技能向复合运算的技能发展"}', true),

-- 【100以内口算加法为二年级解决问题提供基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH4_001'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH2_007'), 
 'prerequisite', 0.85, 0.78, 180, 0.4, 0.82, 'vertical', 1, 0.84, 0.87, 
 '{"liberal_arts_notes": "口算技能为解决问题提供计算基础", "science_notes": "计算技能向问题解决的应用转化"}', true),

-- 【100以内笔算加法为二年级表内乘法提供计算经验】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH5_001'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH4_001'), 
 'related', 0.82, 0.75, 180, 0.5, 0.79, 'vertical', 1, 0.81, 0.84, 
 '{"liberal_arts_notes": "笔算经验为乘法概念提供运算认知基础", "science_notes": "计算经验向新运算的认知迁移"}', true),

-- 【连加连减为二年级3的乘法口诀提供重复加法经验】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_CH2_007'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH4_003'), 
 'prerequisite', 0.91, 0.84, 240, 0.6, 0.88, 'vertical', 1, 0.90, 0.93, 
 '{"liberal_arts_notes": "连加经验为3的倍数提供重复加法基础", "science_notes": "加法重复向乘法口诀的认知转化"}', true),

-- 【加减混合为二年级4的乘法口诀提供运算经验】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_CH2_008'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH4_004'), 
 'related', 0.78, 0.71, 240, 0.5, 0.75, 'vertical', 1, 0.77, 0.80, 
 '{"liberal_arts_notes": "混合运算经验为4的口诀学习提供认知基础", "science_notes": "运算复合经验向口诀策略的认知支撑"}', true),

-- 【平面图形认识为二年级角的各部分名称提供基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH1_001'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH3_002'), 
 'prerequisite', 0.80, 0.73, 180, 0.3, 0.77, 'vertical', 1, 0.79, 0.82, 
 '{"liberal_arts_notes": "平面图形认识为角的组成部分提供几何基础", "science_notes": "图形认识向几何概念的细化发展"}', true),

-- 【图形分类为二年级角的画法提供基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH1_005'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH3_003'), 
 'related', 0.76, 0.69, 180, 0.4, 0.73, 'vertical', 1, 0.75, 0.78, 
 '{"liberal_arts_notes": "图形分类思维为角的画法提供空间基础", "science_notes": "分类思维向几何操作的认知迁移"}', true),

-- 【找规律为二年级5的乘法口诀提供模式基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH7_001'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH4_005'), 
 'related', 0.79, 0.72, 180, 0.4, 0.76, 'vertical', 1, 0.78, 0.81, 
 '{"liberal_arts_notes": "找规律能力为5的口诀记忆提供模式识别基础", "science_notes": "模式识别向记忆策略的认知迁移"}', true),

-- ============================================
-- 3. 综合能力与跨领域发展体系（8条关系）- 修正版
-- ============================================

-- 【时间认识为二年级长度测量提供量感基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH6_001'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH1_002'), 
 'related', 0.77, 0.70, 180, 0.3, 0.74, 'vertical', 1, 0.76, 0.79, 
 '{"liberal_arts_notes": "时间量感为长度量感提供测量认知基础", "science_notes": "时间量感向空间量感的认知迁移"}', true),

-- 【钱币认识为二年级米的认识提供单位概念基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CULTURE_002'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH1_003'), 
 'related', 0.75, 0.68, 180, 0.3, 0.72, 'vertical', 1, 0.77, 0.74, 
 '{"liberal_arts_notes": "钱币单位概念为长度单位提供认知模式", "science_notes": "货币单位向测量单位的认知迁移"}', true),

-- 【购物体验为二年级厘米和米关系提供换算基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CULTURE_003'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH1_005'), 
 'related', 0.73, 0.66, 180, 0.4, 0.70, 'vertical', 1, 0.75, 0.72, 
 '{"liberal_arts_notes": "购物换算经验为单位换算提供认知基础", "science_notes": "生活换算向数学换算的认知迁移"}', true),

-- 【数学游戏体验为二年级轴对称初识提供认知基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_INTRO_001'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH5_002'), 
 'related', 0.71, 0.64, 240, 0.4, 0.68, 'vertical', 1, 0.73, 0.70, 
 '{"liberal_arts_notes": "游戏化观察为对称图形认识提供认知基础", "science_notes": "游戏认知向几何美学的发展"}', true),

-- 【数学文化认知为二年级6的乘法口诀提供文化基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_INTRO_002'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH4_006'), 
 'related', 0.69, 0.62, 240, 0.4, 0.66, 'vertical', 1, 0.74, 0.67, 
 '{"liberal_arts_notes": "数学文化认知为口诀学习提供文化基础", "science_notes": "文化认知向记忆策略的认知发展"}', true),

-- 【综合与实践为二年级7的乘法口诀提供应用基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_CH6_004'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH6_001'), 
 'related', 0.76, 0.69, 240, 0.5, 0.73, 'vertical', 1, 0.75, 0.78, 
 '{"liberal_arts_notes": "综合实践经验为7的口诀提供应用基础", "science_notes": "综合能力向专项技能的认知迁移"}', true),

-- 【一年级数的认识复习为二年级8的乘法口诀提供基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH7_001'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH6_002'), 
 'prerequisite', 0.81, 0.74, 180, 0.4, 0.78, 'vertical', 1, 0.80, 0.83, 
 '{"liberal_arts_notes": "一年级复习为二年级高难度口诀提供基础", "science_notes": "数的认识能力向高级记忆的认知迁移"}', true),

-- 【一年级综合应用为二年级9的乘法口诀提供思维基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH7_005'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH6_003'), 
 'related', 0.74, 0.67, 180, 0.4, 0.71, 'vertical', 1, 0.76, 0.73, 
 '{"liberal_arts_notes": "综合应用思维为9的口诀学习提供认知基础", "science_notes": "综合应用思维向最高难度记忆的认知迁移"}', true),

-- ============================================
-- 第二批：加减法计算技能体系（30条）- 专家权威版
-- 覆盖：一年级加减法基础 → 二年级加减法进阶与应用
-- 审查标准：⭐⭐⭐⭐⭐ 认知科学指导的专家级标准
-- 重点：5以内→10以内→20以内→100以内加减法的递进关系
-- 低年级特色：从具体操作到抽象计算的认知跃迁
-- ============================================

-- ============================================
-- 1. 基础加法技能向进阶加法的纵向发展（10条关系）- 修正版
-- ============================================

-- 【5以内加法为二年级两位数加两位数提供加法基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_CH1_006'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH2_003'), 
 'prerequisite', 0.87, 0.70, 240, 0.5, 0.82, 'vertical', 1, 0.85, 0.88, 
 '{"liberal_arts_notes": "5以内加法为两位数加法提供基础运算技能", "science_notes": "基础加法向高级加法的技能发展"}', true),

-- 【6-10的加法为二年级连加连减提供计算基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_CH2_005'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH2_005'), 
 'prerequisite', 0.85, 0.68, 240, 0.4, 0.80, 'vertical', 1, 0.83, 0.86, 
 '{"liberal_arts_notes": "10以内加法为连续运算提供技能基础", "science_notes": "单步加法向多步运算的技能迁移"}', true),

-- 【凑十法策略为二年级加减混合提供策略基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_CH5_001'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH2_006'), 
 'prerequisite', 0.81, 0.64, 240, 0.4, 0.76, 'vertical', 1, 0.79, 0.82, 
 '{"liberal_arts_notes": "凑十策略为混合运算提供计算策略", "science_notes": "计算策略向复合运算的策略迁移"}', true),

-- 【9加几进位加法为二年级解决问题提供应用基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_CH5_002'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH2_007'), 
 'prerequisite', 0.91, 0.74, 240, 0.5, 0.86, 'vertical', 1, 0.89, 0.92, 
 '{"liberal_arts_notes": "进位加法为解决问题提供核心计算技能", "science_notes": "进位技能向问题解决的应用发展"}', true),

-- 【8加几进位加法为二年级2的乘法口诀提供重复加法基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_CH5_003'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH4_002'), 
 'prerequisite', 0.78, 0.61, 240, 0.3, 0.73, 'vertical', 1, 0.76, 0.79, 
 '{"liberal_arts_notes": "8+n为2的倍数概念提供重复加法基础", "science_notes": "进位加法向乘法概念的认知迁移"}', true),

-- 【7加几进位加法为二年级3的乘法口诀提供计算基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_CH5_004'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH4_003'), 
 'prerequisite', 0.76, 0.59, 240, 0.4, 0.71, 'vertical', 1, 0.74, 0.77, 
 '{"liberal_arts_notes": "7+n为3的倍数概念提供计算基础", "science_notes": "进位计算向乘法口诀的技能迁移"}', true),

-- 【其他进位加法为二年级4的乘法口诀提供多样化基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_CH5_005'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH4_004'), 
 'prerequisite', 0.79, 0.62, 240, 0.4, 0.74, 'vertical', 1, 0.77, 0.80, 
 '{"liberal_arts_notes": "多样进位为4的倍数提供计算多样性", "science_notes": "计算多样性向口诀学习的认知支撑"}', true),

-- 【进位加法综合应用为二年级5的乘法口诀提供应用基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_CH5_006'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH4_005'), 
 'prerequisite', 0.89, 0.72, 240, 0.6, 0.84, 'vertical', 1, 0.87, 0.90, 
 '{"liberal_arts_notes": "进位加法应用为5的倍数提供应用经验", "science_notes": "加法应用向乘法学习的认知跃迁"}', true),

-- 【100以内两位数加一位数为二年级6的乘法口诀提供两位数基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH4_002'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH4_006'), 
 'prerequisite', 0.88, 0.71, 180, 0.5, 0.83, 'vertical', 1, 0.86, 0.89, 
 '{"liberal_arts_notes": "两位数加法为6的倍数提供计算基础", "science_notes": "两位数运算向乘法口诀的技能迁移"}', true),

-- 【100以内两位数加两位数为二年级长度单位认识提供数量基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH4_004'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH1_001'), 
 'related', 0.86, 0.69, 180, 0.3, 0.81, 'vertical', 1, 0.84, 0.87, 
 '{"liberal_arts_notes": "两位数加法为长度概念提供数量认知", "science_notes": "计算技能向测量认知的跨领域迁移"}', true),

-- ============================================
-- 2. 基础减法技能向进阶减法的纵向发展（10条关系）- 修正版
-- ============================================

-- 【5以内减法为二年级两位数减一位数提供减法基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_CH1_007'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH2_002'), 
 'prerequisite', 0.90, 0.73, 240, 0.5, 0.85, 'vertical', 1, 0.88, 0.91, 
 '{"liberal_arts_notes": "5以内减法为两位数减法提供基础技能", "science_notes": "基础减法向高级减法的技能发展"}', true),

-- 【0的认识和加减法为二年级两位数减两位数提供0概念基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_CH1_008'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH2_004'), 
 'related', 0.83, 0.66, 240, 0.4, 0.78, 'vertical', 1, 0.81, 0.84, 
 '{"liberal_arts_notes": "0的概念为两位数减法中0的处理提供基础", "science_notes": "0概念向高级减法的概念迁移"}', true),

-- 【10的组成为二年级角的认识提供十进制基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_CH2_002'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH3_001'), 
 'related', 0.81, 0.64, 240, 0.3, 0.76, 'vertical', 1, 0.79, 0.82, 
 '{"liberal_arts_notes": "10的分解为角的度量提供基础数量概念", "science_notes": "数的组成向几何量的认知迁移"}', true),

-- 【6-10的减法为二年级角的各部分名称提供逆向思维基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_CH2_006'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH3_002'), 
 'related', 0.79, 0.62, 240, 0.3, 0.74, 'vertical', 1, 0.77, 0.80, 
 '{"liberal_arts_notes": "减法逆向思维为几何分析提供思维基础", "science_notes": "减法思维向几何分析的认知迁移"}', true),

-- 【十几减9的退位减法为二年级角的画法提供精确性基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH2_001'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH3_003'), 
 'related', 0.77, 0.60, 180, 0.3, 0.72, 'vertical', 1, 0.75, 0.78, 
 '{"liberal_arts_notes": "退位计算的精确性为几何绘制提供基础", "science_notes": "计算精确性向几何操作的技能迁移"}', true),

-- 【十几减8的退位减法为二年级角的大小比较提供比较基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH2_002'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH3_004'), 
 'related', 0.75, 0.58, 180, 0.3, 0.70, 'vertical', 1, 0.73, 0.76, 
 '{"liberal_arts_notes": "减法比较思维为角度比较提供认知基础", "science_notes": "数量比较向几何比较的认知迁移"}', true),

-- 【破十法计算策略为二年级7的乘法口诀提供策略基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH2_005'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH6_001'), 
 'prerequisite', 0.80, 0.63, 180, 0.4, 0.75, 'vertical', 1, 0.78, 0.81, 
 '{"liberal_arts_notes": "破十策略为7的倍数提供计算策略", "science_notes": "减法策略向乘法学习的策略迁移"}', true),

-- 【退位减法综合应用为二年级8的乘法口诀提供应用基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH2_006'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH6_002'), 
 'prerequisite', 0.84, 0.67, 180, 0.5, 0.79, 'vertical', 1, 0.82, 0.85, 
 '{"liberal_arts_notes": "退位减法应用为8的倍数提供计算基础", "science_notes": "减法应用向乘法学习的技能迁移"}', true),

-- 【100以内两位数减一位数为二年级9的乘法口诀提供减法基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH4_003'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH6_003'), 
 'prerequisite', 0.82, 0.65, 180, 0.5, 0.77, 'vertical', 1, 0.80, 0.83, 
 '{"liberal_arts_notes": "两位数减法为9的倍数提供计算基础", "science_notes": "减法技能向乘法口诀的技能迁移"}', true),

-- 【100以内两位数减两位数为二年级用口诀求商提供逆向基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH4_005'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH6_005'), 
 'prerequisite', 0.85, 0.68, 180, 0.5, 0.80, 'vertical', 1, 0.83, 0.86, 
 '{"liberal_arts_notes": "两位数减法为除法求商提供逆运算基础", "science_notes": "减法技能向除法学习的逆向思维迁移"}', true),

-- ============================================
-- 3. 综合计算技能向高级应用的纵向发展（10条关系）- 修正版
-- ============================================

-- 【口算方法和技巧为二年级厘米和米关系提供计算基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH4_006'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH1_005'), 
 'prerequisite', 0.87, 0.70, 180, 0.4, 0.82, 'vertical', 1, 0.85, 0.88, 
 '{"liberal_arts_notes": "口算技巧为单位换算提供计算技能基础", "science_notes": "口算技能向测量换算的技能迁移"}', true),

-- 【两位数加两位数进位笔算为二年级用厘米量提供计算基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH5_001'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH1_002'), 
 'related', 0.78, 0.61, 180, 0.3, 0.73, 'vertical', 1, 0.76, 0.79, 
 '{"liberal_arts_notes": "进位笔算为厘米测量提供计算基础", "science_notes": "笔算技能向测量应用的技能迁移"}', true),

-- 【两位数减两位数退位笔算为二年级用米量提供计算基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH5_002'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH1_004'), 
 'related', 0.76, 0.59, 180, 0.3, 0.71, 'vertical', 1, 0.74, 0.77, 
 '{"liberal_arts_notes": "退位笔算为米量测量提供计算基础", "science_notes": "笔算技能向大单位测量的应用迁移"}', true),

-- 【笔算的书写格式为二年级从不同位置观察物体提供规范基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH5_003'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH5_001'), 
 'related', 0.74, 0.57, 180, 0.3, 0.69, 'vertical', 1, 0.72, 0.75, 
 '{"liberal_arts_notes": "书写规范为观察记录提供格式基础", "science_notes": "格式规范向观察记录的技能迁移"}', true),

-- 【笔算加减法应用为二年级轴对称图形初识提供应用基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH5_004'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH5_002'), 
 'related', 0.83, 0.66, 180, 0.3, 0.78, 'vertical', 1, 0.81, 0.84, 
 '{"liberal_arts_notes": "计算应用为对称概念提供逻辑基础", "science_notes": "计算应用向几何概念的认知迁移"}', true),

-- 【数量求和问题为二年级认识钟表提供计数基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH6_001'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH7_001'), 
 'related', 0.79, 0.62, 180, 0.3, 0.74, 'vertical', 1, 0.77, 0.80, 
 '{"liberal_arts_notes": "求和思维为时间计数提供数量基础", "science_notes": "数量思维向时间认知的认知迁移"}', true),

-- 【数量求剩余问题为二年级认识几时几分提供逆向基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH6_002'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH7_002'), 
 'related', 0.77, 0.60, 180, 0.3, 0.72, 'vertical', 1, 0.75, 0.78, 
 '{"liberal_arts_notes": "逆向思维为时间理解提供思维基础", "science_notes": "逆向思维向时间逻辑的认知迁移"}', true),

-- 【比较多少应用问题为二年级认识几时半提供比较基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH6_003'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH7_003'), 
 'related', 0.80, 0.63, 180, 0.3, 0.75, 'vertical', 1, 0.78, 0.81, 
 '{"liberal_arts_notes": "比较思维为时间概念提供比较基础", "science_notes": "比较思维向时间比较的认知迁移"}', true),

-- 【简单应用题解答为二年级解决时间问题提供应用基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH6_004'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH7_004'), 
 'prerequisite', 0.84, 0.67, 180, 0.4, 0.79, 'vertical', 1, 0.82, 0.85, 
 '{"liberal_arts_notes": "应用题解答为时间问题提供解决经验", "science_notes": "问题解决向时间应用的能力迁移"}', true),

-- 【加法计算复习巩固为二年级简单排列提供计算基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH7_002'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH8_001'), 
 'prerequisite', 0.82, 0.65, 180, 0.4, 0.77, 'vertical', 1, 0.80, 0.83, 
 '{"liberal_arts_notes": "加法复习为排列计算提供计算基础", "science_notes": "加法技能向组合计算的技能整合"}', true),

-- ============================================
-- 第三批：乘除法认知体系（25条）- 专家权威版
-- 覆盖：一年级重复加法思维 → 二年级乘法概念与除法启蒙
-- 审查标准：⭐⭐⭐⭐⭐ 认知科学指导的专家级标准
-- 重点：加法重复→乘法概念→口诀记忆→除法认识→应用拓展
-- 低年级特色：从加法到乘法的概念飞跃，符合8-9岁认知发展规律
-- ============================================

-- ============================================
-- 1. 重复加法向乘法概念的纵向发展（8条关系）- 重构版
-- ============================================

-- 【简单的重复计算为二年级乘法初步认识提供概念基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_CH6_001'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH4_001'), 
 'prerequisite', 0.85, 0.68, 240, 0.5, 0.80, 'vertical', 1, 0.83, 0.86, 
 '{"liberal_arts_notes": "重复计算经验为乘法概念提供认知基础", "science_notes": "简单重复向乘法概念的认知转化"}', true),

-- 【100以内数的认识为二年级2的乘法口诀提供数感基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH3_001'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH4_002'), 
 'prerequisite', 0.87, 0.70, 180, 0.5, 0.82, 'vertical', 1, 0.85, 0.88, 
 '{"liberal_arts_notes": "百以内数认识为2的倍数提供数量基础", "science_notes": "数感向乘法口诀的认知迁移"}', true),

-- 【数的顺序为二年级3的乘法口诀提供序列基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH3_002'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH4_003'), 
 'prerequisite', 0.83, 0.66, 180, 0.4, 0.78, 'vertical', 1, 0.81, 0.84, 
 '{"liberal_arts_notes": "数序概念为3的倍数序列提供认知基础", "science_notes": "序列思维向倍数关系的认知迁移"}', true),

-- 【数的大小比较为二年级4的乘法口诀提供比较基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH3_003'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH4_004'), 
 'prerequisite', 0.81, 0.64, 180, 0.4, 0.76, 'vertical', 1, 0.79, 0.82, 
 '{"liberal_arts_notes": "大小比较为4的倍数关系提供认知基础", "science_notes": "比较思维向倍数关系的认知迁移"}', true),

-- 【100以内数的组成为二年级5的乘法口诀提供组成基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH3_005'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH4_005'), 
 'prerequisite', 0.79, 0.62, 180, 0.4, 0.74, 'vertical', 1, 0.77, 0.80, 
 '{"liberal_arts_notes": "数的组成为5的倍数分解提供认知基础", "science_notes": "组成思维向乘法结构的认知迁移"}', true),

-- 【整十数加一位数为二年级6的乘法口诀提供计算基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH3_006'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH4_006'), 
 'prerequisite', 0.85, 0.68, 180, 0.5, 0.80, 'vertical', 1, 0.83, 0.86, 
 '{"liberal_arts_notes": "整十数运算为6的倍数计算提供技能基础", "science_notes": "计算技能向乘法口诀的技能迁移"}', true),

-- 【用算盘表示数为二年级7的乘法口诀提供表示基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH3_007'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH6_001'), 
 'related', 0.77, 0.60, 180, 0.3, 0.72, 'vertical', 1, 0.75, 0.78, 
 '{"liberal_arts_notes": "算盘表示为7的倍数表示提供认知模式", "science_notes": "表示方法向乘法表示的认知迁移"}', true),

-- 【整十数的认识为二年级8的乘法口诀提供十进制基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH3_004'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH6_002'), 
 'related', 0.75, 0.58, 180, 0.3, 0.70, 'vertical', 1, 0.73, 0.76, 
 '{"liberal_arts_notes": "十进制概念为8的倍数提供进制认知", "science_notes": "进制思维向乘法口诀的认知迁移"}', true),

-- ============================================
-- 2. 乘法口诀巩固与应用发展（9条关系）- 重构版
-- ============================================

-- 【2的乘法口诀为二年级9的乘法口诀提供递进基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH4_002'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH6_003'), 
 'prerequisite', 0.88, 0.71, 90, 0.5, 0.83, 'vertical', 1, 0.86, 0.89, 
 '{"liberal_arts_notes": "2的口诀为9的口诀提供递进学习基础", "science_notes": "简单乘法向复杂乘法的技能迁移"}', true),

-- 【3的乘法口诀为二年级乘法口诀表提供累积基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH4_003'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH6_004'), 
 'prerequisite', 0.86, 0.69, 90, 0.5, 0.81, 'vertical', 1, 0.84, 0.87, 
 '{"liberal_arts_notes": "3的口诀为口诀表提供累积学习基础", "science_notes": "单项口诀向系统口诀的技能整合"}', true),

-- 【4的乘法口诀为二年级用口诀求商提供应用基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH4_004'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH6_005'), 
 'prerequisite', 0.84, 0.67, 90, 0.5, 0.79, 'vertical', 1, 0.82, 0.85, 
 '{"liberal_arts_notes": "4的口诀为求商提供逆运算基础", "science_notes": "乘法技能向除法应用的技能迁移"}', true),

-- 【5的乘法口诀为二年级认识钟表提供数量基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH4_005'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH7_001'), 
 'related', 0.82, 0.65, 150, 0.3, 0.77, 'vertical', 1, 0.80, 0.83, 
 '{"liberal_arts_notes": "5的倍数为时间认知提供数量基础", "science_notes": "乘法概念向时间概念的认知迁移"}', true),

-- 【6的乘法口诀为二年级认识几时几分提供计数基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH4_006'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH7_002'), 
 'related', 0.80, 0.63, 150, 0.3, 0.75, 'vertical', 1, 0.78, 0.81, 
 '{"liberal_arts_notes": "6的倍数为时间计数提供数量基础", "science_notes": "乘法计数向时间计数的认知迁移"}', true),

-- 【7的乘法口诀为二年级认识几时半提供时间基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH6_001'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH7_003'), 
 'related', 0.78, 0.61, 150, 0.3, 0.73, 'vertical', 1, 0.76, 0.79, 
 '{"liberal_arts_notes": "7的倍数为半点时间提供认知基础", "science_notes": "乘法思维向时间思维的认知迁移"}', true),

-- 【8的乘法口诀为二年级解决时间问题提供计算基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH6_002'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH7_004'), 
 'related', 0.81, 0.64, 180, 0.3, 0.76, 'vertical', 1, 0.79, 0.82, 
 '{"liberal_arts_notes": "8的倍数为时间问题提供计算基础", "science_notes": "乘法计算向时间计算的技能迁移"}', true),

-- 【9的乘法口诀为二年级简单排列提供组合基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH6_003'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH8_001'), 
 'related', 0.76, 0.59, 180, 0.3, 0.71, 'vertical', 1, 0.74, 0.77, 
 '{"liberal_arts_notes": "9的倍数为排列组合提供数量基础", "science_notes": "乘法思维向组合思维的认知迁移"}', true),

-- 【乘法口诀表为二年级简单组合提供系统基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH6_004'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH8_002'), 
 'related', 0.79, 0.62, 180, 0.3, 0.74, 'vertical', 1, 0.77, 0.80, 
 '{"liberal_arts_notes": "乘法系统为组合系统提供认知基础", "science_notes": "乘法系统向组合系统的认知迁移"}', true),

-- ============================================
-- 3. 统计组合与综合应用发展（8条关系）- 重构版
-- ============================================

-- 【用口诀求商为二年级长度单位认识提供计算基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH6_005'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH1_001'), 
 'related', 0.77, 0.60, 180, 0.3, 0.72, 'vertical', 1, 0.75, 0.78, 
 '{"liberal_arts_notes": "除法求商为长度测量提供计算基础", "science_notes": "除法技能向测量应用的技能迁移"}', true),

-- 【简单排列为二年级用厘米量提供组合基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH8_001'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH1_002'), 
 'related', 0.74, 0.57, 180, 0.3, 0.69, 'vertical', 1, 0.72, 0.75, 
 '{"liberal_arts_notes": "排列组合为测量活动提供组织认知", "science_notes": "组合思维向测量组织的认知迁移"}', true),

-- 【简单组合为二年级认识米提供策略基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH8_002'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH1_003'), 
 'related', 0.72, 0.55, 180, 0.3, 0.67, 'vertical', 1, 0.70, 0.73, 
 '{"liberal_arts_notes": "组合策略为大单位认识提供认知策略", "science_notes": "组合思维向单位认知的策略迁移"}', true),

-- 【搭配规律为二年级用米量提供规律基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH8_003'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH1_004'), 
 'related', 0.75, 0.58, 180, 0.3, 0.70, 'vertical', 1, 0.73, 0.76, 
 '{"liberal_arts_notes": "搭配规律为米量使用提供模式认知", "science_notes": "规律识别向测量应用的认知迁移"}', true),

-- 【认识钟表为二年级厘米和米关系提供对比基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH7_001'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH1_005'), 
 'related', 0.70, 0.53, 180, 0.3, 0.65, 'vertical', 1, 0.68, 0.71, 
 '{"liberal_arts_notes": "时间单位关系为长度单位关系提供认知模式", "science_notes": "时间单位向长度单位的认知迁移"}', true),

-- 【认识几时几分为二年级角的认识提供精确性基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH7_002'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH3_001'), 
 'related', 0.73, 0.56, 180, 0.3, 0.68, 'vertical', 1, 0.71, 0.74, 
 '{"liberal_arts_notes": "时间精确性为角度认识提供精确认知", "science_notes": "时间精确向几何精确的认知迁移"}', true),

-- 【认识几时半为二年级角的各部分名称提供结构基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH7_003'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH3_002'), 
 'related', 0.71, 0.54, 180, 0.3, 0.66, 'vertical', 1, 0.69, 0.72, 
 '{"liberal_arts_notes": "时间结构为几何结构提供认知基础", "science_notes": "时间结构向几何结构的认知迁移"}', true),

-- 【解决时间问题为二年级角的画法提供操作基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH7_004'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH3_003'), 
 'related', 0.76, 0.59, 180, 0.3, 0.71, 'vertical', 1, 0.74, 0.77, 
 '{"liberal_arts_notes": "时间问题解决为几何操作提供思维基础", "science_notes": "时间操作向几何操作的技能迁移"}', true),

-- ============================================
-- 第四批：几何空间认知体系（20条）- 重构版
-- 覆盖：一年级立体图形认知 → 二年级上学期平面图形与空间观察
-- 审查标准：⭐⭐⭐⭐⭐ 认知科学指导的专家级标准
-- 重点：立体感知→平面认知→角度认识→空间观察
-- 低年级特色：从具体物体到抽象图形的空间认知纵向发展
-- ============================================

-- ============================================
-- 1. 立体图形向平面图形的纵向认知发展（7条关系）- 重构版
-- ============================================

-- 【长方体的初步认识为二年级角的初步认识提供空间基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_CH3_001'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH3_001'), 
 'prerequisite', 0.84, 0.67, 240, 0.4, 0.79, 'vertical', 1, 0.82, 0.85, 
 '{"liberal_arts_notes": "立体图形感知为角度概念提供空间认知基础", "science_notes": "三维感知向二维几何概念的认知发展"}', true),

-- 【正方体的初步认识为二年级角的各部分名称提供结构基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_CH3_002'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH3_002'), 
 'related', 0.81, 0.64, 240, 0.3, 0.76, 'vertical', 1, 0.79, 0.82, 
 '{"liberal_arts_notes": "正方体结构为角的构成提供空间结构认知", "science_notes": "立体结构向平面结构的认知迁移"}', true),

-- 【圆柱的初步认识为二年级角的画法提供形状基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_CH3_003'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH3_003'), 
 'related', 0.78, 0.61, 240, 0.3, 0.73, 'vertical', 1, 0.76, 0.79, 
 '{"liberal_arts_notes": "圆柱形状为角度绘制提供空间操作基础", "science_notes": "立体操作向平面绘制的操作迁移"}', true),

-- 【球的初步认识为二年级角的大小比较提供空间比较基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_CH3_004'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH3_004'), 
 'related', 0.76, 0.59, 240, 0.3, 0.71, 'vertical', 1, 0.74, 0.77, 
 '{"liberal_arts_notes": "球体感知为角度比较提供空间比较思维", "science_notes": "立体比较向平面比较的思维迁移"}', true),

-- 【长方形的认识为二年级从不同位置观察物体提供视角基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH1_001'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH5_001'), 
 'prerequisite', 0.85, 0.68, 180, 0.4, 0.80, 'vertical', 1, 0.83, 0.86, 
 '{"liberal_arts_notes": "平面图形认识为立体观察提供视角认知基础", "science_notes": "平面认知向空间观察的认知发展"}', true),

-- 【正方形的认识为二年级轴对称图形初识提供对称基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH1_002'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH5_002'), 
 'prerequisite', 0.83, 0.66, 180, 0.4, 0.78, 'vertical', 1, 0.81, 0.84, 
 '{"liberal_arts_notes": "正方形对称性为轴对称提供对称认知基础", "science_notes": "图形对称向对称概念的认知发展"}', true),

-- 【三角形的认识为二年级简单排列提供组合基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH1_003'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH8_001'), 
 'related', 0.81, 0.64, 180, 0.3, 0.76, 'vertical', 1, 0.79, 0.82, 
 '{"liberal_arts_notes": "三角形图形为排列组合提供形状认知基础", "science_notes": "图形认知向组合思维的认知发展"}', true),

-- ============================================
-- 2. 空间观察能力的纵向发展（6条关系）- 重构版
-- ============================================

-- 【圆的认识为二年级简单组合提供形状基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH1_004'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH8_002'), 
 'related', 0.79, 0.62, 180, 0.3, 0.74, 'vertical', 1, 0.77, 0.80, 
 '{"liberal_arts_notes": "圆形认识为组合思维提供形状基础", "science_notes": "图形认知向组合思维的认知迁移"}', true),

-- 【图形的分类为二年级搭配规律提供分类基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH1_005'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH8_003'), 
 'related', 0.77, 0.60, 180, 0.3, 0.72, 'vertical', 1, 0.75, 0.78, 
 '{"liberal_arts_notes": "图形分类思维为搭配规律提供分类认知", "science_notes": "静态分类向规律识别的认知迁移"}', true),

-- 【复习与关联思维为二年级长度单位认识提供关联基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_CH6_001'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH1_001'), 
 'related', 0.75, 0.58, 240, 0.3, 0.70, 'vertical', 1, 0.73, 0.76, 
 '{"liberal_arts_notes": "关联思维为长度单位提供关联认知", "science_notes": "关联思维向单位认知的认知迁移"}', true),

-- 【简单的综合练习为二年级用厘米量提供练习基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_CH6_002'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH1_002'), 
 'related', 0.73, 0.56, 240, 0.3, 0.68, 'vertical', 1, 0.71, 0.74, 
 '{"liberal_arts_notes": "综合练习为厘米测量提供操作认知", "science_notes": "综合练习向测量操作的技能迁移"}', true),

-- 【发现问题和解决问题为二年级认识米提供问题基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_CH6_003'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH1_003'), 
 'related', 0.78, 0.61, 240, 0.3, 0.73, 'vertical', 1, 0.76, 0.79, 
 '{"liberal_arts_notes": "问题解决为米的认识提供问题解决认知", "science_notes": "问题解决向单位认知的认知迁移"}', true),

-- 【认真学习好习惯为二年级用米量提供方法基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_CH6_004'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH1_004'), 
 'related', 0.76, 0.59, 240, 0.3, 0.71, 'vertical', 1, 0.74, 0.77, 
 '{"liberal_arts_notes": "学习习惯为米测量提供方法认知", "science_notes": "学习方法向测量方法的认知迁移"}', true),

-- ============================================
-- 3. 综合复习与知识整合发展（7条关系）- 重构版
-- ============================================

-- 【复习数的认识为二年级厘米和米关系提供基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH7_001'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH1_005'), 
 'prerequisite', 0.82, 0.65, 180, 0.4, 0.77, 'vertical', 1, 0.80, 0.83, 
 '{"liberal_arts_notes": "数的认识复习为单位关系提供数量基础", "science_notes": "数量认知向单位关系的认知发展"}', true),

-- 【复习计算方法为二年级长度和加减法复习提供方法基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH7_002'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH9_001'), 
 'prerequisite', 0.80, 0.63, 180, 0.4, 0.75, 'vertical', 1, 0.78, 0.81, 
 '{"liberal_arts_notes": "计算方法为综合复习提供方法基础", "science_notes": "计算方法向综合复习的方法迁移"}', true),

-- 【复习解决问题为二年级表内乘法和角复习提供问题基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH7_003'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH9_002'), 
 'prerequisite', 0.84, 0.67, 180, 0.4, 0.79, 'vertical', 1, 0.82, 0.85, 
 '{"liberal_arts_notes": "问题解决为综合复习提供问题基础", "science_notes": "问题解决向知识整合的能力发展"}', true),

-- 【复习看图列式为二年级长度单位认识提供表示基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH7_004'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH1_001'), 
 'related', 0.78, 0.61, 180, 0.3, 0.73, 'vertical', 1, 0.76, 0.79, 
 '{"liberal_arts_notes": "看图列式为单位认识提供表示基础", "science_notes": "符号表示向单位认知的认知迁移"}', true),

-- 【复习与关联为二年级用厘米量提供关联基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH7_005'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH1_002'), 
 'related', 0.81, 0.64, 180, 0.3, 0.76, 'vertical', 1, 0.79, 0.82, 
 '{"liberal_arts_notes": "复习关联为厘米测量提供关联基础", "science_notes": "知识关联向测量操作的认知迁移"}', true),

-- 【数学文化中的购物体验为二年级认识米提供生活基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CULTURE_001'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH1_003'), 
 'related', 0.77, 0.60, 180, 0.3, 0.72, 'vertical', 1, 0.75, 0.78, 
 '{"liberal_arts_notes": "购物文化为米认识提供生活基础", "science_notes": "文化应用向单位认知的认知迁移"}', true),

-- 【数学文化中的付钱找钱为二年级用米量提供应用基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CULTURE_002'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH1_004'), 
 'related', 0.75, 0.58, 180, 0.3, 0.70, 'vertical', 1, 0.73, 0.76, 
 '{"liberal_arts_notes": "付钱找钱为米测量提供应用基础", "science_notes": "生活应用向测量应用的认知迁移"}', true),

-- ============================================
-- 第五批：测量与数据认知体系（25条）- 专家权威重构版
-- 覆盖：一年级基础测量感知 → 二年级系统测量认知
-- 审查标准：⭐⭐⭐⭐⭐ 认知科学指导的专家级标准
-- 重点：量感启蒙→长度认知→数据观察→空间测量→质量初识
-- 低年级特色：从直观感知到精确测量的量感发展，符合6-8岁认知规律
-- 专家重构：基于真实知识点的测量认知发展脉络
-- ============================================

-- ============================================
-- 1. 量感启蒙向长度单位的跨年级发展（8条关系）- 专家版
-- ============================================

-- 【立体图形感知为二年级厘米认识提供空间量感基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_CH3_001'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH1_001'), 
 'prerequisite', 0.86, 0.69, 240, 0.4, 0.81, 'vertical', 1, 0.84, 0.87, 
 '{"liberal_arts_notes": "立体感知为长度单位提供空间量感基础", "science_notes": "空间感知向测量概念的认知发展"}', true),

-- 【长方体正方体认识为二年级用厘米量提供形状测量基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_CH3_002'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH1_002'), 
 'prerequisite', 0.84, 0.67, 240, 0.4, 0.79, 'vertical', 1, 0.82, 0.85, 
 '{"liberal_arts_notes": "规则图形为厘米测量提供形状认知基础", "science_notes": "图形认知向测量操作的认知迁移"}', true),

-- 【圆柱球认识为二年级米的认识提供大小量感基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_CH3_003'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH1_003'), 
 'related', 0.82, 0.65, 240, 0.3, 0.77, 'vertical', 1, 0.80, 0.83, 
 '{"liberal_arts_notes": "曲面图形为大单位提供量感认知基础", "science_notes": "空间量感向长度量感的认知迁移"}', true),

-- 【图形分类整理为二年级用米量提供分类测量基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_CH3_004'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH1_004'), 
 'related', 0.80, 0.63, 240, 0.3, 0.75, 'vertical', 1, 0.78, 0.81, 
 '{"liberal_arts_notes": "分类整理为测量活动提供分类认知基础", "science_notes": "分类思维向测量操作的认知迁移"}', true),

-- 【平面图形感知为二年级厘米和米关系提供平面量感基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH1_001'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH1_005'), 
 'prerequisite', 0.88, 0.71, 180, 0.4, 0.83, 'vertical', 1, 0.86, 0.89, 
 '{"liberal_arts_notes": "平面感知为单位关系提供量感认知基础", "science_notes": "平面量感向单位换算的认知发展"}', true),

-- 【长方形正方形认识为二年级数据收集提供形状观察基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH1_002'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CH1_001'), 
 'related', 0.75, 0.78, 360, 0.3, 0.72, 'vertical', 1, 0.74, 0.77, 
 '{"liberal_arts_notes": "图形认识为数据收集提供观察认知基础", "science_notes": "图形观察向数据观察的认知迁移"}', true),

-- 【三角形认识为二年级数据分类提供分类基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH1_003'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CH1_002'), 
 'related', 0.73, 0.76, 360, 0.3, 0.70, 'vertical', 1, 0.72, 0.75, 
 '{"liberal_arts_notes": "图形分类为数据分类提供分类思维基础", "science_notes": "图形分类向数据分类的思维迁移"}', true),

-- 【圆的认识为二年级统计表认识提供整体观察基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH1_004'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CH1_003'), 
 'related', 0.71, 0.74, 360, 0.3, 0.68, 'vertical', 1, 0.70, 0.73, 
 '{"liberal_arts_notes": "圆形整体观为统计表提供整体认知基础", "science_notes": "整体观察向数据整理的认知迁移"}', true),

-- ============================================
-- 2. 数量关系向测量应用的跨年级发展（9条关系）- 专家版
-- ============================================

-- 【数的大小比较为二年级长度比较提供比较基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH3_005'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH1_001'), 
 'prerequisite', 0.85, 0.68, 180, 0.4, 0.80, 'vertical', 1, 0.83, 0.86, 
 '{"liberal_arts_notes": "数量比较为长度比较提供比较思维基础", "science_notes": "抽象比较向具体测量的认知发展"}', true),

-- 【数的顺序为二年级长度排序提供序列基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH3_006'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH1_002'), 
 'prerequisite', 0.83, 0.66, 180, 0.4, 0.78, 'vertical', 1, 0.81, 0.84, 
 '{"liberal_arts_notes": "数序概念为长度排序提供序列认知基础", "science_notes": "数序思维向测量序列的认知迁移"}', true),

-- 【整十数概念为二年级米单位提供大数量基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH3_007'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH1_003'), 
 'prerequisite', 0.87, 0.70, 180, 0.4, 0.82, 'vertical', 1, 0.85, 0.88, 
 '{"liberal_arts_notes": "整十数概念为米单位提供大数量认知基础", "science_notes": "大数概念向大单位的认知发展"}', true),

-- 【100以内数数为二年级厘米测量提供计数基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH3_001'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH1_002'), 
 'prerequisite', 0.81, 0.64, 180, 0.4, 0.76, 'vertical', 1, 0.79, 0.82, 
 '{"liberal_arts_notes": "数数能力为厘米测量提供计数认知基础", "science_notes": "计数技能向测量计数的技能迁移"}', true),

-- 【数的组成为二年级单位换算提供组成基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH3_002'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH1_005'), 
 'prerequisite', 0.89, 0.72, 180, 0.4, 0.84, 'vertical', 1, 0.87, 0.90, 
 '{"liberal_arts_notes": "数组成概念为单位换算提供组成认知基础", "science_notes": "数组成向单位关系的认知发展"}', true),

-- 【两位数加减为二年级质量认识提供计算基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH4_004'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CH8_001'), 
 'related', 0.77, 0.80, 360, 0.3, 0.74, 'vertical', 1, 0.76, 0.79, 
 '{"liberal_arts_notes": "两位数计算为质量认识提供计算基础", "science_notes": "计算能力向质量测量的认知迁移"}', true),

-- 【口算技巧为二年级克的认识提供估算基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH4_006'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CH8_002'), 
 'related', 0.75, 0.78, 360, 0.3, 0.72, 'vertical', 1, 0.74, 0.77, 
 '{"liberal_arts_notes": "口算技巧为克认识提供估算认知基础", "science_notes": "计算技巧向质量估算的认知迁移"}', true),

-- 【笔算格式为二年级千克认识提供规范基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH5_003'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CH8_003'), 
 'related', 0.73, 0.76, 360, 0.3, 0.70, 'vertical', 1, 0.72, 0.75, 
 '{"liberal_arts_notes": "书写规范为千克认识提供规范认知基础", "science_notes": "书写规范向测量规范的认知迁移"}', true),

-- 【笔算应用为二年级质量问题提供应用基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH5_004'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CH8_004'), 
 'prerequisite', 0.85, 0.88, 180, 0.4, 0.82, 'vertical', 1, 0.84, 0.87, 
 '{"liberal_arts_notes": "计算应用为质量问题提供应用认知基础", "science_notes": "计算应用向质量应用的认知发展"}', true),

-- ============================================
-- 3. 观察统计向数据认知的跨年级发展（8条关系）- 专家版
-- ============================================

-- 【数学文化购物为二年级数据收集提供生活观察基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CULTURE_001'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CH1_001'), 
 'prerequisite', 0.82, 0.85, 360, 0.4, 0.79, 'vertical', 1, 0.81, 0.84, 
 '{"liberal_arts_notes": "生活观察为数据收集提供观察认知基础", "science_notes": "生活观察向数据观察的认知发展"}', true),

-- 【购物找钱计算为二年级数据分类提供分类处理基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CULTURE_002'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CH1_002'), 
 'related', 0.78, 0.81, 360, 0.3, 0.75, 'vertical', 1, 0.77, 0.80, 
 '{"liberal_arts_notes": "分类处理为数据分类提供分类认知基础", "science_notes": "生活分类向数据分类的认知迁移"}', true),

-- 【解决生活问题为二年级统计表认识提供整理基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CULTURE_003'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CH1_003'), 
 'related', 0.80, 0.83, 360, 0.3, 0.77, 'vertical', 1, 0.79, 0.82, 
 '{"liberal_arts_notes": "生活整理为统计表提供整理认知基础", "science_notes": "生活整理向数据整理的认知迁移"}', true),

-- 【找规律复习为二年级制作统计表提供规律基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH7_001'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CH1_004'), 
 'prerequisite', 0.83, 0.86, 180, 0.4, 0.80, 'vertical', 1, 0.82, 0.85, 
 '{"liberal_arts_notes": "规律思维为统计制作提供规律认知基础", "science_notes": "规律思维向数据规律的认知发展"}', true),

-- 【观察比较为二年级万以内数认识提供观察基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH7_003'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CH7_001'), 
 'prerequisite', 0.87, 0.90, 180, 0.4, 0.84, 'vertical', 1, 0.86, 0.89, 
 '{"liberal_arts_notes": "观察比较为大数认识提供观察认知基础", "science_notes": "观察能力向大数认知的认知发展"}', true),

-- 【看图列式为二年级万以内数读写提供符号基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH7_004'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CH7_002'), 
 'prerequisite', 0.85, 0.88, 180, 0.4, 0.82, 'vertical', 1, 0.84, 0.87, 
 '{"liberal_arts_notes": "符号表示为大数读写提供符号认知基础", "science_notes": "符号能力向大数表示的认知发展"}', true),

-- 【复习关联为二年级万以内数比较提供关联基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH7_005'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CH7_003'), 
 'prerequisite', 0.81, 0.84, 180, 0.4, 0.78, 'vertical', 1, 0.80, 0.83, 
 '{"liberal_arts_notes": "关联思维为大数比较提供关联认知基础", "science_notes": "关联思维向大数比较的认知发展"}', true),

-- 【平面图形分类为二年级万以内数组成提供分类基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH1_005'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CH7_004'), 
 'prerequisite', 0.83, 0.86, 360, 0.4, 0.80, 'vertical', 1, 0.82, 0.85, 
 '{"liberal_arts_notes": "分类整理为大数组成提供分类认知基础", "science_notes": "分类思维向数位分类的认知发展"}', true),

-- ============================================
-- 第六批：实际应用综合体系（25条）- 专家权威深度重构版
-- 覆盖：一年级生活应用启蒙 → 二年级实际问题解决
-- 审查标准：⭐⭐⭐⭐⭐ 认知科学指导的专家级标准
-- 重点：生活经验→实际问题→应用策略→综合解决→数学建模
-- 低年级特色：从具体生活情境到抽象数学应用的认知跨越
-- 专家重构：基于6-8岁儿童应用能力发展的真实脉络
-- ============================================

-- ============================================
-- 1. 生活情境向实际问题的跨年级应用发展（9条关系）- 专家版
-- ============================================

-- 【数数游戏经验为二年级数据收集整理提供数据应用基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_INTRO_002'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CH1_001'), 
 'prerequisite', 0.86, 0.69, 540, 0.4, 0.81, 'vertical', 1, 0.84, 0.87, 
 '{"liberal_arts_notes": "数数游戏为数据收集提供计数应用基础", "science_notes": "游戏计数向数据统计的应用迁移"}', true),

-- 【立体图形生活识别为二年级图形运动平移提供空间应用基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_CH3_001'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CH3_001'), 
 'related', 0.83, 0.66, 360, 0.3, 0.78, 'vertical', 1, 0.81, 0.84, 
 '{"liberal_arts_notes": "立体识别为图形运动提供空间感知基础", "science_notes": "静态识别向动态变换的空间认知迁移"}', true),

-- 【分解组合生活应用为二年级图形运动旋转提供变换应用基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_CH1_005'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CH3_002'), 
 'related', 0.81, 0.64, 540, 0.3, 0.76, 'vertical', 1, 0.79, 0.82, 
 '{"liberal_arts_notes": "分解组合为图形旋转提供变换思维基础", "science_notes": "分解思维向旋转变换的空间迁移"}', true),

-- 【整时生活认知为二年级图形运动对称提供对称应用基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_CH6_001'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CH3_003'), 
 'related', 0.79, 0.62, 180, 0.3, 0.74, 'vertical', 1, 0.77, 0.80, 
 '{"liberal_arts_notes": "整时认知为对称变换提供规律认知基础", "science_notes": "时间规律向图形对称的规律迁移"}', true),

-- 【100以内数的应用为二年级万以内数的认识提供数值应用基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH3_003'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CH7_001'), 
 'prerequisite', 0.87, 0.70, 180, 0.4, 0.82, 'vertical', 1, 0.85, 0.88, 
 '{"liberal_arts_notes": "百内数应用为万内数认识提供数值基础", "science_notes": "小数值应用向大数值认识的认知扩展"}', true),

-- 【人民币生活应用为二年级万以内数的读写提供实际应用基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH6_001'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CH7_002'), 
 'related', 0.85, 0.68, 180, 0.3, 0.80, 'vertical', 1, 0.83, 0.86, 
 '{"liberal_arts_notes": "人民币应用为万内数读写提供实际基础", "science_notes": "货币应用向数位读写的应用迁移"}', true),

-- 【简单加减实际应用为二年级万以内数的大小比较提供比较应用基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH6_002'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CH7_003'), 
 'prerequisite', 0.83, 0.66, 180, 0.4, 0.78, 'vertical', 1, 0.81, 0.84, 
 '{"liberal_arts_notes": "加减应用为数值比较提供比较应用基础", "science_notes": "计算应用向比较判断的认知迁移"}', true),

-- 【找规律生活观察为二年级万以内数的近似数提供估算应用基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH7_001'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CH7_004'), 
 'related', 0.81, 0.64, 180, 0.3, 0.76, 'vertical', 1, 0.79, 0.82, 
 '{"liberal_arts_notes": "规律观察为近似数提供估算思维基础", "science_notes": "规律思维向估算应用的认知迁移"}', true),

-- 【看图解决问题为二年级克和千克的认识提供实际应用基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH7_004'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CH8_001'), 
 'prerequisite', 0.85, 0.68, 180, 0.4, 0.80, 'vertical', 1, 0.83, 0.86, 
 '{"liberal_arts_notes": "看图解决为质量认识提供图式应用基础", "science_notes": "图式解决向质量测量的应用迁移"}', true),

-- ============================================
-- 2. 计算应用向问题解决的跨年级应用发展（8条关系）- 专家版
-- ============================================

-- 【进位加法实际应用为二年级表内除法的含义提供分配应用基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_CH5_006'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CH2_001'), 
 'related', 0.83, 0.66, 540, 0.3, 0.78, 'vertical', 1, 0.81, 0.84, 
 '{"liberal_arts_notes": "加法应用为除法含义提供分配思维基础", "science_notes": "累加应用向平均分配的运算迁移"}', true),

-- 【退位减法实际应用为二年级用2-6的乘法口诀求商提供逆向应用基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH2_006'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CH2_002'), 
 'prerequisite', 0.87, 0.70, 360, 0.4, 0.82, 'vertical', 1, 0.85, 0.88, 
 '{"liberal_arts_notes": "减法应用为乘法求商提供逆向应用基础", "science_notes": "减法逆向向乘除互逆的运算迁移"}', true),

-- 【笔算加法应用为二年级用7-9的乘法口诀求商提供计算应用基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH5_004'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CH2_003'), 
 'related', 0.81, 0.64, 360, 0.3, 0.76, 'vertical', 1, 0.79, 0.82, 
 '{"liberal_arts_notes": "笔算应用为高级口诀求商提供计算基础", "science_notes": "笔算策略向口诀运算的策略迁移"}', true),

-- 【100以内口算应用为二年级混合运算含义提供运算应用基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH4_006'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CH5_001'), 
 'prerequisite', 0.89, 0.72, 180, 0.4, 0.84, 'vertical', 1, 0.87, 0.90, 
 '{"liberal_arts_notes": "口算应用为混合运算提供运算策略基础", "science_notes": "单一运算向复合运算的策略扩展"}', true),

-- 【数的顺序应用为二年级有余数除法的除法竖式计算提供顺序应用基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH3_004'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CH6_003'), 
 'related', 0.83, 0.66, 360, 0.3, 0.78, 'vertical', 1, 0.81, 0.84, 
 '{"liberal_arts_notes": "数序思维为除法竖式提供顺序应用基础", "science_notes": "顺序思维向竖式步骤的认知迁移"}', true),

-- 【整十数加减应用为二年级有余数除法的竖式计算提供计算应用基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH4_005'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CH6_003'), 
 'related', 0.85, 0.68, 180, 0.3, 0.80, 'vertical', 1, 0.83, 0.86, 
 '{"liberal_arts_notes": "整十加减为余数竖式提供格式应用基础", "science_notes": "竖式格式向除法竖式的格式迁移"}', true),

-- 【相差多少应用为二年级解决有余数除法实际问题提供应用策略基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH6_003'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CH6_004'), 
 'prerequisite', 0.87, 0.70, 180, 0.4, 0.82, 'vertical', 1, 0.85, 0.88, 
 '{"liberal_arts_notes": "相差应用为余数问题提供实际应用基础", "science_notes": "比较应用向余数应用的问题迁移"}', true),

-- 【加减综合应用为二年级数学广角推理提供逻辑应用基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH6_004'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CH9_001'), 
 'related', 0.81, 0.64, 360, 0.3, 0.76, 'vertical', 1, 0.79, 0.82, 
 '{"liberal_arts_notes": "综合应用为逻辑推理提供应用思维基础", "science_notes": "综合思维向逻辑推理的思维迁移"}', true),

-- ============================================
-- 3. 知识整合向复习应用的跨年级应用发展（8条关系）- 专家版
-- ============================================

-- 【一年级复习总结为二年级表内除法复习提供复习应用基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH7_005'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CH4_001'), 
 'prerequisite', 0.89, 0.72, 360, 0.4, 0.84, 'vertical', 1, 0.87, 0.90, 
 '{"liberal_arts_notes": "复习总结为除法复习提供复习策略基础", "science_notes": "复习方法向高级复习的方法迁移"}', true),

-- 【综合实践应用为二年级万以内的加法和减法复习提供实践应用基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_CH6_002'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CH4_002'), 
 'related', 0.85, 0.68, 540, 0.3, 0.80, 'vertical', 1, 0.83, 0.86, 
 '{"liberal_arts_notes": "实践应用为高级加减复习提供实践基础", "science_notes": "实践经验向系统复习的方法迁移"}', true),

-- 【基础问题解决为二年级克和千克复习提供问题应用基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_CH6_003'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CH4_003'), 
 'related', 0.83, 0.66, 540, 0.3, 0.78, 'vertical', 1, 0.81, 0.84, 
 '{"liberal_arts_notes": "问题解决为质量复习提供应用基础", "science_notes": "问题解决向测量复习的应用迁移"}', true),

-- 【好习惯培养为二年级图形与几何复习提供学习应用基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_CH6_004'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_REVIEW_002'), 
 'related', 0.81, 0.64, 540, 0.3, 0.76, 'vertical', 1, 0.79, 0.82, 
 '{"liberal_arts_notes": "学习习惯为图形复习提供习惯基础", "science_notes": "学习方法向几何复习的方法迁移"}', true),

-- 【平面图形认识应用为二年级统计复习提供观察应用基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH1_001'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CH1_001'), 
 'related', 0.79, 0.62, 360, 0.3, 0.74, 'vertical', 1, 0.77, 0.80, 
 '{"liberal_arts_notes": "图形认识为统计复习提供观察基础", "science_notes": "图形观察向数据统计的观察迁移"}', true),



-- 【时间认识应用为二年级数学文化内容提供时间应用基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_CH6_001'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CULTURE_001'), 
 'extension', 0.85, 0.68, 360, 0.4, 0.80, 'vertical', 1, 0.83, 0.86, 
 '{"liberal_arts_notes": "时间认识为数学文化提供历史应用基础", "science_notes": "时间概念向文化理解的认知扩展"}', true),

-- 【数学游戏经验为二年级数学思维拓展提供思维应用基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_INTRO_001'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_REVIEW_005'), 
 'extension', 0.83, 0.66, 540, 0.4, 0.78, 'vertical', 1, 0.81, 0.84, 
 '{"liberal_arts_notes": "数学游戏为思维拓展提供趣味基础", "science_notes": "游戏思维向创新思维的思维扩展"}', true),

-- ============================================
-- 第七批：思维方法启蒙体系（20条）- 专家权威深度重构版
-- 覆盖：一年级直观思维启蒙 → 二年级抽象思维发展
-- 审查标准：⭐⭐⭐⭐⭐ 认知科学指导的专家级标准
-- 重点：观察思维→分类思维→逻辑思维→策略思维→创造思维
-- 低年级特色：符合6-8岁儿童从具体运算到形式运算的思维发展规律
-- 专家重构：基于皮亚杰认知发展理论的科学思维培养体系
-- ============================================

-- ============================================
-- 1. 直观观察思维向逻辑分析思维的跨年级发展（7条关系）- 专家版
-- ============================================

-- 【数学游戏观察为二年级数据收集整理提供观察思维基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_INTRO_001'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CH1_001'), 
 'prerequisite', 0.87, 0.70, 540, 0.4, 0.82, 'vertical', 1, 0.85, 0.88, 
 '{"liberal_arts_notes": "游戏观察为数据收集提供观察思维基础", "science_notes": "游戏观察向统计观察的思维跃迁"}', true),

-- 【图形观察识别为二年级简单推理提供识别思维基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH1_001'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CH9_002'), 
 'prerequisite', 0.85, 0.68, 360, 0.4, 0.80, 'vertical', 1, 0.83, 0.86, 
 '{"liberal_arts_notes": "图形观察为简单推理提供视觉思维基础", "science_notes": "视觉识别向逻辑推理的思维发展"}', true),

-- 【平面图形对比为二年级稍复杂推理提供对比思维基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH1_004'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CH9_003'), 
 'related', 0.83, 0.66, 360, 0.3, 0.78, 'vertical', 1, 0.81, 0.84, 
 '{"liberal_arts_notes": "图形对比为复杂推理提供比较思维基础", "science_notes": "比较思维向推理思维的认知迁移"}', true),

-- 【图形分类整理为二年级数学广角推理提供分类思维基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH1_005'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CH9_001'), 
 'prerequisite', 0.89, 0.72, 360, 0.4, 0.84, 'vertical', 1, 0.87, 0.90, 
 '{"liberal_arts_notes": "分类整理为逻辑推理提供分类思维基础", "science_notes": "分类思维向逻辑推理的思维跃迁"}', true),

-- 【空间位置观察为二年级图形运动对称提供空间思维基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_CH3_003'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CH3_003'), 
 'related', 0.81, 0.64, 360, 0.3, 0.76, 'vertical', 1, 0.79, 0.82, 
 '{"liberal_arts_notes": "空间观察为对称认识提供空间思维基础", "science_notes": "空间感知向对称思维的认知迁移"}', true),

-- 【数量大小观察为二年级万以内数的大小比较提供观察思维基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_CH2_006'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CH7_003'), 
 'prerequisite', 0.85, 0.68, 540, 0.4, 0.80, 'vertical', 1, 0.83, 0.86, 
 '{"liberal_arts_notes": "数量观察为大数比较提供观察思维基础", "science_notes": "数量感知向大数比较的思维扩展"}', true),

-- 【时间先后观察为二年级图形运动平移提供序列思维基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_CH6_001'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CH3_001'), 
 'related', 0.79, 0.62, 180, 0.3, 0.74, 'vertical', 1, 0.77, 0.80, 
 '{"liberal_arts_notes": "时间序列为图形平移提供序列思维基础", "science_notes": "时间序列向空间序列的思维迁移"}', true),

-- ============================================
-- 2. 计算策略思维向高级策略思维的跨年级发展（7条关系）- 专家版
-- ============================================

-- 【凑十法策略为二年级混合运算含义提供策略思维基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_CH5_001'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CH5_001'), 
 'prerequisite', 0.91, 0.74, 540, 0.4, 0.86, 'vertical', 1, 0.89, 0.92, 
 '{"liberal_arts_notes": "凑十策略为混合运算提供计算策略基础", "science_notes": "基础策略向复合策略的策略跃迁"}', true),

-- 【破十法策略为二年级加减乘混合提供逆向策略基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH2_005'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CH5_002'), 
 'prerequisite', 0.87, 0.70, 360, 0.4, 0.82, 'vertical', 1, 0.85, 0.88, 
 '{"liberal_arts_notes": "破十策略为三则混合提供逆向策略基础", "science_notes": "逆向策略向复合策略的策略发展"}', true),

-- 【分解组合策略为二年级有余数除法意义提供分解策略基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_CH1_005'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CH6_002'), 
 'prerequisite', 0.85, 0.68, 540, 0.4, 0.80, 'vertical', 1, 0.83, 0.86, 
 '{"liberal_arts_notes": "分解组合为余数理解提供分解策略基础", "science_notes": "分解策略向除法策略的认知迁移"}', true),

-- 【口算验证策略为二年级求有余数除法的商提供验证策略基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_CH5_006'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CH6_003'), 
 'related', 0.83, 0.66, 540, 0.3, 0.78, 'vertical', 1, 0.81, 0.84, 
 '{"liberal_arts_notes": "验证策略为除法求商提供检验策略基础", "science_notes": "验证策略向除法策略的策略迁移"}', true),

-- 【简单应用策略为二年级解决有余数除法实际问题提供应用策略基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH6_004'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CH6_004'), 
 'prerequisite', 0.89, 0.72, 180, 0.4, 0.84, 'vertical', 1, 0.87, 0.90, 
 '{"liberal_arts_notes": "应用策略为余数问题提供解题策略基础", "science_notes": "简单策略向复杂策略的策略扩展"}', true),

-- 【估算判断策略为二年级万以内数的近似数提供估算策略基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH3_005'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CH7_004'), 
 'related', 0.81, 0.64, 360, 0.3, 0.76, 'vertical', 1, 0.79, 0.82, 
 '{"liberal_arts_notes": "估算判断为近似数提供估算策略基础", "science_notes": "估算策略向高级估算的策略发展"}', true),

-- 【计算规律发现为二年级表内乘法口诀规律提供规律策略基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH4_006'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH4_002'), 
 'related', 0.87, 0.70, 180, 0.3, 0.82, 'vertical', 1, 0.85, 0.88, 
 '{"liberal_arts_notes": "规律发现为乘法口诀提供规律策略基础", "science_notes": "规律策略向记忆策略的策略迁移"}', true),

-- ============================================
-- 3. 探索创新思维向综合应用思维的跨年级发展（6条关系）- 专家版
-- ============================================

-- 【游戏探索思维为二年级用天平称物体提供探索思维基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_INTRO_002'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CH8_002'), 
 'related', 0.83, 0.66, 540, 0.3, 0.78, 'vertical', 1, 0.81, 0.84, 
 '{"liberal_arts_notes": "游戏探索为工具使用提供探索思维基础", "science_notes": "探索思维向实验思维的认知迁移"}', true),

-- 【问题发现思维为二年级用天平称物体的方法提供发现思维基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_CH6_003'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CH8_003'), 
 'related', 0.85, 0.68, 540, 0.3, 0.80, 'vertical', 1, 0.83, 0.86, 
 '{"liberal_arts_notes": "问题发现为方法探索提供发现思维基础", "science_notes": "发现思维向方法思维的认知迁移"}', true),

-- 【实践操作思维为二年级解决质量问题提供操作思维基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_CH6_002'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CH8_004'), 
 'related', 0.87, 0.70, 540, 0.3, 0.82, 'vertical', 1, 0.85, 0.88, 
 '{"liberal_arts_notes": "实践操作为质量问题提供操作思维基础", "science_notes": "操作思维向问题解决的思维迁移"}', true),

-- 【总结反思思维为二年级克和千克认识提供反思思维基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH7_005'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CH8_001'), 
 'related', 0.81, 0.64, 180, 0.3, 0.76, 'vertical', 1, 0.79, 0.82, 
 '{"liberal_arts_notes": "反思总结为质量认识提供反思思维基础", "science_notes": "反思思维向测量思维的认知迁移"}', true),

-- 【综合整理思维为二年级数学文化小小设计师提供综合思维基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_CH6_004'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CULTURE_001'), 
 'extension', 0.89, 0.72, 540, 0.4, 0.84, 'vertical', 1, 0.87, 0.90, 
 '{"liberal_arts_notes": "综合整理为设计创新提供综合思维基础", "science_notes": "综合思维向创新思维的思维跃迁"}', true),

-- 【创新表达思维为二年级数学思维拓展提供创新思维基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH7_004'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_REVIEW_005'), 
 'extension', 0.85, 0.68, 180, 0.4, 0.80, 'vertical', 1, 0.83, 0.86, 
 '{"liberal_arts_notes": "创新表达为思维拓展提供创新思维基础", "science_notes": "创新思维向高级思维的思维扩展"}', true),

-- ============================================
-- 第八批：学习方法与习惯体系（25条）- 专家权威版
-- 覆盖：一年级基础学习方法 → 二年级学习策略与习惯
-- 审查标准：⭐⭐⭐⭐⭐ 认知科学指导的专家级标准
-- 重点：口算方法→笔算规范→计算策略→问题解决策略→学习习惯培养
-- 低年级特色：学习方法从模仿到理解再到自主运用的发展规律
-- ============================================

-- ============================================
-- 1. 基础口算方法向高级计算策略的跨年级发展（8条关系）
-- ============================================

-- 【5以内口算为二年级口算乘法提供口算基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_CH1_006'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH4_002'), 
 'prerequisite', 0.92, 0.95, 240, 0.5, 0.89, 'vertical', 1, 0.91, 0.94, 
 '{"liberal_arts_notes": "基础口算为乘法口算提供计算方法基础", "science_notes": "口算方法向高级口算的方法迁移"}', true),

-- 【10以内口算方法为二年级口诀记忆策略提供方法基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_CH2_005'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH4_003'), 
 'related', 0.87, 0.90, 240, 0.4, 0.84, 'vertical', 1, 0.86, 0.89, 
 '{"liberal_arts_notes": "口算方法为口诀记忆提供学习方法基础", "science_notes": "口算方法向记忆方法的学习迁移"}', true),

-- 【凑十法策略为二年级计算策略提供策略学习基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_CH5_001'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH2_006'), 
 'related', 0.89, 0.92, 240, 0.4, 0.86, 'vertical', 1, 0.88, 0.91, 
 '{"liberal_arts_notes": "凑十策略为混合运算提供计算策略学习", "science_notes": "基础策略向复合策略的策略学习"}', true),

-- 【破十法策略为二年级除法策略提供逆向策略基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH2_005'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH6_005'), 
 'related', 0.84, 0.87, 180, 0.4, 0.81, 'vertical', 1, 0.83, 0.86, 
 '{"liberal_arts_notes": "破十策略为除法求商提供逆向策略学习", "science_notes": "减法策略向除法策略的策略迁移"}', true),

-- 【口算技巧总结为二年级计算规律发现提供规律学习基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH4_006'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH4_005'), 
 'related', 0.86, 0.89, 180, 0.3, 0.83, 'vertical', 1, 0.85, 0.88, 
 '{"liberal_arts_notes": "口算技巧为乘法规律提供规律发现学习", "science_notes": "计算技巧向规律发现的学习迁移"}', true),

-- 【估算方法为二年级估算策略提供估算学习基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH3_005'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CH7_008'), 
 'prerequisite', 0.88, 0.91, 360, 0.4, 0.85, 'vertical', 1, 0.87, 0.90, 
 '{"liberal_arts_notes": "基础估算为高级估算提供估算方法学习", "science_notes": "估算方法向高级估算的方法发展"}', true),

-- 【数感培养方法为二年级量感培养提供感知学习基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_CH2_001'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH1_001'), 
 'related', 0.85, 0.88, 240, 0.3, 0.82, 'vertical', 1, 0.84, 0.87, 
 '{"liberal_arts_notes": "数感培养为量感培养提供感知学习基础", "science_notes": "数感学习向量感学习的认知发展"}', true),

-- 【计算检验方法为二年级解决问题检验提供检验学习基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_CH5_006'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH2_007'), 
 'related', 0.83, 0.86, 240, 0.3, 0.80, 'vertical', 1, 0.82, 0.85, 
 '{"liberal_arts_notes": "计算检验为问题检验提供检验方法学习", "science_notes": "检验方法向问题检验的方法迁移"}', true),

-- ============================================
-- 2. 笔算规范向高级书写策略的跨年级发展（8条关系）
-- ============================================

-- 【笔算格式规范为二年级竖式书写提供格式学习基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH5_003'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CH2_003'), 
 'prerequisite', 0.91, 0.94, 180, 0.4, 0.88, 'vertical', 1, 0.90, 0.93, 
 '{"liberal_arts_notes": "笔算格式为除法竖式提供书写规范学习", "science_notes": "书写规范向高级书写的规范迁移"}', true),

-- 【进位笔算方法为二年级进位乘法提供进位学习基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH5_001'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CH3_001'), 
 'prerequisite', 0.89, 0.92, 180, 0.4, 0.86, 'vertical', 1, 0.88, 0.91, 
 '{"liberal_arts_notes": "进位笔算为乘法进位提供进位方法学习", "science_notes": "进位方法向乘法进位的方法迁移"}', true),

-- 【退位笔算方法为二年级退位除法提供退位学习基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH5_002'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CH3_002'), 
 'prerequisite', 0.87, 0.90, 180, 0.4, 0.84, 'vertical', 1, 0.86, 0.89, 
 '{"liberal_arts_notes": "退位笔算为除法退位提供退位方法学习", "science_notes": "退位方法向除法退位的方法迁移"}', true),

-- 【笔算验算方法为二年级乘除验算提供验算学习基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH5_004'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CH3_003'), 
 'related', 0.85, 0.88, 180, 0.3, 0.82, 'vertical', 1, 0.84, 0.87, 
 '{"liberal_arts_notes": "笔算验算为乘除验算提供验算方法学习", "science_notes": "验算方法向高级验算的方法迁移"}', true),

-- 【书写工整习惯为二年级复杂计算书写提供书写学习基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_CH6_004'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CH5_001'), 
 'related', 0.82, 0.85, 240, 0.3, 0.79, 'vertical', 1, 0.81, 0.84, 
 '{"liberal_arts_notes": "书写习惯为混合运算提供书写学习基础", "science_notes": "书写习惯向复杂书写的习惯迁移"}', true),

-- 【步骤清晰习惯为二年级解题步骤提供步骤学习基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_CH6_003'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CH5_002'), 
 'related', 0.84, 0.87, 240, 0.3, 0.81, 'vertical', 1, 0.83, 0.86, 
 '{"liberal_arts_notes": "解题步骤为混合运算提供步骤学习基础", "science_notes": "步骤习惯向复杂步骤的习惯迁移"}', true),

-- 【计算细心习惯为二年级计算准确性提供准确学习基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_CH6_004'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CH5_003'), 
 'related', 0.86, 0.89, 240, 0.3, 0.83, 'vertical', 1, 0.85, 0.88, 
 '{"liberal_arts_notes": "计算细心为复杂运算提供准确性学习", "science_notes": "细心习惯向高精度计算的习惯迁移"}', true),

-- 【整理归纳方法为二年级知识系统化提供整理学习基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_CH6_002'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_REVIEW_004'), 
 'prerequisite', 0.88, 0.91, 240, 0.4, 0.85, 'vertical', 1, 0.87, 0.90, 
 '{"liberal_arts_notes": "综合实践为知识系统化提供整理学习基础", "science_notes": "实践整理向知识系统化的方法迁移"}', true),

-- ============================================
-- 3. 学习习惯向高级学习策略的跨年级发展（9条关系）
-- ============================================

-- 【专注听讲习惯为二年级深度学习提供专注学习基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_CH6_004'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH4_001'), 
 'related', 0.87, 0.90, 240, 0.3, 0.84, 'vertical', 1, 0.86, 0.89, 
 '{"liberal_arts_notes": "专注习惯为乘法学习提供专注学习基础", "science_notes": "专注习惯向深度学习的习惯迁移"}', true),

-- 【主动提问习惯为二年级探究学习提供探究学习基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_INTRO_001'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CH9_001'), 
 'related', 0.83, 0.86, 540, 0.3, 0.80, 'vertical', 1, 0.82, 0.85, 
 '{"liberal_arts_notes": "主动提问为逻辑推理提供探究学习基础", "science_notes": "提问习惯向推理探究的学习迁移"}', true),

-- 【认真观察习惯为二年级数据观察提供观察学习基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH7_003'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CH1_001'), 
 'related', 0.85, 0.88, 180, 0.3, 0.82, 'vertical', 1, 0.84, 0.87, 
 '{"liberal_arts_notes": "观察习惯为数据收集提供观察学习基础", "science_notes": "观察习惯向数据观察的习惯迁移"}', true),

-- 【独立思考习惯为二年级自主解题提供思考学习基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_CH6_003'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CH6_004'), 
 'related', 0.86, 0.89, 240, 0.3, 0.83, 'vertical', 1, 0.85, 0.88, 
 '{"liberal_arts_notes": "独立思考为余数问题提供思考学习基础", "science_notes": "思考习惯向自主解题的习惯迁移"}', true),

-- 【合作交流习惯为二年级小组学习提供合作学习基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_INTRO_002'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH8_001'), 
 'related', 0.82, 0.85, 240, 0.3, 0.79, 'vertical', 1, 0.81, 0.84, 
 '{"liberal_arts_notes": "合作交流为排列学习提供合作学习基础", "science_notes": "合作习惯向小组学习的习惯迁移"}', true),

-- 【及时复习习惯为二年级知识巩固提供复习学习基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_CH6_004'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH9_001'), 
 'prerequisite', 0.89, 0.92, 240, 0.4, 0.86, 'vertical', 1, 0.88, 0.91, 
 '{"liberal_arts_notes": "学习习惯为新知巩固提供复习学习基础", "science_notes": "学习习惯向系统复习的习惯迁移"}', true),

-- 【错误反思习惯为二年级纠错学习提供反思学习基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_CH6_004'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CH6_003'), 
 'related', 0.84, 0.87, 240, 0.3, 0.81, 'vertical', 1, 0.83, 0.86, 
 '{"liberal_arts_notes": "错误反思为求商纠错提供反思学习基础", "science_notes": "反思习惯向纠错学习的习惯迁移"}', true),

-- 【知识关联习惯为二年级综合应用提供关联学习基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH6_004'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CH7_009'), 
 'prerequisite', 0.87, 0.90, 180, 0.4, 0.84, 'vertical', 1, 0.86, 0.89, 
 '{"liberal_arts_notes": "综合应用为大数应用提供关联学习基础", "science_notes": "应用关联向综合应用的学习迁移"}', true),



-- ============================================
-- 第九批：数学文化与综合素养体系（20条）- 专家权威版
-- 覆盖：一年级数学文化启蒙 → 二年级数学文化深化与综合素养
-- 审查标准：⭐⭐⭐⭐⭐ 认知科学指导的专家级标准
-- 重点：数学游戏→数学发现→数学创新→数学文化→综合素养
-- 低年级特色：从数学游戏到数学文化认知的启蒙发展规律
-- ============================================

-- ============================================
-- 1. 数学启蒙向数学文化认知的跨年级发展（7条关系）
-- ============================================

-- 【数学游戏体验为二年级数学文化体验提供文化启蒙基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_INTRO_001'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CULTURE_001'), 
 'extension', 0.86, 0.89, 540, 0.4, 0.83, 'vertical', 1, 0.85, 0.88, 
 '{"liberal_arts_notes": "数学游戏为数学文化提供启蒙体验基础", "science_notes": "游戏体验向文化体验的认知发展"}', true),

-- 【数学文化体验为二年级创新设计提供文化创新基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_INTRO_002'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CULTURE_001'), 
 'extension', 0.88, 0.91, 540, 0.5, 0.85, 'vertical', 1, 0.87, 0.90, 
 '{"liberal_arts_notes": "文化体验为创新设计提供文化创新基础", "science_notes": "文化认知向设计创新的文化发展"}', true),

-- 【生活中的数学为二年级数学应用文化提供应用文化基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CULTURE_002'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CULTURE_001'), 
 'extension', 0.84, 0.87, 360, 0.4, 0.81, 'vertical', 1, 0.83, 0.86, 
 '{"liberal_arts_notes": "购物数学为应用文化提供应用基础", "science_notes": "购物应用向文化应用的认知发展"}', true),

-- 【数学文化认知为二年级学科文化提供学科基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_INTRO_001'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CULTURE_001'), 
 'extension', 0.87, 0.90, 540, 0.4, 0.84, 'vertical', 1, 0.86, 0.89, 
 '{"liberal_arts_notes": "数学游戏为学科文化提供文化认知基础", "science_notes": "游戏文化向学科文化的文化发展"}', true),

-- 【购物中的数学为二年级测量文化提供测量应用基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CULTURE_002'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CH8_001'), 
 'related', 0.82, 0.85, 360, 0.3, 0.79, 'vertical', 1, 0.81, 0.84, 
 '{"liberal_arts_notes": "购物数学为测量文化提供应用认知基础", "science_notes": "购物应用向测量应用的认知迁移"}', true),

-- 【解决生活问题为二年级质量认知文化提供问题文化基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CULTURE_003'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CH8_002'), 
 'related', 0.85, 0.88, 360, 0.3, 0.82, 'vertical', 1, 0.84, 0.87, 
 '{"liberal_arts_notes": "生活问题为质量文化提供问题认知基础", "science_notes": "问题解决向质量认知的认知迁移"}', true),

-- 【数学实践活动为二年级工具使用文化提供实践文化基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_CH6_002'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CH8_003'), 
 'related', 0.83, 0.86, 240, 0.3, 0.80, 'vertical', 1, 0.82, 0.85, 
 '{"liberal_arts_notes": "实践活动为工具文化提供实践认知基础", "science_notes": "实践活动向工具应用的实践迁移"}', true),

-- ============================================
-- 2. 思维品质向数学素养的跨年级发展（7条关系）
-- ============================================

-- 【数感培养为二年级数据处理素养提供数感基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_CH2_001'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CH1_001'), 
 'related', 0.87, 0.90, 240, 0.3, 0.84, 'vertical', 1, 0.86, 0.89, 
 '{"liberal_arts_notes": "数感培养为数据素养提供感知基础", "science_notes": "数感培养向数据素养的素养发展"}', true),

-- 【空间观念为二年级几何思维素养提供空间基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH1_001'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH3_001'), 
 'prerequisite', 0.89, 0.92, 360, 0.4, 0.86, 'vertical', 1, 0.88, 0.91, 
 '{"liberal_arts_notes": "空间观念为几何素养提供空间思维基础", "science_notes": "空间认知向几何素养的思维发展"}', true),

-- 【逻辑推理启蒙为二年级推理素养提供逻辑基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH7_001'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CH9_001'), 
 'prerequisite', 0.85, 0.88, 180, 0.4, 0.82, 'vertical', 1, 0.84, 0.87, 
 '{"liberal_arts_notes": "推理启蒙为推理素养提供逻辑思维基础", "science_notes": "推理启蒙向推理素养的逻辑发展"}', true),

-- 【模式识别为二年级规律发现素养提供识别基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH7_001'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CH9_002'), 
 'related', 0.83, 0.86, 180, 0.3, 0.80, 'vertical', 1, 0.82, 0.85, 
 '{"liberal_arts_notes": "模式识别为规律素养提供识别思维基础", "science_notes": "模式识别向规律素养的认知发展"}', true),

-- 【问题解决能力为二年级应用素养提供解决基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_CH6_003'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CH6_004'), 
 'prerequisite', 0.88, 0.91, 240, 0.4, 0.85, 'vertical', 1, 0.87, 0.90, 
 '{"liberal_arts_notes": "问题解决为应用素养提供解决能力基础", "science_notes": "问题解决向应用素养的能力发展"}', true),

-- 【数学交流表达为二年级表达素养提供表达基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH6_001'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH8_003'), 
 'related', 0.81, 0.84, 180, 0.3, 0.78, 'vertical', 1, 0.80, 0.83, 
 '{"liberal_arts_notes": "简单问题为搭配素养提供表达基础", "science_notes": "问题表达向搭配表达的表达发展"}', true),

-- 【创新思维为二年级创新素养提供创新基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_INTRO_001'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH8_002'), 
 'extension', 0.86, 0.89, 540, 0.4, 0.83, 'vertical', 1, 0.85, 0.88, 
 '{"liberal_arts_notes": "游戏创新为组合素养提供创新思维基础", "science_notes": "创新思维向数学创新的创新发展"}', true),

-- ============================================
-- 3. 综合实践向核心素养的跨年级发展（6条关系）
-- ============================================

-- 【综合实践经验为二年级综合应用素养提供实践基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_CH6_002'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CH7_009'), 
 'prerequisite', 0.84, 0.87, 240, 0.4, 0.81, 'vertical', 1, 0.83, 0.86, 
 '{"liberal_arts_notes": "综合实践为应用素养提供实践基础", "science_notes": "实践经验向应用素养的实践发展"}', true),

-- 【数学建模启蒙为二年级建模素养提供建模基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH6_001'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CH8_004'), 
 'related', 0.82, 0.85, 180, 0.3, 0.79, 'vertical', 1, 0.81, 0.84, 
 '{"liberal_arts_notes": "简单建模为质量建模提供建模思维基础", "science_notes": "建模启蒙向建模素养的建模发展"}', true),

-- 【数学思考习惯为二年级思维素养提供思考基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_CH6_004'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CH9_003'), 
 'prerequisite', 0.87, 0.90, 240, 0.4, 0.84, 'vertical', 1, 0.86, 0.89, 
 '{"liberal_arts_notes": "思考习惯为思维素养提供思考基础", "science_notes": "思考习惯向思维素养的思维发展"}', true),

-- 【学习反思能力为二年级反思素养提供反思基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH7_005'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_REVIEW_005'), 
 'prerequisite', 0.85, 0.88, 180, 0.4, 0.82, 'vertical', 1, 0.84, 0.87, 
 '{"liberal_arts_notes": "学习反思为反思素养提供反思基础", "science_notes": "反思能力向反思素养的反思发展"}', true),

-- 【合作学习经验为二年级协作素养提供合作基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_INTRO_002'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH8_003'), 
 'related', 0.83, 0.86, 540, 0.3, 0.80, 'vertical', 1, 0.82, 0.85, 
 '{"liberal_arts_notes": "合作学习为协作素养提供合作基础", "science_notes": "合作经验向协作素养的协作发展"}', true),

-- 【终身学习意识为二年级学习素养提供学习基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_CH6_004'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH9_001'), 
 'extension', 0.88, 0.91, 240, 0.4, 0.85, 'vertical', 1, 0.87, 0.90, 
 '{"liberal_arts_notes": "学习意识为学习素养提供终身学习基础", "science_notes": "学习意识向学习素养的素养发展"}', true),

-- ============================================
-- 第十批：知识综合与系统化体系（25条）- 专家权威版
-- 覆盖：一年级知识整合 → 二年级知识系统化与综合运用
-- 审查标准：⭐⭐⭐⭐⭐ 认知科学指导的专家级标准
-- 重点：知识点整合→单元综合→学期系统化→年级跨越→终身发展
-- 低年级特色：从分散知识到系统认知的整合发展规律
-- ============================================

-- ============================================
-- 1. 单元知识向系统知识的跨年级发展（8条关系）
-- ============================================

-- 【数的认识系统为二年级数系统扩展提供数系统基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_CH1_001'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CH7_001'), 
 'prerequisite', 0.94, 0.97, 540, 0.5, 0.91, 'vertical', 1, 0.93, 0.96, 
 '{"liberal_arts_notes": "基础数系统为大数系统提供数认知基础", "science_notes": "数系统向大数系统的系统发展"}', true),

-- 【计算方法系统为二年级计算体系提供方法系统基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_CH2_004'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH2_004'), 
 'prerequisite', 0.92, 0.95, 240, 0.5, 0.89, 'vertical', 1, 0.91, 0.94, 
 '{"liberal_arts_notes": "进位加法为钟表认识提供计算系统基础", "science_notes": "计算系统向时间认识的系统发展"}', true),

-- 【图形认知系统为二年级几何体系提供几何系统基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH1_004'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH3_002'), 
 'prerequisite', 0.90, 0.93, 360, 0.5, 0.87, 'vertical', 1, 0.89, 0.92, 
 '{"liberal_arts_notes": "形状对比为角的认识提供几何系统基础", "science_notes": "几何对比向角认识的系统发展"}', true),

-- 【测量概念系统为二年级测量体系提供测量系统基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH3_001'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH1_001'), 
 'prerequisite', 0.88, 0.91, 180, 0.5, 0.85, 'vertical', 1, 0.87, 0.90, 
 '{"liberal_arts_notes": "基础测量为精确测量提供测量系统基础", "science_notes": "测量系统向精确测量的系统发展"}', true),

-- 【应用解决系统为二年级应用体系提供应用系统基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH6_001'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CH6_001'), 
 'prerequisite', 0.89, 0.92, 180, 0.4, 0.86, 'vertical', 1, 0.88, 0.91, 
 '{"liberal_arts_notes": "基础应用为复杂应用提供应用系统基础", "science_notes": "应用系统向复杂应用的系统发展"}', true),

-- 【口算与笔算系统为二年级运算体系提供运算系统基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH4_001'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CH3_001'), 
 'prerequisite', 0.91, 0.94, 180, 0.4, 0.88, 'vertical', 1, 0.90, 0.93, 
 '{"liberal_arts_notes": "运算基础为进位运算提供运算系统基础", "science_notes": "运算系统向高级运算的系统发展"}', true),

-- 【观察与思考系统为二年级思维体系提供思维系统基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH7_003'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CH9_001'), 
 'prerequisite', 0.87, 0.90, 180, 0.4, 0.84, 'vertical', 1, 0.86, 0.89, 
 '{"liberal_arts_notes": "观察思考为逻辑推理提供思维系统基础", "science_notes": "思维系统向逻辑思维的系统发展"}', true),

-- 【数学文化系统为二年级文化体系提供文化系统基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CULTURE_001'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CULTURE_001'), 
 'extension', 0.85, 0.88, 360, 0.4, 0.82, 'vertical', 1, 0.84, 0.87, 
 '{"liberal_arts_notes": "数学文化为设计文化提供文化系统基础", "science_notes": "文化系统向设计文化的系统发展"}', true),

-- ============================================
-- 2. 学期综合向年级系统的跨年级发展（9条关系）
-- ============================================

-- 【一年级上学期综合为二年级上学期提供综合基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_CH6_002'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH9_001'), 
 'prerequisite', 0.93, 0.96, 240, 0.5, 0.90, 'vertical', 1, 0.92, 0.95, 
 '{"liberal_arts_notes": "上学期综合为新年级综合提供综合基础", "science_notes": "学期综合向年级综合的综合发展"}', true),

-- 【一年级下学期综合为二年级下学期提供综合基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH6_004'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_REVIEW_002'), 
 'prerequisite', 0.91, 0.94, 180, 0.5, 0.88, 'vertical', 1, 0.90, 0.93, 
 '{"liberal_arts_notes": "综合应用为解决问题复习提供综合基础", "science_notes": "应用综合向问题复习的综合发展"}', true),

-- 【数与运算综合为二年级数运算系统提供运算综合基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_CH2_008'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CH5_001'), 
 'prerequisite', 0.89, 0.92, 540, 0.4, 0.86, 'vertical', 1, 0.88, 0.91, 
 '{"liberal_arts_notes": "混合运算为复合运算提供运算综合基础", "science_notes": "运算综合向复合运算的运算发展"}', true),

-- 【图形与空间综合为二年级空间系统提供空间综合基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH1_005'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH5_001'), 
 'prerequisite', 0.87, 0.90, 360, 0.4, 0.84, 'vertical', 1, 0.86, 0.89, 
 '{"liberal_arts_notes": "图形分类为观察物体提供空间综合基础", "science_notes": "空间综合向观察物体的空间发展"}', true),

-- 【量与测量综合为二年级测量系统提供测量综合基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH3_007'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CH8_001'), 
 'prerequisite', 0.85, 0.88, 360, 0.4, 0.82, 'vertical', 1, 0.84, 0.87, 
 '{"liberal_arts_notes": "算盘表示为质量认识提供测量综合基础", "science_notes": "测量综合向质量测量的测量发展"}', true),

-- 【统计与概率综合为二年级数据系统提供数据综合基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH7_003'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CH1_001'), 
 'prerequisite', 0.83, 0.86, 180, 0.4, 0.80, 'vertical', 1, 0.82, 0.85, 
 '{"liberal_arts_notes": "观察比较为数据收集提供数据综合基础", "science_notes": "数据综合向数据收集的数据发展"}', true),

-- 【实践与综合为二年级综合系统提供实践综合基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_CH6_002'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH8_001'), 
 'prerequisite', 0.84, 0.87, 240, 0.4, 0.81, 'vertical', 1, 0.83, 0.86, 
 '{"liberal_arts_notes": "综合实践为排列问题提供实践综合基础", "science_notes": "实践综合向排列问题的实践发展"}', true),

-- 【数学广角综合为二年级推理系统提供推理综合基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH7_002'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CH9_002'), 
 'prerequisite', 0.86, 0.89, 180, 0.4, 0.83, 'vertical', 1, 0.85, 0.88, 
 '{"liberal_arts_notes": "简单推理为简单推理提供推理综合基础", "science_notes": "推理综合向推理问题的推理发展"}', true),

-- 【学习习惯综合为二年级学习系统提供学习综合基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_CH6_004'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH4_001'), 
 'prerequisite', 0.88, 0.91, 240, 0.4, 0.85, 'vertical', 1, 0.87, 0.90, 
 '{"liberal_arts_notes": "学习习惯为乘法学习提供学习综合基础", "science_notes": "学习综合向乘法学习的学习发展"}', true),

-- ============================================
-- 3. 知识网络向终身发展的跨年级发展（8条关系）
-- ============================================

-- 【一年级知识网络为二年级知识建构提供建构基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH6_003'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_REVIEW_003'), 
 'prerequisite', 0.92, 0.95, 180, 0.5, 0.89, 'vertical', 1, 0.91, 0.94, 
 '{"liberal_arts_notes": "相差问题为混合运算复习提供建构基础", "science_notes": "问题网络向知识建构的网络发展"}', true),

-- 【基础能力体系为二年级核心能力提供能力基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_CH6_003'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CH9_003'), 
 'prerequisite', 0.90, 0.93, 240, 0.5, 0.87, 'vertical', 1, 0.89, 0.92, 
 '{"liberal_arts_notes": "问题解决为复杂推理提供能力基础", "science_notes": "能力体系向核心能力的能力发展"}', true),

-- 【思维方法体系为二年级思维品质提供品质基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH7_001'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CH9_002'), 
 'prerequisite', 0.88, 0.91, 180, 0.4, 0.85, 'vertical', 1, 0.87, 0.90, 
 '{"liberal_arts_notes": "找规律为简单推理提供品质基础", "science_notes": "思维体系向思维品质的思维发展"}', true),

-- 【学习策略体系为二年级学习能力提供策略基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_CH6_004'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_REVIEW_005'), 
 'prerequisite', 0.89, 0.92, 240, 0.4, 0.86, 'vertical', 1, 0.88, 0.91, 
 '{"liberal_arts_notes": "学习习惯为数学文化提供策略基础", "science_notes": "策略体系向学习能力的策略发展"}', true),

-- 【创新意识体系为二年级创新能力提供创新基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CH6_001'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CULTURE_001'), 
 'extension', 0.87, 0.90, 180, 0.5, 0.84, 'vertical', 1, 0.86, 0.89, 
 '{"liberal_arts_notes": "简单问题为设计创新提供创新基础", "science_notes": "问题创新向创新能力的创新发展"}', true),

-- 【实践应用体系为二年级应用能力提供应用基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S2_CULTURE_003'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S2_CH8_004'), 
 'prerequisite', 0.85, 0.88, 360, 0.4, 0.82, 'vertical', 1, 0.84, 0.87, 
 '{"liberal_arts_notes": "生活解决为质量问题提供应用基础", "science_notes": "应用体系向应用能力的应用发展"}', true),

-- 【文化素养体系为二年级文化品格提供文化基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_INTRO_002'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CULTURE_001'), 
 'extension', 0.86, 0.89, 540, 0.4, 0.83, 'vertical', 1, 0.85, 0.88, 
 '{"liberal_arts_notes": "数学体验为应用文化提供文化基础", "science_notes": "体验文化向文化品格的文化发展"}', true),

-- 【终身学习体系为二年级发展潜能提供发展基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G1S1_CH6_004'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G2S1_CH9_004'), 
 'extension', 0.91, 0.94, 240, 0.5, 0.88, 'vertical', 1, 0.90, 0.93, 
 '{"liberal_arts_notes": "学习习惯为搭配复习提供发展基础", "science_notes": "学习体系向发展潜能的终身发展"}', true); 