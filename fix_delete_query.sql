-- 修复DELETE语句的多种解决方案

-- ============================================
-- 解决方案1: 分步删除（推荐）
-- ============================================

-- 步骤1: 先删除作为源节点的关系
DELETE FROM knowledge_relationships 
WHERE grade_span = 0 
  AND source_node_id IN (
    SELECT id FROM knowledge_nodes 
    WHERE node_code LIKE 'MATH_G2S1_%' OR node_code LIKE 'MATH_G2S2_%'
  );

-- 步骤2: 再删除作为目标节点的关系
DELETE FROM knowledge_relationships 
WHERE grade_span = 0 
  AND target_node_id IN (
    SELECT id FROM knowledge_nodes 
    WHERE node_code LIKE 'MATH_G2S1_%' OR node_code LIKE 'MATH_G2S2_%'
  );

-- ============================================
-- 解决方案2: 使用EXISTS子查询（更安全）
-- ============================================

DELETE FROM knowledge_relationships kr
WHERE kr.grade_span = 0 
  AND (
    EXISTS (
      SELECT 1 FROM knowledge_nodes kn 
      WHERE kn.id = kr.source_node_id 
        AND (kn.node_code LIKE 'MATH_G2S1_%' OR kn.node_code LIKE 'MATH_G2S2_%')
    )
    OR 
    EXISTS (
      SELECT 1 FROM knowledge_nodes kn 
      WHERE kn.id = kr.target_node_id 
        AND (kn.node_code LIKE 'MATH_G2S1_%' OR kn.node_code LIKE 'MATH_G2S2_%')
    )
  );

-- ============================================
-- 解决方案3: 使用JOIN删除（性能更好）
-- ============================================

DELETE kr FROM knowledge_relationships kr
INNER JOIN knowledge_nodes kn_source ON kr.source_node_id = kn_source.id
WHERE kr.grade_span = 0 
  AND (kn_source.node_code LIKE 'MATH_G2S1_%' OR kn_source.node_code LIKE 'MATH_G2S2_%');

DELETE kr FROM knowledge_relationships kr
INNER JOIN knowledge_nodes kn_target ON kr.target_node_id = kn_target.id
WHERE kr.grade_span = 0 
  AND (kn_target.node_code LIKE 'MATH_G2S1_%' OR kn_target.node_code LIKE 'MATH_G2S2_%');

-- ============================================
-- 解决方案4: 如果是PostgreSQL，使用USING子句
-- ============================================

DELETE FROM knowledge_relationships kr
USING knowledge_nodes kn_source, knowledge_nodes kn_target
WHERE kr.grade_span = 0 
  AND (
    (kr.source_node_id = kn_source.id AND (kn_source.node_code LIKE 'MATH_G2S1_%' OR kn_source.node_code LIKE 'MATH_G2S2_%'))
    OR 
    (kr.target_node_id = kn_target.id AND (kn_target.node_code LIKE 'MATH_G2S1_%' OR kn_target.node_code LIKE 'MATH_G2S2_%'))
  );

-- ============================================
-- 解决方案5: 临时禁用外键约束（谨慎使用）
-- ============================================

-- 仅在必要时使用，记得重新启用约束
-- SET FOREIGN_KEY_CHECKS = 0; -- MySQL
-- SET session_replication_role = replica; -- PostgreSQL

-- 执行原始删除语句
-- DELETE FROM knowledge_relationships 
-- WHERE grade_span=0 AND ((source_node_id IN (SELECT id FROM knowledge_nodes WHERE node_code LIKE 'MATH_G2S1_%' OR node_code LIKE 'MATH_G2S2_%'))
--    OR (target_node_id IN (SELECT id FROM knowledge_nodes WHERE node_code LIKE 'MATH_G2S1_%' OR node_code LIKE 'MATH_G2S2_%')));

-- 重新启用约束
-- SET FOREIGN_KEY_CHECKS = 1; -- MySQL
-- SET session_replication_role = DEFAULT; -- PostgreSQL

-- ============================================
-- 验证删除结果
-- ============================================

-- 检查删除后的结果
SELECT COUNT(*) as remaining_grade2_relationships
FROM knowledge_relationships 
WHERE grade_span = 0 
  AND (
    source_node_id IN (SELECT id FROM knowledge_nodes WHERE node_code LIKE 'MATH_G2S1_%' OR node_code LIKE 'MATH_G2S2_%')
    OR 
    target_node_id IN (SELECT id FROM knowledge_nodes WHERE node_code LIKE 'MATH_G2S1_%' OR node_code LIKE 'MATH_G2S2_%')
  );
