---
description: 
globs: 
alwaysApply: false
---
# 微信小程序开发规范

本文档汇总了微信小程序开发的关键规范和最佳实践，帮助开发者构建高效、稳定的小程序应用。

## 页面跳转机制

### 页面栈限制

- 微信小程序页面栈最多只能有10个页面
- 超出限制后，`wx.navigateTo` 将失效，但不会报错
- 合理设计页面层级，避免过深的导航结构

### 页面导航API

- **wx.navigateTo**: 保留当前页面，打开新页面，页面栈+1
- **wx.redirectTo**: 关闭当前页面，打开新页面，页面栈不变
- **wx.navigateBack**: 关闭当前页面，返回上一页或多级页面，页面栈-n
- **wx.switchTab**: 跳转到tabBar页面，并关闭所有非tabBar页面
- **wx.reLaunch**: 关闭所有页面，打开到应用内的某个页面

### 最佳实践

- 对于详情页、表单页等非关键路径，优先使用 `redirectTo` 而非 `navigateTo`
- 有明确返回路径的页面，可以使用 `navigateTo`
- 使用 TabBar 实现主要功能区域的切换
- 在页面跳转前检查页面栈深度: `getCurrentPages().length`
- 利用页面参数和事件通信在不同页面间传递数据

```javascript
// 页面跳转前判断页面栈深度
const pages = getCurrentPages();
if (pages.length >= 9) {
  // 接近页面栈上限，使用redirectTo代替navigateTo
  wx.redirectTo({
    url: '/pages/detail/index?id=123'
  });
} else {
  wx.navigateTo({
    url: '/pages/detail/index?id=123'
  });
}
```

## 分包加载

### 分包结构设计

- 主包只包含首页和框架必需代码
- 相关功能放在同一分包内
- 分包大小控制在2MB以内
- 避免分包间相互依赖

### 性能优化

- 合理使用分包预加载配置
- 控制单个分包大小
- 优化首次启动加载
- 使用按需加载策略

### 分包预加载配置

```json
{
  "preloadRule": {
    "pages/index/index": {
      "network": "all",
      "packages": ["packageA"]
    }
  }
}
```

## 性能优化

### 启动性能

- 减少主包体积，加快首次加载
- 优化首屏渲染节点数量
- 使用骨架屏提升体验
- 延迟加载非首屏内容

### 渲染性能

- 避免频繁setData操作
- 合并多次setData
- 仅传输必要数据
- 长列表使用虚拟列表

```javascript
// 优化前
this.setData({ 'item.value': value1 });
this.setData({ 'item.label': value2 });

// 优化后
this.setData({
  'item.value': value1,
  'item.label': value2
});
```

### 网络请求

- 合并请求
- 使用缓存减少请求
- 避免重复请求
- 实现请求错误重试机制

## 代码规范

### 组件化开发

- 业务逻辑拆分为可复用组件
- 公共UI封装为基础组件
- 使用Component构造器创建组件
- 遵循单一职责原则

### 文件结构

- 按功能模块组织文件
- 相关文件放在同一目录
- 组件样式独立于页面样式
- 公共样式统一管理

## 兼容性与适配

### 设备适配

- 使用rpx单位适配不同屏幕
- 关键操作区域避免底部安全区干扰
- 兼容异形屏和刘海屏
- 测试不同机型的显示效果

### 版本兼容

- 使用wx.canIUse检测API可用性
- 关键功能提供降级方案
- 定期更新基础库最低版本
- 避免使用试验性API

