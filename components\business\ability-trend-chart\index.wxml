<!-- 能力发展历史趋势图组件 -->
<view class="trend-chart-container">
  <!-- 图表控制面板 -->
  <view class="chart-controls">
    <view class="control-group">
      <view class="control-label">时间范围</view>
      <view class="control-options">
        <view class="time-option {{timeRange === '1month' ? 'active' : ''}}" 
              data-range="1month" bind:tap="onTimeRangeChange">1个月</view>
        <view class="time-option {{timeRange === '3months' ? 'active' : ''}}" 
              data-range="3months" bind:tap="onTimeRangeChange">3个月</view>
        <view class="time-option {{timeRange === '6months' ? 'active' : ''}}" 
              data-range="6months" bind:tap="onTimeRangeChange">6个月</view>
        <view class="time-option {{timeRange === '1year' ? 'active' : ''}}" 
              data-range="1year" bind:tap="onTimeRangeChange">1年</view>
      </view>
    </view>
    
    <view class="control-group">
      <view class="control-label">显示维度</view>
      <view class="dimension-toggles">
        <view class="dimension-toggle {{selectedDimensions.indexOf('计算能力') > -1 ? 'active' : ''}}"
              data-dimension="计算能力" bind:tap="onDimensionToggle">
          <view class="toggle-color" style="background-color: {{colorScheme[0]}}"></view>
          <text>计算能力</text>
        </view>
        <view class="dimension-toggle {{selectedDimensions.indexOf('推理能力') > -1 ? 'active' : ''}}"
              data-dimension="推理能力" bind:tap="onDimensionToggle">
          <view class="toggle-color" style="background-color: {{colorScheme[1]}}"></view>
          <text>推理能力</text>
        </view>
        <view class="dimension-toggle {{selectedDimensions.indexOf('空间想象') > -1 ? 'active' : ''}}"
              data-dimension="空间想象" bind:tap="onDimensionToggle">
          <view class="toggle-color" style="background-color: {{colorScheme[2]}}"></view>
          <text>空间想象</text>
        </view>
        <view class="dimension-toggle {{selectedDimensions.indexOf('数据分析') > -1 ? 'active' : ''}}"
              data-dimension="数据分析" bind:tap="onDimensionToggle">
          <view class="toggle-color" style="background-color: {{colorScheme[3]}}"></view>
          <text>数据分析</text>
        </view>
        <view class="dimension-toggle {{selectedDimensions.indexOf('应用能力') > -1 ? 'active' : ''}}"
              data-dimension="应用能力" bind:tap="onDimensionToggle">
          <view class="toggle-color" style="background-color: {{colorScheme[4]}}"></view>
          <text>应用能力</text>
        </view>
        <view class="dimension-toggle {{selectedDimensions.indexOf('逻辑思维') > -1 ? 'active' : ''}}"
              data-dimension="逻辑思维" bind:tap="onDimensionToggle">
          <view class="toggle-color" style="background-color: {{colorScheme[5]}}"></view>
          <text>逻辑思维</text>
        </view>
      </view>
    </view>
    
    <view class="control-group">
      <view class="control-options">
        <view class="option-item" bind:tap="onToggleMilestones">
          <view class="option-icon {{showMilestones ? 'active' : ''}}">📍</view>
          <text>里程碑</text>
        </view>
        <view class="option-item" bind:tap="onExportChart">
          <view class="option-icon">💾</view>
          <text>导出</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 图表主体 -->
  <view class="chart-main">
    <view class="chart-title">能力发展历史趋势</view>
    
    <!-- SVG图表 -->
    <view class="chart-svg-container" wx:if="{{chartReady}}">
      <view class="svg-wrapper">
        <rich-text nodes="{{svgContent}}"></rich-text>
      </view>
    </view>
    
    <!-- 加载状态 -->
    <view class="chart-loading" wx:else>
      <view class="loading-icon"></view>
      <view class="loading-text">图表加载中...</view>
    </view>
  </view>

  <!-- 里程碑列表 -->
  <view class="milestones-section" wx:if="{{showMilestones && milestones.length > 0}}">
    <view class="milestones-title">能力发展里程碑</view>
    <view class="milestones-list">
      <view class="milestone-item" wx:for="{{milestones}}" wx:key="id">
        <view class="milestone-icon {{item.type}}">
          <text>{{item.type === 'breakthrough' ? '🎯' : (item.type === 'plateau' ? '⏸️' : (item.type === 'regression' ? '⚠️' : '🔄'))}}</text>
        </view>
        <view class="milestone-content">
          <view class="milestone-title">{{item.title}}</view>
          <view class="milestone-date">{{item.date}}</view>
          <view class="milestone-description">{{item.description}}</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 数据洞察 -->
  <view class="chart-insights" wx:if="{{trendData && trendData.insights}}">
    <view class="insights-title">趋势分析</view>
    <view class="insights-content">
      <view class="insight-item" wx:for="{{trendData.insights}}" wx:key="dimension">
        <view class="insight-dimension">{{item.dimension}}</view>
        <view class="insight-trend {{item.trend}}">
          <text class="trend-icon">{{item.trend === 'improving' ? '📈' : (item.trend === 'declining' ? '📉' : '➖')}}</text>
          <text class="trend-text">{{item.trend === 'improving' ? '持续提升' : (item.trend === 'declining' ? '需要关注' : '保持稳定')}}</text>
        </view>
        <view class="insight-description">{{item.description}}</view>
      </view>
    </view>
  </view>
</view> 