.learning-plan-chart {
  padding: 24rpx;
  box-sizing: border-box;
  width: 100%;
}

.card-style {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
  margin-bottom: 24rpx;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.chart-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.chart-date {
  font-size: 26rpx;
  color: #999999;
}

.chart-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.progress-circle-container {
  margin-right: 24rpx;
}

.stats-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.stats-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 12rpx;
  border-bottom: 1px solid #F0F0F0;
}

.stats-item:last-child {
  border-bottom: none;
}

.stats-label {
  font-size: 28rpx;
  color: #666666;
}

.stats-value {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
}

/* 响应式布局 */
@media screen and (max-width: 375px) {
  .chart-content {
    flex-direction: column;
  }
  
  .progress-circle-container {
    margin-right: 0;
    margin-bottom: 24rpx;
  }
  
  .stats-container {
    width: 100%;
  }
}

/* iPad适配 */
.ipad-mode .learning-plan-chart {
  max-width: 680rpx;
  margin-left: auto;
  margin-right: auto;
} 