/* 容器样式 */
.container {
  min-height: 100vh;
  background-color: #f7f9ff;
  display: flex;
  flex-direction: column;
  padding-bottom: calc(24rpx + var(--safe-bottom, 0px));
  position: relative;
  overflow: hidden;
  background: linear-gradient(180deg, #F0F3FF 0%, #F8FAFF 100%);
}

/* 背景装饰 */
.container::before {
  content: "";
  position: absolute;
  top: -200rpx;
  right: -200rpx;
  width: 600rpx;
  height: 600rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(90, 103, 246, 0.1), rgba(90, 103, 246, 0.05));
  z-index: 0;
}

.container::after {
  content: "";
  position: absolute;
  bottom: -200rpx;
  left: -200rpx;
  width: 500rpx;
  height: 500rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(90, 103, 246, 0.08), rgba(90, 103, 246, 0.03));
  z-index: 0;
}

/* 登录卡片样式 */
.login-card {
  background-color: #FFFFFF;
  border-radius: 24rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.03);
  margin: 60rpx 40rpx;
  padding: 50rpx 40rpx;
  overflow: hidden; 
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.login-card.fade-in {
  opacity: 1;
  transform: translateY(0);
}

.login-card.fade-out {
  opacity: 0;
  transform: translateY(20rpx);
}

/* Logo区域样式 */
.logo-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 60rpx;
}

.logo {
  font-size: 52rpx;
  font-weight: bold;
  background: linear-gradient(90deg, #4776E6, #8E54E9);
  -webkit-background-clip: text;
  color: transparent;
  margin-bottom: 20rpx;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.slogan {
  font-size: 30rpx;
  color: #7a7f9a;
  letter-spacing: 2rpx;
}

/* 微信授权区域 */
.auth-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
}

/* 微信图标容器 */
.wechat-icon-container {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.wechat-icon-circle {
  width: 140rpx;
  height: 140rpx;
  border-radius: 50%;
  background-color: #07c160;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 16rpx rgba(7, 193, 96, 0.2);
  position: relative;
  overflow: hidden;
}

.wechat-icon-circle::after {
  content: "";
  position: absolute;
  top: -20rpx;
  right: -20rpx;
  width: 80rpx;
  height: 80rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
}

/* 微信图标 */
.icon-wechat-big {
  width: 80rpx;
  height: 80rpx;
  background-color: #ffffff;
  mask: url("data:image/svg+xml,%3Csvg t='1710327834584' class='icon' viewBox='0 0 1024 1024' version='1.1' xmlns='http://www.w3.org/2000/svg' p-id='4318'%3E%3Cpath d='M690.1 377.4c5.9 0 11.8 0.2 17.6 0.5-24.4-128.7-158.3-227.1-319.9-227.1C209 150.8 64 271.4 64 420.2c0 81.1 43.6 154.2 111.9 203.6 5.5 3.9 9.1 10.3 9.1 17.6 0 2.4-0.5 4.6-1.1 6.9-5.5 20.3-14.2 52.8-14.6 54.3-0.7 2.6-1.7 5.2-1.7 7.9 0 5.9 4.8 10.8 10.8 10.8 2.4 0 4.8-0.7 6.9-2l70.9-40.9c5.3-3.1 11-5 17.2-5 3.3 0 6.7 0.5 9.8 1.4 33.5 9.5 68.8 14.8 105.7 14.8 6 0 11.9-0.1 17.8-0.4-7.1-21-10.9-43.1-10.9-66 0-135.8 132.2-245.8 295.3-245.8z m-194.3-86.5c23.8 0 43.2 19.3 43.2 43.1s-19.3 43.1-43.2 43.1c-23.8 0-43.2-19.3-43.2-43.1s19.4-43.1 43.2-43.1z m-215.9 86.2c-23.8 0-43.2-19.3-43.2-43.1s19.3-43.1 43.2-43.1 43.2 19.3 43.2 43.1-19.4 43.1-43.2 43.1z' fill='%2307c160' p-id='4319'%3E%3C/path%3E%3Cpath d='M866.7 792.7c56.9-41.2 93.2-102 93.2-169.7 0-124-120.8-224.5-269.9-224.5-149 0-269.9 100.5-269.9 224.5S540.9 847.5 690 847.5c30.8 0 60.6-4.4 88.1-12.3 2.6-0.8 5.2-1.2 7.9-1.2 5.2 0 9.9 1.6 14.3 4.1l59.1 34c1.7 1 3.6 1.5 5.5 1.5 5 0 9.3-4.1 9.3-9.1 0-2.1-0.8-4.3-1.4-6.1-5.6-19.9-14.5-51.9-14.5-51.9-0.5-1.9-0.9-3.8-0.9-5.8 0-5.9 3.2-11 7.9-14.5z m-30.5-48.1c17.8 0 32.1-14.3 32.1-32.1s-14.3-32.1-32.1-32.1-32.1 14.3-32.1 32.1 14.3 32.1 32.1 32.1z m-160.6 0c17.8 0 32.1-14.3 32.1-32.1s-14.3-32.1-32.1-32.1-32.1 14.3-32.1 32.1 14.3 32.1 32.1 32.1z' fill='%2307c160' p-id='4320'%3E%3C/path%3E%3C/svg%3E") no-repeat center;
  mask-size: 100% 100%;
  -webkit-mask: url("data:image/svg+xml,%3Csvg t='1710327834584' class='icon' viewBox='0 0 1024 1024' version='1.1' xmlns='http://www.w3.org/2000/svg' p-id='4318'%3E%3Cpath d='M690.1 377.4c5.9 0 11.8 0.2 17.6 0.5-24.4-128.7-158.3-227.1-319.9-227.1C209 150.8 64 271.4 64 420.2c0 81.1 43.6 154.2 111.9 203.6 5.5 3.9 9.1 10.3 9.1 17.6 0 2.4-0.5 4.6-1.1 6.9-5.5 20.3-14.2 52.8-14.6 54.3-0.7 2.6-1.7 5.2-1.7 7.9 0 5.9 4.8 10.8 10.8 10.8 2.4 0 4.8-0.7 6.9-2l70.9-40.9c5.3-3.1 11-5 17.2-5 3.3 0 6.7 0.5 9.8 1.4 33.5 9.5 68.8 14.8 105.7 14.8 6 0 11.9-0.1 17.8-0.4-7.1-21-10.9-43.1-10.9-66 0-135.8 132.2-245.8 295.3-245.8z m-194.3-86.5c23.8 0 43.2 19.3 43.2 43.1s-19.3 43.1-43.2 43.1c-23.8 0-43.2-19.3-43.2-43.1s19.4-43.1 43.2-43.1z m-215.9 86.2c-23.8 0-43.2-19.3-43.2-43.1s19.3-43.1 43.2-43.1 43.2 19.3 43.2 43.1-19.4 43.1-43.2 43.1z' fill='%23ffffff' p-id='4319'%3E%3C/path%3E%3Cpath d='M866.7 792.7c56.9-41.2 93.2-102 93.2-169.7 0-124-120.8-224.5-269.9-224.5-149 0-269.9 100.5-269.9 224.5S540.9 847.5 690 847.5c30.8 0 60.6-4.4 88.1-12.3 2.6-0.8 5.2-1.2 7.9-1.2 5.2 0 9.9 1.6 14.3 4.1l59.1 34c1.7 1 3.6 1.5 5.5 1.5 5 0 9.3-4.1 9.3-9.1 0-2.1-0.8-4.3-1.4-6.1-5.6-19.9-14.5-51.9-14.5-51.9-0.5-1.9-0.9-3.8-0.9-5.8 0-5.9 3.2-11 7.9-14.5z m-30.5-48.1c17.8 0 32.1-14.3 32.1-32.1s-14.3-32.1-32.1-32.1-32.1 14.3-32.1 32.1 14.3 32.1 32.1 32.1z m-160.6 0c17.8 0 32.1-14.3 32.1-32.1s-14.3-32.1-32.1-32.1-32.1 14.3-32.1 32.1 14.3 32.1 32.1 32.1z' fill='%23ffffff' p-id='4320'%3E%3C/path%3E%3C/svg%3E") no-repeat center;
  -webkit-mask-size: 100% 100%;
}

/* 授权信息区域 */
.auth-info {
  text-align: center;
  margin-bottom: 40rpx;
}

.auth-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}

.auth-desc {
  font-size: 28rpx;
  color: #7a7f9a;
  line-height: 1.5;
}

/* 微信登录按钮 */
.wechat-auth-btn {
  width: 90%;
  height: 92rpx;
  background: linear-gradient(90deg, #07c160, #10d56c);
  color: #fff;
  font-size: 32rpx;
  font-weight: 500;
  border-radius: 46rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 40rpx;
  box-shadow: 0 8rpx 16rpx rgba(7, 193, 96, 0.2);
  position: relative;
  overflow: hidden;
  transition: all 0.3s;
  margin-top: 20rpx;
}

.wechat-auth-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0.2) 50%, rgba(255,255,255,0) 100%);
  transition: all 0.5s;
}

.wechat-auth-btn:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 8rpx rgba(7, 193, 96, 0.15);
}

.wechat-auth-btn:active::before {
  left: 100%;
}

/* 其他登录方式 */
.other-login {
  margin-top: 60rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* 账号密码登录按钮 */
.account-login {
  margin-top: 30rpx;
  font-size: 28rpx;
  color: #8E54E9;
  padding: 16rpx 40rpx;
  border-radius: 40rpx;
  background-color: #f5f7fa;
  box-shadow: 0 4rpx 10rpx rgba(142, 84, 233, 0.1);
  transition: all 0.3s;
  text-align: center;
}

.account-login:active {
  transform: scale(0.95);
  background-color: #f0f3ff;
}

/* 用户头像 */
.user-avatar {
  width: 140rpx;
  height: 140rpx;
  border-radius: 70rpx;
  overflow: hidden;
  margin-bottom: 16rpx;
  border: 4rpx solid #eef3ff;
  box-shadow: 0 4rpx 16rpx rgba(62, 123, 250, 0.2);
}

.user-avatar image {
  width: 100%;
  height: 100%;
}

.welcome-text {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-top: 16rpx;
  margin-bottom: 24rpx;
  text-align: center;
}

.privacy-note {
  font-size: 24rpx;
  color: #666;
  text-align: center;
  margin-bottom: 24rpx;
  background-color: rgba(255, 238, 210, 0.5);
  padding: 12rpx 20rpx;
  border-radius: 24rpx;
  max-width: 85%;
  margin-left: auto;
  margin-right: auto;
}

.skip-btn {
  margin-top: 30rpx;
  font-size: 28rpx;
  color: #999;
  padding: 16rpx;
}

.skip-btn:active {
  opacity: 0.7;
}

/* 信息表单 */
.form-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  text-align: center;
  margin-bottom: 12rpx;
  position: relative;
  display: inline-block;
}

.info-form {
  padding: 0;
  position: relative;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.form-header {
  text-align: center;
  margin-bottom: 50rpx;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.form-title-wrapper {
  display: flex;
  justify-content: center;
  margin-bottom: 10rpx;
  flex-direction: column;
  align-items: center;
  width: 100%;
}

.form-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 12rpx;
  text-align: center;
}

.title-underline {
  width: 80rpx;
  height: 6rpx;
  background: #3E7BFA;
  border-radius: 3rpx;
  margin-bottom: 12rpx;
}

.form-title::after {
  content: none;
}

.form-subtitle {
  font-size: 26rpx;
  color: #999;
  text-align: center;
  margin-bottom: 30rpx;
}

.form-group {
  margin-bottom: 0;
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}

.form-item {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  margin-bottom: 0;
  transition: all 0.3s;
}

.form-label {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  height: 96rpx;
  background-color: #f7f8fc;
  border-radius: 48rpx;
  padding: 0 24rpx;
  transition: all 0.3s;
}

.input-wrapper:focus-within {
  background-color: #f0f3ff;
  box-shadow: 0 2rpx 12rpx rgba(90, 103, 246, 0.1);
}

/* 输入框相关样式 */
.input-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.input-icon .icon {
  color: #8E54E9;
  opacity: 0.8;
}

.picker-view {
  flex: 1;
  height: 100%;
  display: flex;
  align-items: center;
  font-size: 30rpx;
  color: #333;
}

.picker-view.placeholder {
  color: #b0b5ca;
}

.input-arrow {
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.input-arrow .icon {
  color: #8E54E9;
  opacity: 0.8;
}

/* 性别选择样式 */
.gender-select {
  display: flex;
  gap: 24rpx;
}

.gender-option {
  flex: 1;
  height: 96rpx;
  background-color: #f7f8fc;
  border: 2rpx solid #f7f8fc;
  border-radius: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  font-size: 28rpx;
  color: #666;
}

.gender-option.active {
  background-color: #e6f2ff;
  border-color: #3E7BFA;
  color: #3E7BFA;
}

.gender-icon {
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.gender-icon.male {
  color: #4A90E2;
}

.gender-icon.female {
  color: #E94A8C;
}

.gender-option.active .gender-icon.male,
.gender-option.active .gender-icon.female {
  color: #3E7BFA;
}

/* 提交按钮样式 */
.submit-btn {
  width: 100%;
  height: 96rpx;
  background: linear-gradient(135deg, #3E7BFA, #4A90E2);
  color: #fff;
  font-size: 32rpx;
  font-weight: 500;
  border-radius: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 16rpx rgba(62, 123, 250, 0.2);
  transition: all 0.3s;
  margin-top: 60rpx;
  border: none;
  position: relative;
  overflow: hidden;
}

.submit-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0.2) 50%, rgba(255,255,255,0) 100%);
  transition: all 0.6s;
}

.submit-btn:active {
  transform: scale(0.98);
  box-shadow: 0 5rpx 10rpx rgba(62, 123, 250, 0.15);
}

.submit-btn:active::before {
  left: 100%;
}

.submit-btn[disabled] {
  background: linear-gradient(135deg, #B0BEC5, #CFD8DC);
  color: #fff;
  opacity: 0.7;
  box-shadow: 0 4rpx 8rpx rgba(176, 190, 197, 0.2);
  transform: none;
}

.btn-loading {
  width: 32rpx;
  height: 32rpx;
  border: 3rpx solid rgba(255, 255, 255, 0.3);
  border-top: 3rpx solid #fff;
  border-radius: 50%;
  animation: loading 1s linear infinite;
  margin-left: 16rpx;
}

@keyframes loading {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.input-prefix {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80rpx;
  height: 80rpx;
}

.input-prefix .icon {
  color: #8E54E9;
}

.form-input {
  flex: 1;
  height: 100%;
  font-size: 30rpx;
  color: #333;
  border: none;
  background-color: transparent;
}

.placeholder {
  color: #b0b5ca;
  font-size: 30rpx;
}

/* 显示密码按钮 */
.show-password {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80rpx;
  height: 80rpx;
  color: #8E54E9;
}

.show-password .icon {
  color: #8E54E9;
  opacity: 0.7;
}

/* 记住密码和忘记密码 */
.login-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 20rpx 0 36rpx;
  padding: 0 10rpx;
}

.remember-password {
  display: flex;
  align-items: center;
}

.checkbox {
  width: 34rpx;
  height: 34rpx;
  border-radius: 17rpx;
  border: 2rpx solid #d0d3e5;
  margin-right: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.checkbox.checked {
  background-color: #4776E6;
  border-color: #4776E6;
}

.remember-password text {
  font-size: 26rpx;
  color: #7a7f9a;
}

.forgot-password {
  font-size: 26rpx;
  color: #4776E6;
}

/* 登录按钮 */
.login-btn {
  width: 100%;
  height: 96rpx;
  background: linear-gradient(135deg, #4776E6, #8E54E9);
  color: #fff;
  font-size: 32rpx;
  font-weight: 500;
  border-radius: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 16rpx rgba(142, 84, 233, 0.2);
  transition: all 0.3s;
  position: relative;
  overflow: hidden;
  margin-bottom: 30rpx;
}

.login-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0.2) 50%, rgba(255,255,255,0) 100%);
  transition: all 0.6s;
}

.login-btn:active {
  transform: scale(0.98);
  box-shadow: 0 5rpx 10rpx rgba(142, 84, 233, 0.15);
}

.login-btn:active::before {
  left: 100%;
}

.login-btn[disabled],
.login-btn-disabled {
  background: linear-gradient(135deg, #8E54E9, #4776E6);
  opacity: 0.5;
  box-shadow: 0 4rpx 8rpx rgba(142, 84, 233, 0.1);
}

/* 分割线 */
.divider {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 30rpx 0;
}

.divider .line {
  flex: 1;
  height: 1px;
  background-color: #eaecf5;
}

.divider .text {
  padding: 0 24rpx;
  font-size: 26rpx;
  color: #8a8fa8;
}

/* 微信登录样式 */
.wechat-login {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
}

.wechat-icon-wrap {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  background-color: #f0f9f4;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16rpx;
  transition: all 0.3s;
}

.wechat-icon-wrap:active {
  transform: scale(0.95);
  background-color: #e6f5ec;
}

.icon-wechat {
  width: 48rpx;
  height: 48rpx;
  background-color: #07c160;
  mask: url("data:image/svg+xml,%3Csvg t='1710327834584' class='icon' viewBox='0 0 1024 1024' version='1.1' xmlns='http://www.w3.org/2000/svg' p-id='4318'%3E%3Cpath d='M690.1 377.4c5.9 0 11.8 0.2 17.6 0.5-24.4-128.7-158.3-227.1-319.9-227.1C209 150.8 64 271.4 64 420.2c0 81.1 43.6 154.2 111.9 203.6 5.5 3.9 9.1 10.3 9.1 17.6 0 2.4-0.5 4.6-1.1 6.9-5.5 20.3-14.2 52.8-14.6 54.3-0.7 2.6-1.7 5.2-1.7 7.9 0 5.9 4.8 10.8 10.8 10.8 2.4 0 4.8-0.7 6.9-2l70.9-40.9c5.3-3.1 11-5 17.2-5 3.3 0 6.7 0.5 9.8 1.4 33.5 9.5 68.8 14.8 105.7 14.8 6 0 11.9-0.1 17.8-0.4-7.1-21-10.9-43.1-10.9-66 0-135.8 132.2-245.8 295.3-245.8z m-194.3-86.5c23.8 0 43.2 19.3 43.2 43.1s-19.3 43.1-43.2 43.1c-23.8 0-43.2-19.3-43.2-43.1s19.4-43.1 43.2-43.1z m-215.9 86.2c-23.8 0-43.2-19.3-43.2-43.1s19.3-43.1 43.2-43.1 43.2 19.3 43.2 43.1-19.4 43.1-43.2 43.1z' fill='%2307c160' p-id='4319'%3E%3C/path%3E%3Cpath d='M866.7 792.7c56.9-41.2 93.2-102 93.2-169.7 0-124-120.8-224.5-269.9-224.5-149 0-269.9 100.5-269.9 224.5S540.9 847.5 690 847.5c30.8 0 60.6-4.4 88.1-12.3 2.6-0.8 5.2-1.2 7.9-1.2 5.2 0 9.9 1.6 14.3 4.1l59.1 34c1.7 1 3.6 1.5 5.5 1.5 5 0 9.3-4.1 9.3-9.1 0-2.1-0.8-4.3-1.4-6.1-5.6-19.9-14.5-51.9-14.5-51.9-0.5-1.9-0.9-3.8-0.9-5.8 0-5.9 3.2-11 7.9-14.5z m-30.5-48.1c17.8 0 32.1-14.3 32.1-32.1s-14.3-32.1-32.1-32.1-32.1 14.3-32.1 32.1 14.3 32.1 32.1 32.1z m-160.6 0c17.8 0 32.1-14.3 32.1-32.1s-14.3-32.1-32.1-32.1-32.1 14.3-32.1 32.1 14.3 32.1 32.1 32.1z' fill='%2307c160' p-id='4320'%3E%3C/path%3E%3C/svg%3E") no-repeat center;
  mask-size: 100% 100%;
  -webkit-mask: url("data:image/svg+xml,%3Csvg t='1710327834584' class='icon' viewBox='0 0 1024 1024' version='1.1' xmlns='http://www.w3.org/2000/svg' p-id='4318'%3E%3Cpath d='M690.1 377.4c5.9 0 11.8 0.2 17.6 0.5-24.4-128.7-158.3-227.1-319.9-227.1C209 150.8 64 271.4 64 420.2c0 81.1 43.6 154.2 111.9 203.6 5.5 3.9 9.1 10.3 9.1 17.6 0 2.4-0.5 4.6-1.1 6.9-5.5 20.3-14.2 52.8-14.6 54.3-0.7 2.6-1.7 5.2-1.7 7.9 0 5.9 4.8 10.8 10.8 10.8 2.4 0 4.8-0.7 6.9-2l70.9-40.9c5.3-3.1 11-5 17.2-5 3.3 0 6.7 0.5 9.8 1.4 33.5 9.5 68.8 14.8 105.7 14.8 6 0 11.9-0.1 17.8-0.4-7.1-21-10.9-43.1-10.9-66 0-135.8 132.2-245.8 295.3-245.8z m-194.3-86.5c23.8 0 43.2 19.3 43.2 43.1s-19.3 43.1-43.2 43.1c-23.8 0-43.2-19.3-43.2-43.1s19.4-43.1 43.2-43.1z m-215.9 86.2c-23.8 0-43.2-19.3-43.2-43.1s19.3-43.1 43.2-43.1 43.2 19.3 43.2 43.1-19.4 43.1-43.2 43.1z' fill='%23ffffff' p-id='4319'%3E%3C/path%3E%3Cpath d='M866.7 792.7c56.9-41.2 93.2-102 93.2-169.7 0-124-120.8-224.5-269.9-224.5-149 0-269.9 100.5-269.9 224.5S540.9 847.5 690 847.5c30.8 0 60.6-4.4 88.1-12.3 2.6-0.8 5.2-1.2 7.9-1.2 5.2 0 9.9 1.6 14.3 4.1l59.1 34c1.7 1 3.6 1.5 5.5 1.5 5 0 9.3-4.1 9.3-9.1 0-2.1-0.8-4.3-1.4-6.1-5.6-19.9-14.5-51.9-14.5-51.9-0.5-1.9-0.9-3.8-0.9-5.8 0-5.9 3.2-11 7.9-14.5z m-30.5-48.1c17.8 0 32.1-14.3 32.1-32.1s-14.3-32.1-32.1-32.1-32.1 14.3-32.1 32.1 14.3 32.1 32.1 32.1z m-160.6 0c17.8 0 32.1-14.3 32.1-32.1s-14.3-32.1-32.1-32.1-32.1 14.3-32.1 32.1 14.3 32.1 32.1 32.1z' fill='%23ffffff' p-id='4320'%3E%3C/path%3E%3C/svg%3E") no-repeat center;
  -webkit-mask-size: 100% 100%;
}

.wechat-login text {
  font-size: 26rpx;
  color: #7a7f9a;
}

/* 注册提示样式 */
.register-tip {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 40rpx;
  padding-top: 10rpx;
}

.register-tip text {
  font-size: 28rpx;
  color: #7a7f9a;
}

.register-link {
  color: #8E54E9 !important;
  margin-left: 10rpx;
}

/* iPad适配 */
.ipad-mode .login-card {
  max-width: 650rpx;
  margin: 80rpx auto;
  padding: 60rpx 50rpx;
  min-height: calc(100vh - 240rpx);
}

.ipad-mode .logo {
  font-size: 60rpx;
}

.ipad-mode .slogan {
  font-size: 34rpx;
}

.ipad-mode .form-header {
  margin-bottom: 60rpx;
}

.ipad-mode .form-group {
  gap: 40rpx;
}

.ipad-mode .input-wrapper,
.ipad-mode .gender-option {
  height: 110rpx;
  padding: 0 30rpx;
}

.ipad-mode .form-input,
.ipad-mode .placeholder,
.ipad-mode .picker-view {
  font-size: 32rpx;
}

.ipad-mode .submit-btn {
  height: 110rpx;
  font-size: 36rpx;
  margin-top: 80rpx;
}

.ipad-mode .form-label {
  font-size: 30rpx;
}

.ipad-mode .gender-select {
  gap: 32rpx;
}

/* 年级选择器iPad适配 */
.ipad-mode .grade-selector-header {
  padding: 40rpx 50rpx 30rpx;
}

.ipad-mode .title {
  font-size: 36rpx;
}

.ipad-mode .cancel-btn, 
.ipad-mode .confirm-btn {
  font-size: 32rpx;
  padding: 20rpx 24rpx;
}

.ipad-mode .grade-selector-content {
  padding: 30rpx 50rpx 50rpx;
}

.ipad-mode .grade-item {
  height: 100rpx;
  font-size: 32rpx;
  margin-bottom: 20rpx;
}

/* 年级选择器小屏适配 */
@media screen and (max-height: 600px) {
  .grade-selector-container {
    max-height: 70vh;
  }
  
  .grade-selector-content {
    max-height: 40vh;
    padding: 16rpx 32rpx 32rpx;
  }
  
  .grade-item {
    height: 72rpx;
    margin-bottom: 12rpx;
    font-size: 28rpx;
  }
  
  .grade-selector-header {
    padding: 24rpx 32rpx 16rpx;
  }
  
  .title {
    font-size: 28rpx;
  }
  
  .cancel-btn, .confirm-btn {
    font-size: 26rpx;
    padding: 12rpx 16rpx;
  }
}

/* 小屏适配 */
@media screen and (max-height: 600px) {
  .login-card {
    margin: 20rpx 30rpx;
    padding: 30rpx 30rpx;
    min-height: calc(100vh - 100rpx);
  }
  
  .logo-container {
    margin-bottom: 30rpx;
  }
  
  .form-header {
    margin-bottom: 30rpx;
  }
  
  .form-group {
    gap: 24rpx;
  }
  
  .input-wrapper,
  .gender-option {
    height: 80rpx;
  }
  
  .submit-btn {
    height: 80rpx;
         margin-top: 40rpx;
   }
}
  
  .form-options {
    margin-bottom: 30rpx;
  }
  
  .login-btn {
    height: 88rpx;
    margin-bottom: 20rpx;
  }
  
  .divider {
    margin: 20rpx 0;
  }
  
  .register-tip {
    margin-top: 30rpx;
  }

/* 账号密码登录表单样式 */
.login-form {
  margin-bottom: 40rpx;
  padding: 0 20rpx;
}

/* 输入框容器 */
.input-container {
  position: relative;
  display: flex;
  align-items: center;
  height: 100rpx;
  background-color: #f5f7ff;
  border-radius: 50rpx;
  margin-bottom: 30rpx;
  padding: 0 24rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.03);
  transition: all 0.3s;
  z-index: 5;
}

.input-container:focus-within {
  background-color: #f0f3ff;
  box-shadow: 0 2rpx 10rpx rgba(71, 118, 230, 0.08);
  transform: translateY(-2rpx);
}

/* 输入框图标包装 */
.input-icon-wrap {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 70rpx;
  height: 70rpx;
}

.input-icon-wrap .icon {
  color: #4776E6;
}

/* 输入字段 */
.input-field {
  flex: 1;
  height: 100%;
  font-size: 30rpx;
  color: #333;
  padding: 0 10rpx;
  background-color: transparent;
  border: none;
  width: 100%;
  z-index: 10;
}

/* 输入框操作区域（如显示密码按钮） */
.input-action {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 70rpx;
  height: 70rpx;
  color: #8E54E9;
}

.input-action .icon {
  color: #8E54E9;
  opacity: 0.8;
}

.placeholder {
  color: #b0b5ca;
  font-size: 30rpx;
}

/* 登录按钮禁用状态 */
.login-btn-disabled {
  background: linear-gradient(135deg, #a8b5e0, #bfabd8);
  box-shadow: 0 4rpx 10rpx rgba(71, 118, 230, 0.1);
  opacity: 0.8;
}

/* 手机号绑定相关样式 */
.phone-binding-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 50rpx 40rpx 60rpx;
  background-color: #ffffff;
  border-radius: 32rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.06);
  margin: 60rpx 40rpx;
}

.phone-auth-section {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* 用户头像 */
.user-avatar-container {
  width: 170rpx;
  height: 170rpx;
  border-radius: 50%;
  background-color: #f5f5f5;
  overflow: hidden;
  margin-top: 20rpx;
  margin-bottom: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.user-avatar {
  width: 100%;
  height: 100%;
  background-color: #f5f5f5;
}

/* 欢迎文本 */
.welcome-text {
  font-size: 40rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 20rpx;
  text-align: center;
  line-height: 1.3;
}

/* 隐私提示信息 */
.privacy-note {
  font-size: 28rpx;
  color: #999999;
  text-align: center;
  background-color: #FFF9ED;
  padding: 18rpx 32rpx;
  border-radius: 36rpx;
  max-width: 90%;
  margin-bottom: 70rpx;
  line-height: 1.5;
}

/* 绑定模块 */
.binding-module {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  margin-bottom: 40rpx;
}

/* 绑定标题和描述 */
.binding-title {
  font-size: 36rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 16rpx;
  text-align: center;
}

.binding-desc {
  font-size: 28rpx;
  color: #AAAAAA;
  text-align: center;
  margin-bottom: 50rpx;
  line-height: 1.4;
}

/* 微信手机号授权按钮 */
.wechat-phone-btn {
  width: 100%;
  height: 92rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #4080FF;
  color: #FFFFFF;
  font-size: 28rpx;
  font-weight: 400;
  border-radius: 46rpx;
  position: relative;
  margin-bottom: 32rpx;
  border: none;
  padding: 0;
  box-shadow: 0 6rpx 12rpx rgba(64, 128, 255, 0.2);
  letter-spacing: 0;
}

.wechat-phone-btn:active {
  opacity: 0.9;
  transform: scale(0.98);
}

.btn-text {
  display: inline-block;
  text-align: center;
  line-height: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 85%;
}

.wechat-icon {
  width: 40rpx;
  height: 40rpx;
  background-color: #FFFFFF;
  mask: url("data:image/svg+xml,%3Csvg t='1710327834584' class='icon' viewBox='0 0 1024 1024' version='1.1' xmlns='http://www.w3.org/2000/svg' p-id='4318'%3E%3Cpath d='M690.1 377.4c5.9 0 11.8 0.2 17.6 0.5-24.4-128.7-158.3-227.1-319.9-227.1C209 150.8 64 271.4 64 420.2c0 81.1 43.6 154.2 111.9 203.6 5.5 3.9 9.1 10.3 9.1 17.6 0 2.4-0.5 4.6-1.1 6.9-5.5 20.3-14.2 52.8-14.6 54.3-0.7 2.6-1.7 5.2-1.7 7.9 0 5.9 4.8 10.8 10.8 10.8 2.4 0 4.8-0.7 6.9-2l70.9-40.9c5.3-3.1 11-5 17.2-5 3.3 0 6.7 0.5 9.8 1.4 33.5 9.5 68.8 14.8 105.7 14.8 6 0 11.9-0.1 17.8-0.4-7.1-21-10.9-43.1-10.9-66 0-135.8 132.2-245.8 295.3-245.8z m-194.3-86.5c23.8 0 43.2 19.3 43.2 43.1s-19.3 43.1-43.2 43.1c-23.8 0-43.2-19.3-43.2-43.1s19.4-43.1 43.2-43.1z m-215.9 86.2c-23.8 0-43.2-19.3-43.2-43.1s19.3-43.1 43.2-43.1 43.2 19.3 43.2 43.1-19.4 43.1-43.2 43.1z' fill='%23ffffff' p-id='4319'%3E%3C/path%3E%3Cpath d='M866.7 792.7c56.9-41.2 93.2-102 93.2-169.7 0-124-120.8-224.5-269.9-224.5-149 0-269.9 100.5-269.9 224.5S540.9 847.5 690 847.5c30.8 0 60.6-4.4 88.1-12.3 2.6-0.8 5.2-1.2 7.9-1.2 5.2 0 9.9 1.6 14.3 4.1l59.1 34c1.7 1 3.6 1.5 5.5 1.5 5 0 9.3-4.1 9.3-9.1 0-2.1-0.8-4.3-1.4-6.1-5.6-19.9-14.5-51.9-14.5-51.9-0.5-1.9-0.9-3.8-0.9-5.8 0-5.9 3.2-11 7.9-14.5z m-30.5-48.1c17.8 0 32.1-14.3 32.1-32.1s-14.3-32.1-32.1-32.1-32.1 14.3-32.1 32.1 14.3 32.1 32.1 32.1z m-160.6 0c17.8 0 32.1-14.3 32.1-32.1s-14.3-32.1-32.1-32.1-32.1 14.3-32.1 32.1 14.3 32.1 32.1 32.1z' fill='%23ffffff' p-id='4320'%3E%3C/path%3E%3C/svg%3E") no-repeat center;
  mask-size: 100% 100%;
  -webkit-mask: url("data:image/svg+xml,%3Csvg t='1710327834584' class='icon' viewBox='0 0 1024 1024' version='1.1' xmlns='http://www.w3.org/2000/svg' p-id='4318'%3E%3Cpath d='M690.1 377.4c5.9 0 11.8 0.2 17.6 0.5-24.4-128.7-158.3-227.1-319.9-227.1C209 150.8 64 271.4 64 420.2c0 81.1 43.6 154.2 111.9 203.6 5.5 3.9 9.1 10.3 9.1 17.6 0 2.4-0.5 4.6-1.1 6.9-5.5 20.3-14.2 52.8-14.6 54.3-0.7 2.6-1.7 5.2-1.7 7.9 0 5.9 4.8 10.8 10.8 10.8 2.4 0 4.8-0.7 6.9-2l70.9-40.9c5.3-3.1 11-5 17.2-5 3.3 0 6.7 0.5 9.8 1.4 33.5 9.5 68.8 14.8 105.7 14.8 6 0 11.9-0.1 17.8-0.4-7.1-21-10.9-43.1-10.9-66 0-135.8 132.2-245.8 295.3-245.8z m-194.3-86.5c23.8 0 43.2 19.3 43.2 43.1s-19.3 43.1-43.2 43.1c-23.8 0-43.2-19.3-43.2-43.1s19.4-43.1 43.2-43.1z m-215.9 86.2c-23.8 0-43.2-19.3-43.2-43.1s19.3-43.1 43.2-43.1 43.2 19.3 43.2 43.1-19.4 43.1-43.2 43.1z' fill='%23ffffff' p-id='4319'%3E%3C/path%3E%3Cpath d='M866.7 792.7c56.9-41.2 93.2-102 93.2-169.7 0-124-120.8-224.5-269.9-224.5-149 0-269.9 100.5-269.9 224.5S540.9 847.5 690 847.5c30.8 0 60.6-4.4 88.1-12.3 2.6-0.8 5.2-1.2 7.9-1.2 5.2 0 9.9 1.6 14.3 4.1l59.1 34c1.7 1 3.6 1.5 5.5 1.5 5 0 9.3-4.1 9.3-9.1 0-2.1-0.8-4.3-1.4-6.1-5.6-19.9-14.5-51.9-14.5-51.9-0.5-1.9-0.9-3.8-0.9-5.8 0-5.9 3.2-11 7.9-14.5z m-30.5-48.1c17.8 0 32.1-14.3 32.1-32.1s-14.3-32.1-32.1-32.1-32.1 14.3-32.1 32.1 14.3 32.1 32.1 32.1z m-160.6 0c17.8 0 32.1-14.3 32.1-32.1s-14.3-32.1-32.1-32.1-32.1 14.3-32.1 32.1 14.3 32.1 32.1 32.1z' fill='%23ffffff' p-id='4320'%3E%3C/path%3E%3C/svg%3E") no-repeat center;
  -webkit-mask-size: 100% 100%;
  margin-right: 8rpx;
  position: relative;
  top: 1px;
  flex-shrink: 0;
}

.wechat-phone-btn text {
  margin-left: 2rpx;
}

/* 跳过绑定 */
.skip-binding {
  font-size: 28rpx;
  color: #AAAAAA;
  padding: 16rpx 32rpx;
  margin-top: 8rpx;
  margin-bottom: 60rpx;
  text-align: center;
}

.skip-binding:active {
  opacity: 0.7;
}

/* 安全提示 */
.security-tips {
  width: 100%;
  background-color: #F0F2F7;
  padding: 20rpx 24rpx;
  border-radius: 12rpx;
  text-align: center;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.security-tips text {
  font-size: 24rpx;
  color: #999999;
  line-height: 1.6;
}

/* 年级选择器样式 */
.grade-selector-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: flex-end;
}

.grade-selector-container {
  width: 100%;
  background-color: #fff;
  border-radius: 24rpx 24rpx 0 0;
  padding: 0;
  max-height: 80vh;
}

.grade-selector-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx 40rpx 20rpx;
  border-bottom: 2rpx solid #f0f0f0;
  position: sticky;
  top: 0;
  background-color: #fff;
  z-index: 10;
}

.cancel-btn, .confirm-btn {
  font-size: 30rpx;
  padding: 16rpx 20rpx;
  color: #3E7BFA;
  font-weight: 500;
}

.cancel-btn {
  color: #999;
}

.title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.grade-selector-content {
  padding: 20rpx 40rpx 40rpx;
  max-height: 50vh;
  overflow-y: auto;
}

.grade-item {
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16rpx;
  margin-bottom: 16rpx;
  font-size: 30rpx;
  color: #333;
  background-color: #f8f9fa;
  transition: all 0.3s;
  border: 2rpx solid transparent;
  position: relative;
}

.grade-item:active {
  background-color: #e0e0e0;
}

.grade-item.active {
  background-color: #e6f2ff;
  color: #3E7BFA;
  border-color: #3E7BFA;
  font-weight: 500;
}

.grade-item.active::after {
  content: "✓";
  position: absolute;
  right: 24rpx;
  color: #3E7BFA;
  font-weight: bold;
  font-size: 28rpx;
} 