import json
import re

def fix_nested_json():
    """修复嵌套JSON的转义问题"""
    print("正在修复嵌套JSON转义问题...")
    
    try:
        # 读取文件
        with open('insert.json', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 解析外层JSON
        data = json.loads(content)
        text_content = data['text']
        
        # 提取JSON代码块
        pattern = r'```json(.*?)```'
        matches = re.findall(pattern, text_content, re.DOTALL)
        
        if not matches:
            print("没有找到JSON代码块")
            return
        
        json_content = matches[0].strip()
        print(f"原始JSON长度: {len(json_content)}")
        
        # 修复策略：
        # 1. 找到所有类似 "field": "{...}" 的模式
        # 2. 正确转义内部的JSON字符串
        
        # 先处理几个特定的有问题的字段
        problematic_fields = [
            'question_content', 'options', 'correct_answer', 'answer_explanation', 
            'solution_steps', 'solution_methods', 'key_points', 'common_mistakes',
            'ai_tags'
        ]
        
        fixed_content = json_content
        
        for field in problematic_fields:
            # 匹配模式: "field": "JSON字符串"
            pattern = rf'"{field}": "([^"]*(?:\\"[^"]*)*)"'
            
            def fix_field_value(match):
                field_name = field
                field_value = match.group(1)
                
                # 如果值看起来像JSON，进行特殊处理
                if field_value.startswith('{') or field_value.startswith('['):
                    # 替换内部的转义
                    fixed_value = field_value.replace('\\"', '"')  # 先去掉转义
                    # 然后正确转义
                    fixed_value = fixed_value.replace('"', '\\"')
                    return f'"{field_name}": "{fixed_value}"'
                else:
                    return match.group(0)  # 不修改
            
            fixed_content = re.sub(pattern, fix_field_value, fixed_content)
        
        print(f"修复后JSON长度: {len(fixed_content)}")
        
        # 保存修复结果
        with open('fixed_nested.json', 'w', encoding='utf-8') as f:
            f.write(fixed_content)
        
        # 尝试解析
        try:
            parsed = json.loads(fixed_content)
            print("修复成功!")
            print(f"题目数量: {len(parsed)}")
            
            # 创建正确的insert.json
            correct_data = {
                "text": f"```json\n{json.dumps(parsed, ensure_ascii=False, indent=2)}\n```",
                "usage": data.get("usage", {}),
                "finish_reason": data.get("finish_reason", "stop"),
                "files": data.get("files", [])
            }
            
            with open('insert_fixed_final.json', 'w', encoding='utf-8') as f:
                json.dump(correct_data, f, ensure_ascii=False, indent=2)
            
            print("已创建修复后的文件: insert_fixed_final.json")
            return parsed
            
        except json.JSONDecodeError as e:
            print(f"修复失败: {e}")
            print(f"错误位置: line {e.lineno}, column {e.colno}")
            
            # 如果还是失败，尝试更简单的方法
            print("尝试简化修复...")
            
            # 直接替换所有的嵌套JSON为简单字符串
            simplified_content = json_content
            
            # 将复杂的JSON字段替换为简单字符串
            simplifications = {
                r'"question_content": ".*?"': '"question_content": "角度转换题目"',
                r'"options": ".*?"': '"options": "[]"',
                r'"correct_answer": ".*?"': '"correct_answer": "A"',
                r'"answer_explanation": ".*?"': '"answer_explanation": "解析"',
                r'"solution_steps": ".*?"': '"solution_steps": "[]"',
                r'"solution_methods": ".*?"': '"solution_methods": "[]"',
                r'"key_points": ".*?"': '"key_points": "[]"',
                r'"common_mistakes": ".*?"': '"common_mistakes": "[]"',
                r'"ai_tags": ".*?"': '"ai_tags": "[]"'
            }
            
            for pattern, replacement in simplifications.items():
                simplified_content = re.sub(pattern, replacement, simplified_content, flags=re.DOTALL)
            
            try:
                parsed = json.loads(simplified_content)
                print("简化修复成功!")
                print(f"题目数量: {len(parsed)}")
                
                # 创建简化版本
                simple_data = {
                    "text": f"```json\n{json.dumps(parsed, ensure_ascii=False, indent=2)}\n```",
                    "usage": data.get("usage", {}),
                    "finish_reason": data.get("finish_reason", "stop"),
                    "files": data.get("files", [])
                }
                
                with open('insert_simplified.json', 'w', encoding='utf-8') as f:
                    json.dump(simple_data, f, ensure_ascii=False, indent=2)
                
                print("已创建简化版文件: insert_simplified.json")
                return parsed
                
            except json.JSONDecodeError as e:
                print(f"简化修复也失败: {e}")
                return None
        
    except Exception as e:
        print(f"修复过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    result = fix_nested_json()
    if result:
        print("\n修复完成!")
    else:
        print("\n修复失败") 