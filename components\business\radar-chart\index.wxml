<view class="radar-chart-wrapper">
  <!-- 标题区域 - 在图谱顶部外部 -->
  <view class="radar-chart-title" wx:if="{{title}}">{{title}}</view>
  
  <!-- 顶部图例 - 仅当legendPosition为top时显示 -->
  <view class="chart-legend" wx:if="{{chartData && legendPosition === 'top'}}">
    <view class="legend-item">
      <view class="legend-color" style="background-color: {{primaryColor}};"></view>
      <text class="legend-text">我的能力</text>
    </view>
    <view class="legend-item">
      <view class="legend-color" style="background-color: {{secondaryColor}};"></view>
      <text class="legend-text">班级平均</text>
    </view>
  </view>
  
  <!-- 雷达图区域 -->
  <view class="radar-chart-container">
    <!-- SVG Base64编码的图像 -->
    <image 
      wx:if="{{imageLoaded && svgBase64}}"
      src="{{svgBase64}}"
      class="svg-image"
      mode="aspectFit"
      style="width: {{width}}px; height: {{height}}px;"
      bindload="onImageLoad"
    ></image>
    
    <!-- 加载中提示 -->
    <view class="loading-container" wx:if="{{!imageLoaded && chartData}}">
      <view class="loading-spinner"></view>
    </view>
  </view>
  
  <!-- 底部图例 - 仅当legendPosition为bottom时显示 -->
  <view class="chart-legend" wx:if="{{chartData && legendPosition === 'bottom'}}">
    <view class="legend-item">
      <view class="legend-color" style="background-color: {{primaryColor}};"></view>
      <text class="legend-text">我的能力</text>
    </view>
    <view class="legend-item">
      <view class="legend-color" style="background-color: {{secondaryColor}};"></view>
      <text class="legend-text">班级平均</text>
    </view>
  </view>
</view> 