<view class="bottom-panel safe-bottom-all-devices custom-class">
  <!-- 顶部功能按钮区 -->
  <view class="top-functions">
    <view class="function-btn {{selectedFeature === 'homework-check' ? 'function-active' : ''}}" 
          id="feature-homework"
          bindtap="onFeatureTap" 
          data-type="homework-check"
          hover-class="btn-hover">
      <view class="icon icon-homework icon-sm"></view>
      <text class="function-text">作业检查</text>
    </view>
    <view class="function-btn {{selectedFeature === 'photo-solve' ? 'function-active' : ''}}" 
          id="feature-photo"
          bindtap="onFeatureTap" 
          data-type="photo-solve"
          hover-class="btn-hover">
      <view class="icon icon-photo-solve icon-sm"></view>
      <text class="function-text">拍照解题</text>
    </view>
    <view class="function-btn {{selectedFeature === 'add-to-wrong' ? 'function-active' : ''}}" 
          id="feature-wrong"
          bindtap="onFeatureTap" 
          data-type="add-to-wrong"
          hover-class="btn-hover">
      <view class="icon icon-wrong-book icon-sm"></view>
      <text class="function-text">加入错题本</text>
    </view>
    <view class="function-btn {{selectedFeature === 'scan-paper' ? 'function-active' : ''}}" 
          id="feature-scan"
          bindtap="onFeatureTap" 
          data-type="scan-paper"
          hover-class="btn-hover">
      <view class="icon icon-scan-paper icon-sm"></view>
      <text class="function-text">拍试卷</text>
    </view>
  </view>

  <!-- 输入区域 -->
  <view class="input-wrapper">
    <view class="input-left">
      <view class="icon icon-camera icon-md icon-primary" bindtap="onTakePhoto" hover-class="icon-hover" aria-label="拍照上传"></view>
    </view>
    <view class="input-middle">
      <input type="text" 
             class="input-field"
             value="{{inputValue}}" 
             bindinput="onInput" 
             placeholder="发消息或按住说话..." 
             confirm-type="send" 
             bindconfirm="onSend" 
             adjust-position="{{false}}"
             cursor-spacing="12"
             bindfocus="onInputFocus"
             bindblur="onInputBlur"
             focus="{{focus}}"
             confirm-hold="{{false}}" />
    </view>
    <view class="input-right">
      <view class="voice-btn {{isRecording ? 'recording' : ''}}" 
            hover-class="icon-hover" 
            bindtouchstart="onStartVoiceRecord" 
            bindtouchend="onEndVoiceRecord" 
            bindtouchmove="onVoiceTouchMove"
            wx:if="{{!inputValue}}"
            aria-label="语音输入">
        <view class="icon icon-voice icon-md icon-primary"></view>
      </view>
      <view class="send-btn {{sendEnabled ? 'enabled' : 'disabled'}}" 
            bindtap="onSend" 
            hover-class="send-btn-hover"
            wx:if="{{inputValue}}"
            aria-disabled="{{!sendEnabled}}"
            aria-label="发送消息">
        <view class="send-arrow-icon"></view>
      </view>
    </view>
  </view>
</view> 