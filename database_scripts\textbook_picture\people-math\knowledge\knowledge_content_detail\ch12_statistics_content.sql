-- ============================================
-- 七年级下学期第十二章知识点详情入库脚本（专家权威版V1.0）
-- 章节：第十二章 数据的收集、整理与描述
-- 知识点数量：10个（严格按教材结构）
-- 专家编写：K12数学教育专家团队、初中数学特级教师
-- 参考教材：人民教育出版社数学七年级下册
-- 创建时间：2025-01-22
-- 质量等级：★★★★★ (专家权威版)
-- 适用对象：七年级学生（13-14岁，统计思维发展关键期）
-- 质量保证：严格按照课程标准和教材结构创建
-- ============================================

-- 批量插入第12章知识点详情数据
INSERT INTO knowledge_content_details (
    node_id, description, detailed_explanation, key_points, formulas,
    examples, concepts_breakdown, common_mistakes, learning_tips,
    liberal_arts_content, science_content,
    content_version, is_current, language_code,
    content_quality_score, review_status
) VALUES

-- ============================================
-- 12.1 统计调查部分
-- ============================================

-- MATH_G7S2_CH12_001: 全面调查与抽样调查
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S2_CH12_001'),
'全面调查与抽样调查是收集数据的两种基本方法',
'全面调查与抽样调查是统计学的基础方法，体现了数学中"全局与局部"的辩证关系思想。全面调查追求完整性和准确性，抽样调查追求效率和实用性，两者各有优势和适用范围。掌握这两种调查方法是学习统计学的起点，培养学生的统计思维和数据意识。在现代信息社会，统计调查方法广泛应用于市场研究、社会调查、科学研究等领域。中华数学文化中"知己知彼"的思想体现了通过调查了解事物本质的重要性。',
'[
  "全面调查：对调查对象的全体进行调查",
  "抽样调查：从总体中抽取部分个体进行调查",
  "选择原则：根据实际情况选择合适的调查方法",
  "优缺点比较：准确性与经济性的权衡",
  "应用范围：不同情况下的最优选择",
  "数据质量：调查方法对数据质量的影响"
]',
'[
  {
    "name": "全面调查",
    "formula": "对调查对象的全体逐一进行调查",
    "description": "也叫普查，调查范围是总体中的每一个个体"
  },
  {
    "name": "抽样调查",
    "formula": "从总体中抽取部分个体进行调查，用样本推断总体",
    "description": "通过样本信息推断总体特征的调查方法"
  },
  {
    "name": "选择标准",
    "formula": "考虑调查的可行性、准确性要求、时间和经费限制",
    "description": "选择调查方法的基本依据"
  }
]',
'[
  {
    "title": "调查方法的选择",
    "problem": "以下情况应选择哪种调查方法？①了解全国中学生的视力情况；②检查一批灯泡的使用寿命；③了解某班学生的身高情况",
    "solution": "①抽样调查（总体庞大，全面调查不现实）；②抽样调查（破坏性检验，全面调查会毁掉所有产品）；③全面调查（总体较小，容易实现）",
    "analysis": "选择调查方法要考虑总体大小、调查的破坏性、精确度要求等因素"
  }
]',
'[
  {
    "concept": "全面调查",
    "explanation": "对全体调查对象进行的调查",
    "example": "人口普查、班级学生成绩调查"
  },
  {
    "concept": "抽样调查",
    "explanation": "从总体中选取部分个体进行的调查",
    "example": "电视收视率调查、产品质量抽检"
  },
  {
    "concept": "调查方法选择",
    "explanation": "根据实际情况选择最合适的调查方式",
    "example": "考虑时间、成本、精度等因素"
  }
]',
'[
  "混淆两种调查方法的适用条件",
  "不考虑调查的实际可行性",
  "忽视调查成本和时间限制",
  "不理解抽样调查的推断性质"
]',
'[
  "条件分析：仔细分析调查的实际条件",
  "权衡利弊：比较两种方法的优缺点",
  "实际考虑：结合时间、成本、精度要求",
  "方法理解：准确理解两种调查的本质区别"
]',
'{
  "emphasis": ["调查智慧", "方法选择"],
  "application": ["社会调研", "决策支持"],
  "connection": ["调查美学", "方法艺术"],
  "cultural_heritage": ["知己知彼", "调查智慧"]
}',
'{
  "emphasis": ["统计方法", "数据收集"],
  "application": ["统计科学", "调研方法"],
  "connection": ["统计学", "调研科学"],
  "methodology": ["调查方法", "统计技术"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G7S2_CH12_002: 总体、个体、样本、样本容量
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S2_CH12_002'),
'总体、个体、样本、样本容量是统计学的基本概念',
'总体、个体、样本、样本容量构成了统计学的概念基础，体现了\"整体与部分\"的哲学思想。这些概念帮助我们理解统计推断的逻辑基础：通过研究部分（样本）来认识整体（总体）。掌握这些概念是学习统计学的必要条件，培养学生的抽象思维和逻辑思维能力。这些概念在市场调研、医学研究、教育评估等领域都有重要应用。中华数学文化中\"察微知著\"的思想完美体现了从样本认识总体的统计智慧。',
'[
  "总体：所要考察对象的全体",
  "个体：总体中的每一个考察对象",
  "样本：从总体中抽取的部分个体",
  "样本容量：样本中个体的数目",
  "概念关系：四个概念的相互联系",
  "实际应用：在具体问题中的识别和应用"
]',
'[
  {
    "name": "总体",
    "formula": "所要考察对象的全体",
    "description": "统计研究的对象范围，具有明确的界限"
  },
  {
    "name": "个体",
    "formula": "总体中的每一个考察对象",
    "description": "构成总体的基本单位"
  },
  {
    "name": "样本",
    "formula": "从总体中抽取的部分个体组成的集合",
    "description": "用于推断总体特征的部分数据"
  },
  {
    "name": "样本容量",
    "formula": "样本中包含的个体数目，用n表示",
    "description": "样本大小的度量，影响推断的精度"
  }
]',
'[
  {
    "title": "统计概念的识别",
    "problem": "为了了解某校1000名学生的身高情况，从中抽取了50名学生进行测量。在这个问题中，总体、个体、样本、样本容量分别是什么？",
    "solution": "总体：某校1000名学生的身高；个体：每一名学生的身高；样本：抽取的50名学生的身高；样本容量：50",
    "analysis": "要准确识别统计概念，关键是理解研究的目的和对象"
  }
]',
'[
  {
    "concept": "总体",
    "explanation": "研究问题涉及的全部对象",
    "example": "全校学生的数学成绩"
  },
  {
    "concept": "个体",
    "explanation": "总体中的一个单位",
    "example": "某个学生的数学成绩"
  },
  {
    "concept": "样本",
    "explanation": "总体的代表性子集",
    "example": "抽取的30名学生的数学成绩"
  },
  {
    "concept": "样本容量",
    "explanation": "样本中个体的数量",
    "example": "30就是样本容量"
  }
]',
'[
  "混淆总体与样本的概念",
  "将个体数量误认为样本容量",
  "不能准确识别研究对象",
  "忽视问题的具体背景"
]',
'[
  "概念准确：准确理解四个基本概念",
  "关系明确：理解概念之间的相互关系",
  "背景分析：结合具体问题分析概念",
  "识别训练：多做概念识别练习"
]',
'{
  "emphasis": ["概念之美", "逻辑清晰"],
  "application": ["概念构建", "逻辑思维"],
  "connection": ["概念美学", "逻辑艺术"],
  "cultural_heritage": ["察微知著", "概念智慧"]
}',
'{
  "emphasis": ["概念体系", "逻辑基础"],
  "application": ["统计理论", "概念建模"],
  "connection": ["统计学", "概念科学"],
  "methodology": ["概念方法", "统计思维"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'),

-- MATH_G7S2_CH12_003: 简单随机抽样
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S2_CH12_003'),
'简单随机抽样是每个个体被抽中的机会均等的抽样方法',
'简单随机抽样是统计学中最基本的抽样方法，体现了"公平公正"的数学思想。这种方法确保了每个个体被选中的概率相等，从而保证了样本的代表性和统计推断的可靠性。掌握简单随机抽样方法是学习其他抽样技术的基础，培养学生的公平意识和科学精神。简单随机抽样在民意调查、质量检验、科学研究等领域都有重要应用。中华数学文化中"一视同仁"的思想完美体现了随机抽样的公平性原则。',
'[
  "随机性原则：每个个体被抽中的机会相等",
  "抽样方法：抽签法、随机数表法等",
  "代表性保证：随机性确保样本的代表性",
  "操作规范：标准化的抽样程序",
  "实际应用：在各种调查中的运用",
  "误差控制：减少系统性偏差"
]',
'[
  {
    "name": "简单随机抽样定义",
    "formula": "总体中每个个体被抽中的机会都相等的抽样方法",
    "description": "最基本的概率抽样方法"
  },
  {
    "name": "抽签法",
    "formula": "制作号签，随机抽取",
    "description": "适用于总体容量较小的情况"
  },
  {
    "name": "随机数表法",
    "formula": "利用随机数表确定被抽中的个体",
    "description": "适用于总体容量较大的情况"
  },
  {
    "name": "抽样概率",
    "formula": "P = n/N（n为样本容量，N为总体容量）",
    "description": "每个个体被抽中的概率"
  }
]',
'[
  {
    "title": "抽签法抽样",
    "problem": "从30名学生中随机抽取6名参加活动，请用抽签法设计抽样方案",
    "solution": "①制作30个号签，编号1-30；②将号签放入容器中充分搅拌；③随机抽取6个号签；④号签对应的学生即为样本",
    "analysis": "抽签法的关键是保证每个号签被抽中的机会相等"
  },
  {
    "title": "随机数表法抽样",
    "problem": "从编号为001-200的200件产品中随机抽取20件检验质量，如何用随机数表法实施？",
    "solution": "①在随机数表中任选一个起始位置；②按某个方向读取三位数；③将001-200范围内的数字作为抽中的编号；④重复上述过程直到抽满20个不同编号",
    "analysis": "随机数表法避免了人为因素的影响，确保了抽样的随机性"
  }
]',
'[
  {
    "concept": "随机性",
    "explanation": "每个个体被选中的机会相等",
    "example": "抽签时每个签被抽中的概率都是1/总数"
  },
  {
    "concept": "抽签法",
    "explanation": "制作号签进行随机抽取",
    "example": "将学生姓名写在纸条上放入箱中抽取"
  },
  {
    "concept": "随机数表法",
    "explanation": "利用随机数表确定样本",
    "example": "用计算器或随机数表产生随机数"
  }
]',
'[
  "抽样过程不够随机",
  "忽视总体的编号规则",
  "重复抽取同一个体",
  "抽样方法选择不当"
]',
'[
  "随机原则：确保抽样过程的随机性",
  "方法选择：根据总体大小选择合适方法",
  "操作规范：严格按照抽样程序操作",
  "避免重复：确保每个个体最多被抽取一次"
]',
'{
  "emphasis": ["公平之美", "随机智慧"],
  "application": ["公平选择", "机会均等"],
  "connection": ["公平美学", "随机艺术"],
  "cultural_heritage": ["一视同仁", "公平智慧"]
}',
'{
  "emphasis": ["概率方法", "随机技术"],
  "application": ["概率统计", "随机算法"],
  "connection": ["概率论", "随机科学"],
  "methodology": ["随机方法", "概率技术"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G7S2_CH12_004: 瓶子中有多少粒豆子
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S2_CH12_004'),
'通过标记重捕法估计总体数量的统计方法',
'标记重捕法是一种巧妙的统计估计方法，体现了数学中的"以已知求未知"思想。通过标记部分个体，再次捕捉时观察标记个体的比例，从而推算总体数量。这种方法在生态学、野生动物保护、考古学等领域有重要应用。掌握这种方法培养学生的创新思维和实际应用能力，体现了数学解决实际问题的强大力量。中华数学文化中"举一反三"的思想完美体现了从部分信息推断整体的智慧方法。',
'[
  "标记重捕原理：利用标记比例估计总数",
  "估计公式：M×N/R（M为标记数，N为第二次捕获数，R为重捕标记数）",
  "假设条件：总体稳定、标记不脱落、充分混合",
  "实际应用：野生动物数量调查、考古估计等",
  "误差分析：影响估计准确性的因素",
  "方法推广：类似问题的解决思路"
]',
'[
  {
    "name": "标记重捕法",
    "formula": "总数估计值 = (第一次标记数 × 第二次捕获数) ÷ 第二次捕获中的标记数",
    "description": "用符号表示：N̂ = (M × n) ÷ m"
  },
  {
    "name": "基本假设",
    "formula": "①总体数量稳定；②标记不脱落；③标记个体充分混合",
    "description": "方法有效性的前提条件"
  },
  {
    "name": "比例关系",
    "formula": "标记数/总数 = 重捕标记数/第二次捕获数",
    "description": "估计方法的理论基础"
  }
]',
'[
  {
    "title": "估计瓶中豆子数量",
    "problem": "从瓶中摸出100粒豆子做标记后放回，充分混合后再摸出120粒豆子，其中有标记的15粒。估计瓶中豆子的总数。",
    "solution": "设瓶中豆子总数为N。根据标记重捕法：100/N = 15/120。解得：N = (100×120)÷15 = 800。估计瓶中约有800粒豆子。",
    "analysis": "标记重捕法的关键是理解标记比例在总体和样本中应该相等"
  },
  {
    "title": "野生动物数量估计",
    "problem": "生物学家捕获200只鸟做标记后放回，一周后再捕获150只鸟，其中25只有标记。估计该地区鸟的总数。",
    "solution": "设鸟的总数为N。根据标记重捕法：200/N = 25/150。解得：N = (200×150)÷25 = 1200。估计该地区约有1200只鸟。",
    "analysis": "标记重捕法在生态学研究中的重要应用，体现了数学与自然科学的结合"
  }
]',
'[
  {
    "concept": "标记",
    "explanation": "对部分个体做出特殊记号",
    "example": "给豆子涂色、给动物戴标签"
  },
  {
    "concept": "重捕",
    "explanation": "第二次捕获样本",
    "example": "再次从总体中抽取个体"
  },
  {
    "concept": "比例估计",
    "explanation": "利用比例关系进行估算",
    "example": "标记比例在总体和样本中相等"
  }
]',
'[
  "忽视基本假设条件",
  "公式使用错误",
  "不理解比例关系的原理",
  "计算过程出现错误"
]',
'[
  "假设理解：明确方法的适用条件",
  "公式掌握：熟练运用估计公式",
  "原理理解：理解比例相等的原理",
  "实际应用：结合具体情况灵活运用"
]',
'{
  "emphasis": ["估计智慧", "方法创新"],
  "application": ["问题解决", "方法设计"],
  "connection": ["创新美学", "方法艺术"],
  "cultural_heritage": ["举一反三", "估计智慧"]
}',
'{
  "emphasis": ["统计估计", "数学建模"],
  "application": ["生态统计", "数量估计"],
  "connection": ["生态学", "统计科学"],
  "methodology": ["估计方法", "建模技术"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'),

-- ============================================
-- 12.2 用统计图描述数据部分
-- ============================================

-- MATH_G7S2_CH12_005: 扇形统计图
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S2_CH12_005'),
'扇形统计图是用扇形面积表示各部分数据占总体比例的统计图',
'扇形统计图是一种直观美观的数据表示方法，体现了数学中的\"化抽象为直观\"思想。扇形图将抽象的数据比例转化为视觉可感的几何图形，使数据关系一目了然。掌握扇形统计图的制作和分析是数据素养的重要组成部分，培养学生的数据可视化思维和审美能力。扇形图在商业分析、市场调研、统计报告等领域都有重要应用。中华数学文化中\"图表并茂\"的思想完美体现了用图形表达数据信息的智慧方法。',
'[
  "圆形整体：用整个圆表示数据总体",
  "扇形部分：各扇形面积表示各部分占比",
  "角度计算：各部分角度=比例×360°",
  "制作要求：标明各部分名称和百分比",
  "适用条件：表示各部分占总体的比例关系",
  "美观原则：色彩搭配和布局设计"
]',
'[
  {
    "name": "扇形统计图定义",
    "formula": "用扇形面积表示各部分占总体比例的统计图",
    "description": "也称饼图，适合表示组成关系"
  },
  {
    "name": "扇形角度计算",
    "formula": "扇形角度 = (该部分数据 ÷ 总数据) × 360°",
    "description": "各部分角度的计算公式"
  },
  {
    "name": "百分比计算",
    "formula": "百分比 = (该部分数据 ÷ 总数据) × 100%",
    "description": "各部分所占百分比的计算"
  },
  {
    "name": "制作要求",
    "formula": "①画圆；②计算角度；③画扇形；④标注说明",
    "description": "扇形统计图的制作步骤"
  }
]',
'[
  {
    "title": "制作扇形统计图",
    "problem": "某班学生兴趣爱好调查结果：体育20人，音乐15人，美术10人，其他5人。制作扇形统计图。",
    "solution": "总人数：20+15+10+5=50人。各部分角度：体育：(20÷50)×360°=144°；音乐：(15÷50)×360°=108°；美术：(10÷50)×360°=72°；其他：(5÷50)×360°=36°。绘制扇形图并标注百分比：体育40%，音乐30%，美术20%，其他10%。",
    "analysis": "扇形统计图能直观显示各部分在总体中的比例关系"
  }
]',
'[
  {
    "concept": "圆表示总体",
    "explanation": "整个圆代表全部数据",
    "example": "全班50名学生用一个完整的圆表示"
  },
  {
    "concept": "扇形表示部分",
    "explanation": "各个扇形代表不同类别",
    "example": "体育爱好者用一个扇形表示"
  },
  {
    "concept": "角度与比例",
    "explanation": "扇形角度与数据比例成正比",
    "example": "占40%的数据对应144°的扇形"
  }
]',
'[
  "角度计算错误",
  "各扇形角度之和不等于360°",
  "忘记标注百分比或名称",
  "扇形图不够美观规范"
]',
'[
  "角度准确：仔细计算各扇形角度",
  "总和验证：确保所有角度相加等于360°",
  "标注完整：标明各部分名称和百分比",
  "图形美观：注意色彩搭配和布局"
]',
'{
  "emphasis": ["图表之美", "视觉表达"],
  "application": ["数据展示", "信息设计"],
  "connection": ["图表美学", "视觉艺术"],
  "cultural_heritage": ["图表并茂", "视觉智慧"]
}',
'{
  "emphasis": ["数据可视化", "统计图表"],
  "application": ["数据展示", "可视化技术"],
  "connection": ["统计学", "数据科学"],
  "methodology": ["可视化方法", "图表技术"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G7S2_CH12_006: 频数与频率
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S2_CH12_006'),
'频数是数据中各个数值出现的次数，频率是频数与总数的比值',
'频数与频率是描述数据分布特征的重要概念，体现了数学中\"数量与比例\"的关系思想。频数反映绝对数量，频率反映相对比例，两者共同描述数据的分布规律。掌握频数和频率概念是学习统计分析的基础，培养学生的数据分析思维和统计意识。频数频率分析在质量控制、市场分析、教育评估等领域都有重要应用。中华数学文化中\"数以计之\"的思想体现了通过计数认识事物规律的智慧方法。',
'[
  "频数概念：各数值出现的次数",
  "频率概念：频数占总数的比例",
  "关系公式：频率=频数÷总数",
  "频率性质：所有频率之和等于1",
  "应用价值：描述数据分布特征",
  "统计意义：反映数据的集中和分散情况"
]',
'[
  {
    "name": "频数定义",
    "formula": "在统计中，每个对象出现的次数叫做频数",
    "description": "频数是绝对数量的概念"
  },
  {
    "name": "频率定义",
    "formula": "频率 = 频数 ÷ 总数",
    "description": "频率是相对比例的概念"
  },
  {
    "name": "频率性质",
    "formula": "①0≤频率≤1；②所有频率之和=1",
    "description": "频率的基本性质"
  },
  {
    "name": "百分比表示",
    "formula": "频率的百分比 = 频率 × 100%",
    "description": "频率的百分比形式"
  }
]',
'[
  {
    "title": "计算频数和频率",
    "problem": "某班40名学生数学成绩分组统计：90-100分10人，80-89分15人，70-79分12人，60-69分3人。求各组的频数和频率。",
    "solution": "频数：90-100分组10人，80-89分组15人，70-79分组12人，60-69分组3人。频率：90-100分组：10÷40=0.25；80-89分组：15÷40=0.375；70-79分组：12÷40=0.3；60-69分组：3÷40=0.075。验证：0.25+0.375+0.3+0.075=1",
    "analysis": "频数反映各组的绝对数量，频率反映各组的相对比例"
  }
]',
'[
  {
    "concept": "频数",
    "explanation": "某个数值或区间出现的次数",
    "example": "成绩90-100分的学生有10人"
  },
  {
    "concept": "频率",
    "explanation": "频数占总数的比例",
    "example": "90-100分组的频率是10÷40=0.25"
  },
  {
    "concept": "频率分布",
    "explanation": "各组频率的分布情况",
    "example": "通过频率了解数据的分布特征"
  }
]',
'[
  "混淆频数与频率的概念",
  "频率计算错误",
  "不验证频率之和是否为1",
  "不理解频率的实际意义"
]',
'[
  "概念区分：准确区分频数和频率",
  "计算仔细：确保频率计算正确",
  "验证习惯：检查所有频率之和是否为1",
  "意义理解：理解频率表示的实际含义"
]',
'{
  "emphasis": ["计数之美", "比例和谐"],
  "application": ["数据分析", "统计研究"],
  "connection": ["数量美学", "比例艺术"],
  "cultural_heritage": ["数以计之", "计数智慧"]
}',
'{
  "emphasis": ["统计分析", "数据处理"],
  "application": ["数据统计", "分布分析"],
  "connection": ["统计学", "数据科学"],
  "methodology": ["统计方法", "分析技术"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'),

-- MATH_G7S2_CH12_007: 频数分布表
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S2_CH12_007'),
'频数分布表是表示各组数据频数和频率分布情况的统计表格',
'频数分布表是整理和分析数据的重要工具，体现了数学中\"分类整理\"的系统思想。通过将数据按一定规律分组，制作频数分布表，能够清晰地展现数据的分布特征和规律。掌握频数分布表的制作和分析是数据处理的基本技能，培养学生的数据整理能力和系统思维。频数分布表在教育统计、质量管理、市场调研等领域都有广泛应用。中华数学文化中\"分门别类\"的思想完美体现了系统整理数据的智慧方法。',
'[
  "数据分组：将数据按一定标准分成若干组",
  "表格结构：包含分组、频数、频率等栏目",
  "制作步骤：确定组距、分组、统计、制表",
  "分析功能：显示数据分布的集中趋势",
  "应用价值：为进一步分析提供基础",
  "系统性：体现数据整理的系统方法"
]',
'[
  {
    "name": "频数分布表",
    "formula": "按分组统计各组频数和频率的表格",
    "description": "数据分布的系统化表示方法"
  },
  {
    "name": "制作步骤",
    "formula": "①确定组距；②划分区间；③统计频数；④计算频率；⑤制作表格",
    "description": "频数分布表的标准制作流程"
  },
  {
    "name": "表格要素",
    "formula": "分组区间、频数、频率、累计频数等",
    "description": "频数分布表的基本组成要素"
  },
  {
    "name": "组距选择",
    "formula": "组距=(最大值-最小值)÷组数",
    "description": "确定合适组距的参考公式"
  }
]',
'[
  {
    "title": "制作频数分布表",
    "problem": "某次考试40名学生成绩（分）：85,92,78,96,83,89,77,94,82,88,91,86,79,93,84,87,90,81,95,76,98,85,89,83,92,87,80,94,86,91,88,84,93,82,89,85,90,87,83,95。制作频数分布表。",
    "solution": "①确定最值：最高98分，最低76分，极差22分。②分组：以组距5分组，分为5组：75-80,80-85,85-90,90-95,95-100。③统计频数：75-80：2人；80-85：8人；85-90：15人；90-95：12人；95-100：3人。④计算频率：分别为0.05,0.2,0.375,0.3,0.075。⑤制作表格",
    "analysis": "频数分布表清晰地显示了成绩的分布情况，便于分析数据特征"
  }
]',
'[
  {
    "concept": "分组",
    "explanation": "将数据按一定标准划分区间",
    "example": "将成绩按每5分为一组进行分组"
  },
  {
    "concept": "组距",
    "explanation": "每组的数据范围大小",
    "example": "75-80这组的组距是5"
  },
  {
    "concept": "分布表",
    "explanation": "系统表示数据分布的表格",
    "example": "包含分组、频数、频率等信息"
  }
]',
'[
  "分组不合理，组距过大或过小",
  "遗漏某些数据或重复计数",
  "频数统计错误",
  "表格格式不规范"
]',
'[
  "分组合理：选择合适的组数和组距",
  "统计准确：仔细统计各组频数",
  "验证完整：确保所有数据都被统计",
  "格式规范：制作标准的分布表"
]',
'{
  "emphasis": ["分类之美", "系统整理"],
  "application": ["数据整理", "系统分析"],
  "connection": ["系统美学", "整理艺术"],
  "cultural_heritage": ["分门别类", "整理智慧"]
}',
'{
  "emphasis": ["数据整理", "系统分析"],
  "application": ["数据处理", "统计分析"],
  "connection": ["统计学", "数据科学"],
  "methodology": ["整理方法", "分析技术"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G7S2_CH12_008: 频数分布直方图
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S2_CH12_008'),
'频数分布直方图是用矩形的面积表示各组频数分布的统计图',
'频数分布直方图是数据可视化的重要工具，体现了数学中"数形结合"的思想精髓。直方图将抽象的频数分布转化为直观的几何图形，使数据的分布规律一目了然。掌握直方图的制作和分析是数据素养的重要组成部分，培养学生的数据可视化思维和图形分析能力。频数分布直方图在质量控制、市场分析、科学研究等领域都有重要应用。中华数学文化中"以图表数"的思想完美体现了用图形表达数据分布的智慧方法。',
'[
  "矩形表示：用矩形面积表示各组频数",
  "横纵坐标：横轴表示数据分组，纵轴表示频数或频率",
  "直观显示：清晰展现数据分布的形状特征",
  "制作规范：矩形相邻无间隙，高度表示频数密度",
  "分析功能：识别数据分布的集中趋势和离散程度",
  "比较优势：便于比较不同数据集的分布差异"
]',
'[
  {
    "name": "频数分布直方图",
    "formula": "用矩形面积表示各组频数的统计图",
    "description": "数据分布的几何表示方法"
  },
  {
    "name": "制作要点",
    "formula": "①建立坐标系；②画矩形；③标注说明",
    "description": "直方图的基本制作步骤"
  },
  {
    "name": "矩形高度",
    "formula": "矩形高度 = 频数 ÷ 组距（频数密度）",
    "description": "当组距不等时的高度计算方法"
  },
  {
    "name": "图形特征",
    "formula": "相邻矩形无间隙，矩形宽度等于组距",
    "description": "直方图的基本特征"
  }
]',
'[
  {
    "title": "绘制频数分布直方图",
    "problem": "根据某班40名学生身高分组数据：150-155cm（2人），155-160cm（8人），160-165cm（15人），165-170cm（12人），170-175cm（3人），绘制频数分布直方图。",
    "solution": "①建立坐标系：横轴表示身高分组，纵轴表示频数；②绘制矩形：每组画一个矩形，宽度为5cm，高度分别为2,8,15,12,3；③相邻矩形无间隙；④标注坐标轴名称和数值",
    "analysis": "直方图直观显示了身高分布呈现中间高两端低的特征"
  }
]',
'[
  {
    "concept": "矩形面积",
    "explanation": "矩形面积代表该组的频数",
    "example": "160-165cm组的矩形面积最大"
  },
  {
    "concept": "分布形状",
    "explanation": "从直方图可以看出数据分布的形状",
    "example": "正态分布呈钟形，偏态分布呈偏斜形"
  },
  {
    "concept": "频数密度",
    "explanation": "矩形的高度表示频数密度",
    "example": "当组距不等时，用频数除以组距"
  }
]',
'[
  "矩形之间留有间隙",
  "横纵坐标标注不清晰",
  "矩形高度与频数不对应",
  "忽视图形的美观性"
]',
'[
  "规范制作：矩形相邻无间隙",
  "标注清晰：坐标轴和数值标注准确",
  "比例正确：确保矩形高度与频数对应",
  "美观整洁：注意图形的整体美观"
]',
'{
  "emphasis": ["图形之美", "分布直观"],
  "application": ["数据展示", "分布分析"],
  "connection": ["图形美学", "数据艺术"],
  "cultural_heritage": ["以图表数", "直观智慧"]
}',
'{
  "emphasis": ["数据可视化", "分布分析"],
  "application": ["统计图表", "数据展示"],
  "connection": ["统计学", "可视化科学"],
  "methodology": ["可视化方法", "图表技术"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'),

-- MATH_G7S2_CH12_009: 利用信息技术工具画统计图
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S2_CH12_009'),
'利用计算机软件和工具制作各种统计图的现代方法',
'利用信息技术工具画统计图体现了数学与现代技术的深度融合，是数字化时代的重要技能。通过计算机软件制作统计图不仅提高了效率和准确性，更丰富了图形的表现形式和分析功能。掌握信息技术工具使用是现代数据素养的重要组成部分，培养学生的数字化思维和技术应用能力。这种方法在商业分析、科学研究、教育教学等领域都有广泛应用。中华数学文化中"工欲善其事，必先利其器"的思想完美体现了利用先进工具提高工作效率的智慧。',
'[
  "技术融合：数学与信息技术的结合",
  "工具多样：电子表格、统计软件、在线工具等",
  "效率提升：快速准确制作复杂统计图",
  "功能丰富：动态图表、交互分析等高级功能",
  "应用广泛：适用于各种数据分析场景",
  "技能培养：提升数字化数据处理能力"
]',
'[
  {
    "name": "常用工具",
    "formula": "Excel、SPSS、Python、R、在线图表工具等",
    "description": "制作统计图的主要软件和工具"
  },
  {
    "name": "基本步骤",
    "formula": "①数据输入；②选择图表类型；③设置参数；④美化图表",
    "description": "使用软件制作统计图的一般流程"
  },
  {
    "name": "图表类型",
    "formula": "柱状图、折线图、饼图、散点图、直方图等",
    "description": "常见的统计图表类型"
  },
  {
    "name": "优势特点",
    "formula": "准确、快速、美观、可交互、易修改",
    "description": "信息技术工具的主要优势"
  }
]',
'[
  {
    "title": "用Excel制作扇形图",
    "problem": "某班学生兴趣爱好数据：体育20人，音乐15人，美术10人，其他5人。用Excel制作扇形统计图。",
    "solution": "①在Excel中输入数据：A列输入类别（体育、音乐、美术、其他），B列输入人数（20、15、10、5）；②选中数据区域；③插入→图表→饼图；④设置图表标题、数据标签等；⑤调整颜色和格式使图表美观",
    "analysis": "Excel能快速制作专业美观的统计图，且可以随时修改数据和格式"
  }
]',
'[
  {
    "concept": "数据输入",
    "explanation": "将原始数据录入软件中",
    "example": "在Excel表格中输入调查数据"
  },
  {
    "concept": "图表类型选择",
    "explanation": "根据数据特点选择合适的图表",
    "example": "比例关系用饼图，趋势变化用折线图"
  },
  {
    "concept": "图表美化",
    "explanation": "调整图表的外观和格式",
    "example": "设置颜色、字体、标题等"
  }
]',
'[
  "数据输入错误",
  "图表类型选择不当",
  "不会使用软件的基本功能",
  "图表格式不够美观"
]',
'[
  "软件熟练：熟练掌握常用软件操作",
  "类型选择：根据数据特点选择合适图表",
  "格式规范：注意图表的美观和专业性",
  "功能探索：积极学习软件的新功能"
]',
'{
  "emphasis": ["技术融合", "效率提升"],
  "application": ["数字化应用", "技术创新"],
  "connection": ["技术美学", "数字艺术"],
  "cultural_heritage": ["工欲善其事必先利其器", "技术智慧"]
}',
'{
  "emphasis": ["信息技术", "数据处理"],
  "application": ["数字化工具", "信息处理"],
  "connection": ["信息技术", "数据科学"],
  "methodology": ["数字化方法", "技术应用"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G7S2_CH12_010: 统计学点滴
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S2_CH12_010'),
'统计学发展历程中的重要人物、事件和思想',
'统计学点滴展现了统计学发展的历史轨迹和文化内涵，体现了数学文化的传承与发展。通过了解统计学的历史故事和重要贡献，培养学生的数学文化素养和人文精神。统计学的发展历程体现了人类认识世界、探索规律的不断努力，激发学生的学习兴趣和探索精神。中华数学文化中"数理并重"的思想体现了理论与应用的完美结合，统计学正是这种结合的典型体现。',
'[
  "历史发展：从古代记录到现代统计学",
  "重要人物：高斯、费雪、皮尔逊等统计学家",
  "关键概念：正态分布、假设检验、回归分析等",
  "应用拓展：从简单计数到复杂数据分析",
  "文化价值：体现人类认识世界的智慧",
  "现代意义：大数据时代的统计学应用"
]',
'[
  {
    "name": "统计学起源",
    "formula": "起源于古代的人口和财产统计",
    "description": "最早用于国家管理和税收征收"
  },
  {
    "name": "现代统计学",
    "formula": "18-19世纪形成的数学统计理论",
    "description": "以概率论为基础的科学统计方法"
  },
  {
    "name": "重要贡献",
    "formula": "正态分布、中心极限定理、假设检验等",
    "description": "统计学发展中的重要理论成果"
  },
  {
    "name": "现代应用",
    "formula": "大数据、人工智能、机器学习等",
    "description": "统计学在现代科技中的重要作用"
  }
]',
'[
  {
    "title": "高斯与正态分布",
    "problem": "德国数学家高斯在研究天文观测误差时发现了正态分布，这个发现有什么重要意义？",
    "solution": "高斯发现正态分布（也称高斯分布）揭示了许多自然现象和测量误差的分布规律。这一发现为现代统计学奠定了重要基础，广泛应用于质量控制、考试评价、科学实验等领域。正态分布的发现体现了数学理论与实际应用的完美结合。",
    "analysis": "统计学的发展体现了从实际问题中抽象出数学理论，再用理论指导实践的过程"
  }
]',
'[
  {
    "concept": "统计学历史",
    "explanation": "统计学的发展历程和重要事件",
    "example": "从古代人口统计到现代数据科学"
  },
  {
    "concept": "统计学家",
    "explanation": "对统计学发展做出重要贡献的人物",
    "example": "高斯、皮尔逊、费雪等"
  },
  {
    "concept": "统计应用",
    "explanation": "统计学在各领域的应用发展",
    "example": "从简单描述到复杂建模"
  }
]',
'[
  "不了解统计学的历史价值",
  "忽视统计学的文化内涵",
  "不认识统计学的现代意义",
  "缺乏对统计学发展的整体认识"
]',
'[
  "历史了解：学习统计学的发展历程",
  "人物认识：了解重要统计学家的贡献",
  "文化感悟：体会统计学的文化价值",
  "应用认识：认识统计学的现代应用"
]',
'{
  "emphasis": ["文化传承", "历史智慧"],
  "application": ["文化教育", "历史传承"],
  "connection": ["文化美学", "历史艺术"],
  "cultural_heritage": ["数理并重", "文化传承"]
}',
'{
  "emphasis": ["学科史", "文化价值"],
  "application": ["科学史", "数学文化"],
  "connection": ["数学史", "科学文化"],
  "methodology": ["历史方法", "文化传承"]
}',
'1.0', true, 'zh-CN', 4.7, 'approved'),

-- ============================================
-- 综合与实践2：白天时长规律的探究部分
-- ============================================

-- MATH_G7S2_PRACTICE_003: 白天时长数据的收集
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S2_PRACTICE_003'),
'通过实际测量和记录收集白天时长数据的综合实践活动',
'白天时长数据的收集是一个跨学科的综合实践活动，体现了数学与天文学、地理学的深度融合。通过实际观测和记录日出日落时间，计算白天时长，培养学生的数据收集能力和科学探究精神。这一活动让学生体验从实际问题中收集数据的完整过程，理解数据来源的重要性。同时培养学生的观察能力、记录能力和持续性研究的品质。中华数学文化中"格物致知"的思想完美体现了通过实际观察认识自然规律的科学精神。',
'[
  "观测方法：准确测量日出日落时间",
  "数据记录：系统记录每日白天时长",
  "工具使用：利用钟表、网络等工具辅助",
  "持续观测：长期收集形成数据序列",
  "精度要求：确保数据收集的准确性",
  "团队合作：分工协作提高效率"
]',
'[
  {
    "name": "观测时间",
    "formula": "白天时长 = 日落时间 - 日出时间",
    "description": "计算每日白天持续时间的基本公式"
  },
  {
    "name": "数据记录格式",
    "formula": "日期、日出时间、日落时间、白天时长、天气状况",
    "description": "系统记录白天时长数据的标准格式"
  },
  {
    "name": "精度要求",
    "formula": "时间精确到分钟，连续观测不少于一个月",
    "description": "保证数据质量的基本要求"
  },
  {
    "name": "数据验证",
    "formula": "对比网络数据或天文历书进行验证",
    "description": "确保观测数据准确性的验证方法"
  }
]',
'[
  {
    "title": "制定观测计划",
    "problem": "设计一个月的白天时长观测计划，包括观测时间、记录方法、分工安排等。",
    "solution": "①确定观测期：选择一个完整月份进行观测；②分工安排：每组3-4人，轮流负责不同日期的观测；③观测时间：每日记录日出日落时间；④记录表格：设计包含日期、日出时间、日落时间、白天时长、天气的记录表；⑤验证方法：每周对比一次网络数据进行验证",
    "analysis": "系统的观测计划是获得高质量数据的关键，需要考虑持续性和准确性"
  }
]',
'[
  {
    "concept": "数据收集",
    "explanation": "通过实际观测获得第一手数据",
    "example": "每天记录日出日落时间"
  },
  {
    "concept": "观测精度",
    "explanation": "确保数据测量的准确性",
    "example": "时间精确到分钟级别"
  },
  {
    "concept": "系统记录",
    "explanation": "按标准格式系统记录数据",
    "example": "使用统一的数据记录表格"
  }
]',
'[
  "观测时间不准确",
  "记录格式不统一",
  "缺乏持续性观测",
  "不注意天气因素影响"
]',
'[
  "准确观测：确保观测时间的准确性",
  "格式统一：使用标准的记录格式",
  "持续进行：坚持长期系统观测",
  "注意环境：考虑天气等环境因素"
]',
'{
  "emphasis": ["实践智慧", "观测精神"],
  "application": ["科学探究", "数据收集"],
  "connection": ["实践美学", "观测艺术"],
  "cultural_heritage": ["格物致知", "实践智慧"]
}',
'{
  "emphasis": ["数据收集", "科学观测"],
  "application": ["实验科学", "数据科学"],
  "connection": ["天文学", "数据科学"],
  "methodology": ["观测方法", "数据收集"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G7S2_PRACTICE_004: 白天时长变化规律的分析
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S2_PRACTICE_004'),
'分析白天时长数据，探究其变化规律的综合实践活动',
'白天时长变化规律的分析是数据分析和科学探究的综合实践，体现了数学在认识自然规律中的重要作用。通过对收集的白天时长数据进行整理、分析和建模，发现白天时长的变化规律，培养学生的数据分析能力和科学思维。这一活动让学生体验完整的科学研究过程，从数据收集到规律发现，理解数学与自然科学的内在联系。中华数学文化中"数理天地"的思想完美体现了用数学方法认识天地运行规律的深邃智慧。',
'[
  "数据整理：系统整理收集的时长数据",
  "图表制作：用统计图展示数据变化趋势",
  "规律探索：发现白天时长的变化规律",
  "成因分析：结合地理知识分析变化原因",
  "建模预测：建立数学模型预测未来变化",
  "交流展示：汇报研究成果和发现"
]',
'[
  {
    "name": "数据整理",
    "formula": "按时间顺序排列，计算各项统计量",
    "description": "对收集的白天时长数据进行系统整理"
  },
  {
    "name": "趋势分析",
    "formula": "制作折线图观察时长变化趋势",
    "description": "用图表直观展示白天时长的变化规律"
  },
  {
    "name": "规律总结",
    "formula": "总结白天时长的周期性变化特点",
    "description": "发现并描述白天时长的变化规律"
  },
  {
    "name": "模型建立",
    "formula": "用数学函数描述白天时长变化",
    "description": "建立数学模型描述变化规律"
  }
]',
'[
  {
    "title": "分析白天时长变化趋势",
    "problem": "根据一个月的白天时长数据，分析其变化趋势并探究变化规律。",
    "solution": "①数据整理：将30天的数据按日期排序，计算平均值、最大值、最小值；②制图分析：制作折线图显示白天时长随时间的变化；③趋势观察：观察数据是递增、递减还是波动变化；④规律总结：总结白天时长变化的周期性特点；⑤成因分析：结合地球公转和季节变化解释规律；⑥建模预测：用数学函数拟合数据并预测未来趋势",
    "analysis": "数据分析要结合图表观察和数量计算，从现象到本质认识规律"
  }
]',
'[
  {
    "concept": "趋势分析",
    "explanation": "观察数据随时间的变化趋势",
    "example": "白天时长是逐渐增长还是减少"
  },
  {
    "concept": "周期性",
    "explanation": "发现数据变化的周期规律",
    "example": "一年中白天时长的周期性变化"
  },
  {
    "concept": "数学建模",
    "explanation": "用数学函数描述变化规律",
    "example": "用正弦函数模拟时长变化"
  }
]',
'[
  "数据分析不够深入",
  "不能发现变化规律",
  "缺乏跨学科思考",
  "模型建立过于简单"
]',
'[
  "深入分析：从多角度分析数据变化",
  "规律探索：善于发现数据中的规律",
  "跨学科思维：结合地理天文知识分析",
  "模型意识：尝试用数学模型描述规律"
]',
'{
  "emphasis": ["规律之美", "天地智慧"],
  "application": ["规律探索", "科学研究"],
  "connection": ["自然美学", "规律艺术"],
  "cultural_heritage": ["数理天地", "规律智慧"]
}',
'{
  "emphasis": ["数据分析", "规律建模"],
  "application": ["数据科学", "数学建模"],
  "connection": ["天文学", "数据科学"],
  "methodology": ["分析方法", "建模技术"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'); 