const app = getApp()
const deviceUtils = require('../../../utils/device-info.js')

Page({
  data: {
    isIPhoneX: false,
    isIPad: false,
    safeAreaBottom: 0,
    username: '',
    password: '',
    rememberPassword: false,
    isLoading: false,
    // 授权相关
    hasUserInfo: false,
    userInfo: {
      name: '',
      grade: '',
      gender: '男' // 默认选中男性
    },
    hasPhoneNumber: false,
    phoneNumber: '',
    // 用户信息录入表单
    showUserInfoForm: false,
    formName: '',
    formGrade: '',
    formGender: 1, // 1-男, 2-女
    gradeOptions: [
      { id: 1, name: '高一' },
      { id: 2, name: '高二' },
      { id: 3, name: '高三' },
      { id: 4, name: '初一' },
      { id: 5, name: '初二' },
      { id: 6, name: '初三' }
    ],
    currentStep: 1, // 1-微信授权, 2-手机号授权, 3-填写信息
    fadeIn: false, // 添加过渡动画控制
    grades: ['小学一年级', '小学二年级', '小学三年级', '小学四年级', '小学五年级', '小学六年级', 
             '初中一年级', '初中二年级', '初中三年级', 
             '高中一年级', '高中二年级', '高中三年级'],
    gradeIndex: -1,
    isSubmitting: false,
    showGradeSelector: false,  // 是否显示年级选择器
    tempGradeIndex: -1,       // 临时选择的年级索引
    showPassword: false,   // 是否显示密码
    passwordType: 'password', // 密码类型根据显示状态切换
  },

  onLoad(options) {
    this.initDeviceInfo()
    
    // 添加延迟显示动画效果
    setTimeout(() => {
      this.setData({
        fadeIn: true
      })
    }, 100)
  },

  onShow() {
    this.initDeviceInfo()
  },

  onResize() {
    // 屏幕旋转时重新获取设备信息
    this.initDeviceInfo()
  },

  initDeviceInfo() {
    // 获取安全区域信息和设备类型
    const safeAreaInfo = deviceUtils.getSafeAreaInfo()
    const isIPad = deviceUtils.isIPad()
    const isIPhoneX = deviceUtils.isIPhoneX()
    const isAndroid = deviceUtils.isAndroid()
    
    this.setData({
      statusBarHeight: safeAreaInfo.statusBarHeight,
      navBarHeight: safeAreaInfo.navBarHeight,
      safeAreaBottom: safeAreaInfo.safeAreaBottom,
      isIPad: isIPad,
      isIPhoneX: isIPhoneX,
      isAndroid: isAndroid
    })
    
    // 设置CSS变量
    deviceUtils.setPageCSSVariables('.container', safeAreaInfo)
  },

  // 输入用户名
  onUsernameInput: function(e) {
    console.log('用户名输入:', e.detail.value);
    this.setData({
      username: e.detail.value
    });
  },

  // 输入密码
  onPasswordInput: function(e) {
    console.log('密码输入:', e.detail.value);
    this.setData({
      password: e.detail.value
    });
  },

  // 切换记住密码
  toggleRememberPassword: function() {
    this.setData({
      rememberPassword: !this.data.rememberPassword
    });
  },

  // 切换显示/隐藏密码
  togglePasswordVisibility: function() {
    this.setData({
      showPassword: !this.data.showPassword
    });
  },

  // 微信授权登录
  wxLogin() {
    this.setData({
      isLoading: true,
      currentStep: 1
    });
    
    // 调用微信接口获取用户信息
    wx.getUserProfile({
      desc: '用于完善用户资料',
      success: (res) => {
        console.log('获取用户信息成功', res);
        
        // 根据微信最新隐私政策，用户可能显示为"微信用户"和默认头像
        let userInfo = res.userInfo;
        
        // 如果头像URL为空，使用默认头像
        if (!userInfo.avatarUrl || userInfo.avatarUrl === '') {
          userInfo.avatarUrl = '/images/default-avatar.png';
        }
        
        this.setData({
          userInfo: userInfo,
          hasUserInfo: true,
          isLoading: false
        });
        
        // 使用动画过渡到下一步
        setTimeout(() => {
          this.setData({
            fadeIn: false
          });
          
          setTimeout(() => {
            this.setData({
              currentStep: 2,
              fadeIn: true
            });
            
            // 进入下一步获取手机号
            wx.showToast({
              title: '授权成功',
              icon: 'success',
              duration: 1000
            });
          }, 300);
        }, 100);
      },
      fail: (err) => {
        console.error('获取用户信息失败', err);
        this.setData({
          isLoading: false
        });
        
        if (err.errMsg && err.errMsg.indexOf('auth deny') > -1) {
          wx.showToast({
            title: '您已拒绝授权',
            icon: 'none',
            duration: 2000
          });
        } else {
          wx.showToast({
            title: '授权失败，请重试',
            icon: 'none',
            duration: 2000
          });
        }
      }
    });
  },

  // 获取手机号
  getPhoneNumber(e) {
    if (e.detail.errMsg !== "getPhoneNumber:ok") {
      if (e.detail.errMsg === "getPhoneNumber:fail auth deny") {
        wx.showToast({
          title: '您已拒绝授权手机号',
          icon: 'none',
          duration: 2000
        });
      } else {
        wx.showToast({
          title: '获取手机号失败',
          icon: 'none',
          duration: 2000
        });
      }
      return;
    }

    this.setData({
      isLoading: true
    });
    
    // 通常需要将code发送到服务端换取手机号，这里简化处理
    wx.showLoading({
      title: '获取手机号中...',
    });
    
    // 模拟服务端解析手机号的过程
    setTimeout(() => {
      wx.hideLoading();
      
      // 假设获取到的手机号
      const phoneNumber = '138****5678';
      
      this.setData({
        phoneNumber: phoneNumber,
        hasPhoneNumber: true,
        isLoading: false
      });
      
      // 使用动画过渡到下一步
      setTimeout(() => {
        this.setData({
          fadeIn: false
        });
        
        setTimeout(() => {
          this.setData({
            currentStep: 3,
            showUserInfoForm: true,
            fadeIn: true
          });
          
          // 如果用户信息中已有年级，设置对应的索引
          if (this.data.userInfo.grade) {
            const gradeIndex = this.data.grades.findIndex(grade => grade === this.data.userInfo.grade);
            if (gradeIndex >= 0) {
              this.setData({
                gradeIndex: gradeIndex
              });
            }
          }
          
          wx.showToast({
            title: '获取成功',
            icon: 'success'
          });
        }, 300);
      }, 1000);
    }, 1000);
  },

  // 跳过手机号授权
  skipPhoneAuth() {
    this.setData({
      fadeIn: false
    });
    
    setTimeout(() => {
      this.setData({
        hasPhoneNumber: true,
        currentStep: 3,
        showUserInfoForm: true,
        fadeIn: true
      });
      
      // 如果用户信息中已有年级，设置对应的索引
      if (this.data.userInfo.grade) {
        const gradeIndex = this.data.grades.findIndex(grade => grade === this.data.userInfo.grade);
        if (gradeIndex >= 0) {
          this.setData({
            gradeIndex: gradeIndex
          });
        }
      }
    }, 300);
  },

  // 显示账号密码登录界面
  showAccountLogin() {
    this.setData({
      fadeIn: false
    });
    
    setTimeout(() => {
      this.setData({
        currentStep: 999,
        fadeIn: true
      });
    }, 300);
  },

  // 设置用户姓名
  onNameInput(e) {
    this.setData({
      formName: e.detail.value
    });
  },
  
  // 选择年级
  onGradeChange(e) {
    const gradeIndex = e.detail.value;
    this.setData({
      formGrade: this.data.gradeOptions[gradeIndex].name
    });
  },
  
  // 设置性别
  onGenderChange(e) {
    this.setData({
      formGender: parseInt(e.detail.value)
    });
  },
  
  // 提交用户信息
  onSubmitUserInfo: function() {
    // 表单验证
    if (!this.data.userInfo.name) {
      wx.showToast({
        title: '请输入姓名',
        icon: 'none'
      });
      return;
    }
    if (!this.data.userInfo.grade) {
      wx.showToast({
        title: '请选择年级',
        icon: 'none'
      });
      return;
    }
    this.setData({
      isSubmitting: true
    });
    
    // 构造完整 userInfo 结构，兼容个人中心展示
    const userInfo = {
      nickName: this.data.userInfo.name,
      nickname: this.data.userInfo.name, // 兼容支持两种字段名
      grade: this.data.userInfo.grade,
      gender: this.data.userInfo.gender,
      schoolName: this.data.userInfo.school || '',
      school: this.data.userInfo.school || '', // 兼容支持两种字段名
      className: this.data.userInfo.className || '',
      avatarUrl: this.data.userInfo.avatarUrl || '/images/default-avatar.png'
    };
    
    console.log('即将存储的用户信息:', userInfo);
    
    setTimeout(() => {
      wx.setStorageSync('userInfo', userInfo);
      wx.setStorageSync('isLoggedIn', true);
      
      // 明确标记全局数据已更新，通知个人中心页面刷新
      app.globalData.userInfoUpdated = true;
      app.globalData.userInfo = {
        ...app.globalData.userInfo,
        ...userInfo,
        isLoggedIn: true
      };
      
      wx.showToast({
        title: '信息提交成功',
        icon: 'success',
        duration: 1500,
        success: () => {
          // 清除页面缓存，确保返回时重新加载
          setTimeout(() => {
            this.setData({ fadeIn: false });
            setTimeout(() => {
              wx.switchTab({ 
                url: '/pages/profile/index',
                success: () => {
                  console.log('成功跳转到个人中心页面');
                }
              });
            }, 300);
          }, 1200);
        }
      });
    }, 800);
  },
  
  // 登录提交
  submitLogin: function() {
    // 保留原函数的调用，确保兼容性
    this.doLogin();
  },

  // 快速登录 - 直接启动微信授权流程
  quickLogin() {
    this.wxLogin();
  },

  // 表单验证
  validateForm() {
    if (!this.data.username.trim()) {
      wx.showToast({
        title: '请输入用户名',
        icon: 'none'
      });
      return false;
    }
    
    if (!this.data.password.trim()) {
      wx.showToast({
        title: '请输入密码',
        icon: 'none'
      });
      return false;
    }
    
    return true;
  },

  // 返回上一页
  goBack() {
    wx.navigateBack();
  },

  // 跳转到注册页面
  goToRegister() {
    wx.showToast({
      title: '注册功能开发中',
      icon: 'none'
    });
  },

  // 跳转到忘记密码页面
  goToForgetPassword() {
    wx.showToast({
      title: '找回密码功能开发中',
      icon: 'none'
    });
  },

  // 根据变量名修改的事件处理函数
  onSelectGender: function(e) {
    const gender = e.currentTarget.dataset.gender;
    this.setData({
      'userInfo.gender': gender
    });
  },

  onSelectGrade: function(e) {
    const index = e.detail.value;
    const grade = this.data.grades[index];
    this.setData({
      gradeIndex: index,
      'userInfo.grade': grade
    });
  },

  onInputName: function(e) {
    this.setData({
      'userInfo.name': e.detail.value
    });
  },

  // 显示年级选择器
  showGradeSelector: function() {
    // 设置临时选择的年级索引为当前选择的年级
    this.setData({
      showGradeSelector: true,
      tempGradeIndex: this.data.gradeIndex >= 0 ? this.data.gradeIndex : -1
    });
  },

  // 隐藏年级选择器
  hideGradeSelector: function() {
    this.setData({
      showGradeSelector: false
    });
  },

  // 阻止事件冒泡
  stopPropagation: function(e) {
    return;
  },

  // 阻止页面滚动
  preventTouchMove: function(e) {
    return;
  },

  // 点击年级项
  onGradeItemClick: function(e) {
    const index = e.currentTarget.dataset.index;
    console.log('点击年级项，索引:', index, '年级:', this.data.grades[index]);
    this.setData({
      tempGradeIndex: index
    });
  },

  // 确认年级选择
  confirmGrade: function() {
    if (this.data.tempGradeIndex >= 0) {
      const grade = this.data.grades[this.data.tempGradeIndex];
      console.log('选择年级:', grade, '索引:', this.data.tempGradeIndex);
      this.setData({
        gradeIndex: this.data.tempGradeIndex,
        'userInfo.grade': grade,
        showGradeSelector: false
      });
      
      // 给用户反馈
      wx.showToast({
        title: `已选择${grade}`,
        icon: 'success',
        duration: 1500
      });
    } else {
      this.hideGradeSelector();
    }
  },

  // 前往忘记密码页面
  goToForgotPassword: function() {
    wx.navigateTo({
      url: '/packageProfile/pages/forgot-password/index',
      fail: function(error) {
        console.error('导航到忘记密码页面失败:', error);
        wx.showToast({
          title: '页面加载失败，请重试',
          icon: 'none'
        });
      }
    });
  },
  
  // 前往注册页面
  goToRegister: function() {
    wx.navigateTo({
      url: '/packageProfile/pages/register/index',
      fail: function(error) {
        console.error('导航到注册页面失败:', error);
        wx.showToast({
          title: '页面加载失败，请重试',
          icon: 'none'
        });
      }
    });
  },
  
  // 提交账号密码登录
  doLogin: function() {
    if (!this.data.username || !this.data.password) {
      wx.showToast({
        title: '请输入用户名和密码',
        icon: 'none'
      })
      return
    }
    
    this.setData({
      isLoading: true
    })
    
    // 模拟登录请求
    setTimeout(() => {
      this.setData({
        isLoading: false
      })
      
      // 构造用户信息
      const userInfo = {
        nickName: this.data.username,
        nickname: this.data.username, // 兼容支持两种字段名
        grade: '初中一年级', // 示例年级
        avatarUrl: '/images/default-avatar.png'
      }
      
      // 保存登录状态
      wx.setStorageSync('isLoggedIn', true)
      wx.setStorageSync('userInfo', userInfo)
      
      // 更新全局用户信息
      if (app.globalData) {
        app.globalData.userInfoUpdated = true
        app.globalData.userInfo = {
          ...app.globalData.userInfo,
          ...userInfo,
          isLoggedIn: true
        }
      }
      
      // 如果选择了记住密码，保存账号密码
      if (this.data.rememberPassword) {
        wx.setStorageSync('savedUsername', this.data.username)
        wx.setStorageSync('savedPassword', this.data.password)
      } else {
        wx.removeStorageSync('savedUsername')
        wx.removeStorageSync('savedPassword')
      }
      
      wx.showToast({
        title: '登录成功',
        icon: 'success',
        duration: 1500
      })
      
      // 延迟返回个人中心页
      setTimeout(() => {
        wx.switchTab({
          url: '/pages/profile/index'
        })
      }, 1500)
    }, 1000)
  },
}) 