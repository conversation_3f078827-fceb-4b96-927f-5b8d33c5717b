<!-- 顶部卡片区域 -->
<view class="profile-header {{isIPad ? 'profile-header--ipad' : ''}}">
  <!-- 未登录状态 -->
  <block wx:if="{{!isLoggedIn}}">
    <view class="profile-card profile-card--guest">
      <view class="login-banner">
        <view class="login-title">数学学习助手</view>
        <view class="login-description">AI智能辅导 · 个性化学习</view>
      </view>
      <view class="login-content">
        <view class="login-avatar">
          <view class="avatar-circle">
            <view class="icon icon-user icon-md"></view>
          </view>
        </view>
        <view class="login-message">登录后使用完整功能</view>
        <button class="login-button" bindtap="onLogin">
          登录
        </button>
      </view>
    </view>
  </block>
  
  <!-- 已登录状态 -->
  <block wx:else>
    <view class="profile-card profile-card--user">
      <view class="profile-card__main">
        <view class="profile-card__avatar-container" bindtap="onEdit">
          <view class="profile-card__avatar">{{nickname ? nickname[0] : 'U'}}</view>
        </view>
        <view class="profile-card__info">
          <view class="profile-card__header">
            <view class="profile-card__name-row">
              <text class="profile-card__name">{{nickname || '用户名'}}</text>
            </view>
            <view class="profile-card__level-row">
              <view class="profile-card__level-tag" bindtap="onLevelTap">{{userLevel}}</view>
              <view class="profile-card__level-progress" bindtap="onLevelTap">
                <view class="profile-card__level-progress-bar" style="width: {{levelProgress || 76}}%;"></view>
              </view>
              <view class="profile-card__level-upgrade" bindtap="onLevelTap">我要升级</view>
            </view>
          </view>
          <view class="profile-card__school-grade-row">
            <view class="profile-card__school">
              <view class="icon icon-school-white icon-xs"></view>
              <text>{{schoolName || '未设置学校'}}</text>
            </view>
            <view class="profile-card__grade">
              <view class="icon icon-class-white icon-xs"></view>
              <text>{{className || '初中一年级'}}</text>
            </view>
          </view>
        </view>
        <view class="profile-card__edit" bindtap="onEdit" wx:if="{{!hideSettings}}">
          <view class="icon icon-settings-white icon-xs"></view>
          <text>设置</text>
        </view>
      </view>
      
      <!-- 学习数据统计 -->
      <view class="profile-stats">
        <view class="profile-stats__item">
          <view class="profile-stats__value">{{solvedCount || 0}}</view>
          <view class="profile-stats__label">已解题</view>
          <view class="profile-stats__progress">
            <view class="profile-stats__progress-bar" style="width: {{solvedCount ? 70 : 0}}%;"></view>
          </view>
        </view>
        <view class="profile-stats__item">
          <view class="profile-stats__value">{{wrongCount || 0}}</view>
          <view class="profile-stats__label">错题数</view>
          <view class="profile-stats__progress">
            <view class="profile-stats__progress-bar profile-stats__progress-bar--wrong" style="width: {{wrongCount ? 50 : 0}}%;"></view>
          </view>
        </view>
        <view class="profile-stats__item">
          <view class="profile-stats__value">{{studyHours || 0}}</view>
          <view class="profile-stats__label">学习时长</view>
          <view class="profile-stats__progress">
            <view class="profile-stats__progress-bar profile-stats__progress-bar--time" style="width: {{studyHours ? 60 : 0}}%;"></view>
          </view>
        </view>
        <view class="profile-stats__item" bindtap="onMoreMedalsTap">
          <view class="profile-stats__value">{{medalCount || 0}}</view>
          <view class="profile-stats__label">勋章数</view>
          <view class="profile-stats__progress">
            <view class="profile-stats__progress-bar profile-stats__progress-bar--medal" style="width: {{medalCount ? 55 : 0}}%;"></view>
          </view>
        </view>
      </view>
      
      <!-- 勋章展示区 -->
      <view class="medals-container" wx:if="{{medals.length > 0}}">
        <scroll-view class="medals-scroll-view" scroll-x enable-flex show-scrollbar="{{false}}">
          <view class="medals-content">
            <block wx:for="{{medals}}" wx:key="id">
              <view class="medal-scroll-item {{item.unlocked ? 'medal-unlocked' : 'medal-locked'}}" data-medal="{{item}}" bindtap="onMedalTap">
                <view class="medal-icon-container">
                  <view class="icon {{item.icon}} icon-md"></view>
                </view>
                <view class="medal-name">{{item.name}}</view>
              </view>
            </block>
            <!-- 添加"更多"按钮 -->
            <view class="medal-scroll-item medal-more-item" bindtap="onMoreMedalsTap">
              <view class="medal-icon-container medal-more-icon">
                <view class="more-icon-circle">
                  <view class="more-icon-plus"></view>
                </view>
              </view>
              <view class="medal-name">更多</view>
            </view>
          </view>
        </scroll-view>
      </view>
    </view>
  </block>
</view> 