/* 引入公共样式 */
@import "../../../styles/common.wxss";
@import "../../../styles/device-adaptation.wxss";

/* 页面容器 */
.knowledge-graph-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f7f8fc;
  overflow: hidden;
  padding-bottom: calc(10rpx + var(--safe-bottom) + 110rpx);
  box-sizing: border-box;
}

/* 固定内容区域 */
.fixed-content {
  flex-shrink: 0;
  background-color: #fff;
  border-bottom: 1rpx solid rgba(238, 238, 238, 0.8);
  z-index: 50;
  margin-bottom: 6rpx;
  box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.05);
}

/* 学段选择 */
.stage-select {
  display: flex;
  padding: 16rpx 32rpx;
  border-bottom: 1rpx solid #f1f1f1;
  background-color: #fff;
  gap: 8rpx;
}

.stage-item {
  flex: 1;
  height: 56rpx;
  line-height: 56rpx;
  text-align: center;
  font-size: 28rpx;
  font-weight: 500;
  color: #666;
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 28rpx;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 1rpx solid rgba(0, 0, 0, 0.06);
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.05);
}

.stage-item.active {
  color: #3E7BFA;
  font-weight: 600;
  background: linear-gradient(135deg, rgba(62, 123, 250, 0.12) 0%, rgba(102, 126, 234, 0.12) 100%);
  box-shadow: 0 4rpx 12rpx rgba(62, 123, 250, 0.25);
  border-color: rgba(62, 123, 250, 0.2);
  transform: translateY(-1rpx);
}

.stage-item:active {
  transform: scale(0.95);
}

/* 年级选择 */
.grade-scroll {
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f1f1f1;
  background-color: #fff;
}

.grade-list {
  display: flex;
  padding: 0 32rpx;
  min-width: max-content;
}

.grade-item {
  min-width: 120rpx;
  height: 56rpx;
  line-height: 56rpx;
  margin-right: 12rpx;
  padding: 0 20rpx;
  text-align: center;
  font-size: 24rpx;
  font-weight: 500;
  color: #666;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 28rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  white-space: nowrap;
  border: 1rpx solid rgba(0, 0, 0, 0.06);
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.05);
}

.grade-item:last-child {
  margin-right: 32rpx;
}

.grade-item.active {
  background: linear-gradient(135deg, #3E7BFA 0%, #667eea 100%);
  color: #fff;
  font-weight: 600;
  box-shadow: 0 4rpx 12rpx rgba(62, 123, 250, 0.4);
  transform: translateY(-1rpx);
  border-color: transparent;
}

.grade-item:active {
  transform: scale(0.95);
}

/* 高中教材选择容器 */
.textbook-select-container {
  background-color: #fff;
  border-bottom: 1rpx solid #f1f1f1;
  padding: 8rpx 0;
}

/* 高中教材选择行样式统一 */
.textbook-select-container .grade-scroll {
  padding: 12rpx 0;
  border-bottom: none;
}

.textbook-select-container .grade-scroll:first-child {
  border-bottom: 1rpx solid rgba(241, 241, 241, 0.8);
  margin-bottom: 4rpx;
}

.textbook-select-container .grade-list {
  display: flex;
  padding: 0 32rpx;
  min-width: max-content;
}

.textbook-select-container .grade-item {
  min-width: 120rpx;
  height: 56rpx;
  line-height: 56rpx;
  margin-right: 12rpx;
  padding: 0 20rpx;
  text-align: center;
  font-size: 24rpx;
  font-weight: 500;
  color: #666;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 28rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  white-space: nowrap;
  border: 1rpx solid rgba(0, 0, 0, 0.06);
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.05);
}

.textbook-select-container .grade-item:last-child {
  margin-right: 32rpx;
}

.textbook-select-container .grade-item.active {
  background: linear-gradient(135deg, #3E7BFA 0%, #667eea 100%);
  color: #fff;
  font-weight: 600;
  box-shadow: 0 4rpx 12rpx rgba(62, 123, 250, 0.4);
  transform: translateY(-1rpx);
  border-color: transparent;
}

.textbook-select-container .grade-item:active {
  transform: scale(0.95);
}





/* 搜索框 */
.search-container {
  position: relative;
  padding: 16rpx 32rpx;
  background-color: #fff;
  z-index: 10;
}

.search-input {
  width: 100%;
  height: 64rpx;
  padding: 0 70rpx 0 32rpx;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 32rpx;
  font-size: 26rpx;
  color: #333;
  box-sizing: border-box;
  border: 1rpx solid rgba(0, 0, 0, 0.06);
  transition: all 0.3s;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.05);
  position: relative;
  z-index: 2;
}

.search-input:focus {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border-color: #3E7BFA;
  box-shadow: 0 0 0 2rpx rgba(62, 123, 250, 0.2);
}

.search-icon {
  position: absolute;
  right: 56rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 28rpx;
  color: #3E7BFA;
  filter: drop-shadow(0 2rpx 4rpx rgba(62, 123, 250, 0.2));
  pointer-events: none;
  z-index: 1;
}

/* 掌握状态筛选 */
.mastery-filter-container {
  background-color: #fff;
  border-bottom: 1rpx solid #f1f1f1;
  padding: 0 0 12rpx 0;
}

.mastery-filter-scroll {
  padding: 8rpx 0;
}

.mastery-filter-list {
  display: flex;
  padding: 0 32rpx;
  min-width: max-content;
  gap: 8rpx;
}

.mastery-filter-item {
  min-width: 80rpx;
  height: 48rpx;
  line-height: 48rpx;
  padding: 0 16rpx;
  text-align: center;
  font-size: 22rpx;
  font-weight: 500;
  color: #666;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 24rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  white-space: nowrap;
  border: 1rpx solid rgba(0, 0, 0, 0.06);
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.05);
  flex-shrink: 0;
}

.mastery-filter-item.active {
  background: linear-gradient(135deg, #3E7BFA 0%, #667eea 100%);
  color: #fff;
  font-weight: 600;
  box-shadow: 0 3rpx 10rpx rgba(62, 123, 250, 0.4);
  transform: translateY(-1rpx);
  border-color: transparent;
}

.mastery-filter-item:active {
  transform: scale(0.95);
}

/* 特殊状态颜色 */
.mastery-filter-item[data-status="not_started"].active {
  background: linear-gradient(135deg, #718096 0%, #4a5568 100%);
  box-shadow: 0 3rpx 10rpx rgba(113, 128, 150, 0.4);
}

.mastery-filter-item[data-status="weak"].active {
  background: linear-gradient(135deg, #d69e2e 0%, #b7791f 100%);
  box-shadow: 0 3rpx 10rpx rgba(214, 158, 46, 0.4);
}

.mastery-filter-item[data-status="learning"].active {
  background: linear-gradient(135deg, #3182ce 0%, #2c5aa0 100%);
  box-shadow: 0 3rpx 10rpx rgba(49, 130, 206, 0.4);
}

.mastery-filter-item[data-status="mastered"].active {
  background: linear-gradient(135deg, #38a169 0%, #2f855a 100%);
  box-shadow: 0 3rpx 10rpx rgba(56, 161, 105, 0.4);
}

/* 可滚动内容区域 */
.scrollable-content {
  flex: 1;
  height: 0;
  background-color: #f7f8fc; /* 统一背景色，与页面容器保持一致 */
}

/* 知识点区域 */
.knowledge-section {
  padding: 0 12rpx; /* 优化左右内边距 */
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 24rpx; /* 减小内边距 */
  margin: 16rpx 0; /* 减小外边距 */
  background-color: #fff; /* 保持背景色 */
  border-radius: 20rpx; /* 保持圆角设计 */
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.06); /* 调整阴影效果 */
  border: 1rpx solid rgba(230, 230, 240, 0.5); /* 保持边框 */
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #3E7BFA;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 32rpx;
  font-size: 28rpx;
  color: #666;
}

/* 章节容器 */
.chapters-container {
  padding: 0; /* 移除内边距，改为使用外边距 */
}

/* 章节标题 */
.chapter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 12rpx 0 8rpx; /* 进一步减小外边距，使布局更紧凑 */
  padding: 16rpx 20rpx; /* 减小内边距 */
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16rpx; /* 恢复原来的圆角 */
  box-shadow: 0 4rpx 20rpx rgba(102, 126, 234, 0.3); /* 恢复原来的阴影 */
  position: relative;
  overflow: hidden;
}

.chapter-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  pointer-events: none;
}

.chapter-title {
  flex: 1;
  position: relative;
  z-index: 1;
}

.chapter-name {
  font-size: 32rpx;
  font-weight: 700;
  color: #fff;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 2rpx;
}

.chapter-count {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.9);
  background-color: rgba(255, 255, 255, 0.2);
  padding: 6rpx 14rpx;
  border-radius: 12rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10rpx);
  position: relative;
  z-index: 1;
}

.chapter-header.special-chapter {
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
}

.chapter-header.special-chapter .chapter-name {
  color: #fff;
}

.chapter-header.special-chapter .chapter-count {
  background-color: rgba(255, 255, 255, 0.25);
  color: rgba(255, 255, 255, 0.95);
}

/* 知识点网格 */
.knowledge-grid-container {
  margin-bottom: 12rpx; /* 进一步减小底部边距 */
  padding: 12rpx; /* 减小内边距，使内容更紧凑 */
  background-color: #fff; /* 保持背景色 */
  border-radius: 16rpx; /* 稍微减小圆角 */
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06); /* 调整阴影效果 */
  border: 1rpx solid rgba(230, 230, 240, 0.5); /* 保持边框 */
}

.knowledge-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(340rpx, 1fr));
  gap: 12rpx; /* 减小间距，使布局更紧凑 */
}

/* 知识点卡片 */
.knowledge-card {
  position: relative;
  background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
  border-radius: 20rpx; /* 更现代的圆角设计 */
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08); /* 提升阴影层次感 */
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  border: 1rpx solid rgba(62, 123, 250, 0.08); /* 使用主题色的淡化版本 */
  backdrop-filter: blur(20rpx);
}

.knowledge-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(90deg, #3E7BFA, #667eea, #764ba2);
  opacity: 0.6;
}

.knowledge-card:active {
  transform: translateY(4rpx) scale(0.97);
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.15);
  border-color: rgba(62, 123, 250, 0.2);
}

.knowledge-card:hover {
  transform: translateY(-4rpx);
  box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.15);
  border-color: rgba(62, 123, 250, 0.15);
}

/* 小节标签 */
.section-badge {
  position: absolute;
  top: 16rpx;
  left: 16rpx;
  background: linear-gradient(135deg, #3E7BFA 0%, #667eea 100%);
  color: #fff;
  padding: 6rpx 12rpx;
  border-radius: 16rpx;
  font-size: 20rpx;
  font-weight: 700;
  line-height: 1.2;
  z-index: 10;
  box-shadow: 0 3rpx 12rpx rgba(62, 123, 250, 0.4);
  max-width: 100rpx;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  backdrop-filter: blur(10rpx);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
}

/* 卡片内容 */
.card-content {
  padding: 20rpx;
  padding-top: 70rpx; /* 为顶部标签预留充足空间 */
  position: relative;
}

.card-name {
  font-size: 32rpx;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 16rpx;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  padding-right: 10rpx; /* 减小右侧间距，标签已移到上方 */
  min-height: 90rpx;
  letter-spacing: 0.5rpx;
}

/* 右上角标签容器 */
.card-badges {
  position: absolute;
  top: 12rpx;
  right: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8rpx;
  z-index: 10;
  max-width: 140rpx;
}

/* 掌握状态标签 */
.mastery-status {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 20rpx;
  font-weight: 600;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
  text-align: center;
  white-space: nowrap;
  min-width: 60rpx;
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

/* 难度标签 */
.difficulty-tag {
  padding: 6rpx 12rpx;
  border-radius: 16rpx;
  font-size: 18rpx;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  min-width: 48rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid rgba(255, 255, 255, 0.5);
}

/* 难度标签配色系统 - 现代小程序风格 */
.difficulty-basic {
  background: linear-gradient(135deg, #e6fffa 0%, #b2f5ea 100%);
  color: #00a88f;
  border: 1rpx solid rgba(0, 168, 143, 0.3);
}

.difficulty-intermediate {
  background: linear-gradient(135deg, #e6f3ff 0%, #bee3f8 100%);
  color: #3182ce;
  border: 1rpx solid rgba(49, 130, 206, 0.3);
}

.difficulty-advanced {
  background: linear-gradient(135deg, #f0fff4 0%, #c6f6d5 100%);
  color: #38a169;
  border: 1rpx solid rgba(56, 161, 105, 0.3);
}

.difficulty-expert {
  background: linear-gradient(135deg, #fffaf0 0%, #fbd38d 100%);
  color: #d69e2e;
  border: 1rpx solid rgba(214, 158, 46, 0.3);
}

.difficulty-master {
  background: linear-gradient(135deg, #fed7d7 0%, #fc8181 100%);
  color: #e53e3e;
  border: 1rpx solid rgba(229, 62, 62, 0.3);
}

/* 掌握状态配色系统 - 现代小程序风格 */
.status-not-started {
  background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
  color: #718096;
  border: 1rpx solid rgba(113, 128, 150, 0.3);
}

.status-weak {
  background: linear-gradient(135deg, #fffaf0 0%, #feebc8 100%);
  color: #d69e2e;
  border: 1rpx solid rgba(214, 158, 46, 0.3);
}

.status-learning {
  background: linear-gradient(135deg, #e6f3ff 0%, #bee3f8 100%);
  color: #3182ce;
  border: 1rpx solid rgba(49, 130, 206, 0.3);
}

.status-mastered {
  background: linear-gradient(135deg, #f0fff4 0%, #c6f6d5 100%);
  color: #38a169;
  border: 1rpx solid rgba(56, 161, 105, 0.3);
}

/* 信息行 */
.card-info-row {
  display: flex;
  flex-wrap: wrap;
  gap: 10rpx;
  margin-bottom: 16rpx;
  padding: 12rpx 0;
  border-top: 1rpx solid rgba(62, 123, 250, 0.1);
}

.importance-level,
.exam-frequency,
.mastery-percentage {
  font-size: 22rpx;
  color: #4a5568;
  background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  border: 1rpx solid rgba(62, 123, 250, 0.15);
  flex-shrink: 0;
  font-weight: 600;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

/* 能力标签 */
.ability-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
  margin-top: 8rpx;
}

.ability-tag {
  font-size: 20rpx;
  color: #3E7BFA;
  background: linear-gradient(135deg, rgba(62, 123, 250, 0.1) 0%, rgba(102, 126, 234, 0.1) 100%);
  padding: 6rpx 14rpx;
  border-radius: 16rpx;
  line-height: 1.3;
  border: 1rpx solid rgba(62, 123, 250, 0.2);
  font-weight: 600;
  box-shadow: 0 2rpx 6rpx rgba(62, 123, 250, 0.1);
  backdrop-filter: blur(10rpx);
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 24rpx; /* 减小内边距 */
  text-align: center;
  background-color: #fff; /* 保持背景色 */
  border-radius: 20rpx; /* 保持圆角设计 */
  margin: 16rpx 0; /* 减小外边距 */
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.06); /* 调整阴影效果 */
  border: 1rpx solid rgba(230, 230, 240, 0.5); /* 保持边框 */
}

.empty-image {
  font-size: 120rpx;
  margin-bottom: 32rpx;
  opacity: 0.7;
  filter: drop-shadow(0 4rpx 8rpx rgba(0, 0, 0, 0.1));
}

.empty-title {
  font-size: 34rpx;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 28rpx;
  color: #6c757d;
  line-height: 1.5;
  margin-bottom: 48rpx;
}

.action-button {
  background: linear-gradient(135deg, #3E7BFA 0%, #667eea 100%);
  color: #fff;
  border: none;
  padding: 18rpx 36rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  box-shadow: 0 4rpx 12rpx rgba(62, 123, 250, 0.3);
}

/* 遮罩层 */
.mask-layer {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s;
}

.mask-layer.visible {
  opacity: 1;
  visibility: visible;
}

/* 详情面板 */
.detail-panel {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100vw;
  height: 85vh;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border-radius: 32rpx 32rpx 0 0;
  z-index: 1001;
  transform: translateY(100%);
  transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  flex-direction: column;
  box-shadow: 0 -8rpx 32rpx rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(20rpx);
}

.detail-panel.visible {
  transform: translateY(0);
}

/* 详情面板头部 */
.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 24rpx 28rpx 16rpx 28rpx;
  border-bottom: 1rpx solid rgba(241, 241, 241, 0.6);
  flex-shrink: 0;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10rpx);
  position: relative;
}

.detail-header::before {
  content: '';
  position: absolute;
  top: 12rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 48rpx;
  height: 4rpx;
  background-color: #e0e0e0;
  border-radius: 2rpx;
}

.detail-title-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding-right: 12rpx;
}

.detail-title {
  font-size: 34rpx;
  font-weight: 700;
  color: #1a1a1a;
  letter-spacing: 0.3rpx;
  line-height: 1.3;
  margin-bottom: 8rpx;
}

.detail-close {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  color: #666;
  background: linear-gradient(135deg, #f5f7fa 0%, #e9ecef 100%);
  border-radius: 24rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.08);
  flex-shrink: 0;
}

/* 详情面板中的难度标签 */
.detail-title-container .difficulty-tag {
  position: static;
  margin-top: 6rpx;
  align-self: flex-start;
  font-size: 20rpx;
  padding: 4rpx 10rpx;
  border-radius: 12rpx;
}

.detail-close:active {
  transform: scale(0.95);
  background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
}

/* 开始学习按钮区域 */
.action-section {
  padding: 16rpx 28rpx;
  border-bottom: 1rpx solid rgba(241, 241, 241, 0.6);
  flex-shrink: 0;
}

.start-learn-button {
  width: 100%;
  height: 68rpx;
  background: linear-gradient(135deg, #3E7BFA 0%, #667eea 100%);
  border-radius: 34rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  box-shadow: 0 4rpx 16rpx rgba(62, 123, 250, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.start-learn-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.start-learn-button:active::before {
  left: 100%;
}

.start-learn-button:active {
  transform: translateY(2rpx) scale(0.98);
  box-shadow: 0 2rpx 12rpx rgba(62, 123, 250, 0.4);
}

.button-icon {
  font-size: 28rpx;
  margin-right: 10rpx;
}

.button-text {
  font-size: 28rpx;
  font-weight: 600;
  color: #ffffff;
  letter-spacing: 0.5rpx;
}

/* 详情内容 */
.detail-content {
  flex: 1;
  height: 0;
  padding: 0 28rpx 20rpx;
}

.detail-section {
  margin-bottom: 24rpx;
  padding: 20rpx;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.4);
}

.section-title {
  font-size: 28rpx;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 16rpx;
  padding-bottom: 8rpx;
  border-bottom: 2rpx solid #3E7BFA;
  display: inline-block;
  position: relative;
  letter-spacing: 0.3rpx;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -2rpx;
  left: 0;
  width: 30%;
  height: 2rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 1rpx;
}

/* 信息网格 */
.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200rpx, 1fr));
  gap: 12rpx;
}

.info-item {
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #f8f9fc 0%, #ffffff 100%);
  padding: 16rpx;
  border-radius: 12rpx;
  border: 1rpx solid rgba(62, 123, 250, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.04);
}

.info-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 3rpx;
  height: 100%;
  background: linear-gradient(135deg, #3E7BFA 0%, #667eea 100%);
}

.info-item:active {
  transform: translateY(-1rpx);
  box-shadow: 0 4rpx 12rpx rgba(62, 123, 250, 0.15);
}

.info-label {
  font-size: 22rpx;
  color: #666;
  margin-bottom: 8rpx;
  font-weight: 500;
  letter-spacing: 0.3rpx;
}

.info-value {
  font-size: 26rpx;
  font-weight: 600;
  color: #1a1a1a;
  letter-spacing: 0.3rpx;
  line-height: 1.3;
}

.status-value {
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
  font-weight: 600;
}

/* 描述内容 */
.desc-content {
  font-size: 26rpx;
  line-height: 1.5;
  color: #333;
  background: linear-gradient(135deg, #f9fafc 0%, #ffffff 100%);
  padding: 16rpx;
  border-radius: 12rpx;
  border: 1rpx solid rgba(0, 0, 0, 0.06);
}

/* 重点内容 */
.key-points {
  background: linear-gradient(135deg, #f9fafc 0%, #ffffff 100%);
  padding: 16rpx;
  border-radius: 12rpx;
  border: 1rpx solid rgba(0, 0, 0, 0.06);
}

.key-point-item {
  font-size: 24rpx;
  line-height: 1.5;
  color: #333;
  margin-bottom: 6rpx;
  position: relative;
  padding-left: 12rpx;
}

.key-point-item::before {
  content: '•';
  position: absolute;
  left: 0;
  color: #3E7BFA;
  font-weight: bold;
}

/* 常见误区 */
.misconceptions {
  background: linear-gradient(135deg, #fff7e6 0%, #fffbf0 100%);
  padding: 16rpx;
  border-radius: 12rpx;
  border: 1rpx solid rgba(250, 140, 22, 0.2);
}

.misconception-item {
  font-size: 24rpx;
  line-height: 1.5;
  color: #fa8c16;
  margin-bottom: 6rpx;
  position: relative;
  padding-left: 12rpx;
}

.misconception-item::before {
  content: '⚠';
  position: absolute;
  left: 0;
  font-size: 20rpx;
}

/* 能力要求 */
.ability-requirements {
  display: flex;
  flex-wrap: wrap;
  gap: 10rpx;
}

.ability-requirements .ability-tag {
  font-size: 22rpx;
  font-weight: 500;
  padding: 6rpx 14rpx;
  border-radius: 20rpx;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  color: #666;
  border: 1rpx solid rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

.ability-requirements .ability-tag.active {
  background: linear-gradient(135deg, #3E7BFA 0%, #667eea 100%);
  color: #fff;
  border-color: transparent;
  box-shadow: 0 2rpx 8rpx rgba(62, 123, 250, 0.3);
}

/* 关系列表 */
.relation-list {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

/* 关系加载状态 */
.relation-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx 20rpx;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border-radius: 12rpx;
  border: 1rpx solid rgba(0, 0, 0, 0.06);
}

.loading-spinner-small {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #3E7BFA;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16rpx;
}

.loading-text-small {
  font-size: 24rpx;
  color: #666;
  text-align: center;
  line-height: 1.4;
}

.relation-item {
  display: flex;
  align-items: center;
  padding: 14rpx 16rpx;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border-radius: 12rpx;
  transition: all 0.3s ease;
  border: 1rpx solid rgba(0, 0, 0, 0.06);
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.04);
}

.relation-item:active {
  background: linear-gradient(135deg, #e6f7ff 0%, #f0f8ff 100%);
  border-color: rgba(62, 123, 250, 0.3);
  transform: translateY(-1rpx);
  box-shadow: 0 4rpx 8rpx rgba(62, 123, 250, 0.15);
}

.relation-name {
  flex: 1;
  font-size: 26rpx;
  font-weight: 500;
  color: #333;
  margin-right: 10rpx;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.relation-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4rpx;
  min-width: 100rpx;
  flex-shrink: 0;
}

.grade-text {
  font-size: 20rpx;
  font-weight: 500;
  color: #3E7BFA;
  background: linear-gradient(135deg, rgba(62, 123, 250, 0.1) 0%, rgba(102, 126, 234, 0.1) 100%);
  padding: 3rpx 8rpx;
  border-radius: 10rpx;
  white-space: nowrap;
  border: 1rpx solid rgba(62, 123, 250, 0.2);
}

.strength-text,
.relation-type {
  font-size: 18rpx;
  color: #999;
  white-space: nowrap;
  font-weight: 500;
}

.relation-arrow {
  margin-left: 8rpx;
  font-size: 20rpx;
  color: #3E7BFA;
  flex-shrink: 0;
  opacity: 0.7;
}

/* 加载动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

.knowledge-card {
  animation: fadeInUp 0.5s ease-out;
}

.chapter-header {
  animation: fadeInUp 0.4s ease-out;
}

.search-icon:active {
  animation: pulse 0.3s ease-in-out;
}

/* 回到顶部按钮 */
.back-to-top-btn {
  position: fixed;
  right: 32rpx;
  bottom: 120rpx;
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #3E7BFA 0%, #667eea 100%);
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 6rpx 20rpx rgba(62, 123, 250, 0.4);
  z-index: 999;
  opacity: 0;
  visibility: hidden;
  transform: scale(0.8) translateY(20rpx);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10rpx);
  border: 2rpx solid rgba(255, 255, 255, 0.2);
}

.back-to-top-btn.show {
  opacity: 1;
  visibility: visible;
  transform: scale(1) translateY(0);
}

.back-to-top-btn:active {
  transform: scale(0.9) translateY(2rpx);
  box-shadow: 0 4rpx 15rpx rgba(62, 123, 250, 0.5);
}

.back-to-top-icon {
  width: 0;
  height: 0;
  border-left: 16rpx solid transparent;
  border-right: 16rpx solid transparent;
  border-bottom: 20rpx solid #fff;
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.2));
}

/* 响应式布局 */
@media (min-width: 768px) {
  .knowledge-grid {
    grid-template-columns: repeat(auto-fill, minmax(300rpx, 1fr));
    gap: 24rpx;
  }
  
  .detail-panel {
    width: 600rpx;
    left: 50%;
    transform: translateX(-50%) translateY(100%);
  }
  
  .detail-panel.visible {
    transform: translateX(-50%) translateY(0);
  }
  
  .chapter-header {
    padding: 28rpx 32rpx;
  }
  
  .card-content {
    padding: 32rpx 28rpx 28rpx;
    padding-top: 64rpx;
  }
  
  .card-name {
    font-size: 34rpx;
  }
  
  .back-to-top-btn {
    right: 60rpx;
    bottom: 100rpx;
    width: 100rpx;
    height: 100rpx;
  }
  
  .back-to-top-icon {
    border-left: 20rpx solid transparent;
    border-right: 20rpx solid transparent;
    border-bottom: 24rpx solid #fff;
  }
} 