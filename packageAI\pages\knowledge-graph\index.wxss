/* 引入公共样式 */
@import "../../../styles/common.wxss";
@import "../../../styles/device-adaptation.wxss";

/* 容器样式 */
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f7f9fc;
  padding-bottom: env(safe-area-inset-bottom);
  position: relative;
  box-sizing: border-box;
}

/* 固定内容区域 */
.fixed-content {
  width: 100%;
  flex-shrink: 0;
  background-color: #fff;
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

/* 可滚动内容区域 */
.scrollable-content {
  flex: 1;
  height: 0;
  width: 100%;
  background-color: #f7f9fc;
}

/* 统一内容板块样式 */
.card-base {
  margin: 24rpx 32rpx;
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.06);
  overflow: hidden;
}

/* 设备适配样式 */
.ipad-mode .stats-container,
.ipad-mode .stage-selector,
.ipad-mode .grade-selector,
.ipad-mode .graph-container,
.ipad-mode .card-base {
  max-width: 750rpx;
  margin-left: auto;
  margin-right: auto;
  width: calc(100% - 64rpx);
}

.iphonex-mode {
  padding-bottom: env(safe-area-inset-bottom);
}

.iphonex-mode .nav-header {
  padding-top: env(safe-area-inset-top);
}

/* 导航栏样式 */
.nav-header {
  display: flex;
  align-items: center;
  width: 100%;
  min-height: 44px;
  background-color: #fff;
  border-bottom: 1rpx solid #eee;
  position: relative;
  z-index: 20;
  box-sizing: border-box;
}

.nav-back {
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding-left: 8px;
}

.nav-title {
  flex: 1;
  font-size: 16px;
  font-weight: 500;
  text-align: center;
  color: #333;
  margin-right: 44px; /* 为了标题居中，右侧留出与左侧按钮相同宽度 */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 学段与年级选择区 */
.selector-area {
  background: #fff;
  width: 100%;
  box-sizing: border-box;
  padding: 0;
  margin: 0;
  box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.03);
}

/* 学段选择样式 */
.stage-select {
  display: flex;
  width: 100%;
  background-color: #fff;
  padding: 8rpx 12rpx;
  box-sizing: border-box;
}

.stage-item {
  flex: 1;
  height: 64rpx;
  line-height: 64rpx;
  text-align: center;
  font-size: 28rpx;
  color: #666;
  position: relative;
  transition: all 0.2s;
}

.stage-item.active {
  color: #3E7BFA;
  font-weight: 500;
}

.stage-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 20rpx;
  height: 3rpx;
  background-color: #3E7BFA;
  border-radius: 2rpx;
}

/* 年级选择样式 */
.grade-scroll {
  background-color: #fff;
  padding: 8rpx 0;
  white-space: nowrap;
  overflow-x: auto;
  width: 100%;
  box-sizing: border-box;
  border-bottom: 1rpx solid #f1f1f1;
}

.grade-list {
  display: inline-flex;
  padding: 0 12rpx;
}

.grade-item {
  min-width: 160rpx;
  height: 56rpx;
  line-height: 56rpx;
  margin-right: 12rpx;
  padding: 0 16rpx;
  text-align: center;
  font-size: 24rpx;
  color: #666;
  background-color: #f5f7fa;
  border-radius: 28rpx;
  box-sizing: border-box;
  display: inline-block;
  white-space: nowrap;
  overflow: visible;
}

.grade-item.active {
  background-color: #3E7BFA;
  color: #fff;
}

/* 知识点统计样式 */
.status-panel {
  display: flex;
  padding: 8rpx 12rpx;
  margin: 8rpx 12rpx;
  background-color: #fff;
  border-radius: 8rpx;
  box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.05);
}

.status-item {
  flex: 1;
  text-align: center;
  padding: 8rpx 0;
  position: relative;
  height: 88rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  box-sizing: border-box;
}

.status-item.active {
  background-color: rgba(245, 247, 250, 0.8);
  border-radius: 4rpx;
}

.status-label {
  font-size: 22rpx;
  margin-bottom: 6rpx;
  line-height: 1.2;
}

.status-count {
  font-size: 28rpx;
  font-weight: bold;
  line-height: 1.2;
}

/* 状态颜色 - 增加优先级 */
.status-panel .status-item .mastered-color { color: #52c41a !important; }
.status-panel .status-item .learning-color { color: #1890ff !important; }
.status-panel .status-item .weak-color { color: #fa8c16 !important; }
.status-panel .status-item .not-mastered-color { color: #f5222d !important; }
.status-panel .status-item .not-started-color { color: #999 !important; }

/* 图谱容器样式 */
.graph-container {
  flex: 1;
  position: relative;
  overflow: hidden;
  min-height: 400rpx;
  margin: 28rpx 24rpx 32rpx;
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.06);
  padding: 28rpx;
  z-index: 5;
}

/* 标题栏样式 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 32rpx;
  border-bottom: 1rpx solid #f1f1f1;
}

.header-title {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.title-icon {
  margin-right: 12rpx;
  display: flex;
  align-items: center;
}

.header-actions {
  display: flex;
  align-items: center;
}

.sort-btn {
  display: flex;
  align-items: center;
  padding: 8rpx 12rpx;
  font-size: 24rpx;
  color: #666;
  background-color: #f5f7fa;
  border-radius: 28rpx;
}

.sort-btn .icon {
  margin-right: 8rpx;
}

/* 知识点列表样式 */
.knowledge-section {
  padding: 24rpx 32rpx;
}

.knowledge-grid-container {
  margin-top: 12rpx;
  padding: 16rpx 0; /* 为小节标签留出垂直空间 */
}

.knowledge-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300rpx, 1fr));
  gap: 32rpx 24rpx; /* 增加垂直间距 */
}

/* 知识点卡片样式 */
.knowledge-card {
  position: relative;
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
  overflow: visible; /* 改为可见，让小节标签能显示 */
  display: flex;
  flex-direction: column;
  transition: all 0.3s;
}

.knowledge-card:active {
  transform: scale(0.98);
  box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.05);
}

.card-status-indicator {
  position: absolute;
  top: 0;
  left: 0;
  width: 8rpx;
  height: 100%;
}

/* 状态指示器颜色 */
.status-mastered { background-color: #52c41a; }
.status-learning { background-color: #1890ff; }
.status-weak { background-color: #fa8c16; }
.status-not-mastered { background-color: #f5222d; }
.status-not-started { background-color: #999; }

/* 状态文本颜色 */
.status-mastered-text { 
  color: #52c41a; 
  font-weight: 500;
  font-size: 22rpx;
  padding: 4rpx 12rpx;
  background-color: rgba(82, 196, 26, 0.1);
  border-radius: 8rpx;
}
.status-learning-text { 
  color: #1890ff; 
  font-weight: 500;
  font-size: 22rpx;
  padding: 4rpx 12rpx;
  background-color: rgba(24, 144, 255, 0.1);
  border-radius: 8rpx;
}
.status-weak-text { 
  color: #fa8c16; 
  font-weight: 500;
  font-size: 22rpx;
  padding: 4rpx 12rpx;
  background-color: rgba(250, 140, 22, 0.1);
  border-radius: 8rpx;
}
.status-not-mastered-text { 
  color: #f5222d; 
  font-weight: 500;
  font-size: 22rpx;
  padding: 4rpx 12rpx;
  background-color: rgba(245, 34, 45, 0.1);
  border-radius: 8rpx;
}
.status-not-started-text { 
  color: #999; 
  font-weight: 500;
  font-size: 22rpx;
  padding: 4rpx 12rpx;
  background-color: rgba(153, 153, 153, 0.1);
  border-radius: 8rpx;
}

/* 卡片内容样式 */
.card-content {
  flex: 1;
  padding: 40rpx 24rpx 24rpx 32rpx; /* 增加top padding为小节标签留空间 */
  display: flex;
  flex-direction: column;
  position: relative;
}

.card-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 16rpx;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  padding-right: 80rpx; /* 为右上角标签预留空间 */
}

.card-info-row {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-bottom: 20rpx;
}

/* 难度标签 */
.difficulty-tag {
  padding: 4rpx 12rpx;
  font-size: 20rpx;
  border-radius: 12rpx;
  color: #fff;
  display: inline-block;
}

/* 右上角难度标签 */
.difficulty-corner {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  z-index: 2;
}

.difficulty-easy { background-color: #52c41a; }
.difficulty-medium { background-color: #1890ff; }
.difficulty-hard { background-color: #fa8c16; }
.difficulty-expert { background-color: #f5222d; }

.card-status {
  font-size: 20rpx;
  display: inline-block;
}

/* 卡片操作按钮 */
.card-actions {
  margin-top: auto;
}

.action-btn {
  height: 56rpx;
  line-height: 56rpx;
  text-align: center;
  font-size: 24rpx;
  color: #fff;
  border-radius: 28rpx;
  padding: 0 24rpx;
  display: inline-block;
}

.full-width {
  width: 100%;
}

/* 按钮颜色 */
.btn-mastered { background-color: #52c41a; }
.btn-learning { background-color: #1890ff; }
.btn-weak { background-color: #fa8c16; }
.btn-not-mastered { background-color: #f5222d; }
.btn-not-started { background-color: #3E7BFA; }

/* 加载状态 */
.loading-container {
  padding: 60rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #e6e6e6;
  border-top: 4rpx solid #3E7BFA;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 20rpx;
  font-size: 26rpx;
  color: #999;
}

/* 空状态样式 */
.empty-state {
  padding: 80rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.empty-image {
  margin-bottom: 32rpx;
}

.empty-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 12rpx;
}

.empty-desc {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 32rpx;
}

.empty-action {
  margin-top: 32rpx;
}

.action-button {
  min-width: 200rpx;
  height: 72rpx;
  line-height: 72rpx;
  font-size: 28rpx;
  color: #fff;
  background-color: #3E7BFA;
  border-radius: 36rpx;
  padding: 0 32rpx;
  border: none;
}

button.action-button::after {
  border: none;
}

/* 遮罩层样式 */
.mask-layer {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 900;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s;
}

.mask-layer.visible {
  opacity: 1;
  visibility: visible;
}

/* 详情面板样式 */
.detail-panel {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: #fff;
  border-radius: 24rpx 24rpx 0 0;
  z-index: 1000;
  transform: translateY(100%);
  transition: transform 0.3s;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  padding-bottom: env(safe-area-inset-bottom);
}

.detail-panel.visible {
  transform: translateY(0);
}

.detail-header {
  padding: 24rpx 32rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid #f1f1f1;
}

.detail-title-container {
  display: flex;
  align-items: center;
  gap: 16rpx;
  flex: 1;
  padding-right: 44px;
}

.detail-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
  min-width: 0;
}

.detail-close {
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  right: 0;
  top: 24rpx;
}

.detail-content {
  flex: 1;
  overflow-y: auto;
  padding: 24rpx 32rpx;
}

/* 描述区域样式 */
.detail-desc-section {
  margin-bottom: 40rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 16rpx;
}

.desc-content {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  text-align: justify;
}

/* 关系区域样式 */
.relation-section {
  margin-bottom: 32rpx;
}

.relation-title {
  font-size: 28rpx;
  font-weight: 500;
  margin-bottom: 16rpx;
  display: flex;
  align-items: center;
}

.prerequisite-title { color: #1890ff; }
.progression-title { color: #52c41a; }
.correlation-title { color: #fa8c16; }
.application-title { color: #722ed1; }
.reinforcement-title { color: #1890ff; }
.analogy-title { color: #eb2f96; }
.contrast-title { color: #faad14; }
.integration-title { color: #13c2c2; }
.derivation-title { color: #eb2f96; }
.abstraction-title { color: #722ed1; }
.specialization-title { color: #fa8c16; }
.generalization-title { color: #52c41a; }
.parallel-title { color: #1890ff; }
.reverse-title { color: #fa8c16; }
.foundation-title { color: #722ed1; }
.cognitive_development-title { color: #eb2f96; }
.extends-title { color: #13c2c2; }
.others-title { color: #666; }

.relation-type {
  font-size: 22rpx;
  color: #666;
  background-color: #f5f5f5;
  padding: 2rpx 8rpx;
  border-radius: 4rpx;
  margin-right: 8rpx;
  display: inline-block;
}

.relation-hint {
  font-size: 22rpx;
  color: #999;
  margin-bottom: 16rpx;
}

.relation-list {
  background-color: #f9f9f9;
  border-radius: 12rpx;
  overflow: hidden;
}

.relation-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 24rpx;
  border-bottom: 1rpx solid #f1f1f1;
  min-height: 60rpx;
}

.relation-item:last-child {
  border-bottom: none;
}

.relation-name {
  flex: 1;
  font-size: 26rpx;
  color: #333;
  margin-right: 16rpx;
  line-height: 1.4;
}

.relation-right {
  display: flex;
  align-items: center;
  gap: 12rpx;
  flex-shrink: 0;
}

.relation-info {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.relation-strength {
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  white-space: nowrap;
  line-height: 1;
}

.status-text {
  font-size: 20rpx;
  font-weight: 500;
  white-space: nowrap;
  line-height: 1;
}

.strength-strong, .strength-0.9 { background-color: #f6ffed; color: #52c41a; }
.strength-medium, .strength-0.7 { background-color: #e6f7ff; color: #1890ff; }
.strength-weak, .strength-0.5 { background-color: #fff7e6; color: #fa8c16; }

.detail-actions {
  padding: 24rpx 32rpx;
  border-top: 1rpx solid #f1f1f1;
}

.learn-button {
  width: 100%;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 28rpx;
  border-radius: 40rpx;
}

/* 主题色样式 - 根据知识点状态 */
.theme-mastered .detail-header { border-color: rgba(82, 196, 26, 0.2); }
.theme-learning .detail-header { border-color: rgba(24, 144, 255, 0.2); }
.theme-weak .detail-header { border-color: rgba(250, 140, 22, 0.2); }
.theme-not-mastered .detail-header { border-color: rgba(245, 34, 45, 0.2); }

/* 详情面板中的状态文本样式 - 与首页保持一致 */
.detail-panel .status-text.status-mastered-text { 
  color: #52c41a; 
  font-weight: 500;
  font-size: 20rpx;
  padding: 2rpx 8rpx;
  background-color: rgba(82, 196, 26, 0.1);
  border-radius: 6rpx;
}
.detail-panel .status-text.status-learning-text { 
  color: #1890ff; 
  font-weight: 500;
  font-size: 20rpx;
  padding: 2rpx 8rpx;
  background-color: rgba(24, 144, 255, 0.1);
  border-radius: 6rpx;
}
.detail-panel .status-text.status-weak-text { 
  color: #fa8c16; 
  font-weight: 500;
  font-size: 20rpx;
  padding: 2rpx 8rpx;
  background-color: rgba(250, 140, 22, 0.1);
  border-radius: 6rpx;
}
.detail-panel .status-text.status-not-mastered-text { 
  color: #f5222d; 
  font-weight: 500;
  font-size: 20rpx;
  padding: 2rpx 8rpx;
  background-color: rgba(245, 34, 45, 0.1);
  border-radius: 6rpx;
}
.detail-panel .status-text.status-not-started-text { 
  color: #999; 
  font-weight: 500;
  font-size: 20rpx;
  padding: 2rpx 8rpx;
  background-color: rgba(153, 153, 153, 0.1);
  border-radius: 6rpx;
}

/* 跨年级节点样式 */
.relation-item.external-node {
  background-color: rgba(24, 144, 255, 0.05);
  border-left: 4rpx solid #1890ff;
}

.grade-text {
  color: #1890ff;
  font-size: 20rpx;
  background-color: rgba(24, 144, 255, 0.1);
  padding: 2rpx 8rpx;
  border-radius: 6rpx;
  white-space: nowrap;
  line-height: 1;
}

.relation-info {
  display: flex;
  align-items: center;
  gap: 8rpx;
  flex-wrap: wrap;
  justify-content: flex-end;
}

/* 虚拟节点样式 */
.relation-item.virtual-node {
  background-color: rgba(245, 108, 108, 0.05);
  border-left: 4rpx solid #F56C6C;
}

.virtual-tag {
  font-size: 20rpx;
  color: #F56C6C;
  background-color: rgba(245, 108, 108, 0.1);
  padding: 2rpx 8rpx;
  border-radius: 6rpx;
  white-space: nowrap;
  line-height: 1;
}

/* 章节容器样式 */
.chapters-container {
  padding: 0 0 32rpx 0;
}

/* 章节标题样式 */
.chapter-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 32rpx 16rpx 32rpx;
  margin-top: 16rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  position: relative;
  overflow: hidden;
}

.chapter-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10rpx);
  z-index: 0;
}

.chapter-title {
  display: flex;
  align-items: center;
  gap: 12rpx;
  z-index: 1;
  position: relative;
}

.chapter-number {
  font-size: 32rpx;
  font-weight: 600;
  color: #fff;
  padding: 8rpx 16rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 20rpx;
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  min-width: 80rpx;
  text-align: center;
}

.chapter-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #fff;
  text-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.3);
}

.chapter-count {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  background: rgba(255, 255, 255, 0.15);
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  backdrop-filter: blur(5rpx);
  z-index: 1;
  position: relative;
}

/* 知识点卡片中的小节信息样式 */
.card-section-info {
  display: flex;
  align-items: center;
  gap: 8rpx;
  margin-bottom: 8rpx;
  font-size: 22rpx;
}

.section-number {
  color: #3E7BFA;
  font-weight: 600;
  background-color: rgba(62, 123, 250, 0.1);
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  font-size: 20rpx;
  line-height: 1;
  min-width: 36rpx;
  text-align: center;
}

.section-title {
  color: #666;
  font-size: 22rpx;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 优化章节间距 */
.chapters-container .knowledge-grid-container {
  margin-top: 0;
  padding-bottom: 24rpx;
}

/* 章节标题的渐变效果变化 */
.chapter-header:nth-child(5n+1) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.chapter-header:nth-child(5n+2) {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.chapter-header:nth-child(5n+3) {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.chapter-header:nth-child(5n+4) {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.chapter-header:nth-child(5n+5) {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

/* 特殊章节样式（章节号为0的综合实践等） */
.chapter-header.special-chapter {
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%) !important;
  border-left: 6rpx solid #ff6b9d;
}

.chapter-header.special-chapter .chapter-name {
  font-style: italic;
  color: #ff6b9d;
  text-shadow: 0 1rpx 2rpx rgba(255, 107, 157, 0.3);
}

.chapter-header.special-chapter .chapter-count {
  background: rgba(255, 107, 157, 0.2);
  color: #ff6b9d;
  border: 1rpx solid rgba(255, 107, 157, 0.3);
}

/* 完整标题样式 */
.chapter-name.full-title {
  font-size: 30rpx;
  font-weight: 600;
  line-height: 1.3;
}

/* 小节号标签样式 */
.section-badge {
  position: absolute;
  top: -8rpx; /* 稍微向上偏移，确保可见 */
  left: 16rpx;
  background: linear-gradient(135deg, #4FC3F7 0%, #29B6F6 100%);
  color: white;
  font-size: 20rpx;
  font-weight: 600;
  padding: 8rpx 12rpx;
  border-radius: 12rpx;
  line-height: 1;
  z-index: 20; /* 提高层级 */
  box-shadow: 0 2rpx 8rpx rgba(79, 195, 247, 0.3);
  min-width: 32rpx;
  text-align: center;
} 