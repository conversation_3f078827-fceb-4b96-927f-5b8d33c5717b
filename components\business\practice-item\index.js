Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 练习题数据对象，包含标题、难度、标签、预计时间等信息
    item: {
      type: Object,
      value: {},
      observer: function(newVal) {
        if (newVal) {
          this._processItemData(newVal);
        }
      }
    },
    // 是否显示序号，默认显示
    showIndex: {
      type: Boolean,
      value: false
    },
    // 练习项的序号，从0开始
    index: {
      type: Number,
      value: 0
    },
    title: {
      type: String,
      value: ''
    },
    type: {
      type: String,
      value: 'practice' // practice, homework, test
    },
    difficulty: {
      type: Number,
      value: 1
    },
    maxDifficulty: {
      type: Number,
      value: 5
    },
    tags: {
      type: Array,
      value: []
    },
    description: {
      type: String,
      value: ''
    },
    questionCount: {
      type: Number,
      value: 0
    },
    userCount: {
      type: Number,
      value: 0
    },
    correctRate: {
      type: Number,
      value: 0
    },
    btnText: {
      type: String,
      value: '开始练习'
    },
    showButton: {
      type: <PERSON>olean,
      value: true
    },
    // 组件类型：practice(练习), homework(作业), quiz(测验), test(测试)
    iconType: {
      type: String,
      value: ''
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    stars: [1, 2, 3, 4, 5],
    iconMap: {
      practice: 'icon-practice',
      homework: 'icon-homework',
      exam: 'icon-exam',
      quiz: 'icon-quiz'
    },
    titleMap: {
      'practice': '练习',
      'homework': '作业',
      'test': '测试'
    },
    typeIcon: 'icon-practice',
    progressPercent: 0,
    completionPercentage: 0,
    // 难度等级对应的星星数
    difficultyMap: {
      'easy': 1,
      'medium': 3,
      'hard': 5
    },
    // 进度条宽度百分比
    progressWidth: '0%',
    // 图标映射
    iconMap: {
      'practice': 'icon-practice',
      'homework': 'icon-homework',
      'quiz': 'icon-quiz',
      'test': 'icon-test'
    },
    // 标题映射
    titleMap: {
      'practice': '练习',
      'homework': '作业',
      'quiz': '测验',
      'test': '测试'
    }
  },

  lifetimes: {
    attached: function() {
      if (this.properties.item) {
        this._processItemData(this.properties.item);
      }
      this.getProgressPercent();
      this.updateProgressWidth();
    }
  },

  observers: {
    'difficulty, maxDifficulty': function(difficulty, maxDifficulty) {
      this._initStars();
    },
    'type': function(type) {
      this._initTypeIcon();
    },
    'item': function(item) {
      if (item) {
        this.getProgressPercent();
        this.setData({
          stars: this.generateStars(item.difficulty || 0),
          completionPercentage: this.getCompletionPercentage(item)
        });
      }
    },
    'item.completion': function() {
      this.updateProgressWidth();
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    _initStars() {
      const { difficulty, maxDifficulty } = this.properties;
      const stars = [];
      
      for (let i = 0; i < maxDifficulty; i++) {
        stars.push({
          active: i < difficulty
        });
      }
      
      this.setData({ stars });
    },

    _initTypeIcon() {
      const typeMap = {
        'default': 'icon-practice',
        'exercise': 'icon-exercise',
        'test': 'icon-test',
        'quiz': 'icon-quiz',
        'challenge': 'icon-challenge',
        'review': 'icon-review'
      };
      
      const typeIcon = typeMap[this.properties.type] || 'icon-practice';
      this.setData({ typeIcon });
    },

    _processItemData(item) {
      let typeIcon = 'icon-practice';
      if (item.type === 'exam') {
        typeIcon = 'icon-exam';
      } else if (item.type === 'quiz') {
        typeIcon = 'icon-quiz';
      } else if (item.type === 'homework') {
        typeIcon = 'icon-homework';
      }

      const stars = [];
      const difficulty = item.difficulty || 0;
      for (let i = 0; i < 5; i++) {
        stars.push(i < difficulty);
      }

      let btnText = '开始练习';
      if (item.status === 'completed') {
        btnText = '再练一次';
      } else if (item.status === 'ongoing') {
        btnText = '继续练习';
      }

      this.setData({
        title: item.title || '',
        description: item.description || '',
        typeIcon: typeIcon,
        tags: item.tags || [],
        stars: stars,
        questionCount: item.questionCount || 0,
        userCount: item.userCount || 0,
        correctRate: item.correctRate || 0,
        btnText: btnText
      });
    },

    /**
     * 练习项点击事件
     */
    onPracticeTap(e) {
      // 阻止事件冒泡
      e.stopPropagation();
      
      // 获取当前练习项
      const { item } = this.properties;
      
      // 触发自定义事件，将练习项数据传递给父组件
      this.triggerEvent('itemClick', { 
        item: item,
        index: this.properties.index
      });
    },

    onTap(e) {
      e.stopPropagation();
      this.triggerEvent('tap', { item: this.properties.item });
    },

    getProgressPercent() {
      const { item } = this.properties;
      if (!item || !item.stats) return 0;
      
      const { finishedCount, totalCount } = item.stats;
      if (!totalCount) return 0;
      
      const progressPercent = Math.min(100, Math.floor((finishedCount / totalCount) * 100));
      this.setData({ progressPercent });
      return progressPercent;
    },

    formatNumber(num) {
      if (num === undefined || num === null) return '0';
      if (num < 1000) return String(num);
      if (num < 10000) return (num / 1000).toFixed(1) + 'k';
      return (num / 10000).toFixed(1) + 'w';
    },

    // 点击整个练习项
    onTapItem() {
      this.triggerEvent('tap', { item: this.properties.item });
    },
    
    // 点击练习按钮
    onTapPractice(e) {
      e.stopPropagation();
      this.triggerEvent('practice', { item: this.properties.item });
    },

    // 获取完成百分比
    getCompletionPercentage(item) {
      if (!item || !item.statistics) return 0;
      const { completed, total } = item.statistics;
      if (!total) return 0;
      return Math.min(Math.round((completed / total) * 100), 100);
    },

    // 生成星级评分数组
    generateStars(difficulty) {
      if (!difficulty) return new Array(5).fill(false);
      const level = Math.min(Math.max(difficulty, 1), 5);
      return new Array(5).fill(false).map((_, index) => index < level);
    },

    // 更新进度条宽度
    updateProgressWidth() {
      const completion = this.properties.item.completion || 0;
      this.setData({
        progressWidth: `${completion}%`
      });
    },

    // 获取图标类名
    getIconClass() {
      const type = this.properties.type;
      if (type === 'practice') return 'icon-practice icon-md';
      if (type === 'homework') return 'icon-homework icon-md';
      if (type === 'test') return 'icon-test icon-md';
      return 'icon-practice icon-md';
    },

    // 获取难度星星数组
    getDifficultyStars() {
      const { item } = this.properties;
      const { difficultyMap } = this.data;
      
      // 默认难度为中等
      const difficulty = item.difficulty || 'medium';
      const starCount = difficultyMap[difficulty] || 3;
      
      // 创建星星数组，总共5颗星
      const stars = Array(5).fill(false).map((_, index) => index < starCount);
      
      return stars;
    },

    // 格式化完成度
    formatCompletion() {
      const completion = this.properties.item.completion || 0;
      return `${completion}%`;
    },

    // 格式化时长，将分钟转为小时+分钟
    formatDuration() {
      const duration = this.properties.item.duration || 0;
      if (duration >= 60) {
        return `${Math.floor(duration / 60)}小时${duration % 60 > 0 ? duration % 60 + '分钟' : ''}`;
      }
      return `${duration}分钟`;
    },

    // 点击整个练习项
    onTapItem() {
      this.triggerEvent('tap', { item: this.properties.item });
    },

    // 点击练习按钮
    onTapPractice(e) {
      e.stopPropagation();
      this.triggerEvent('practice', { item: this.properties.item });
    }
  }
}) 