const cloud = require('wx-server-sdk');
const { Pool } = require('pg');

// 初始化云环境
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

// PostgreSQL连接池配置 - 使用环境变量
const pool = new Pool({
  host: process.env.host || '**************',
  port: parseInt(process.env.port) || 5434,
  database: process.env.db || 'k12',
  user: process.env.name || 'postgres',
  password: process.env.password || 'difyai123456',
  max: 10,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
  ssl: false
});

// 云函数入口函数
exports.main = async (event, context) => {
  console.log('==== 云函数开始执行 ====');
  console.log('接收到的事件数据:', JSON.stringify(event, null, 2));
  console.log('执行上下文:', {
    requestId: context.requestId,
    memory: context.memory_limit_in_mb,
    timeLimit: context.time_limit_in_ms
  });
  
  try {
    const { action, params = {} } = event;
    
    console.log('解析的操作类型:', action);
    console.log('解析的参数:', JSON.stringify(params, null, 2));
    
    if (!action) {
      throw new Error('缺少操作类型(action)参数');
    }
    
    let result;
    
    console.log(`开始执行操作: ${action}`);
    
    switch (action) {
      case 'getKnowledgeNodes':
        console.log('执行getKnowledgeNodes操作');
        result = await getKnowledgeNodes(pool, params);
        break;
      case 'getKnowledgeRelationships':
        console.log('执行getKnowledgeRelationships操作');
        result = await getKnowledgeRelationships(pool, params);
        break;
      case 'getStudentMastery':
        console.log('执行getStudentMastery操作');
        result = await getStudentMastery(pool, params);
        break;
      case 'getKnowledgeContent':
        console.log('执行getKnowledgeContent操作');
        result = await getKnowledgeContent(pool, params);
        break;
      case 'getLearningPath':
        console.log('执行getLearningPath操作');
        result = await getLearningPath(pool, params);
        break;
      case 'searchKnowledge':
        console.log('执行searchKnowledge操作');
        result = await searchKnowledge(pool, params);
        break;
      case 'getKnowledgeByGrade':
        console.log('执行getKnowledgeByGrade操作');
        result = await getKnowledgeByGrade(pool, params);
        break;
      case 'getPrerequisites':
        console.log('执行getPrerequisites操作');
        result = await getPrerequisites(pool, params);
        break;
      case 'updateMasteryStatus':
        console.log('执行updateMasteryStatus操作');
        result = await updateMasteryStatus(pool, params);
        break;
      case 'getRecommendations':
        console.log('执行getRecommendations操作');
        result = await getRecommendations(pool, params);
        break;
      default:
        throw new Error(`未知的操作类型: ${action}`);
    }
    
    console.log(`操作 ${action} 执行成功，结果数据类型:`, typeof result);
    console.log(`操作 ${action} 返回数据条数:`, Array.isArray(result) ? result.length : '非数组数据');
    
    const response = {
      success: true,
      data: result,
      timestamp: new Date().toISOString()
    };
    
    console.log('==== 云函数执行成功 ====');
    return response;
    
  } catch (error) {
    console.error('==== 云函数执行错误 ====');
    console.error('错误类型:', error.constructor.name);
    console.error('错误消息:', error.message);
    console.error('错误堆栈:', error.stack);
    
    const errorResponse = {
      success: false,
      error: error.message,
      errorType: error.constructor.name,
      timestamp: new Date().toISOString()
    };
    
    console.log('返回的错误响应:', JSON.stringify(errorResponse, null, 2));
    return errorResponse;
  }
};

/**
 * 获取知识点列表
 * @param {Pool} pool 数据库连接池
 * @param {Object} params 查询参数
 */
async function getKnowledgeNodes(pool, params) {
  const {
    subject,
    gradeLevel,
    difficulty,
    academicTrack,
    limit = 50,
    offset = 0,
    searchKeyword,
    nodeIds  // 新增：支持批量查询指定的节点ID列表
  } = params;
  
  console.log('查询知识点列表参数:', params);
  
  let query = `
    SELECT 
      kn.id,
      kn.node_code,
      kn.node_name,
      
      -- 学科分类
      kn.subject,
      kn.domain_id,
      kn.textbook_edition_id,
      kn.grade_level,
      kn.semester,
      
      -- 文理科适用性
      kn.knowledge_applicability,
      kn.target_academic_tracks,
      
      -- 教材定位
      kn.chapter_number,
      kn.section_number,
      kn.chapter_title,
      kn.section_title,
      kn.unit_name,
      
      -- 学习属性
      kn.difficulty,
      kn.estimated_time_minutes,
      kn.cognitive_complexity,
      kn.importance_level,
      kn.exam_frequency,
      
      -- 文理科差异化属性
      kn.liberal_arts_difficulty,
      kn.science_difficulty,
      kn.liberal_arts_emphasis_points,
      kn.science_emphasis_points,
      
      -- 知识点状态
      kn.knowledge_type,
      kn.memory_tips,
      kn.common_misconceptions,
      
      -- 教学大纲和考试相关
      kn.curriculum_standard,
      kn.exam_importance_level,
      kn.gaokao_frequency,
      kn.zhongkao_applicability,
      
      -- 核心能力标签
      kn.requires_memorization,
      kn.requires_understanding,
      kn.requires_application,
      kn.requires_analysis,
      kn.requires_synthesis,
      kn.requires_evaluation,
      
      -- 能力要求
      kn.requires_basic_concept,
      kn.requires_calculation_skill,
      kn.requires_application_ability,
      
      -- 文理科特殊能力要求
      kn.requires_abstract_thinking,
      kn.requires_practical_application,
      kn.requires_logical_proof,
      
      -- 版本控制
      kn.version,
      kn.is_active,
      kn.created_at,
      kn.updated_at
    FROM knowledge_nodes kn
    WHERE kn.is_active = true
  `;
  
  const queryParams = [];
  let paramCount = 0;
  
  // 如果指定了节点ID列表，优先按ID查询
  if (nodeIds && Array.isArray(nodeIds) && nodeIds.length > 0) {
    console.log('批量查询节点ID:', nodeIds);
    paramCount++;
    // 使用字符串转换进行安全比较，避免类型不匹配
    query += ` AND (kn.id::text = ANY($${paramCount}) OR kn.node_code = ANY($${paramCount}))`;
    queryParams.push(nodeIds);
  } else {
    // 否则按其他条件查询
    if (subject) {
      paramCount++;
      query += ` AND kn.subject = $${paramCount}`;
      queryParams.push(subject);
    }
    
    if (gradeLevel) {
      paramCount++;
      query += ` AND kn.grade_level = $${paramCount}`;
      queryParams.push(gradeLevel);
    }
    
    if (difficulty) {
      paramCount++;
      query += ` AND kn.difficulty = $${paramCount}`;
      queryParams.push(difficulty);
    }
    
    if (academicTrack) {
      paramCount++;
      query += ` AND $${paramCount} = ANY(kn.target_academic_tracks)`;
      queryParams.push(academicTrack);
    }
    
    if (searchKeyword) {
      paramCount++;
      query += ` AND (kn.node_name ILIKE $${paramCount} OR kn.chapter_title ILIKE $${paramCount} OR kn.section_title ILIKE $${paramCount})`;
      queryParams.push(`%${searchKeyword}%`);
    }
  }
  
  query += ` ORDER BY kn.grade_level, kn.chapter_number, kn.section_number, kn.importance_level DESC, kn.created_at DESC`;
  
  // 只有在不是批量查询ID时才应用分页
  if (!nodeIds || !Array.isArray(nodeIds) || nodeIds.length === 0) {
    query += ` LIMIT $${paramCount + 1} OFFSET $${paramCount + 2}`;
    queryParams.push(limit, offset);
  }
  
  const client = await pool.connect();
  try {
    console.log('执行查询SQL:', query);
    console.log('查询参数:', queryParams);
    
    const result = await client.query(query, queryParams);
    
    console.log('查询结果行数:', result.rows.length);
    
    return result.rows;
  } catch (error) {
    console.error('查询知识点列表失败:', error);
    throw error;
  } finally {
    client.release();
  }
}

/**
 * 获取知识点关系
 * @param {Pool} pool 数据库连接池
 * @param {Object} params 查询参数
 */
async function getKnowledgeRelationships(pool, params) {
  const { nodeId, relationshipType } = params;
  
  if (!nodeId) {
    throw new Error('节点ID不能为空');
  }
  
  console.log('查询知识点关系，节点ID:', nodeId, '关系类型:', relationshipType);
  
  const client = await pool.connect();
  try {
    // 首先查找节点的实际数字ID（支持node_code和数字id查询）
    let actualNodeId = null;
    const findNodeQuery = `
      SELECT id, node_code FROM knowledge_nodes 
      WHERE (id::text = $1 OR node_code = $1) AND is_active = true
      LIMIT 1
    `;
    
    console.log('查找节点实际ID，查询SQL:', findNodeQuery);
    console.log('查找节点实际ID，参数:', [nodeId]);
    
    const nodeResult = await client.query(findNodeQuery, [nodeId]);
    if (nodeResult.rows.length > 0) {
      actualNodeId = nodeResult.rows[0].id;
      console.log('找到实际节点ID:', actualNodeId, '原始查询ID:', nodeId, '节点代码:', nodeResult.rows[0].node_code);
    } else {
      console.log('未找到对应的节点，ID:', nodeId);
      return [];
    }
    
    // 查询关系数据 - 使用数字ID查询
    let query = `
      SELECT 
        kr.id,
        kr.source_node_id,
        kr.target_node_id,
        kr.relationship_type,
        
        -- 关系强度和信心度
        kr.strength,
        kr.confidence,
        
        -- 文理科关系强度差异
        kr.liberal_arts_strength,
        kr.science_strength,
        
        -- 学习路径信息
        kr.learning_gap_days,
        kr.prerequisite_coverage,
        kr.difficulty_increase,
        
        -- 跨年级关联支持
        kr.cross_grade_type,
        kr.grade_span,
        
        -- 文理科特殊关系说明
        kr.track_specific_notes,
        
        -- 状态和时间
        kr.is_active,
        kr.created_at,
        
        -- 源节点信息
        sn.node_name as source_name,
        sn.node_code as source_code,
        sn.difficulty as source_difficulty,
        sn.chapter_number as source_chapter,
        sn.section_number as source_section,
        sn.chapter_title as source_chapter_title,
        sn.section_title as source_section_title,
        sn.grade_level as source_grade_level,
        sn.subject as source_subject,
        
        -- 目标节点信息
        tn.node_name as target_name,
        tn.node_code as target_code,
        tn.difficulty as target_difficulty,
        tn.chapter_number as target_chapter,
        tn.section_number as target_section,
        tn.chapter_title as target_chapter_title,
        tn.section_title as target_section_title,
        tn.grade_level as target_grade_level,
        tn.subject as target_subject
      FROM knowledge_relationships kr
      JOIN knowledge_nodes sn ON kr.source_node_id = sn.id AND sn.is_active = true
      JOIN knowledge_nodes tn ON kr.target_node_id = tn.id AND tn.is_active = true
      WHERE kr.is_active = true AND (kr.source_node_id = $1 OR kr.target_node_id = $1)
    `;
    
    const queryParams = [actualNodeId];
    
    if (relationshipType) {
      query += ` AND kr.relationship_type = $2`;
      queryParams.push(relationshipType);
    }
    
    query += ` ORDER BY kr.strength DESC, kr.confidence DESC, kr.created_at DESC`;
    
    console.log('执行关系查询SQL:', query);
    console.log('查询参数:', queryParams);
    
    const result = await client.query(query, queryParams);
    
    console.log('关系查询结果行数:', result.rows.length);
    if (result.rows.length > 0) {
      console.log('关系数据示例:', result.rows[0]);
    }
    
    return result.rows;
  } catch (error) {
    console.error('查询知识点关系失败:', error);
    throw error;
  } finally {
    client.release();
  }
}

/**
 * 获取学生掌握状态
 * @param {Pool} pool 数据库连接池
 * @param {Object} params 查询参数
 */
async function getStudentMastery(pool, params) {
  const { studentId, nodeId, gradeLevel, subject } = params;
  
  if (!studentId) {
    throw new Error('学生ID不能为空');
  }
  
  let query = `
    SELECT 
      skm.id,
      skm.student_id,
      skm.knowledge_node_id,
      
      -- 掌握状态
      skm.mastery_status,
      skm.mastery_percentage,
      
      -- 文理科差异化掌握评估
      skm.track_specific_mastery,
      skm.liberal_arts_score,
      skm.science_score,
      
      -- 能力维度掌握情况
      skm.basic_concept_mastery,
      skm.calculation_skill_mastery,
      skm.application_ability_mastery,
      
      -- 文理科特殊能力掌握
      skm.abstract_thinking_mastery,
      skm.practical_application_mastery,
      skm.logical_proof_mastery,
      
      -- 学习统计
      skm.total_study_time_minutes,
      skm.practice_count,
      skm.correct_count,
      skm.last_practice_date,
      
      -- 预测和建议
      skm.predicted_mastery_days,
      skm.recommended_practice_count,
      skm.next_review_date,
      
      -- 文理科个性化建议
      skm.track_specific_suggestions,
      
      -- AI评估
      skm.ai_assessment,
      skm.weakness_analysis,
      skm.improvement_suggestions,
      
      skm.created_at,
      skm.updated_at,
      
      -- 知识点信息
      kn.node_code,
      kn.node_name,
      kn.subject,
      kn.grade_level,
      kn.chapter_number,
      kn.section_number,
      kn.chapter_title,
      kn.section_title,
      kn.difficulty,
      kn.importance_level
    FROM student_knowledge_mastery skm
    JOIN knowledge_nodes kn ON skm.knowledge_node_id = kn.id
    WHERE skm.student_id = $1 AND kn.is_active = true
  `;
  
  const queryParams = [studentId];
  let paramCount = 1;
  
  if (nodeId) {
    paramCount++;
    query += ` AND skm.knowledge_node_id = $${paramCount}`;
    queryParams.push(nodeId);
  }
  
  if (gradeLevel) {
    paramCount++;
    query += ` AND kn.grade_level = $${paramCount}`;
    queryParams.push(gradeLevel);
  }
  
  if (subject) {
    paramCount++;
    query += ` AND kn.subject = $${paramCount}`;
    queryParams.push(subject);
  }
  
  query += ` ORDER BY kn.grade_level, kn.chapter_number NULLS LAST, kn.section_number NULLS LAST, kn.importance_level DESC`;
  
  const client = await pool.connect();
  try {
    const result = await client.query(query, queryParams);
    return result.rows;
  } finally {
    client.release();
  }
}

/**
 * 获取知识点详细内容
 * @param {Pool} pool 数据库连接池
 * @param {Object} params 查询参数
 */
async function getKnowledgeContent(pool, params) {
  const { nodeId } = params;
  
  if (!nodeId) {
    throw new Error('节点ID不能为空');
  }
  
  console.log('查询知识点详细内容，节点ID:', nodeId);
  
  // 支持多种节点ID格式的查询：数字ID和node_code
  const query = `
    SELECT 
      kn.*,
      kcd.description,
      kcd.detailed_explanation,
      kcd.key_points,
      kcd.formulas,
      kcd.examples,
      kcd.concepts_breakdown,
      kcd.common_mistakes,
      kcd.learning_tips,
      kcd.liberal_arts_content,
      kcd.science_content
    FROM knowledge_nodes kn
    LEFT JOIN knowledge_content_details kcd ON kn.id = kcd.node_id AND kcd.is_current = true
    WHERE (kn.id::text = $1 OR kn.node_code = $1) AND kn.is_active = true
  `;
  
  const client = await pool.connect();
  try {
    console.log('执行查询SQL:', query);
    console.log('查询参数:', [nodeId]);
    
    const result = await client.query(query, [nodeId]);
    
    console.log('查询结果行数:', result.rows.length);
    if (result.rows.length > 0) {
      console.log('查询到的数据:', result.rows[0]);
    }
    
    return result.rows[0] || null;
  } catch (error) {
    console.error('查询知识点详细内容失败:', error);
    throw error;
  } finally {
    client.release();
  }
}

/**
 * 获取学习路径
 * @param {Pool} pool 数据库连接池
 * @param {Object} params 查询参数
 */
async function getLearningPath(pool, params) {
  const { academicTrack, gradeLevel, subject } = params;
  
  let query = `
    SELECT 
      atlp.id,
      atlp.path_name,
      atlp.academic_track,
      atlp.grade_level,
      atlp.subject,
      atlp.knowledge_sequence,
      atlp.milestone_nodes,
      atlp.optional_nodes,
      atlp.total_estimated_hours,
      atlp.learning_objectives,
      atlp.is_default
    FROM academic_track_learning_paths atlp
    WHERE atlp.is_active = true
  `;
  
  const queryParams = [];
  let paramCount = 0;
  
  if (academicTrack) {
    paramCount++;
    query += ` AND atlp.academic_track = $${paramCount}`;
    queryParams.push(academicTrack);
  }
  
  if (gradeLevel) {
    paramCount++;
    query += ` AND atlp.grade_level = $${paramCount}`;
    queryParams.push(gradeLevel);
  }
  
  if (subject) {
    paramCount++;
    query += ` AND atlp.subject = $${paramCount}`;
    queryParams.push(subject);
  }
  
  query += ` ORDER BY atlp.is_default DESC, atlp.grade_level, atlp.path_name`;
  
  const client = await pool.connect();
  try {
    const result = await client.query(query, queryParams);
    return result.rows;
  } finally {
    client.release();
  }
}

/**
 * 搜索知识点
 * @param {Pool} pool 数据库连接池
 * @param {Object} params 查询参数
 */
async function searchKnowledge(pool, params) {
  const { keyword, subject, gradeLevel, limit = 20 } = params;
  
  if (!keyword) {
    throw new Error('搜索关键词不能为空');
  }
  
  let query = `
    SELECT 
      kn.id,
      kn.node_code,
      kn.node_name,
      kn.subject,
      kn.grade_level,
      kn.difficulty,
      kn.chapter_title,
      kn.section_title,
      kn.importance_level,
      kcd.description,
      ts_rank_cd(
        to_tsvector('simple', kn.node_name || ' ' || COALESCE(kn.chapter_title, '') || ' ' || COALESCE(kcd.description, '')),
        plainto_tsquery('simple', $1)
      ) as rank
    FROM knowledge_nodes kn
    LEFT JOIN knowledge_content_details kcd ON kn.id = kcd.node_id AND kcd.is_current = true
    WHERE kn.is_active = true
    AND (
      kn.node_name ILIKE $1 
      OR kn.chapter_title ILIKE $1 
      OR kn.section_title ILIKE $1
      OR kcd.description ILIKE $1
    )
  `;
  
  const queryParams = [`%${keyword}%`];
  let paramCount = 1;
  
  if (subject) {
    paramCount++;
    query += ` AND kn.subject = $${paramCount}`;
    queryParams.push(subject);
  }
  
  if (gradeLevel) {
    paramCount++;
    query += ` AND kn.grade_level = $${paramCount}`;
    queryParams.push(gradeLevel);
  }
  
  query += ` ORDER BY rank DESC, kn.importance_level DESC LIMIT $${paramCount + 1}`;
  queryParams.push(limit);
  
  const client = await pool.connect();
  try {
    const result = await client.query(query, queryParams);
    return result.rows;
  } finally {
    client.release();
  }
}

/**
 * 按年级获取知识点
 * @param {Pool} pool 数据库连接池
 * @param {Object} params 查询参数
 */
async function getKnowledgeByGrade(pool, params) {
  const { gradeLevel, subject, semester } = params;
  
  if (!gradeLevel) {
    throw new Error('年级不能为空');
  }
  
  let query = `
    SELECT 
      kn.id,
      kn.node_code,
      kn.node_name,
      
      -- 学科分类
      kn.subject,
      kn.domain_id,
      kn.textbook_edition_id,
      kn.grade_level,
      kn.semester,
      
      -- 文理科适用性
      kn.knowledge_applicability,
      kn.target_academic_tracks,
      
      -- 教材定位
      kn.chapter_number,
      kn.section_number,
      kn.chapter_title,
      kn.section_title,
      kn.unit_name,
      
      -- 学习属性
      kn.difficulty,
      kn.estimated_time_minutes,
      kn.cognitive_complexity,
      kn.importance_level,
      kn.exam_frequency,
      
      -- 文理科差异化属性
      kn.liberal_arts_difficulty,
      kn.science_difficulty,
      kn.liberal_arts_emphasis_points,
      kn.science_emphasis_points,
      
      -- 知识点状态
      kn.knowledge_type,
      kn.memory_tips,
      kn.common_misconceptions,
      
      -- 教学大纲和考试相关
      kn.curriculum_standard,
      kn.exam_importance_level,
      kn.gaokao_frequency,
      kn.zhongkao_applicability,
      
      -- 核心能力标签
      kn.requires_memorization,
      kn.requires_understanding,
      kn.requires_application,
      kn.requires_analysis,
      kn.requires_synthesis,
      kn.requires_evaluation,
      
      -- 能力要求
      kn.requires_basic_concept,
      kn.requires_calculation_skill,
      kn.requires_application_ability,
      
      -- 文理科特殊能力要求
      kn.requires_abstract_thinking,
      kn.requires_practical_application,
      kn.requires_logical_proof,
      
      -- 版本控制
      kn.version,
      kn.is_active,
      kn.created_at,
      kn.updated_at
    FROM knowledge_nodes kn
    WHERE kn.is_active = true AND kn.grade_level = $1
  `;
  
  const queryParams = [gradeLevel];
  let paramCount = 1;
  
  if (subject) {
    paramCount++;
    query += ` AND kn.subject = $${paramCount}`;
    queryParams.push(subject);
  }
  
  if (semester) {
    paramCount++;
    query += ` AND kn.semester = $${paramCount}`;
    queryParams.push(semester);
  }
  
  query += ` ORDER BY kn.chapter_number NULLS LAST, kn.section_number NULLS LAST, kn.importance_level DESC`;
  
  const client = await pool.connect();
  try {
    const result = await client.query(query, queryParams);
    return result.rows;
  } finally {
    client.release();
  }
}

/**
 * 获取前置知识点
 * @param {Pool} pool 数据库连接池
 * @param {Object} params 查询参数
 */
async function getPrerequisites(pool, params) {
  const { nodeId } = params;
  
  if (!nodeId) {
    throw new Error('节点ID不能为空');
  }
  
  const query = `
    WITH RECURSIVE prerequisites AS (
      SELECT 
        kr.source_node_id as node_id,
        kr.target_node_id,
        kr.strength,
        kr.relationship_type,
        1 as level
      FROM knowledge_relationships kr
      WHERE kr.target_node_id = $1 
      AND kr.relationship_type = 'prerequisite'
      AND kr.is_active = true
      
      UNION ALL
      
      SELECT 
        kr.source_node_id as node_id,
        kr.target_node_id,
        kr.strength,
        kr.relationship_type,
        p.level + 1
      FROM knowledge_relationships kr
      JOIN prerequisites p ON kr.target_node_id = p.node_id
      WHERE kr.relationship_type = 'prerequisite'
      AND kr.is_active = true
      AND p.level < 5
    )
    SELECT 
      p.node_id,
      p.level,
      p.strength,
      kn.node_name,
      kn.difficulty,
      kn.grade_level,
      kn.subject,
      kn.importance_level
    FROM prerequisites p
    JOIN knowledge_nodes kn ON p.node_id = kn.id
    ORDER BY p.level, p.strength DESC, kn.importance_level DESC
  `;
  
  const client = await pool.connect();
  try {
    const result = await client.query(query, [nodeId]);
    return result.rows;
  } finally {
    client.release();
  }
}

/**
 * 更新掌握状态
 * @param {Pool} pool 数据库连接池
 * @param {Object} params 更新参数
 */
async function updateMasteryStatus(pool, params) {
  const { 
    studentId, 
    nodeId, 
    masteryStatus, 
    masteryPercentage, 
    studyTimeMinutes = 0,
    isCorrect = false
  } = params;
  
  if (!studentId || !nodeId) {
    throw new Error('学生ID和节点ID不能为空');
  }
  
  const query = `
    INSERT INTO student_knowledge_mastery (
      student_id, 
      knowledge_node_id, 
      mastery_status, 
      mastery_percentage,
      total_study_time_minutes,
      practice_count,
      correct_count,
      last_practice_date,
      updated_at
    ) VALUES ($1, $2, $3, $4, $5, 1, $6, CURRENT_DATE, CURRENT_TIMESTAMP)
    ON CONFLICT (student_id, knowledge_node_id) 
    DO UPDATE SET
      mastery_status = EXCLUDED.mastery_status,
      mastery_percentage = EXCLUDED.mastery_percentage,
      total_study_time_minutes = student_knowledge_mastery.total_study_time_minutes + EXCLUDED.total_study_time_minutes,
      practice_count = student_knowledge_mastery.practice_count + 1,
      correct_count = student_knowledge_mastery.correct_count + EXCLUDED.correct_count,
      last_practice_date = EXCLUDED.last_practice_date,
      updated_at = EXCLUDED.updated_at
    RETURNING *
  `;
  
  const client = await pool.connect();
  try {
    const result = await client.query(query, [
      studentId, 
      nodeId, 
      masteryStatus, 
      masteryPercentage,
      studyTimeMinutes,
      isCorrect ? 1 : 0
    ]);
    return result.rows[0];
  } finally {
    client.release();
  }
}

/**
 * 获取个性化推荐
 * @param {Pool} pool 数据库连接池
 * @param {Object} params 查询参数
 */
async function getRecommendations(pool, params) {
  const { studentId, academicTrack, limit = 10 } = params;
  
  if (!studentId) {
    throw new Error('学生ID不能为空');
  }
  
  const query = `
    WITH student_weak_points AS (
      SELECT 
        skm.knowledge_node_id,
        skm.mastery_percentage,
        kn.importance_level,
        kn.difficulty,
        kn.grade_level
      FROM student_knowledge_mastery skm
      JOIN knowledge_nodes kn ON skm.knowledge_node_id = kn.id
      WHERE skm.student_id = $1 
      AND skm.mastery_status IN ('weak', 'learning')
      AND skm.mastery_percentage < 80
    ),
    recommended_nodes AS (
      SELECT 
        kn.id,
        kn.node_name,
        kn.difficulty,
        kn.importance_level,
        kn.estimated_time_minutes,
        kn.subject,
        kn.grade_level,
        CASE 
          WHEN swp.knowledge_node_id IS NOT NULL THEN 'weak_improvement'
          WHEN kr.source_node_id IS NOT NULL THEN 'prerequisite_missing'
          ELSE 'next_learning'
        END as recommendation_type,
        CASE 
          WHEN swp.knowledge_node_id IS NOT NULL THEN (100 - swp.mastery_percentage) * kn.importance_level
          WHEN kr.source_node_id IS NOT NULL THEN kr.strength * 100 * kn.importance_level
          ELSE kn.importance_level * 50
        END as priority_score
      FROM knowledge_nodes kn
      LEFT JOIN student_weak_points swp ON kn.id = swp.knowledge_node_id
      LEFT JOIN knowledge_relationships kr ON kn.id = kr.source_node_id 
        AND kr.target_node_id IN (SELECT knowledge_node_id FROM student_weak_points)
        AND kr.relationship_type = 'prerequisite'
      LEFT JOIN student_knowledge_mastery existing_skm ON kn.id = existing_skm.knowledge_node_id 
        AND existing_skm.student_id = $1
      WHERE kn.is_active = true
      AND (swp.knowledge_node_id IS NOT NULL OR kr.source_node_id IS NOT NULL OR existing_skm.id IS NULL)
      AND ($2::text IS NULL OR $2 = ANY(kn.target_academic_tracks) OR kn.knowledge_applicability = 'universal')
    )
    SELECT * FROM recommended_nodes
    ORDER BY priority_score DESC, importance_level DESC
    LIMIT $3
  `;
  
  const client = await pool.connect();
  try {
    const result = await client.query(query, [studentId, academicTrack, limit]);
    return result.rows;
  } finally {
    client.release();
  }
}