/**
 * 数学公式组件
 * 用于渲染数学公式，支持LaTeX语法
 */

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 数学公式文本，支持LaTeX语法
    formula: {
      type: String,
      value: '',
      observer: function(newVal) {
        this.renderFormula();
      }
    },
    // 渲染模式：inline(行内) 或 block(块级)
    mode: {
      type: String,
      value: 'inline' // 可选值：inline, block
    },
    // 字体大小
    fontSize: {
      type: Number,
      value: 16
    },
    // 是否显示步骤分解按钮
    showStepsButton: {
      type: Boolean,
      value: false
    },
    // 步骤分解数据
    steps: {
      type: Array,
      value: []
    },
    // 是否可点击查看详情
    clickable: {
      type: Boolean,
      value: false
    },
    // 是否启用动画演示
    enableAnimation: {
      type: Boolean,
      value: false
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    renderedFormula: '',
    showSteps: false,
    currentStep: 0,
    isPlaying: false,
    playInterval: null
  },

  lifetimes: {
    attached: function() {
      this.renderFormula();
    },
    detached: function() {
      // 清除播放定时器
      if (this.data.playInterval) {
        clearInterval(this.data.playInterval);
      }
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 渲染数学公式
     */
    renderFormula: function() {
      const formula = this.data.formula;
      if (!formula) {
        this.setData({
          renderedFormula: ''
        });
        return;
      }

      // 在真实项目中，这里应该调用渲染引擎处理LaTeX公式
      // 示例中简单替换一些常见的数学符号，实际实现应使用专业的公式渲染库
      let rendered = formula
        .replace(/\\frac{([^}]*)}{([^}]*)}/g, '$1/$2')
        .replace(/\\cdot/g, '·')
        .replace(/\\times/g, '×')
        .replace(/\\div/g, '÷')
        .replace(/\\sqrt{([^}]*)}/g, '√$1')
        .replace(/\^2/g, '²')
        .replace(/\^3/g, '³')
        .replace(/\\pi/g, 'π')
        .replace(/\\theta/g, 'θ')
        .replace(/\\alpha/g, 'α')
        .replace(/\\beta/g, 'β')
        .replace(/\\sum/g, 'Σ')
        .replace(/\\int/g, '∫')
        .replace(/\\infty/g, '∞');

      this.setData({
        renderedFormula: rendered
      });
    },

    /**
     * 处理公式点击事件
     */
    handleTap: function() {
      if (!this.data.clickable) return;

      // 触发点击事件
      this.triggerEvent('tap', {
        formula: this.data.formula
      });

      // 如果有步骤数据且允许显示步骤按钮，则显示步骤
      if (this.data.showStepsButton && this.data.steps.length > 0) {
        this.toggleSteps();
      }
    },

    /**
     * 切换步骤显示状态
     */
    toggleSteps: function() {
      const showSteps = !this.data.showSteps;
      this.setData({
        showSteps,
        currentStep: 0
      });

      // 如果关闭步骤显示，停止播放
      if (!showSteps && this.data.isPlaying) {
        this.stopAnimation();
      }
    },

    /**
     * 显示上一步
     */
    prevStep: function() {
      let currentStep = this.data.currentStep;
      if (currentStep > 0) {
        currentStep--;
        this.setData({
          currentStep
        });
      }
    },

    /**
     * 显示下一步
     */
    nextStep: function() {
      let currentStep = this.data.currentStep;
      if (currentStep < this.data.steps.length - 1) {
        currentStep++;
        this.setData({
          currentStep
        });
      }
    },

    /**
     * 播放/暂停动画演示
     */
    toggleAnimation: function() {
      if (!this.data.enableAnimation) return;

      if (this.data.isPlaying) {
        this.stopAnimation();
      } else {
        this.startAnimation();
      }
    },

    /**
     * 开始动画演示
     */
    startAnimation: function() {
      if (this.data.playInterval) {
        clearInterval(this.data.playInterval);
      }

      this.setData({
        isPlaying: true
      });

      const playInterval = setInterval(() => {
        let currentStep = this.data.currentStep;
        if (currentStep < this.data.steps.length - 1) {
          currentStep++;
          this.setData({
            currentStep
          });
        } else {
          // 播放完成，停止播放或循环
          this.setData({
            currentStep: 0
          });
        }
      }, 2000); // 每2秒播放一步

      this.setData({
        playInterval
      });
    },

    /**
     * 停止动画演示
     */
    stopAnimation: function() {
      if (this.data.playInterval) {
        clearInterval(this.data.playInterval);
      }

      this.setData({
        isPlaying: false,
        playInterval: null
      });
    },

    /**
     * 语音讲解
     */
    speakExplanation: function() {
      const currentStep = this.data.currentStep;
      const step = this.data.steps[currentStep] || {};
      const explanation = step.explanation || this.data.formula;

      // 触发语音讲解事件
      this.triggerEvent('speak', {
        text: explanation,
        stepIndex: currentStep
      });
    }
  }
}) 