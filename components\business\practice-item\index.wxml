<view class="practice-item" bindtap="onTapItem">
  <view class="practice-container">
    <!-- 头部区域 -->
    <view class="header">
      <view class="icon {{getIconClass()}}"></view>
      <view class="title">{{item.title || titleMap[type]}}</view>
      <view class="status" wx:if="{{item.status}}">{{item.status}}</view>
    </view>

    <!-- 描述区域 -->
    <view class="description" wx:if="{{item.description}}">{{item.description}}</view>

    <!-- 统计区域 -->
    <view class="statistics">
      <!-- 难度级别 -->
      <view class="difficulty">
        <text class="label">难度：</text>
        <view class="stars">
          <view 
            wx:for="{{5}}" 
            wx:key="index" 
            class="star {{index < difficultyMap[item.difficulty || 'medium'] ? 'star-active' : ''}}"
          ></view>
        </view>
      </view>

      <!-- 题目数量 -->
      <view class="questions" wx:if="{{item.questions}}">
        <text class="icon icon-question icon-sm"></text>
        <text>{{item.questions}}题</text>
      </view>

      <!-- 时长 -->
      <view class="duration" wx:if="{{item.duration}}">
        <text class="icon icon-time icon-sm"></text>
        <text>{{formatDuration()}}</text>
      </view>
    </view>

    <!-- 完成度区域 -->
    <view class="completion" wx:if="{{item.completion !== undefined}}">
      <view class="completion-text">
        <text>完成度</text>
        <text class="percent">{{formatCompletion()}}</text>
      </view>
      <view class="progress-bar">
        <view class="progress" style="width: {{progressWidth}}"></view>
      </view>
    </view>

    <!-- 底部区域 -->
    <view class="footer">
      <!-- 标签列表 -->
      <view class="tags" wx:if="{{item.tags && item.tags.length > 0}}">
        <view 
          wx:for="{{item.tags}}" 
          wx:key="index" 
          wx:for-item="tag" 
          class="tag"
        >{{tag}}</view>
      </view>

      <!-- 知识点列表 -->
      <view class="knowledge-points" wx:if="{{item.knowledgePoints && item.knowledgePoints.length > 0}}">
        <view 
          wx:for="{{item.knowledgePoints}}" 
          wx:key="index" 
          wx:for-item="point" 
          class="knowledge-point"
        >{{point.name || point}}</view>
      </view>

      <!-- 操作按钮 -->
      <view class="actions">
        <button 
          class="btn-practice" 
          catchtap="onTapPractice"
        >开始{{titleMap[type]}}</button>
      </view>
    </view>
  </view>
</view> 