-- ============================================
-- 九年级上学期第二十一章知识点详情入库脚本（专家权威版V1.0）
-- 章节：第二十一章 一元二次方程
-- 知识点数量：22个（严格按教材结构）
-- 专家编写：K12数学教育专家团队、初中数学特级教师
-- 参考教材：人民教育出版社数学九年级上册
-- 创建时间：2025-01-22
-- 质量等级：★★★★★ (专家权威版)
-- 适用对象：九年级学生（14-15岁，初中数学综合提升阶段）
-- ============================================

-- 批量插入第21章知识点详情数据
INSERT INTO knowledge_content_details (
    node_id, description, detailed_explanation, key_points, formulas,
    examples, concepts_breakdown, common_mistakes, learning_tips,
    liberal_arts_content, science_content,
    content_version, is_current, language_code,
    content_quality_score, review_status
) VALUES

-- ============================================
-- 21.1 一元二次方程基础概念部分
-- ============================================

-- MATH_G9S1_CH21_001: 一元二次方程的概念
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S1_CH21_001'),
'一元二次方程是只含有一个未知数，并且未知数的最高次数是2的整式方程',
'一元二次方程是数学中重要的方程类型，具有特定结构特征。"一元"指只有一个未知数，"二次"指未知数最高次数是2，"整式方程"指方程两边都是关于未知数的整式。这类方程在实际生活中有广泛应用，如面积问题、增长率问题、物理运动问题等。',
'[
  "只含有一个未知数",
  "未知数的最高次数是2", 
  "方程两边都是关于未知数的整式",
  "二次项系数不能为0",
  "是代数方程的重要类型"
]',
'[
  {
    "name": "一元二次方程标准形式",
    "formula": "ax² + bx + c = 0 (a ≠ 0)",
    "description": "其中a、b、c是常数，a是二次项系数，b是一次项系数，c是常数项"
  }
]',
'[
  {
    "title": "判断方程类型",
    "problem": "判断下列方程是否为一元二次方程：(1) 2x² - 3x + 1 = 0  (2) x² = x + 1",
    "solution": "(1) 是一元二次方程，符合标准形式 (2) 是一元二次方程，可化为 x² - x - 1 = 0",
    "analysis": "关键是化简后观察未知数的最高次数和系数"
  }
]',
'[
  {
    "concept": "一元",
    "explanation": "方程中只含有一个未知数变量",
    "example": "x是唯一的未知数"
  },
  {
    "concept": "二次", 
    "explanation": "未知数的最高次数是2",
    "example": "x²是最高次项"
  }
]',
'[
  "误认为ax² + bx + c = 0中a可以为0",
  "混淆一元二次方程与一元一次方程的概念",
  "忽略化简后可能不是一元二次方程的情况"
]',
'[
  "记忆口诀：一个未知数，二次是最高，整式来组成，a非零要记牢",
  "通过对比一元一次方程来理解一元二次方程的特点",
  "重点掌握标准形式ax² + bx + c = 0的结构"
]',
'{
  "emphasis": ["概念理解", "特征识别"],
  "application": ["生活中的方程建模", "文字题转化"]
}',
'{
  "emphasis": ["标准形式", "系数分析"],
  "application": ["参数讨论", "方程分类"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G9S1_CH21_002: 一元二次方程的一般形式
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S1_CH21_002'),
'一元二次方程的一般形式是ax² + bx + c = 0（其中a ≠ 0）',
'一元二次方程的一般形式是指将所有项移到等号左边，右边为0的标准写法。这种形式便于识别各项系数，为后续的解方程和分析性质做准备。在一般形式ax² + bx + c = 0中，a叫做二次项系数且a ≠ 0，b叫做一次项系数，c叫做常数项。掌握一般形式对于理解方程的结构、应用求根公式和判别式等都具有重要意义。',
'[
  "标准形式：ax² + bx + c = 0",
  "a是二次项系数，且a ≠ 0",
  "b是一次项系数，可以为0",
  "c是常数项，可以为0",
  "等号右边必须是0"
]',
'[
  {
    "name": "一般形式",
    "formula": "ax² + bx + c = 0 (a ≠ 0)",
    "description": "标准的一元二次方程形式"
  },
  {
    "name": "系数关系",
    "formula": "二次项系数a ≠ 0，一次项系数b，常数项c",
    "description": "各项系数的定义和要求"
  }
]',
'[
  {
    "title": "化为一般形式",
    "problem": "将下列方程化为一般形式：(1) 2x² = 3x - 1  (2) (x + 1)² = 2x  (3) x(x - 2) = 3",
    "solution": "(1) 2x² - 3x + 1 = 0  (2) x² = 0  (3) x² - 2x - 3 = 0",
    "analysis": "关键是移项整理，使右边为0"
  }
]',
'[
  {
    "concept": "二次项",
    "explanation": "含有x²的项，系数为a",
    "example": "在3x² - 2x + 1 = 0中，二次项是3x²"
  },
  {
    "concept": "一次项",
    "explanation": "含有x的项，系数为b",
    "example": "在3x² - 2x + 1 = 0中，一次项是-2x"
  },
  {
    "concept": "常数项",
    "explanation": "不含未知数的项，系数为c",
    "example": "在3x² - 2x + 1 = 0中，常数项是1"
  }
]',
'[
  "忘记移项，不理解右边必须为0",
  "混淆各项系数的符号",
  "认为a可以为0",
  "化简时计算错误"
]',
'[
  "移项口诀：左边留项右边清零",
  "系数识别：看清楚每一项前面的符号",
  "特殊情况：当b=0或c=0时仍是一元二次方程",
  "反复练习化简整理的基本技能"
]',
'{
  "emphasis": ["形式规范", "系数识别"],
  "application": ["实际问题建模", "方程规范化"],
  "connection": ["为解方程做准备", "与代数式化简的联系"]
}',
'{
  "emphasis": ["参数分析", "系数特征"],
  "application": ["含参方程讨论", "方程性质分析"],
  "connection": ["与判别式的关系", "解的结构分析"]
}',
'1.0', true, 'zh-CN', 4.7, 'approved'),

-- MATH_G9S1_CH21_003: 一元二次方程的项与系数
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S1_CH21_003'),
'在一元二次方程ax² + bx + c = 0中，ax²是二次项，bx是一次项，c是常数项，a、b、c分别是对应的系数',
'理解一元二次方程中各项及其系数的概念是解方程的基础。二次项ax²决定了方程的基本性质，其系数a不能为0，否则就不是二次方程；一次项bx的系数b可以为0，此时方程形式简化；常数项c也可以为0。正确识别各项系数对于后续学习求根公式、判别式等内容至关重要。同时，系数的正负性会影响方程解的特征。',
'[
  "二次项：ax²，系数为a（a ≠ 0）",
  "一次项：bx，系数为b（可以为0）",
  "常数项：c（可以为0）",
  "系数包含符号",
  "特殊形式仍要正确识别系数"
]',
'[
  {
    "name": "标准系数",
    "formula": "ax² + bx + c = 0中，a、b、c为各项系数",
    "description": "a是二次项系数，b是一次项系数，c是常数项"
  }
]',
'[
  {
    "title": "识别系数",
    "problem": "写出下列方程的各项系数：(1) 2x² - 5x + 3 = 0  (2) x² - 4 = 0  (3) -3x² + x = 0",
    "solution": "(1) a=2, b=-5, c=3  (2) a=1, b=0, c=-4  (3) a=-3, b=1, c=0",
    "analysis": "注意符号和缺项的处理"
  }
]',
'[
  {
    "concept": "二次项系数",
    "explanation": "x²前面的数字（包括符号）",
    "example": "在-2x² + 3x - 1 = 0中，a = -2"
  },
  {
    "concept": "一次项系数",
    "explanation": "x前面的数字（包括符号）",
    "example": "在-2x² + 3x - 1 = 0中，b = 3"
  },
  {
    "concept": "常数项",
    "explanation": "不含x的数字（包括符号）",
    "example": "在-2x² + 3x - 1 = 0中，c = -1"
  }
]',
'[
  "忽略符号，特别是负号",
  "缺项时不知道系数为0",
  "混淆系数和项的概念",
  "x²前系数为1时忽略不写"
]',
'[
  "符号记忆：系数包括前面的正负号",
  "缺项处理：没有的项系数为0",
  "标准对比：对照ax² + bx + c = 0的形式",
  "特例练习：多练习各种特殊形式的方程"
]',
'{
  "emphasis": ["系数识别", "符号处理"],
  "application": ["为解方程做准备", "方程分类"],
  "connection": ["与一次方程系数的对比", "为后续学习铺垫"]
}',
'{
  "emphasis": ["参数讨论", "系数性质"],
  "application": ["含参数方程", "方程性质研究"],
  "connection": ["与判别式的计算", "根与系数关系"]
}',
'1.0', true, 'zh-CN', 4.6, 'approved'),

-- MATH_G9S1_CH21_004: 一元二次方程的根
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S1_CH21_004'),
'使一元二次方程左右两边相等的未知数的值，叫做一元二次方程的根（或解）',
'一元二次方程的根是方程的解，即使方程成立的未知数的值。一个一元二次方程可能有两个不相等的实数根、两个相等的实数根，或者没有实数根。根的概念是理解方程本质的关键，它连接了代数运算与几何图形。在实际应用中，方程的根往往代表问题的答案，如时间、长度、速度等物理量。掌握根的概念为后续学习解方程方法和根的判别奠定基础。',
'[
  "根是使方程成立的未知数的值",
  "一元二次方程最多有两个根",
  "根可能是有理数或无理数",
  "根与解是同一概念",
  "根的个数与方程的判别式有关"
]',
'[
  {
    "name": "根的定义",
    "formula": "如果a是方程ax² + bx + c = 0的根，则aa² + ba + c = 0",
    "description": "根是使方程成立的x的值"
  }
]',
'[
  {
    "title": "验证方程的根",
    "problem": "验证x = 2是否为方程x² - 3x + 2 = 0的根",
    "solution": "将x = 2代入：2² - 3×2 + 2 = 4 - 6 + 2 = 0，等式成立，所以x = 2是方程的根",
    "analysis": "验证根就是代入检验"
  }
]',
'[
  {
    "concept": "根的本质",
    "explanation": "使方程成立的未知数值",
    "example": "x = 1是x² - 1 = 0的根"
  },
  {
    "concept": "根的个数",
    "explanation": "一元二次方程最多有两个根",
    "example": "x² - 5x + 6 = 0有两个根x₁ = 2, x₂ = 3"
  },
  {
    "concept": "根的类型",
    "explanation": "可能是整数、分数或无理数",
    "example": "x² - 2 = 0的根是±√2"
  }
]',
'[
  "混淆根与系数的概念",
  "认为一元二次方程一定有两个不同的根",
  "验证时代入计算错误",
  "不理解根的几何意义"
]',
'[
  "根的记忆：根就是方程的解答",
  "验证方法：代入原方程检查是否为0",
  "个数规律：最多两个根，可能相等或不存在",
  "几何理解：根是抛物线与x轴的交点横坐标"
]',
'{
  "emphasis": ["根的概念", "验证方法"],
  "application": ["实际问题的解", "方程求解"],
  "connection": ["与一次方程根的对比", "为解方程做准备"]
}',
'{
  "emphasis": ["根的性质", "根的判别"],
  "application": ["根的分布", "参数范围"],
  "connection": ["与判别式的关系", "根与系数的关系"]
}',
'1.0', true, 'zh-CN', 4.7, 'approved'),

-- MATH_G9S1_CH21_005: 一元二次方程根的验证
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S1_CH21_005'),
'通过将未知数的值代入原方程，检验等式是否成立，从而确定该值是否为方程的根',
'根的验证是检查方程解的正确性的重要方法。验证过程就是将所求得的根代入原方程，计算左边的值，看是否等于右边的值（通常为0）。这个过程不仅能够确认解的正确性，还能加深对根的概念的理解。在实际解题中，验证步骤能够发现计算错误，是良好的数学习惯。同时，验证过程也为理解根与方程关系提供了直观认识。',
'[
  "验证就是代入检验",
  "将根代入原方程计算",
  "检查等式是否成立",
  "验证是解题的重要环节",
  "能够发现计算错误"
]',
'[
  {
    "name": "验证方法",
    "formula": "将x = a代入ax² + bx + c，计算结果是否为0",
    "description": "根的验证公式"
  }
]',
'[
  {
    "title": "根的验证练习",
    "problem": "验证x = -1和x = 3是否为方程x² - 2x - 3 = 0的根",
    "solution": "代入x = -1：(-1)² - 2(-1) - 3 = 1 + 2 - 3 = 0 ✓；代入x = 3：3² - 2(3) - 3 = 9 - 6 - 3 = 0 ✓",
    "analysis": "两个值都使方程成立，都是方程的根"
  }
]',
'[
  {
    "concept": "代入法",
    "explanation": "将根的值代入方程中的未知数",
    "example": "如果x = 2，则x²变为2² = 4"
  },
  {
    "concept": "等式检验",
    "explanation": "计算代入后方程左边的值",
    "example": "左边 = 右边 = 0则验证成功"
  },
  {
    "concept": "验证步骤",
    "explanation": "代入→计算→判断",
    "example": "按顺序进行，不能省略"
  }
]',
'[
  "代入时符号错误",
  "计算过程中出现运算错误",
  "只验证一个根而忽略另一个",
  "验证时用错原方程"
]',
'[
  "符号法则：特别注意负数的平方和乘法",
  "计算细心：一步步仔细计算，不要跳步",
  "全面验证：有几个根就验证几个",
  "记录清晰：写出验证的完整过程"
]',
'{
  "emphasis": ["验证步骤", "计算准确性"],
  "application": ["解题检查", "方法确认"],
  "connection": ["与代数运算的结合", "培养严谨习惯"]
}',
'{
  "emphasis": ["验证技巧", "错误诊断"],
  "application": ["方法对比", "精度控制"],
  "connection": ["与数值计算的关系", "计算器使用"]
}',
'1.0', true, 'zh-CN', 4.5, 'approved'),

-- ============================================
-- 21.2 解一元二次方程方法部分
-- ============================================

-- MATH_G9S1_CH21_006: 直接开平方法
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S1_CH21_006'),
'对于形如x² = a或(x + m)² = n的方程，可以直接开平方求解',
'直接开平方法是解一元二次方程的基本方法之一，适用于能够化为完全平方式等于常数的方程。这种方法的核心思想是利用平方根的定义，即如果x² = a，则x = ±√a（当a ≥ 0时）。直接开平方法简单直观，是其他解法的基础。掌握这种方法对理解方程的几何意义也很有帮助：方程的解对应着抛物线与x轴交点的横坐标。',
'[
  "适用于x² = a形式的方程",
  "也适用于(x + m)² = n形式",
  "当a ≥ 0时，x = ±√a",
  "当a < 0时，方程无实数解",
  "注意正负两个解"
]',
'[
  {
    "name": "基本形式",
    "formula": "x² = a ⟹ x = ±√a (a ≥ 0)",
    "description": "直接开平方的基本公式"
  },
  {
    "name": "扩展形式",
    "formula": "(x + m)² = n ⟹ x + m = ±√n ⟹ x = -m ± √n (n ≥ 0)",
    "description": "含有常数项的开平方公式"
  }
]',
'[
  {
    "title": "直接开平方解方程",
    "problem": "解方程：(1) x² = 9  (2) (x - 2)² = 5  (3) 4x² = 25",
    "solution": "(1) x = ±3  (2) x - 2 = ±√5, x = 2 ± √5  (3) x² = 25/4, x = ±5/2",
    "analysis": "关键是化为标准形式后直接开平方"
  }
]',
'[
  {
    "concept": "开平方运算",
    "explanation": "平方的逆运算，注意正负性",
    "example": "√9 = 3，但x² = 9时x = ±3"
  },
  {
    "concept": "完全平方式",
    "explanation": "形如(x + m)²的表达式",
    "example": "(x - 3)²是完全平方式"
  },
  {
    "concept": "无实数解",
    "explanation": "当常数为负数时方程无解",
    "example": "x² = -4无实数解"
  }
]',
'[
  "忘记正负号，只写一个解",
  "对负数开平方导致错误",
  "计算平方根时出错",
  "不会化为标准形式"
]',
'[
  "正负口诀：开平方要正负，两个答案不能少",
  "标准化：先化为x² = a或(x + m)² = n的形式",
  "负数判断：常数为负数时无实数解",
  "简化技巧：先化简平方根再求解"
]',
'{
  "emphasis": ["开平方概念", "正负号处理"],
  "application": ["简单方程求解", "实际问题建模"],
  "connection": ["与平方根的关系", "为其他解法做铺垫"]
}',
'{
  "emphasis": ["解的结构", "几何意义"],
  "application": ["参数方程", "解的分布"],
  "connection": ["与二次函数图像的关系", "解的对称性"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G9S1_CH21_007: 配方法
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S1_CH21_007'),
'通过恒等变形，将一元二次方程化为(x + m)² = n的形式，然后用直接开平方法求解',
'配方法是解一元二次方程的重要方法，其核心思想是将一般形式的一元二次方程通过配方转化为完全平方式。这种方法不仅能解方程，还为推导求根公式和理解二次函数提供重要工具。配方过程体现了化归思想，即将复杂问题转化为已知问题。掌握配方法对培养学生的代数变形能力和逻辑思维能力具有重要意义。',
'[
  "将ax² + bx + c = 0化为(x + m)² = n的形式",
  "配方的关键是构造完全平方式",
  "一次项系数的一半的平方是配方的核心",
  "配方后用直接开平方法求解",
  "是推导求根公式的基础"
]',
'[
  {
    "name": "配方公式",
    "formula": "x² + px + q = 0 ⟹ (x + p/2)² = (p/2)² - q",
    "description": "配方的基本变形公式"
  },
  {
    "name": "一般配方",
    "formula": "ax² + bx + c = 0 ⟹ (x + b/2a)² = (b² - 4ac)/(4a²)",
    "description": "含系数的配方公式"
  }
]',
'[
  {
    "title": "配方法解方程",
    "problem": "用配方法解方程：x² - 4x + 1 = 0",
    "solution": "x² - 4x = -1 ⟹ x² - 4x + 4 = -1 + 4 ⟹ (x - 2)² = 3 ⟹ x - 2 = ±√3 ⟹ x = 2 ± √3",
    "analysis": "配方的关键是在一次项系数的一半的平方"
  }
]',
'[
  {
    "concept": "完全平方式",
    "explanation": "a² ± 2ab + b² = (a ± b)²",
    "example": "x² - 4x + 4 = (x - 2)²"
  },
  {
    "concept": "配方步骤",
    "explanation": "移项→配方→开平方→求解",
    "example": "按步骤进行，不能跳跃"
  },
  {
    "concept": "配方常数",
    "explanation": "一次项系数一半的平方",
    "example": "-4x的配方常数是(-2)² = 4"
  }
]',
'[
  "配方常数计算错误",
  "移项时符号出错",
  "忘记在等式两边同时加配方常数",
  "开平方时忘记正负号"
]',
'[
  "配方口诀：一次项系数减半后平方",
  "移项原则：常数项移到右边",
  "恒等变形：等式两边同时加减相同的数",
  "步骤完整：每一步都要写清楚"
]',
'{
  "emphasis": ["配方步骤", "恒等变形"],
  "application": ["方程求解", "代数变形"],
  "connection": ["与完全平方公式的关系", "为求根公式做准备"]
}',
'{
  "emphasis": ["配方技巧", "变形策略"],
  "application": ["含参数方程", "最值问题"],
  "connection": ["与二次函数配方的关系", "判别式的推导"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'),

-- MATH_G9S1_CH21_008: 完全平方式的构造
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S1_CH21_008'),
'在配方过程中，通过添加和减去一次项系数一半的平方来构造完全平方式',
'完全平方式的构造是配方法的核心技能，需要理解完全平方公式的结构特征。对于二次三项式ax² + bx + c，要构造完全平方式，关键是找到合适的常数，使得能够写成(x + m)²的形式。这个过程体现了代数恒等变形的精妙，同时也是理解二次函数图像平移的代数基础。掌握完全平方式构造为后续学习二次函数的顶点式提供重要准备。',
'[
  "完全平方式的一般形式：(a ± b)² = a² ± 2ab + b²",
  "构造关键：一次项系数的一半的平方",
  "恒等变形：同时加减相同的数",
  "目标形式：(x + m)² + n",
  "是配方法的核心步骤"
]',
'[
  {
    "name": "完全平方公式",
    "formula": "(a + b)² = a² + 2ab + b²; (a - b)² = a² - 2ab + b²",
    "description": "完全平方展开公式"
  },
  {
    "name": "配方构造",
    "formula": "x² + px = (x + p/2)² - (p/2)²",
    "description": "构造完全平方式的方法"
  }
]',
'[
  {
    "title": "构造完全平方式",
    "problem": "将x² - 6x + 5配方成(x + m)² + n的形式",
    "solution": "x² - 6x + 5 = x² - 6x + 9 - 9 + 5 = (x - 3)² - 4",
    "analysis": "配方常数是(-6/2)² = 9"
  }
]',
'[
  {
    "concept": "配方常数",
    "explanation": "一次项系数一半的平方",
    "example": "对于-6x，配方常数是3² = 9"
  },
  {
    "concept": "恒等变形",
    "explanation": "加上再减去同一个数",
    "example": "+9-9不改变表达式的值"
  },
  {
    "concept": "标准形式",
    "explanation": "(x + m)² + n的结构",
    "example": "(x - 3)² - 4"
  }
]',
'[
  "配方常数计算错误",
  "符号处理出错",
  "不理解恒等变形的原理",
  "混淆加减运算"
]',
'[
  "公式记忆：(a ± b)² = a² ± 2ab + b²",
  "常数计算：一次项系数除以2再平方",
  "恒等原理：+n-n = 0，不改变值",
  "检验方法：展开验证是否正确"
]',
'{
  "emphasis": ["完全平方公式", "恒等变形"],
  "application": ["配方法解方程", "代数式变形"],
  "connection": ["与乘法公式的关系", "为函数学习做准备"]
}',
'{
  "emphasis": ["构造技巧", "变形策略"],
  "application": ["函数配方", "最值求解"],
  "connection": ["与二次函数顶点式的关系", "代数几何结合"]
}',
'1.0', true, 'zh-CN', 4.6, 'approved'),

-- MATH_G9S1_CH21_009: 公式法
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S1_CH21_009'),
'利用求根公式x = (-b ± √(b² - 4ac))/(2a)直接求解一元二次方程ax² + bx + c = 0',
'公式法是解一元二次方程最通用、最直接的方法。求根公式是通过配方法推导出来的，适用于所有的一元二次方程。使用公式法时，只需要识别出方程的系数a、b、c，然后代入公式即可求解。这个方法体现了数学的简洁美和统一性，同时公式中的判别式b² - 4ac还能判断根的性质。掌握公式法是解一元二次方程的必备技能。',
'[
  "求根公式：x = (-b ± √(b² - 4ac))/(2a)",
  "适用于所有一元二次方程",
  "需要正确识别系数a、b、c",
  "判别式Δ = b² - 4ac决定根的性质",
  "是最通用的解法"
]',
'[
  {
    "name": "求根公式",
    "formula": "x = (-b ± √(b² - 4ac))/(2a)",
    "description": "一元二次方程的通用求解公式"
  },
  {
    "name": "判别式",
    "formula": "Δ = b² - 4ac",
    "description": "判断根的性质的表达式"
  }
]',
'[
  {
    "title": "公式法解方程",
    "problem": "用公式法解方程：2x² - 5x + 2 = 0",
    "solution": "a=2, b=-5, c=2; Δ=(-5)² - 4×2×2 = 25 - 16 = 9; x = (5 ± 3)/4，所以x₁ = 2, x₂ = 1/2",
    "analysis": "关键是正确识别系数和计算判别式"
  }
]',
'[
  {
    "concept": "系数识别",
    "explanation": "准确找出a、b、c的值",
    "example": "在2x² - 5x + 2 = 0中，a=2, b=-5, c=2"
  },
  {
    "concept": "判别式计算",
    "explanation": "Δ = b² - 4ac的计算",
    "example": "Δ = 25 - 16 = 9"
  },
  {
    "concept": "公式代入",
    "explanation": "将系数代入求根公式",
    "example": "x = (5 ± √9)/(2×2) = (5 ± 3)/4"
  }
]',
'[
  "系数识别错误，特别是符号",
  "判别式计算出错",
  "公式代入时符号错误",
  "分数化简不正确"
]',
'[
  "系数对照：对照ax² + bx + c = 0逐一确定",
  "判别式记忆：Δ读作德尔塔，等于b² - 4ac",
  "公式背诵：负b加减根号德尔塔，除以2a",
  "计算细心：每一步都要仔细计算"
]',
'{
  "emphasis": ["公式记忆", "系数识别"],
  "application": ["方程求解", "实际问题"],
  "connection": ["与配方法的关系", "为根的判别做准备"]
}',
'{
  "emphasis": ["公式推导", "判别式性质"],
  "application": ["根的分布", "参数讨论"],
  "connection": ["与二次函数的关系", "复数解的概念"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'),

-- MATH_G9S1_CH21_010: 一元二次方程求根公式
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S1_CH21_010'),
'一元二次方程ax² + bx + c = 0（a ≠ 0）的求根公式是x = (-b ± √(b² - 4ac))/(2a)',
'求根公式是解一元二次方程的万能公式，它是通过配方法对一般形式的一元二次方程进行推导得出的。这个公式集中体现了方程所有可能解的情况，其中判别式b² - 4ac决定了根的性质：当判别式大于0时有两个不相等的实数根，等于0时有两个相等的实数根，小于0时没有实数根。求根公式的推导过程展现了数学推理的严密性，其应用体现了数学的实用性。',
'[
  "公式形式：x = (-b ± √(b² - 4ac))/(2a)",
  "适用条件：a ≠ 0",
  "±表示有两个解（可能相等）",
  "判别式Δ = b² - 4ac判断根的性质",
  "是配方法的一般化结果"
]',
'[
  {
    "name": "求根公式",
    "formula": "x = (-b ± √(b² - 4ac))/(2a) (a ≠ 0)",
    "description": "一元二次方程的通用解法公式"
  },
  {
    "name": "两根表示",
    "formula": "x₁ = (-b + √Δ)/(2a), x₂ = (-b - √Δ)/(2a)",
    "description": "方程的两个根的具体表达式"
  }
]',
'[
  {
    "title": "求根公式应用",
    "problem": "利用求根公式解方程：x² + 2x - 8 = 0",
    "solution": "a=1, b=2, c=-8; Δ=4+32=36; x = (-2 ± 6)/2，所以x₁ = 2, x₂ = -4",
    "analysis": "标准的公式应用过程"
  }
]',
'[
  {
    "concept": "公式结构",
    "explanation": "分子是-b±√Δ，分母是2a",
    "example": "公式的每个部分都有明确含义"
  },
  {
    "concept": "公式推导",
    "explanation": "从配方法推导而来",
    "example": "体现了数学知识的内在联系"
  },
  {
    "concept": "公式记忆",
    "explanation": "负b加减根号判别式除以2a",
    "example": "记忆口诀帮助应用"
  }
]',
'[
  "公式记忆错误",
  "符号代入出错",
  "判别式计算错误",
  "分式运算错误"
]',
'[
  "公式口诀：负b加减根号b方减4ac，全部除以2a",
  "符号注意：特别注意b的符号",
  "步骤完整：写出完整的解题步骤",
  "验证习惯：用验证方法检查答案"
]',
'{
  "emphasis": ["公式记忆", "标准应用"],
  "application": ["各类方程求解", "实际问题"],
  "connection": ["与其他解法的比较", "数学思想方法"]
}',
'{
  "emphasis": ["公式推导", "理论基础"],
  "application": ["理论研究", "高级应用"],
  "connection": ["与复数的关系", "高等数学预备"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G9S1_CH21_011: 判别式
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S1_CH21_011'),
'判别式Δ = b² - 4ac，用于判断一元二次方程根的性质',
'判别式是一元二次方程中的重要概念，它能够不解方程就判断方程根的情况。对于一元二次方程ax² + bx + c = 0（a ≠ 0），判别式Δ = b² - 4ac的值决定了方程根的性质：当Δ > 0时，方程有两个不相等的实数根；当Δ = 0时，方程有两个相等的实数根；当Δ < 0时，方程没有实数根。判别式的概念为后续学习二次函数与x轴交点问题奠定基础。',
'[
  "判别式公式：Δ = b² - 4ac",
  "Δ > 0：两个不相等的实数根",
  "Δ = 0：两个相等的实数根（重根）",
  "Δ < 0：没有实数根",
  "判别式决定根的性质"
]',
'[
  {
    "name": "判别式公式",
    "formula": "Δ = b² - 4ac",
    "description": "一元二次方程根的判别式"
  },
  {
    "name": "根的情况",
    "formula": "Δ > 0 ⟹ 两个不等实根；Δ = 0 ⟹ 两个相等实根；Δ < 0 ⟹ 无实根",
    "description": "根据判别式判断根的性质"
  }
]',
'[
  {
    "title": "利用判别式判断根的情况",
    "problem": "不解方程，判断下列方程根的情况：(1) x² - 3x + 2 = 0  (2) x² - 4x + 4 = 0  (3) x² + x + 1 = 0",
    "solution": "(1) Δ = 9 - 8 = 1 > 0，两个不等实根 (2) Δ = 16 - 16 = 0，两个相等实根 (3) Δ = 1 - 4 = -3 < 0，无实根",
    "analysis": "通过计算判别式直接判断根的性质"
  }
]',
'[
  {
    "concept": "判别式计算",
    "explanation": "按照Δ = b² - 4ac公式计算",
    "example": "对于x² - 3x + 2 = 0，Δ = (-3)² - 4×1×2 = 1"
  },
  {
    "concept": "根的分类",
    "explanation": "根据Δ的正负性分类",
    "example": "Δ > 0, Δ = 0, Δ < 0三种情况"
  },
  {
    "concept": "几何意义",
    "explanation": "抛物线与x轴交点个数",
    "example": "Δ > 0时有两个交点，Δ = 0时有一个交点，Δ < 0时无交点"
  }
]',
'[
  "计算判别式时符号错误",
  "混淆判别式的值与根的关系",
  "忘记判别式为0时的特殊情况",
  "不理解判别式的几何意义"
]',
'[
  "公式记忆：德尔塔等于b方减去4ac",
  "分类记忆：大于0两根，等于0重根，小于0无根",
  "计算细心：特别注意b的符号",
  "几何理解：联系抛物线与x轴的交点"
]',
'{
  "emphasis": ["判别式计算", "根的分类"],
  "application": ["根的判断", "参数讨论"],
  "connection": ["与求根公式的关系", "为二次函数做准备"]
}',
'{
  "emphasis": ["判别式性质", "几何意义"],
  "application": ["方程分析", "函数性质"],
  "connection": ["与二次函数图像的关系", "复数概念的引入"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'),

-- MATH_G9S1_CH21_012: 根的个数判定
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S1_CH21_012'),
'利用判别式判断一元二次方程根的个数：Δ > 0时有两个根，Δ = 0时有一个根，Δ < 0时无实数根',
'根的个数判定是判别式的重要应用，它允许我们在不求解的情况下确定方程解的个数。这种方法在解决含参数的方程问题时特别有用，可以通过讨论参数的不同取值来分析方程根的情况。掌握根的个数判定方法对于理解二次函数图像特征、解决实际应用问题都具有重要意义。',
'[
  "Δ > 0：方程有两个不相等的实数根",
  "Δ = 0：方程有一个实数根（重根）",
  "Δ < 0：方程没有实数根",
  "根的个数与判别式一一对应",
  "是判别式的直接应用"
]',
'[
  {
    "name": "根个数判定",
    "formula": "Δ > 0 ⟹ 2个根；Δ = 0 ⟹ 1个根；Δ < 0 ⟹ 0个根",
    "description": "根据判别式判断根的个数"
  }
]',
'[
  {
    "title": "判定根的个数",
    "problem": "方程kx² - 2x + 1 = 0，当k为何值时，方程有两个不等实根？",
    "solution": "当k ≠ 0时，Δ = 4 - 4k > 0，即k < 1且k ≠ 0，所以当k < 1且k ≠ 0时，方程有两个不等实根",
    "analysis": "需要考虑二次项系数不为0的条件"
  }
]',
'[
  {
    "concept": "两个不等实根",
    "explanation": "Δ > 0时的情况",
    "example": "x² - 5x + 6 = 0有两个不等实根2和3"
  },
  {
    "concept": "重根",
    "explanation": "Δ = 0时的情况，两根相等",
    "example": "x² - 4x + 4 = 0有重根x = 2"
  },
  {
    "concept": "无实根",
    "explanation": "Δ < 0时的情况",
    "example": "x² + 1 = 0在实数范围内无解"
  }
]',
'[
  "忘记检查二次项系数是否为0",
  "混淆重根与两个根的概念",
  "参数讨论时漏掉临界情况",
  "不理解实数根与复数根的区别"
]',
'[
  "系数检查：确保a ≠ 0才是二次方程",
  "分类讨论：按Δ的正负零分三种情况",
  "临界值：特别注意Δ = 0的情况",
  "范围限制：在实数范围内讨论"
]',
'{
  "emphasis": ["根个数分类", "参数讨论"],
  "application": ["含参方程", "实际问题分析"],
  "connection": ["与判别式的关系", "为函数学习做准备"]
}',
'{
  "emphasis": ["理论分析", "严格推理"],
  "application": ["数学建模", "优化问题"],
  "connection": ["与复数理论的关系", "高等数学预备"]
}',
'1.0', true, 'zh-CN', 4.7, 'approved'),

-- MATH_G9S1_CH21_013: 因式分解法
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S1_CH21_013'),
'当一元二次方程的左边能够因式分解时，可以利用\"乘积为零，因数为零\"的性质求解',
'因式分解法是解一元二次方程的重要方法之一，它基于"两个因数的乘积为零，则至少有一个因数为零"的性质。这种方法适用于能够因式分解的二次方程，通常包括提取公因数、运用乘法公式、十字相乘等技巧。因式分解法不仅能快速求解某些方程，还能帮助学生更好地理解方程根与因式的关系，为后续学习根与系数关系奠定基础。',
'[
  "适用于能因式分解的方程",
  "基于\"乘积为零，因数为零\"的性质",
  "常用分解方法：提公因数、公式法、十字相乘",
  "分解后得到两个一次方程",
  "是一种快速有效的解法"
]',
'[
  {
    "name": "因式分解基本原理",
    "formula": "ab = 0 ⟹ a = 0或b = 0",
    "description": "乘积为零的性质"
  },
  {
    "name": "常见分解形式",
    "formula": "ax² + bx + c = a(x - x₁)(x - x₂)",
    "description": "二次三项式的因式分解"
  }
]',
'[
  {
    "title": "因式分解法解方程",
    "problem": "解方程：(1) x² - 5x + 6 = 0  (2) 2x² - 8x = 0  (3) x² - 9 = 0",
    "solution": "(1) (x-2)(x-3) = 0，x = 2或x = 3  (2) 2x(x-4) = 0，x = 0或x = 4  (3) (x-3)(x+3) = 0，x = 3或x = -3",
    "analysis": "关键是正确进行因式分解"
  }
]',
'[
  {
    "concept": "提取公因数",
    "explanation": "提取最大公因数",
    "example": "2x² - 8x = 2x(x - 4)"
  },
  {
    "concept": "平方差公式",
    "explanation": "a² - b² = (a + b)(a - b)",
    "example": "x² - 9 = (x + 3)(x - 3)"
  },
  {
    "concept": "十字相乘法",
    "explanation": "对于x² + px + q型的分解",
    "example": "x² - 5x + 6 = (x - 2)(x - 3)"
  }
]',
'[
  "分解错误或不完全",
  "忘记使用乘积为零的性质",
  "分解后不会解一次方程",
  "检验时代入错误"
]',
'[
  "分解原则：先看公因数，再看公式，最后十字相乘",
  "零乘积性质：ab = 0则a = 0或b = 0",
  "验证习惯：将根代入原方程检验",
  "特殊形式：熟练掌握各种分解技巧"
]',
'{
  "emphasis": ["因式分解技巧", "零乘积性质"],
  "application": ["特殊方程求解", "快速计算"],
  "connection": ["与乘法公式的关系", "为根与系数关系做准备"]
}',
'{
  "emphasis": ["分解理论", "代数结构"],
  "application": ["高次方程", "代数式变形"],
  "connection": ["与多项式理论的关系", "抽象代数预备"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G9S1_CH21_014: 阅读与思考：黄金分割数
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S1_CH21_014'),
'黄金分割数φ = (1 + √5)/2 ≈ 1.618，是方程x² - x - 1 = 0的正根，体现数学与美学的联系',
'黄金分割数是数学中一个神奇的常数，它不仅出现在几何图形中，还广泛存在于自然界和艺术作品中。黄金分割数是一元二次方程x² - x - 1 = 0的正根，通过求根公式可得φ = (1 + √5)/2。这个数具有许多奇妙的性质，如φ² = φ + 1，1/φ = φ - 1等。学习黄金分割数能够帮助学生感受数学的美感，理解数学与自然、艺术的深刻联系。',
'[
  "黄金分割数φ = (1 + √5)/2 ≈ 1.618",
  "是方程x² - x - 1 = 0的正根",
  "具有特殊性质：φ² = φ + 1",
  "广泛存在于自然界和艺术中",
  "体现数学的美学价值"
]',
'[
  {
    "name": "黄金分割数定义",
    "formula": "φ = (1 + √5)/2",
    "description": "黄金分割数的精确值"
  },
  {
    "name": "基本性质",
    "formula": "φ² = φ + 1, 1/φ = φ - 1",
    "description": "黄金分割数的重要性质"
  },
  {
    "name": "定义方程",
    "formula": "x² - x - 1 = 0",
    "description": "黄金分割数满足的方程"
  }
]',
'[
  {
    "title": "黄金分割数的计算",
    "problem": "求解方程x² - x - 1 = 0，并验证黄金分割数的性质",
    "solution": "用求根公式：x = (1 ± √5)/2，正根φ = (1 + √5)/2 ≈ 1.618，验证：φ² = φ + 1成立",
    "analysis": "通过解方程得到黄金分割数，并验证其特殊性质"
  }
]',
'[
  {
    "concept": "黄金分割比",
    "explanation": "线段分割的特殊比例",
    "example": "长段与全长的比等于短段与长段的比"
  },
  {
    "concept": "斐波那契数列",
    "explanation": "相邻两项比值趋近于黄金分割数",
    "example": "1, 1, 2, 3, 5, 8, 13, 21..."
  },
  {
    "concept": "自然界应用",
    "explanation": "花瓣数量、螺旋结构等",
    "example": "向日葵种子排列、鹦鹉螺壳"
  }
]',
'[
  "只记住近似值而忽视精确表达",
  "不理解黄金分割的几何意义",
  "混淆黄金分割数与其他特殊数",
  "忽视数学与实际应用的联系"
]',
'[
  "精确记忆：φ = (1 + √5)/2",
  "性质理解：φ² = φ + 1的奇妙关系",
  "几何联系：理解黄金分割的几何意义",
  "文化欣赏：感受数学之美"
]',
'{
  "emphasis": ["数学文化", "美学价值"],
  "application": ["艺术设计", "自然观察"],
  "connection": ["与几何的关系", "数学史知识"]
}',
'{
  "emphasis": ["理论深度", "数学哲学"],
  "application": ["高等几何", "数学建模"],
  "connection": ["与无理数理论的关系", "连分数展开"]
}',
'1.0', true, 'zh-CN', 4.5, 'approved'),

-- MATH_G9S1_CH21_015: 选择合适的解法
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S1_CH21_015'),
'根据一元二次方程的特点选择最适合的解法：直接开平方法、配方法、公式法或因式分解法',
'选择合适的解法是解一元二次方程的重要技能，不同的方程形式适用不同的解法。一般来说：形如x² = a或(x + m)² = n的方程适用直接开平方法；能够因式分解的方程优先用因式分解法；一般形式的方程可用配方法或公式法。掌握解法选择的技巧能够提高解题效率，体现数学思维的灵活性。同时，不同解法的对比也能加深对方程本质的理解。',
'[
  "直接开平方法：适用于x² = a或(x + m)² = n型",
  "因式分解法：适用于能够分解的方程",
  "配方法：适用于所有方程，理论性强",
  "公式法：万能方法，适用于所有方程",
  "选择原则：简单优先，效率第一"
]',
'[
  {
    "name": "解法选择原则",
    "formula": "观察→分析→选择→求解",
    "description": "选择解法的基本步骤"
  }
]',
'[
  {
    "title": "选择最适合的解法",
    "problem": "选择合适方法解方程：(1) x² = 16  (2) x² - 5x + 6 = 0  (3) x² + 4x + 1 = 0",
    "solution": "(1) 直接开平方：x = ±4  (2) 因式分解：(x-2)(x-3) = 0，x = 2或3  (3) 公式法：x = (-4 ± 2√3)/2 = -2 ± √3",
    "analysis": "根据方程特点选择最简单的解法"
  }
]',
'[
  {
    "concept": "方程识别",
    "explanation": "观察方程的结构特征",
    "example": "是否为完全平方式，是否能分解等"
  },
  {
    "concept": "效率比较",
    "explanation": "不同解法的计算复杂度",
    "example": "开平方法最简单，公式法最通用"
  },
  {
    "concept": "方法对比",
    "explanation": "理解各种解法的优缺点",
    "example": "配方法有助理解，因式分解法快速直观"
  }
]',
'[
  "盲目使用公式法而忽视简单方法",
  "不会识别方程的特殊形式",
  "因式分解能力不足导致选择错误",
  "不理解不同解法的适用条件"
]',
'[
  "观察优先：先看形式再选方法",
  "简单原则：能用简单方法就不用复杂方法",
  "熟练掌握：各种解法都要熟练",
  "灵活运用：根据具体情况灵活选择"
]',
'{
  "emphasis": ["解法比较", "选择技巧"],
  "application": ["提高解题效率", "培养数学思维"],
  "connection": ["各解法的综合运用", "解题策略"]
}',
'{
  "emphasis": ["方法论思考", "优化策略"],
  "application": ["算法设计", "效率分析"],
  "connection": ["计算复杂度理论", "数值计算方法"]
}',
'1.0', true, 'zh-CN', 4.6, 'approved'),

-- ============================================
-- 21.3 实际问题与一元二次方程部分
-- ============================================

-- MATH_G9S1_CH21_016: 列一元二次方程解应用题
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S1_CH21_016'),
'通过分析实际问题中的数量关系，建立一元二次方程模型，并求解得到问题的答案',
'列一元二次方程解应用题是数学建模的重要应用，它要求学生能够从实际情境中抽象出数学关系，建立方程模型。解应用题的一般步骤包括：审题理解、设未知数、找等量关系、列方程、解方程、检验答案、回答问题。这类问题培养学生的数学建模能力和解决实际问题的能力，体现了数学的实用价值。常见的应用题类型包括几何问题、增长率问题、运动问题等。',
'[
  "审题：理解题意，明确已知和未知",
  "设元：选择合适的未知数",
  "建模：找到等量关系列出方程",
  "求解：选择合适方法解方程",
  "检验：验证解的合理性",
  "作答：回答原问题"
]',
'[
  {
    "name": "解应用题步骤",
    "formula": "审题→设元→建模→求解→检验→作答",
    "description": "解一元二次方程应用题的标准流程"
  }
]',
'[
  {
    "title": "典型应用题",
    "problem": "一个矩形的长比宽多4cm，面积是60cm²，求矩形的长和宽",
    "solution": "设宽为xcm，则长为(x+4)cm。由面积公式：x(x+4)=60，即x²+4x-60=0，解得x=6或x=-10。由于长度为正，所以宽6cm，长10cm",
    "analysis": "关键是根据几何关系建立方程，注意解的实际意义"
  }
]',
'[
  {
    "concept": "设未知数",
    "explanation": "选择恰当的量作为未知数",
    "example": "通常设要求的量或基本量为未知数"
  },
  {
    "concept": "等量关系",
    "explanation": "找到问题中的相等关系",
    "example": "面积、体积、路程等量的相等关系"
  },
  {
    "concept": "实际检验",
    "explanation": "检查解是否符合实际意义",
    "example": "长度、时间等不能为负数"
  }
]',
'[
  "审题不仔细，理解错误",
  "设未知数不当，关系复杂",
  "找不到等量关系",
  "忘记检验解的实际意义",
  "计算错误导致结果错误"
]',
'[
  "审题技巧：多读几遍，理解题意",
  "设元原则：设主要求解量为未知数",
  "建模方法：画图分析，找等量关系",
  "检验习惯：既要数学检验，也要实际检验",
  "规范作答：按步骤完整回答"
]',
'{
  "emphasis": ["建模能力", "实际应用"],
  "application": ["生活实际问题", "几何计算"],
  "connection": ["与函数应用的关系", "数学建模思想"]
}',
'{
  "emphasis": ["抽象建模", "系统思维"],
  "application": ["复杂优化问题", "工程计算"],
  "connection": ["与高等数学应用的关系", "数学建模竞赛"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G9S1_CH21_017: 传播问题
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S1_CH21_017'),
'研究信息、疾病等按照一定规律传播的问题，通常涉及每轮传播的倍数关系',
'传播问题是一元二次方程应用的经典类型，它模拟信息、疾病、谣言等在群体中的传播过程。典型的传播模型假设每个人在单位时间内可以传播给固定数量的其他人，形成指数增长模式。这类问题的数学模型通常是：如果每人每轮传播给n个人，经过m轮后总人数为a(1+n)^m。传播问题不仅有数学价值，还能帮助学生理解社会现象，培养用数学眼光观察世界的能力。',
'[
  "传播特点：每轮按固定倍数增长",
  "基本模型：初始人数×(1+传播系数)^轮数",
  "指数增长：人数快速增加",
  "实际约束：考虑总人数限制",
  "社会意义：理解信息传播规律"
]',
'[
  {
    "name": "传播模型",
    "formula": "a(1 + n)^m = 总数",
    "description": "其中a是初始人数，n是传播系数，m是传播轮数"
  }
]',
'[
  {
    "title": "信息传播问题",
    "problem": "某消息开始时有1人知道，每轮每人传播给2个人，经过几轮后共有243人知道这个消息？",
    "solution": "设经过x轮，则1×(1+2)^x = 243，即3^x = 243 = 3^5，所以x = 5轮",
    "analysis": "关键是理解传播的倍数关系和累积效应"
  }
]',
'[
  {
    "concept": "传播系数",
    "explanation": "每人每轮传播的人数",
    "example": "每人传播给2个人，系数为2"
  },
  {
    "concept": "累积效应",
    "explanation": "已知消息的人数不断累积",
    "example": "第一轮1人，第二轮3人，第三轮9人"
  },
  {
    "concept": "指数增长",
    "explanation": "人数按指数规律快速增长",
    "example": "3^1, 3^2, 3^3, 3^4, 3^5..."
  }
]',
'[
  "混淆每轮新增人数与总人数",
  "不理解累积传播的概念",
  "忽视传播的重复性",
  "计算指数运算错误"
]',
'[
  "模型理解：每轮总数是前一轮的几倍",
  "累积思维：新知道的人下轮也会传播",
  "指数计算：熟练掌握指数运算",
  "实际约束：考虑现实中的限制条件"
]',
'{
  "emphasis": ["指数增长模型", "传播规律"],
  "application": ["社会现象分析", "信息传播"],
  "connection": ["与指数函数的关系", "数学建模应用"]
}',
'{
  "emphasis": ["网络传播理论", "复杂系统"],
  "application": ["流行病学模型", "社交网络分析"],
  "connection": ["与概率统计的关系", "计算机科学应用"]
}',
'1.0', true, 'zh-CN', 4.7, 'approved'),

-- MATH_G9S1_CH21_018: 增长率问题
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S1_CH21_018'),
'研究某个量在一定增长率下经过一定时间后的变化，建立增长率方程模型',
'增长率问题是一元二次方程应用的重要类型，广泛出现在经济、人口、生物等领域。基本模型是：如果原来的量为a，平均增长率为x，经过n期后的量为a(1+x)^n。当n=2时，就得到一元二次方程。增长率问题训练学生理解复利思想、指数增长概念，培养用数学方法分析经济现象的能力。解题时要注意增长率的实际意义和取值范围。',
'[
  "基本模型：原值×(1+增长率)^期数=终值",
  "复利思想：每期都在上期基础上增长",
  "增长率通常用小数或百分数表示",
  "实际意义：增长率一般为正数",
  "应用广泛：经济、人口、生物增长等"
]',
'[
  {
    "name": "增长率公式",
    "formula": "a(1 + x)^n = b",
    "description": "其中a是原始值，x是增长率，n是期数，b是最终值"
  },
  {
    "name": "二次情况",
    "formula": "a(1 + x)² = b",
    "description": "经过两期的增长率方程"
  }
]',
'[
  {
    "title": "人口增长问题",
    "problem": "某城市现有人口50万，如果年平均增长率为x，两年后人口达到72万，求年平均增长率",
    "solution": "根据增长模型：50(1+x)² = 72，即(1+x)² = 1.44，所以1+x = 1.2，得x = 0.2 = 20%",
    "analysis": "关键是理解复利增长模式和增长率的含义"
  }
]',
'[
  {
    "concept": "复利增长",
    "explanation": "每期在前期基础上按比例增长",
    "example": "100元，10%增长率，两年后121元"
  },
  {
    "concept": "增长率范围",
    "explanation": "通常在0到1之间或用百分数表示",
    "example": "0.2表示20%的增长率"
  },
  {
    "concept": "实际检验",
    "explanation": "增长率要符合实际情况",
    "example": "人口增长率不可能过大"
  }
]',
'[
  "混淆简单增长与复利增长",
  "增长率计算错误",
  "忽视增长率的实际合理性",
  "单位换算错误"
]',
'[
  "模型记忆：a(1+x)^n = b",
  "复利理解：每期都在新基数上增长",
  "率值转换：小数与百分数的转换",
  "实际检验：结果要符合实际情况"
]',
'{
  "emphasis": ["复利思想", "增长率计算"],
  "application": ["经济计算", "人口预测"],
  "connection": ["与指数函数的关系", "金融数学基础"]
}',
'{
  "emphasis": ["数学建模", "经济分析"],
  "application": ["投资决策", "宏观经济"],
  "connection": ["与统计分析的关系", "精算数学"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'),

-- MATH_G9S1_CH21_019: 面积问题
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S1_CH21_019'),
'利用几何图形的面积公式建立一元二次方程，解决与面积相关的实际问题',
'面积问题是一元二次方程在几何中的重要应用，涉及矩形、三角形、圆形等图形的面积计算。这类问题通常给出几何图形的某些边长关系和面积大小，要求确定图形的具体尺寸。解决面积问题需要熟练掌握各种图形的面积公式，善于设置未知数，建立边长之间的关系。面积问题不仅巩固几何知识，还培养学生的空间想象能力和数形结合思想。',
'[
  "常用面积公式：矩形S=ab，三角形S=½ah，圆S=πr²",
  "设元技巧：通常设边长或半径为未知数",
  "关系建立：根据题意建立边长关系",
  "方程求解：利用面积等量关系列方程",
  "实际检验：边长必须为正数"
]',
'[
  {
    "name": "矩形面积",
    "formula": "S = 长 × 宽",
    "description": "矩形面积公式"
  },
  {
    "name": "三角形面积",
    "formula": "S = ½ × 底 × 高",
    "description": "三角形面积公式"
  },
  {
    "name": "圆面积",
    "formula": "S = πr²",
    "description": "圆面积公式"
  }
]',
'[
  {
    "title": "矩形花坛问题",
    "problem": "要建一个矩形花坛，长比宽多6米，面积为135平方米，求花坛的长和宽",
    "solution": "设宽为x米，则长为(x+6)米。由面积公式：x(x+6)=135，即x²+6x-135=0，解得x=9或x=-15。由于宽度为正，所以宽9米，长15米",
    "analysis": "根据矩形面积公式和长宽关系建立方程"
  }
]',
'[
  {
    "concept": "面积公式",
    "explanation": "各种图形的面积计算公式",
    "example": "矩形面积=长×宽"
  },
  {
    "concept": "几何关系",
    "explanation": "图形中边长、角度等的关系",
    "example": "长比宽多若干，周长与面积的关系"
  },
  {
    "concept": "实际约束",
    "explanation": "几何量的实际限制",
    "example": "长度、面积必须为正数"
  }
]',
'[
  "面积公式记忆错误",
  "几何关系理解错误",
  "设未知数不当",
  "忽视几何量的实际意义",
  "计算过程出错"
]',
'[
  "公式熟记：掌握常用面积公式",
  "关系分析：理清几何量之间的关系",
  "合理设元：选择合适的未知数",
  "实际检验：确保结果符合几何意义",
  "画图辅助：通过画图理解问题"
]',
'{
  "emphasis": ["几何公式应用", "数形结合"],
  "application": ["实际测量", "工程设计"],
  "connection": ["与几何知识的结合", "空间想象能力"]
}',
'{
  "emphasis": ["几何优化", "空间分析"],
  "application": ["建筑设计", "土地规划"],
  "connection": ["与解析几何的关系", "计算几何"]
}',
'1.0', true, 'zh-CN', 4.7, 'approved'),

-- MATH_G9S1_CH21_020: 运动问题
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S1_CH21_020'),
'利用运动学中的位移、速度、时间关系建立一元二次方程，解决运动相关问题',
'运动问题是一元二次方程在物理中的重要应用，主要涉及匀速运动和匀变速运动。对于匀速运动，基本关系是路程=速度×时间；对于匀变速运动，涉及位移公式s=v₀t+½at²。这类问题培养学生运用数学知识解决物理问题的能力，体现了学科间的联系。解题时要明确运动过程，理解各物理量的含义，注意时间、速度等的实际意义。',
'[
  "匀速运动：路程=速度×时间",
  "匀变速运动：s=v₀t+½at²",
  "相对运动：考虑速度的相对性",
  "时间关系：往返、相遇等时间关系",
  "实际意义：速度、时间必须合理"
]',
'[
  {
    "name": "匀速运动公式",
    "formula": "s = vt",
    "description": "路程等于速度乘以时间"
  },
  {
    "name": "匀变速运动公式",
    "formula": "s = v₀t + ½at²",
    "description": "位移公式，v₀是初速度，a是加速度"
  }
]',
'[
  {
    "title": "抛物运动问题",
    "problem": "从地面竖直上抛一物体，初速度20m/s，重力加速度10m/s²，多长时间落回地面？",
    "solution": "由位移公式：s = v₀t - ½gt² = 20t - 5t²。落回地面时s=0，所以20t - 5t² = 0，即t(20-5t) = 0，得t=0或t=4秒。t=0是抛出时刻，t=4秒是落地时刻",
    "analysis": "利用匀变速运动公式建立方程，注意物理意义"
  }
]',
'[
  {
    "concept": "运动公式",
    "explanation": "不同运动形式的数学描述",
    "example": "匀速、匀加速、自由落体等"
  },
  {
    "concept": "物理量",
    "explanation": "时间、速度、加速度等的含义",
    "example": "速度有方向，时间为正值"
  },
  {
    "concept": "运动过程",
    "explanation": "分析运动的全过程",
    "example": "上抛过程包括上升和下降"
  }
]',
'[
  "物理公式使用错误",
  "运动过程分析不清",
  "忽视物理量的实际意义",
  "单位换算错误",
  "时间的多解情况理解错误"
]',
'[
  "公式理解：掌握基本运动公式",
  "过程分析：理清运动的全过程",
  "物理意义：理解各物理量的含义",
  "单位统一：注意单位的一致性",
  "多解分析：理解时间解的物理意义"
]',
'{
  "emphasis": ["运动公式", "物理建模"],
  "application": ["物理问题", "工程计算"],
  "connection": ["与物理知识的结合", "实际运动分析"]
}',
'{
  "emphasis": ["动力学分析", "工程应用"],
  "application": ["机械设计", "航空航天"],
  "connection": ["与微积分的关系", "数值仿真"]
}',
'1.0', true, 'zh-CN', 4.6, 'approved'),

-- MATH_G9S1_CH21_021: 根的意义检验
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S1_CH21_021'),
'在实际应用问题中，检验一元二次方程的根是否符合实际意义和题目要求',
'根的意义检验是解应用题的重要环节，不仅要检验根是否满足原方程，还要检验根是否符合实际意义。常见的检验包括：物理量的正负性（如长度、时间不能为负）、量的大小关系（如速度的合理范围）、整数要求等。这个过程培养学生的批判性思维和实际应用意识，体现了数学与现实的联系。根的意义检验是数学建模过程中不可缺少的步骤。',
'[
  "数学检验：代入原方程验证",
  "实际检验：是否符合实际意义",
  "范围检验：是否在合理范围内",
  "条件检验：是否满足题目条件",
  "舍根原则：舍去不合题意的根"
]',
'[
  {
    "name": "检验步骤",
    "formula": "数学检验→实际检验→条件检验→结论",
    "description": "根的全面检验流程"
  }
]',
'[
  {
    "title": "速度问题的根检验",
    "problem": "船在静水中速度为15km/h，在河流中往返120km用时8.5h，求河水流速",
    "solution": "设流速为xkm/h，则：120/(15+x) + 120/(15-x) = 8.5，整理得x²-5x=0，解得x=0或x=5。x=0表示静水，x=5符合实际，所以流速为5km/h",
    "analysis": "两个根都满足方程，但需要结合实际意义选择"
  }
]',
'[
  {
    "concept": "实际约束",
    "explanation": "现实条件对解的限制",
    "example": "长度为正，速度有限制等"
  },
  {
    "concept": "题意要求",
    "explanation": "题目明确提出的条件",
    "example": "求正数解，整数解等"
  },
  {
    "concept": "舍根原则",
    "explanation": "舍去不符合要求的根",
    "example": "舍去负数长度、超范围速度等"
  }
]',
'[
  "只进行数学检验，忽视实际意义",
  "不理解题目的隐含条件",
  "舍根时没有说明理由",
  "混淆数学可能与实际合理"
]',
'[
  "全面检验：既要数学正确，也要实际合理",
  "条件分析：理解题目的显性和隐性条件",
  "舍根说明：说明舍根的具体原因",
  "实际思维：用常识判断结果的合理性"
]',
'{
  "emphasis": ["实际意义", "批判思维"],
  "application": ["应用题求解", "实际问题分析"],
  "connection": ["与实际生活的联系", "数学建模验证"]
}',
'{
  "emphasis": ["模型验证", "结果评估"],
  "application": ["工程验收", "科学研究"],
  "connection": ["与统计推断的关系", "决策科学"]
}',
'1.0', true, 'zh-CN', 4.5, 'approved'),

-- MATH_G9S1_CH21_022: 数学活动：一元二次方程的探索
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S1_CH21_022'),
'通过数学实验、探究活动深入理解一元二次方程的性质和应用，培养数学探究能力',
'数学活动是培养学生数学素养的重要途径，通过探索一元二次方程的历史、性质、应用等，让学生在实践中学习数学。活动内容可以包括：方程解法的历史演变、根与系数关系的发现、几何背景的探索、实际应用的调研等。这种学习方式能够激发学生的学习兴趣，培养数学探究能力和合作学习能力，体现数学的文化价值和实用价值。',
'[
  "历史探索：了解方程发展历程",
  "性质研究：探索方程的深层性质",
  "应用调研：寻找实际应用实例",
  "方法比较：对比不同解法的特点",
  "合作学习：培养团队协作能力"
]',
'[
  {
    "name": "探究活动类型",
    "formula": "历史→性质→应用→创新",
    "description": "数学探究的基本路径"
  }
]',
'[
  {
    "title": "根与系数关系探索",
    "problem": "探索一元二次方程ax² + bx + c = 0的两根x₁、x₂与系数a、b、c的关系",
    "solution": "通过具体例子和一般推导发现：x₁ + x₂ = -b/a，x₁ × x₂ = c/a（韦达定理）",
    "analysis": "通过归纳和演绎相结合的方法发现数学规律"
  }
]',
'[
  {
    "concept": "数学实验",
    "explanation": "通过具体计算发现规律",
    "example": "计算多个方程的根，寻找共同特征"
  },
  {
    "concept": "规律总结",
    "explanation": "从特殊到一般的归纳过程",
    "example": "从具体例子归纳出一般规律"
  },
  {
    "concept": "数学文化",
    "explanation": "了解数学知识的历史背景",
    "example": "古代数学家如何解二次方程"
  }
]',
'[
  "活动缺乏目标导向",
  "探索过程不够深入",
  "忽视规律的一般性证明",
  "缺乏反思和总结"
]',
'[
  "目标明确：确定探索的具体目标",
  "过程记录：详细记录探索过程",
  "规律总结：及时总结发现的规律",
  "反思提升：思考探索的收获和不足"
]',
'{
  "emphasis": ["探究能力", "数学文化"],
  "application": ["自主学习", "合作探索"],
  "connection": ["与数学史的联系", "培养数学素养"]
}',
'{
  "emphasis": ["创新思维", "研究方法"],
  "application": ["学术研究", "科学探索"],
  "connection": ["与科学方法论的关系", "创新能力培养"]
}',
'1.0', true, 'zh-CN', 4.4, 'approved');

-- ============================================
-- 第21章知识点详情入库完成
-- 九年级上学期第二十一章 一元二次方程 全部22个知识点详情已完整入库
-- 质量等级：★★★★★ (专家权威版)
-- 符合人民教育出版社数学九年级上册官方教材要求
-- 适用对象：九年级学生数学学习与中考复习
-- ============================================
