#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通用重复关系查找工具 - Universal Duplicate Relationship Finder
支持多种SQL文件格式，查找重复的知识点关系

使用方法:
    python find_duplicates.py [文件路径] [选项]
    
示例:
    python find_duplicates.py grade_6_internal_relationships.sql
    python find_duplicates.py my_relations.sql --verbose
    python find_duplicates.py *.sql --summary-only
"""

import re
import sys
import os
import argparse
import glob
import chardet
from collections import defaultdict
from pathlib import Path

class RelationshipDuplicateFinder:
    """重复关系查找器"""
    
    def __init__(self, verbose=False, summary_only=False):
        self.verbose = verbose
        self.summary_only = summary_only
        self.patterns = {
            # 标准知识点关系模式
            'knowledge_nodes': r"\(\(SELECT id FROM knowledge_nodes WHERE node_code = '([^']+)'\),\s*\(SELECT id FROM knowledge_nodes WHERE node_code = '([^']+)'\),\s*'([^']+)'",
            
            # 通用关系模式 (source_id, target_id, type)
            'generic_ids': r"\((\d+),\s*(\d+),\s*'([^']+)'",
            
            # 字符串关系模式 ('source', 'target', 'type')
            'string_relations': r"'([^']+)',\s*'([^']+)',\s*'([^']+)'"
        }
    
    def detect_file_encoding(self, filename):
        """检测文件编码"""
        try:
            with open(filename, 'rb') as f:
                raw_data = f.read()
            
            # 使用chardet检测编码
            detected = chardet.detect(raw_data)
            encoding = detected.get('encoding', 'utf-8')
            confidence = detected.get('confidence', 0)
            
            if self.verbose:
                print(f"[编码] 检测到编码: {encoding} (置信度: {confidence:.2f})")
            
            return encoding
        except Exception as e:
            if self.verbose:
                print(f"[警告] 编码检测失败: {e}")
            return 'utf-8'
    
    def read_file_with_encoding(self, filename):
        """尝试多种编码读取文件"""
        # 常见编码列表
        encodings = ['utf-8', 'utf-8-sig', 'gbk', 'gb2312', 'latin1', 'cp1252']
        
        # 首先尝试自动检测的编码
        detected_encoding = self.detect_file_encoding(filename)
        if detected_encoding and detected_encoding not in encodings:
            encodings.insert(0, detected_encoding)
        
        for encoding in encodings:
            try:
                with open(filename, 'r', encoding=encoding) as f:
                    content = f.read()
                
                if self.verbose:
                    print(f"[成功] 使用编码 {encoding} 成功读取文件")
                return content
                
            except UnicodeDecodeError:
                if self.verbose:
                    print(f"[失败] 编码 {encoding} 读取失败")
                continue
            except Exception as e:
                if self.verbose:
                    print(f"[错误] 使用编码 {encoding} 时出错: {e}")
                continue
        
        raise Exception(f"无法使用任何编码读取文件: {filename}")
    
    def detect_pattern(self, content):
        """自动检测文件中使用的关系模式"""
        for pattern_name, pattern in self.patterns.items():
            matches = re.findall(pattern, content, re.MULTILINE | re.DOTALL)
            if matches:
                if self.verbose:
                    print(f"[检测] 检测到模式: {pattern_name} (找到 {len(matches)} 个匹配)")
                return pattern_name, pattern
        
        print("[警告] 未检测到已知的关系模式")
        return None, None
    
    def extract_relationships(self, filename):
        """从SQL文件中提取所有关系"""
        if not os.path.exists(filename):
            raise FileNotFoundError(f"文件不存在: {filename}")
        
        # 使用改进的文件读取方法
        content = self.read_file_with_encoding(filename)
        
        # 自动检测模式
        pattern_name, pattern = self.detect_pattern(content)
        if not pattern:
            return []
        
        matches = re.findall(pattern, content, re.MULTILINE | re.DOTALL)
        relationships = []
        
        for i, match in enumerate(matches):
            if len(match) >= 3:
                source, target, rel_type = match[0], match[1], match[2]
                relationships.append({
                    'index': i + 1,
                    'source': source,
                    'target': target,
                    'type': rel_type,
                    'key': f"{source}|{target}|{rel_type}",
                    'pattern': pattern_name
                })
        
        return relationships
    
    def find_duplicates(self, relationships):
        """查找重复关系"""
        seen = defaultdict(list)
        
        for rel in relationships:
            seen[rel['key']].append(rel)
        
        duplicates = {k: v for k, v in seen.items() if len(v) > 1}
        return duplicates
    
    def print_summary(self, filename, relationships, duplicates):
        """打印摘要信息"""
        print(f"[文件] 分析文件: {Path(filename).name}")
        print("=" * 60)
        print(f"[统计] 总关系数量: {len(relationships)}")
        
        if duplicates:
            total_duplicates = sum(len(rels) - 1 for rels in duplicates.values())
            print(f"[错误] 发现 {len(duplicates)} 组重复关系 (共 {total_duplicates} 个重复项)")
        else:
            print("[成功] 未发现重复关系")
    
    def print_duplicates(self, duplicates):
        """详细打印重复关系"""
        if not duplicates:
            return
            
        print(f"\n[详情] 重复关系详情:")
        for i, (key, rels) in enumerate(duplicates.items(), 1):
            source, target, rel_type = key.split('|')
            print(f"\n{i}. 重复关系: {source} → {target} ({rel_type})")
            print(f"   出现次数: {len(rels)}")
            
            if self.verbose:
                for rel in rels:
                    print(f"   - 位置 {rel['index']} [{rel['pattern']}]")
            else:
                positions = [str(rel['index']) for rel in rels]
                print(f"   - 位置: {', '.join(positions)}")
    
    def print_statistics(self, relationships):
        """打印统计信息"""
        if not relationships:
            return
            
        # 关系类型统计
        type_counts = defaultdict(int)
        pattern_counts = defaultdict(int)
        
        for rel in relationships:
            type_counts[rel['type']] += 1
            pattern_counts[rel['pattern']] += 1
        
        print(f"\n[统计] 关系类型统计:")
        for rel_type, count in sorted(type_counts.items()):
            percentage = (count / len(relationships)) * 100
            print(f"  {rel_type}: {count} ({percentage:.1f}%)")
        
        if self.verbose and len(pattern_counts) > 1:
            print(f"\n[模式] 模式统计:")
            for pattern, count in sorted(pattern_counts.items()):
                print(f"  {pattern}: {count}")
    
    def analyze_file(self, filename):
        """分析单个文件"""
        try:
            relationships = self.extract_relationships(filename)
            duplicates = self.find_duplicates(relationships)
            
            # 打印结果
            self.print_summary(filename, relationships, duplicates)
            
            if not self.summary_only:
                self.print_duplicates(duplicates)
                self.print_statistics(relationships)
            
            return len(duplicates) == 0  # 返回True表示没有重复
            
        except Exception as e:
            print(f"[错误] 处理文件 {filename} 时出错: {e}")
            if self.verbose:
                import traceback
                traceback.print_exc()
            return False
    
    def analyze_files(self, file_patterns):
        """分析多个文件"""
        all_files = []
        
        # 展开文件模式
        for pattern in file_patterns:
            if '*' in pattern or '?' in pattern:
                all_files.extend(glob.glob(pattern))
            else:
                all_files.append(pattern)
        
        if not all_files:
            print("[错误] 没有找到匹配的文件")
            return False
        
        success_count = 0
        total_files = len(all_files)
        
        for i, filename in enumerate(all_files):
            if total_files > 1:
                print(f"\n{'='*20} 文件 {i+1}/{total_files} {'='*20}")
            
            if self.analyze_file(filename):
                success_count += 1
        
        # 多文件总结
        if total_files > 1:
            print(f"\n{'='*50}")
            print(f"[结果] 处理完成: {success_count}/{total_files} 个文件无重复关系")
        
        return success_count == total_files

def main():
    parser = argparse.ArgumentParser(
        description="通用重复关系查找工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  %(prog)s grade_6_internal_relationships.sql          # 分析单个文件
  %(prog)s *.sql --verbose                            # 分析所有SQL文件(详细模式)
  %(prog)s file1.sql file2.sql --summary-only        # 只显示摘要
  %(prog)s relations.sql -v -s                       # 简洁详细模式
        """
    )
    
    parser.add_argument(
        'files', 
        nargs='*', 
        default=['grade_6_internal_relationships.sql'],
        help='要分析的SQL文件路径 (支持通配符)'
    )
    
    parser.add_argument(
        '-v', '--verbose',
        action='store_true',
        help='详细输出模式'
    )
    
    parser.add_argument(
        '-s', '--summary-only',
        action='store_true',
        help='只显示摘要信息'
    )
    
    parser.add_argument(
        '--version',
        action='version',
        version='%(prog)s 2.1.0'
    )
    
    args = parser.parse_args()
    
    # 创建查找器实例
    finder = RelationshipDuplicateFinder(
        verbose=args.verbose,
        summary_only=args.summary_only
    )
    
    # 分析文件
    success = finder.analyze_files(args.files)
    
    # 返回适当的退出码
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main() 