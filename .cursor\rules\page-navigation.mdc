---
description: 
globs: 
alwaysApply: false
---
# 页面导航规范

本文档定义了小程序中页面导航的最佳实践和规范，确保统一的导航体验和避免常见的导航问题。

## 目录

1. [统一导航工具](mdc:#统一导航工具)
2. [动态跳转模式](mdc:#动态跳转模式)
3. [页面复用检测](mdc:#页面复用检测)
4. [页面参数传递](mdc:#页面参数传递)
5. [返回行为处理](mdc:#返回行为处理)
6. [标签页切换](mdc:#标签页切换)
7. [常见导航问题](mdc:#常见导航问题)

## 统一导航工具

### 使用navigator.js

本项目使用 [utils/navigator.js](mdc:utils/navigator.js) 作为统一的页面导航工具，提供以下功能：

- 页面路径常量化管理
- 智能页面栈管理
- 参数编码和处理
- 统一错误处理
- 防止重复页面

```javascript
// 导入导航工具
const navigator = require('../../utils/navigator.js');

// 基本导航
navigator.navigateTo(navigator.PATHS.KNOWLEDGE_DETAIL);

// 带参数导航
navigator.navigateTo(navigator.PATHS.KNOWLEDGE_DETAIL, {
  id: '123',
  type: 'concept'
});

// 带选项导航
navigator.navigateTo(navigator.PATHS.KNOWLEDGE_DETAIL, { id: '123' }, {
  isTabPage: false,
  stackThreshold: 8,
  errorMessage: '页面无法访问',
  onFail: function(err) {
    console.error('导航失败', err);
  }
});
```

### 导航方法

`navigator.js` 提供以下导航方法：

- **navigateTo**：智能导航到新页面，保留当前页面
- **redirectTo**：关闭当前页面，导航到新页面
- **reLaunch**：关闭所有页面，打开新页面
- **navigateBack**：返回上一页或多级页面
- **checkAndNavigateBack**：检查并返回到已存在的页面

## 动态跳转模式

为避免微信小程序页面栈溢出问题，`navigator.js` 实现了动态跳转模式选择：

```javascript
// 动态跳转模式选择：根据页面栈深度自动选择合适的导航方法
const stackDepth = pages.length;
const maxStackDepth = 10; // 微信小程序的最大页面栈深度
const redirectThreshold = options.stackThreshold || 9;

if (stackDepth >= maxStackDepth) {
  // 页面栈已达最大限制，使用reLaunch重置路由栈
  wx.reLaunch({ url: targetUrl });
} else if (stackDepth >= redirectThreshold) {
  // 接近页面栈限制，使用redirectTo
  wx.redirectTo({ url: targetUrl });
} else {
  // 正常情况，使用navigateTo
  wx.navigateTo({ url: targetUrl });
}
```

这种机制确保了：

1. 当页面栈深度 < 9 时：使用 `wx.navigateTo` 正常导航
2. 当页面栈深度 ≥ 9 且 < 10 时：自动切换为 `wx.redirectTo` 进行页面替换
3. 当页面栈深度 ≥ 10 时：使用 `wx.reLaunch` 强制重置路由栈

开发者可以通过 `stackThreshold` 选项自定义触发 redirectTo 的阈值：

```javascript
// 自定义阈值为8
navigator.navigateTo(navigator.PATHS.DETAIL, { id: '123' }, { 
  stackThreshold: 8 
});
```

## 页面复用检测

为避免重复打开相同页面，`navigator.js` 实现了页面复用检测：

```javascript
// 页面复用检测：检查目标页面是否已在页面栈中
if (!options.forceNavigate && checkAndNavigateBack(url)) {
  return; // 已找到目标页面并返回，无需继续导航
}
```

检测的具体逻辑：

1. 在跳转前遍历当前页面栈中的所有页面
2. 比较目标页面路径与栈中页面路径
3. 如果找到匹配的页面，计算需要返回的层级(delta)
4. 使用 `wx.navigateBack({ delta })` 直接返回到已存在的页面

这种机制避免了重复打开同一个页面，减少了内存占用和页面栈深度。

如果需要强制打开新页面（即使目标页面已在栈中），可以使用 `forceNavigate` 选项：

```javascript
// 强制打开新页面，不检查是否已存在
navigator.navigateTo(navigator.PATHS.DETAIL, { id: '123' }, { 
  forceNavigate: true 
});
```

## 页面参数传递

### 参数编码

`navigator.js` 自动处理参数编码，避免特殊字符问题：

```javascript
// 内部会自动处理参数编码
navigator.navigateTo(navigator.PATHS.KNOWLEDGE_DETAIL, {
  title: '函数与方程',
  desc: '包含x、y的等式f(x,y)=0'  // 特殊字符会被自动编码
});
```

### 复杂参数

对于复杂参数，可以使用以下方法：

1. **序列化传递**：
```javascript
// 发送方
const complexData = { list: [1,2,3], obj: { a: 1, b: 2 } };
navigator.navigateTo(navigator.PATHS.DETAIL, {
  data: JSON.stringify(complexData)
});

// 接收方
Page({
  onLoad(options) {
    const complexData = JSON.parse(options.data || '{}');
    this.setData({ list: complexData.list });
  }
});
```

2. **全局状态传递**：
```javascript
// 发送方
const app = getApp();
app.globalData.tempData = complexData;
navigator.navigateTo(navigator.PATHS.DETAIL, { dataId: 'temp1' });

// 接收方
Page({
  onLoad(options) {
    const app = getApp();
    const data = app.globalData.tempData;
    // 使用完后及时清理全局数据
    app.globalData.tempData = null;
  }
});
```

## 返回行为处理

### 自定义返回行为

处理物理返回键和导航栏返回按钮：

```javascript
Page({
  // 处理物理返回键和导航栏返回按钮
  onBackPress() {
    // 返回前执行一些逻辑
    if (this.data.hasUnsavedChanges) {
      wx.showModal({
        title: '提示',
        content: '有未保存的更改，确定要离开吗？',
        success: (res) => {
          if (res.confirm) {
            navigator.navigateBack();
          }
        }
      });
      return true; // 阻止默认返回行为
    }
    return false; // 使用默认返回行为
  },
  
  // 自定义返回按钮点击事件
  onCustomBackTap() {
    // 保存数据
    this.saveData().then(() => {
      navigator.navigateBack();
    });
  }
});
```

### 返回到指定页面

使用 `checkAndNavigateBack` 方法返回到指定页面：

```javascript
// 返回到首页
const isBackToHome = navigator.checkAndNavigateBack(navigator.PATHS.INDEX);

// 如果首页不在页面栈中，使用reLaunch回到首页
if (!isBackToHome) {
  navigator.reLaunch(navigator.PATHS.INDEX);
}
```

## 标签页切换

### TabBar页面导航

对于TabBar页面，需要使用特殊处理：

```javascript
// 导航到TabBar页面
navigator.navigateTo(navigator.PATHS.AI, {}, { isTabPage: true });

// 内部实现会使用switchTab
if (options.isTabPage) {
  wx.switchTab({
    url: targetUrl,
    fail: handleNavigationFail(targetUrl, options)
  });
  return;
}
```

### 导航到特定标签页并显示特定内容

有时需要导航到TabBar页面并显示特定内容：

```javascript
// 导航到AI助手页面并打开历史记录标签
navigator.navigateTo(navigator.PATHS.AI_ASSISTANT, { tab: 'history' }, { isTabPage: true });

// 在目标页面的onLoad中处理
Page({
  onLoad(options) {
    if (options.tab) {
      this.switchTab(options.tab);
    }
  },
  switchTab(tabName) {
    this.setData({ currentTab: tabName });
  }
});
```

## 常见导航问题

### 页面栈溢出

微信小程序限制页面栈最大深度为10层。超过此限制会导致导航失败。

**问题表现**：
- 页面无法正常打开
- 控制台报错"页面栈层数超过10层限制"

**解决方案**：
1. 使用本项目的动态跳转模式自动处理
2. 在关键节点使用redirectTo替代navigateTo
3. 优化页面结构，减少深层导航需求

### 参数长度限制

微信小程序URL参数有长度限制。

**问题表现**：
- 参数无法完整传递
- 页面加载异常

**解决方案**：
1. 使用全局状态管理传递大型数据
2. 数据本地存储后，仅传递索引或ID
3. 拆分大型数据为多个小参数

### 页面路径错误

导航到不存在的页面会导致错误。

**问题表现**：
- 页面无法打开
- 控制台报错"页面不存在"

**解决方案**：
1. 使用路径常量而非手动输入路径
2. 导航前验证页面路径有效性
3. 添加错误处理，在导航失败时提供友好的用户提示

### 频繁重复导航

重复导航到同一页面会增加页面栈深度和内存占用。

**问题表现**：
- 返回时需要多次点击才能回到起始页面
- 应用性能下降，内存占用增加

**解决方案**：
1. 使用页面复用检测避免重复打开页面
2. 在需要刷新数据时使用页面生命周期事件而非重新打开页面
3. 合理使用redirectTo替代navigateTo

### 返回行为不一致

自定义导航可能导致返回行为不一致。

**问题表现**：
- 物理返回键和界面返回按钮行为不一致
- 返回后数据状态不一致

**解决方案**：
1. 统一使用navigator.js提供的导航方法
2. 实现一致的onBackPress处理
3. 在关键页面记录返回路径



