.typewriter-container {
  display: inline-flex;
  align-items: flex-start;
  position: relative;
  width: 100%;
}

/* 打字机效果样式 */
.typewriter-text {
  word-break: break-word;
  white-space: pre-wrap;
  line-height: 1.5;
  width: 100%;
  display: inline-block;
}

/* 光标样式 */
.typewriter-cursor {
  display: inline-block;
  width: 2px;
  height: 1.2em;
  margin-left: 2px;
  background-color: #333;
  animation: blink 0.8s infinite;
  vertical-align: middle;
  flex-shrink: 0;
}

.typewriter-char {
  transition: all 0.2s ease;
}

.typewriter-char.highlight {
  color: #3E7BFA;
  font-weight: bold;
}

@keyframes blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0; }
}

/* 高亮模式样式 */
.highlight-text {
  display: flex;
  flex-wrap: wrap;
}

.highlight-text .text-item {
  transition: color 0.2s ease;
}

.highlight-text .highlight {
  color: #3E7BFA;
  font-weight: bold;
}

/* 淡入效果 */
.fade-in {
  animation: fadeIn 1s ease forwards;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
} 