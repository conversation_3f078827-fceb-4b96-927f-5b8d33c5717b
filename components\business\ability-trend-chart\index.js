// 能力发展历史趋势图组件
Component({
  properties: {
    // 趋势数据
    trendData: {
      type: Object,
      value: null,
      observer: function(newVal) {
        if (newVal && newVal.datasets && newVal.datasets.length > 0) {
          this.drawTrendChart();
        }
      }
    },
    // 图表宽度
    width: {
      type: Number,
      value: 350
    },
    // 图表高度  
    height: {
      type: Number,
      value: 200
    },
    // 时间范围
    timeRange: {
      type: String,
      value: '3months' // 1month, 3months, 6months, 1year
    },
    // 显示的能力维度
    selectedDimensions: {
      type: Array,
      value: ['计算能力', '推理能力', '空间想象', '数据分析', '应用能力', '逻辑思维']
    },
    // 主题色彩
    colorScheme: {
      type: Array,
      value: ['#3E7BFA', '#42B983', '#F5A623', '#E64340', '#9C27B0', '#FF5722']
    }
  },

  data: {
    svgContent: '',
    chartReady: false,
    chartHeight: 200,
    maxValue: 100,
    minValue: 0,
    
    // 交互状态
    hoveredPoint: null,
    showTooltip: false,
    tooltipData: null,
    
    // 里程碑数据
    milestones: [],
    showMilestones: true
  },

  lifetimes: {
    ready() {
      if (this.properties.trendData) {
        this.drawTrendChart();
      }
    }
  },

  methods: {
    /**
     * 绘制趋势图表
     */
    drawTrendChart() {
      const data = this.properties.trendData;
      if (!data || !data.datasets || data.datasets.length === 0) {
        console.warn('趋势图数据为空');
        return;
      }

      try {
        const processedData = this.processData(data);
        const svgString = this.generateSvg(processedData);
        
        this.setData({
          svgContent: svgString,
          chartReady: true
        });
        
        this.triggerEvent('chartReady');
      } catch (error) {
        console.error('绘制趋势图失败:', error);
      }
    },

    /**
     * 处理图表数据
     */
    processData(rawData) {
      const { datasets, dates, milestones } = rawData;
      const selectedDims = this.properties.selectedDimensions;
      
      // 过滤选中的维度
      const filteredDatasets = datasets.filter(dataset => 
        selectedDims.includes(dataset.label)
      );

      // 计算数值范围
      let minVal = 100, maxVal = 0;
      filteredDatasets.forEach(dataset => {
        dataset.data.forEach(value => {
          if (value !== null && value !== undefined) {
            minVal = Math.min(minVal, value);
            maxVal = Math.max(maxVal, value);
          }
        });
      });

      // 添加一些边距
      const margin = (maxVal - minVal) * 0.1;
      minVal = Math.max(0, minVal - margin);
      maxVal = Math.min(100, maxVal + margin);

      this.setData({
        maxValue: maxVal,
        minValue: minVal,
        milestones: milestones || []
      });

      return {
        datasets: filteredDatasets,
        dates: dates || [],
        dateRange: this.generateDateRange(),
        minValue: minVal,
        maxValue: maxVal
      };
    },

    /**
     * 生成日期范围
     */
    generateDateRange() {
      const range = this.properties.timeRange;
      const endDate = new Date();
      let startDate = new Date();
      
      switch(range) {
        case '1month':
          startDate.setMonth(endDate.getMonth() - 1);
          break;
        case '3months':
          startDate.setMonth(endDate.getMonth() - 3);
          break;
        case '6months':
          startDate.setMonth(endDate.getMonth() - 6);
          break;
        case '1year':
          startDate.setFullYear(endDate.getFullYear() - 1);
          break;
        default:
          startDate.setMonth(endDate.getMonth() - 3);
      }
      
      return { startDate, endDate };
    },

    /**
     * 生成SVG内容
     */
    generateSvg(data) {
      const { width, height } = this.properties;
      const padding = { top: 20, right: 30, bottom: 40, left: 50 };
      const chartWidth = width - padding.left - padding.right;
      const chartHeight = height - padding.top - padding.bottom;
      
      let svg = `
        <svg width="${width}" height="${height}" viewBox="0 0 ${width} ${height}" xmlns="http://www.w3.org/2000/svg">
          <defs>
            ${this.generateGradients()}
            ${this.generateFilters()}
          </defs>
          
          <!-- 背景网格 -->
          ${this.generateGrid(padding, chartWidth, chartHeight, data)}
          
          <!-- Y轴标签 -->
          ${this.generateYAxisLabels(padding, chartHeight, data)}
          
          <!-- X轴标签 -->
          ${this.generateXAxisLabels(padding, chartWidth, chartHeight, data)}
          
          <!-- 数据线条 -->
          ${this.generateDataLines(padding, chartWidth, chartHeight, data)}
          
          <!-- 数据点 -->
          ${this.generateDataPoints(padding, chartWidth, chartHeight, data)}
          
          <!-- 里程碑标记 -->
          ${this.generateMilestones(padding, chartWidth, chartHeight, data)}
          
          <!-- 图例 -->
          ${this.generateLegend(width, data)}
        </svg>
      `;
      
      return svg;
    },

    /**
     * 生成渐变定义
     */
    generateGradients() {
      const colors = this.properties.colorScheme;
      return colors.map((color, index) => `
        <linearGradient id="gradient${index}" x1="0%" y1="0%" x2="0%" y2="100%">
          <stop offset="0%" style="stop-color:${color};stop-opacity:0.3" />
          <stop offset="100%" style="stop-color:${color};stop-opacity:0.1" />
        </linearGradient>
      `).join('');
    },

    /**
     * 生成滤镜效果
     */
    generateFilters() {
      return `
        <filter id="dropshadow" x="-20%" y="-20%" width="140%" height="140%">
          <feDropShadow dx="0" dy="2" stdDeviation="3" flood-opacity="0.1"/>
        </filter>
      `;
    },

    /**
     * 生成网格
     */
    generateGrid(padding, chartWidth, chartHeight, data) {
      const { minValue, maxValue } = data;
      const ySteps = 5;
      const xSteps = Math.min(data.dates.length, 8);
      
      let grid = '';
      
      // 水平网格线
      for (let i = 0; i <= ySteps; i++) {
        const y = padding.top + (chartHeight / ySteps) * i;
        grid += `<line x1="${padding.left}" y1="${y}" x2="${padding.left + chartWidth}" y2="${y}" stroke="#E5E7EB" stroke-width="1" opacity="0.5"/>`;
      }
      
      // 垂直网格线
      for (let i = 0; i <= xSteps; i++) {
        const x = padding.left + (chartWidth / xSteps) * i;
        grid += `<line x1="${x}" y1="${padding.top}" x2="${x}" y2="${padding.top + chartHeight}" stroke="#E5E7EB" stroke-width="1" opacity="0.3"/>`;
      }
      
      return grid;
    },

    /**
     * 生成Y轴标签
     */
    generateYAxisLabels(padding, chartHeight, data) {
      const { minValue, maxValue } = data;
      const steps = 5;
      let labels = '';
      
      for (let i = 0; i <= steps; i++) {
        const value = maxValue - (maxValue - minValue) * (i / steps);
        const y = padding.top + (chartHeight / steps) * i;
        
        labels += `
          <text x="${padding.left - 10}" y="${y + 4}" 
                text-anchor="end" font-size="10" fill="#6B7280">
            ${Math.round(value)}
          </text>
        `;
      }
      
      return labels;
    },

    /**
     * 生成X轴标签
     */
    generateXAxisLabels(padding, chartWidth, chartHeight, data) {
      const dates = data.dates;
      if (!dates || dates.length === 0) return '';
      
      let labels = '';
      const step = Math.max(1, Math.floor(dates.length / 6));
      
      dates.forEach((date, index) => {
        if (index % step === 0 || index === dates.length - 1) {
          const x = padding.left + (chartWidth / (dates.length - 1)) * index;
          const y = padding.top + chartHeight + 20;
          
          labels += `
            <text x="${x}" y="${y}" 
                  text-anchor="middle" font-size="9" fill="#6B7280">
              ${this.formatDate(date)}
            </text>
          `;
        }
      });
      
      return labels;
    },

    /**
     * 生成数据线条
     */
    generateDataLines(padding, chartWidth, chartHeight, data) {
      const { datasets, dates, minValue, maxValue } = data;
      const colors = this.properties.colorScheme;
      let lines = '';
      
      datasets.forEach((dataset, datasetIndex) => {
        const color = colors[datasetIndex % colors.length];
        let pathData = '';
        let areaData = '';
        
        dataset.data.forEach((value, pointIndex) => {
          if (value !== null && value !== undefined) {
            const x = padding.left + (chartWidth / (dates.length - 1)) * pointIndex;
            const y = padding.top + chartHeight - ((value - minValue) / (maxValue - minValue)) * chartHeight;
            
            if (pathData === '') {
              pathData = `M ${x} ${y}`;
              areaData = `M ${x} ${padding.top + chartHeight} L ${x} ${y}`;
            } else {
              pathData += ` L ${x} ${y}`;
              areaData += ` L ${x} ${y}`;
            }
          }
        });
        
        // 完成面积路径
        const lastX = padding.left + chartWidth;
        areaData += ` L ${lastX} ${padding.top + chartHeight} Z`;
        
        // 面积填充
        lines += `
          <path d="${areaData}" 
                fill="url(#gradient${datasetIndex})" 
                opacity="0.3"/>
        `;
        
        // 线条
        lines += `
          <path d="${pathData}" 
                stroke="${color}" 
                stroke-width="2.5" 
                fill="none" 
                filter="url(#dropshadow)"
                stroke-linecap="round" 
                stroke-linejoin="round"/>
        `;
      });
      
      return lines;
    },

    /**
     * 生成数据点
     */
    generateDataPoints(padding, chartWidth, chartHeight, data) {
      const { datasets, dates, minValue, maxValue } = data;
      const colors = this.properties.colorScheme;
      let points = '';
      
      datasets.forEach((dataset, datasetIndex) => {
        const color = colors[datasetIndex % colors.length];
        
        dataset.data.forEach((value, pointIndex) => {
          if (value !== null && value !== undefined) {
            const x = padding.left + (chartWidth / (dates.length - 1)) * pointIndex;
            const y = padding.top + chartHeight - ((value - minValue) / (maxValue - minValue)) * chartHeight;
            
            points += `
              <circle cx="${x}" cy="${y}" r="4" 
                      fill="white" 
                      stroke="${color}" 
                      stroke-width="2"
                      filter="url(#dropshadow)"
                      data-value="${value}"
                      data-date="${dates[pointIndex]}"
                      data-dimension="${dataset.label}"/>
            `;
          }
        });
      });
      
      return points;
    },

    /**
     * 生成里程碑标记
     */
    generateMilestones(padding, chartWidth, chartHeight, data) {
      if (!this.data.showMilestones || !this.data.milestones.length) {
        return '';
      }
      
      let milestones = '';
      const { dates, minValue, maxValue } = data;
      
      this.data.milestones.forEach(milestone => {
        const dateIndex = dates.findIndex(date => date === milestone.date);
        if (dateIndex >= 0) {
          const x = padding.left + (chartWidth / (dates.length - 1)) * dateIndex;
          const iconY = padding.top - 10;
          
          // 根据里程碑类型选择图标
          const icon = this.getMilestoneIcon(milestone.type);
          const color = this.getMilestoneColor(milestone.type);
          
          milestones += `
            <line x1="${x}" y1="${padding.top}" x2="${x}" y2="${padding.top + chartHeight}" 
                  stroke="${color}" stroke-width="1" stroke-dasharray="3,3" opacity="0.7"/>
            <circle cx="${x}" cy="${iconY}" r="6" fill="${color}" opacity="0.9"/>
            <text x="${x}" y="${iconY + 3}" text-anchor="middle" font-size="8" fill="white">${icon}</text>
          `;
        }
      });
      
      return milestones;
    },

    /**
     * 生成图例
     */
    generateLegend(width, data) {
      const datasets = data.datasets;
      const colors = this.properties.colorScheme;
      let legend = '';
      
      const legendY = 15;
      const itemWidth = 80;
      const startX = (width - datasets.length * itemWidth) / 2;
      
      datasets.forEach((dataset, index) => {
        const x = startX + index * itemWidth;
        const color = colors[index % colors.length];
        
        legend += `
          <rect x="${x}" y="${legendY - 2}" width="12" height="2" fill="${color}"/>
          <text x="${x + 18}" y="${legendY + 2}" font-size="10" fill="#374151">${dataset.label}</text>
        `;
      });
      
      return legend;
    },

    /**
     * 格式化日期
     */
    formatDate(dateStr) {
      const date = new Date(dateStr);
      const month = date.getMonth() + 1;
      const day = date.getDate();
      return `${month}/${day}`;
    },

    /**
     * 获取里程碑图标
     */
    getMilestoneIcon(type) {
      const icons = {
        'breakthrough': '↗',
        'plateau': '→',
        'regression': '↘',
        'recovery': '↗'
      };
      return icons[type] || '•';
    },

    /**
     * 获取里程碑颜色
     */
    getMilestoneColor(type) {
      const colors = {
        'breakthrough': '#42B983',
        'plateau': '#F5A623',
        'regression': '#E64340',
        'recovery': '#3E7BFA'
      };
      return colors[type] || '#9CA3AF';
    },

    /**
     * 切换时间范围
     */
    onTimeRangeChange(e) {
      const timeRange = e.detail.value;
      this.triggerEvent('timeRangeChange', { timeRange });
    },

    /**
     * 切换显示维度
     */
    onDimensionToggle(e) {
      const dimension = e.currentTarget.dataset.dimension;
      const selectedDimensions = [...this.properties.selectedDimensions];
      const index = selectedDimensions.indexOf(dimension);
      
      if (index > -1) {
        selectedDimensions.splice(index, 1);
      } else {
        selectedDimensions.push(dimension);
      }
      
      this.triggerEvent('dimensionChange', { selectedDimensions });
    },

    /**
     * 切换里程碑显示
     */
    onToggleMilestones() {
      this.setData({
        showMilestones: !this.data.showMilestones
      });
      this.drawTrendChart();
    },

    /**
     * 导出图表
     */
    onExportChart() {
      this.triggerEvent('exportChart', {
        svgContent: this.data.svgContent,
        chartType: 'trend'
      });
    }
  }
}); 