"use strict";
function CustomKatex() {}

// 获取原始KaTeX模块
var originalKatex = require("./katex.min");

// 创建一个修改过的渲染函数，设置strict为ignore
function renderWithOptions(latex, displayMode) {
  try {
    // 使用忽略严格模式的渲染配置
    return originalKatex.default(latex, {
      strict: 'ignore',
      displayMode: displayMode || false
    });
  } catch (e) {
    // 即使出错也返回原始公式文本
    console.log('KaTeX渲染错误：', e.message);
    return "<span style='color:red;'>" + latex + "</span>";
  }
}

// 导出自定义KaTeX模块
module.exports = {
  default: renderWithOptions
}; 