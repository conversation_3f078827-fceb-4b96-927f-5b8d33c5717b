/* pages/test/knowledge-graph-test.wxss */
.container {
  padding: 40rpx;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* 页面标题 */
.page-header {
  text-align: center;
  margin-bottom: 60rpx;
}

.page-title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #fff;
  margin-bottom: 20rpx;
}

.page-subtitle {
  display: block;
  font-size: 28rpx;
  color: #f0f0f0;
  opacity: 0.9;
}

/* 操作按钮区 */
.action-section {
  display: flex;
  gap: 30rpx;
  margin-bottom: 60rpx;
  justify-content: center;
}

.action-btn {
  flex: 1;
  max-width: 300rpx;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 32rpx;
  font-weight: 600;
  border: none;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.action-btn.primary {
  background: linear-gradient(45deg, #ff6b6b, #ee5a24);
  color: #fff;
}

.action-btn.secondary {
  background: #fff;
  color: #667eea;
}

.action-btn:not([disabled]):active {
  transform: translateY(4rpx);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.3);
}

/* 测试区域 */
.test-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 30rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 16rpx 64rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  text-align: center;
}

.test-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  justify-content: space-between;
}

.test-btn {
  width: 48%;
  height: 70rpx;
  background: linear-gradient(45deg, #667eea, #764ba2);
  color: #fff;
  border: none;
  border-radius: 35rpx;
  font-size: 28rpx;
  font-weight: 500;
  box-shadow: 0 6rpx 20rpx rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.test-btn:not([disabled]):active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.4);
}

.test-btn[disabled] {
  opacity: 0.6;
  background: #ccc;
}

/* 结果区域 */
.results-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 30rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 16rpx 64rpx rgba(0, 0, 0, 0.1);
}

.results-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.result-item {
  background: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  border-left: 8rpx solid #ccc;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.result-item.success {
  border-left-color: #27ae60;
}

.result-item.error {
  border-left-color: #e74c3c;
}

.result-item:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.result-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.result-status {
  width: 50rpx;
  height: 50rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 28rpx;
  font-weight: bold;
}

.result-status.success {
  background: #27ae60;
}

.result-status.error {
  background: #e74c3c;
}

.result-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.result-time {
  font-size: 24rpx;
  color: #666;
}

.result-tip {
  font-size: 24rpx;
  color: #999;
  font-style: italic;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 100rpx 40rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 30rpx;
  margin-bottom: 40rpx;
}

.empty-text {
  display: block;
  font-size: 36rpx;
  color: #666;
  margin-bottom: 20rpx;
}

.empty-hint {
  display: block;
  font-size: 28rpx;
  color: #999;
}

/* 加载状态 */
.loading-state {
  text-align: center;
  padding: 80rpx 40rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 30rpx;
  margin-bottom: 40rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 30rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  display: block;
  font-size: 28rpx;
  color: #666;
}

/* 数据库信息 */
.db-info {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 30rpx;
  padding: 40rpx;
  box-shadow: 0 16rpx 64rpx rgba(0, 0, 0, 0.1);
}

.db-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  text-align: center;
}

.db-details {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.db-item {
  display: block;
  font-size: 26rpx;
  color: #666;
  padding: 15rpx 20rpx;
  background: #f8f9fa;
  border-radius: 10rpx;
  border-left: 4rpx solid #667eea;
}

/* 响应式调整 */
@media screen and (max-width: 750rpx) {
  .test-btn {
    width: 100%;
  }
  
  .action-section {
    flex-direction: column;
    align-items: center;
  }
  
  .action-btn {
    width: 100%;
    max-width: none;
  }
} 