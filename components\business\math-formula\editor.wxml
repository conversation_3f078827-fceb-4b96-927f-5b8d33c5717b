<!-- 遮罩层 -->
<view class="formula-editor-mask" wx:if="{{show}}" catchtouchmove="preventTap" bindtap="handleCancel"></view>

<!-- 公式编辑器容器 -->
<view class="formula-editor {{show ? 'formula-editor-show' : ''}}" catchtouchmove="preventTap" catchtap="preventTap">
  <!-- 编辑器头部 -->
  <view class="formula-editor-header">
    <view class="formula-editor-title">编辑数学公式</view>
    <view class="formula-editor-actions">
      <view class="action-btn" bindtap="togglePreview">
        <view class="action-icon {{showPreview ? 'icon-eye' : 'icon-eye-off'}}"></view>
        <text>{{showPreview ? '隐藏预览' : '显示预览'}}</text>
      </view>
      <view class="action-btn" bindtap="clearInput">
        <view class="action-icon icon-delete"></view>
        <text>清空</text>
      </view>
    </view>
  </view>

  <!-- 公式输入区域 -->
  <view class="formula-editor-input-area">
    <input 
      class="formula-input {{!isValid ? 'input-error' : ''}}" 
      value="{{inputValue}}" 
      bindinput="handleInput" 
      placeholder="{{placeholder}}" 
    />
    <view class="formula-error" wx:if="{{error}}">{{error}}</view>
  </view>

  <!-- 公式预览区域 -->
  <view class="formula-editor-preview">
    <view class="preview-title">预览</view>
    <view class="preview-content">
      <block wx:if="{{inputValue}}">
        <math-formula formula="{{inputValue}}" />
      </block>
      <view class="preview-placeholder" wx:else>公式预览将显示在这里</view>
    </view>
  </view>

  <!-- 符号选择器 -->
  <view class="formula-tabs">
    <view class="tabs-header">
      <view 
        wx:for="{{symbolCategories}}" 
        wx:key="id" 
        class="tab-item {{currentTab === item.id ? 'tab-active' : ''}}"
        data-id="{{item.id}}"
        bindtap="switchTab"
      >
        {{item.name}}
      </view>
    </view>
    
    <view class="tabs-content">
      <scroll-view scroll-y class="symbols-container">
        <view class="symbols-grid">
          <view 
            wx:for="{{currentTab === 'basic' ? basicSymbols : (currentTab === 'algebra' ? algebraSymbols : (currentTab === 'geometry' ? geometrySymbols : (currentTab === 'calculus' ? calculusSymbols : (currentTab === 'sets' ? setsSymbols : logicSymbols))))}}" 
            wx:key="latex"
            class="symbol-item"
            data-latex="{{item.latex}}"
            bindtap="insertSymbol"
          >
            <view class="symbol-text">{{item.symbol}}</view>
            <view class="symbol-desc">{{item.desc}}</view>
          </view>
        </view>
      </scroll-view>
    </view>
  </view>

  <!-- 底部按钮区 -->
  <view class="formula-editor-footer">
    <button class="footer-btn cancel-btn" bindtap="handleCancel">取消</button>
    <button class="footer-btn confirm-btn" bindtap="handleConfirm">确认</button>
  </view>
</view> 