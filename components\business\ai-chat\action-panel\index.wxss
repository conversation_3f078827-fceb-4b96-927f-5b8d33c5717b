/* 引入全局图标样式 */
@import "../../../../styles/icons.wxss";

/* 底部功能和输入区域样式 */
.bottom-panel {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  border-top: 1rpx solid rgba(0, 0, 0, 0.05);
  padding-top: 16rpx;
  padding-bottom: calc(16rpx + env(safe-area-inset-bottom));
  z-index: 100;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.03);
}

/* 确保底部安全区域适配所有设备 */
.safe-bottom-all-devices {
  padding-bottom: calc(16rpx + max(env(safe-area-inset-bottom), 0px));
}

/* 功能区域样式 */
.top-functions {
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 0 20rpx 20rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
}

.function-btn {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding: 15rpx 0;
  border-radius: 8rpx;
  flex: 1;
  margin: 0 4rpx;
  transition: all 0.2s ease;
}

.function-active {
  background-color: rgba(123, 114, 251, 0.05);
}

.function-btn .icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 8rpx;
  color: #9B95FB;
  opacity: 0.9;
  flex-shrink: 0;
}

.function-text {
  font-size: 24rpx;
  color: #666;
  text-align: center;
  line-height: 1.2;
  margin-top: 0;
  display: inline-block;
  vertical-align: middle;
}

.function-active .function-text {
  color: #7B72FB;
}

.function-active .icon {
  color: #7B72FB;
  opacity: 1;
}

/* 输入区域样式 */
.input-wrapper {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx 16rpx;
}

.input-left, .input-right {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 72rpx;
}

.input-middle {
  flex: 1;
  margin: 0 16rpx;
}

.input-field {
  background: #F5F5F5;
  border-radius: 36rpx;
  padding: 16rpx 24rpx;
  font-size: 28rpx;
  width: 100%;
  box-sizing: border-box;
  min-height: 72rpx;
}

/* 发送按钮样式 */
.send-btn {
  width: 72rpx;
  height: 72rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.send-btn.enabled {
  background-color: #7B72FB;
}

.send-btn.disabled {
  background-color: #CCCCCC;
}

.send-arrow-icon {
  width: 32rpx;
  height: 32rpx;
  background-color: white;
  clip-path: polygon(0% 0%, 100% 50%, 0% 100%);
}

/* 语音按钮样式 */
.voice-btn {
  padding: 12rpx;
  border-radius: 50%;
}

.voice-btn.recording {
  background-color: rgba(123, 114, 251, 0.1);
}

/* 按钮悬浮效果 */
.btn-hover {
  opacity: 0.8;
  background-color: rgba(123, 114, 251, 0.05);
}

.icon-hover {
  opacity: 0.7;
}

.send-btn-hover {
  opacity: 0.9;
} 