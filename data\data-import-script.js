/**
 * K12数学知识点数据导入脚本
 * 将PostgreSQL数据库中的知识图谱数据转换为微信小程序JavaScript格式
 * 
 * 数据源：
 * - 531个知识点 (knowledge_graph_nodes表)
 * - 1305条同级关系 (knowledge_graph_edges表)
 * - 1121条跨级关系 (knowledge_graph_cross_links表)
 * 
 * 使用方法：
 * 1. 运行 node data-import-script.js
 * 2. 自动生成 knowledge-nodes-full.js 和 knowledge-relationships-full.js
 */

// 注释掉Node.js模块引入，微信小程序环境不支持
// const fs = require('fs');
// const path = require('path');

// 数据库原始数据映射 (从SQL插入语句提取)
const rawKnowledgeNodes = [
  // 小学1年级 (44个知识点)
  { id: 'e1n001', name: '数一数', level: 1, difficulty: 'easy', semester: 'first', textbook: '一年级上册', chapter: '准备课', section: '数一数', chapter_number: 0, section_number: '0.1', description: '能够数出数量在20以内的物体的个数，掌握数数的基本方法' },
  { id: 'e1n002', name: '比多少', level: 1, difficulty: 'easy', semester: 'first', textbook: '一年级上册', chapter: '准备课', section: '比多少', chapter_number: 0, section_number: '0.2', description: '能够比较两组物体的多少，理解"多"、"少"、"一样多"的含义' },
  { id: 'e1n003', name: '上、下、前、后', level: 1, difficulty: 'easy', semester: 'first', textbook: '一年级上册', chapter: '第一单元 位置', section: '1.1 上、下、前、后', chapter_number: 1, section_number: '1.1', description: '能够识别和描述物体的相对位置关系：上下、前后' },
  { id: 'e1n004', name: '左、右', level: 1, difficulty: 'easy', semester: 'first', textbook: '一年级上册', chapter: '第一单元 位置', section: '1.2 左、右', chapter_number: 1, section_number: '1.2', description: '能够识别和描述物体的左右位置关系' },
  { id: 'e1n005', name: '位置', level: 1, difficulty: 'easy', semester: 'first', textbook: '一年级上册', chapter: '第一单元 位置', section: '1.3 位置', chapter_number: 1, section_number: '1.3', description: '综合运用方位词描述物体的位置关系' },
  { id: 'e1n006', name: '1~5的认识', level: 1, difficulty: 'easy', semester: 'first', textbook: '一年级上册', chapter: '第二单元 1~5的认识和加减法', section: '2.1 1~5的认识', chapter_number: 2, section_number: '2.1', description: '认识1~5各数，了解数的实际含义，掌握数的顺序' },
  { id: 'e1n007', name: '比大小', level: 1, difficulty: 'easy', semester: 'first', textbook: '一年级上册', chapter: '第二单元 1~5的认识和加减法', section: '2.2 比大小', chapter_number: 2, section_number: '2.2', description: '会比较5以内数的大小，认识">"、"<"、"="符号，理解大小关系的含义' },
  { id: 'e1n008', name: '第几', level: 1, difficulty: 'easy', semester: 'first', textbook: '一年级上册', chapter: '第二单元 1~5的认识和加减法', section: '2.3 第几', chapter_number: 2, section_number: '2.3', description: '理解基数和序数的区别，能正确使用"第几"表示物体的位置' },
  { id: 'e1n009', name: '分与合', level: 1, difficulty: 'medium', semester: 'first', textbook: '一年级上册', chapter: '第二单元 1~5的认识和加减法', section: '2.4 分与合', chapter_number: 2, section_number: '2.4', description: '掌握5以内数的分解和组成，理解数的组成关系' },
  { id: 'e1n010', name: '加法', level: 1, difficulty: 'medium', semester: 'first', textbook: '一年级上册', chapter: '第二单元 1~5的认识和加减法', section: '2.5 加法', chapter_number: 2, section_number: '2.5', description: '理解加法的含义，掌握5以内数的加法计算' },
  
  // 继续添加其他年级数据... (完整的531个知识点)
  // 由于篇幅限制，这里展示结构，实际需要包含所有数据
];

// 知识点关系数据 (从knowledge_graph_edges表)
const rawIntraGradeRelationships = [
  { id: 'e1l001', source: 'e1n001', target: 'e1n002', type: 'foundation', strength: 0.95, description: '数一数的数感是比多少逻辑思维的认知基础' },
  { id: 'e1l002', source: 'e1n002', target: 'e1n032', type: 'abstraction', strength: 0.8, description: '比多少的对应思想抽象为分类的逻辑标准' },
  // ... 继续添加1305条关系数据
];

// 跨年级关系数据 (从knowledge_graph_cross_links表)
const rawCrossGradeRelationships = [
  { id: 'e12l001', source: 'e1n006', target: 'e2n036', type: 'foundation', strength: 0.95, description: '一年级1~5的认识为二年级1000以内数的认识奠定基础' },
  { id: 'e12l026', source: 'e1n014', target: 'e2n008', type: 'foundation', strength: 0.95, description: '一年级认识平面图形是二年级角的初步认识的基础' },
  // ... 继续添加1121条跨年级关系数据
];

// 数据转换函数
class DataConverter {
  constructor() {
    this.processedNodes = [];
    this.processedRelationships = {
      intra_grade_relationships: [],
      cross_grade_relationships: []
    };
  }

  /**
   * 转换知识点数据为完整结构
   */
  convertKnowledgeNode(rawNode) {
    const convertedNode = {
      id: rawNode.id,
      name: rawNode.name,
      code: rawNode.id,
      
      educational_metadata: {
        grade: rawNode.level,
        semester: rawNode.semester,
        subject: "数学",
        textbook: rawNode.textbook,
        chapter: rawNode.chapter,
        section: rawNode.section,
        chapter_number: rawNode.chapter_number,
        section_number: rawNode.section_number,
        estimated_time: this.estimateTimeByDifficulty(rawNode.difficulty)
      },
      
      difficulty_info: {
        level: rawNode.difficulty,
        cognitive_load: this.mapCognitiveLoad(rawNode.difficulty),
        prerequisite_count: this.calculatePrerequisiteCount(rawNode.id),
        complexity_score: this.calculateComplexityScore(rawNode.difficulty, rawNode.level)
      },
      
      knowledge_classification: {
        domain: this.mapDomain(rawNode.chapter),
        subdomain: this.mapSubdomain(rawNode.section),
        concept_type: this.mapConceptType(rawNode.name),
        learning_objectives: this.generateLearningObjectives(rawNode),
        key_concepts: this.extractKeyConcepts(rawNode.description),
        skills: this.mapSkills(rawNode.difficulty)
      },
      
      learning_content: {
        concept_definition: rawNode.description,
        key_points: this.generateKeyPoints(rawNode),
        examples: this.generateExamples(rawNode),
        common_mistakes: this.generateCommonMistakes(rawNode),
        teaching_tips: this.generateTeachingTips(rawNode)
      },
      
      practice_assessment: {
        practice_types: this.generatePracticeTypes(rawNode),
        difficulty_levels: this.generateDifficultyLevels(rawNode.difficulty),
        assessment_criteria: this.generateAssessmentCriteria(rawNode),
        sample_problems: this.generateSampleProblems(rawNode),
        solution_strategies: this.generateSolutionStrategies(rawNode)
      },
      
      learning_guidance: {
        study_methods: this.generateStudyMethods(rawNode),
        time_allocation: this.generateTimeAllocation(rawNode.difficulty),
        review_schedule: this.generateReviewSchedule(rawNode.difficulty),
        extension_activities: this.generateExtensionActivities(rawNode),
        remedial_strategies: this.generateRemedialStrategies(rawNode)
      },
      
      cognitive_analysis: {
        mental_models: this.generateMentalModels(rawNode),
        reasoning_patterns: this.generateReasoningPatterns(rawNode),
        transfer_potential: this.calculateTransferPotential(rawNode),
        abstraction_level: this.mapAbstractionLevel(rawNode.level),
        cognitive_bridges: this.generateCognitiveBridges(rawNode)
      },
      
      knowledge_network: {
        prerequisite_links: this.findPrerequisiteLinks(rawNode.id),
        successor_links: this.findSuccessorLinks(rawNode.id),
        parallel_links: this.findParallelLinks(rawNode.id),
        application_links: this.findApplicationLinks(rawNode.id),
        cross_domain_links: this.findCrossDomainLinks(rawNode.id)
      },
      
      learning_path: {
        entry_points: this.generateEntryPoints(rawNode),
        milestone_checkpoints: this.generateMilestoneCheckpoints(rawNode),
        alternative_routes: this.generateAlternativeRoutes(rawNode),
        acceleration_options: this.generateAccelerationOptions(rawNode),
        support_resources: this.generateSupportResources(rawNode)
      },
      
      personalization_data: {
        learning_style_adaptations: this.generateLearningStyleAdaptations(rawNode),
        ability_level_adjustments: this.generateAbilityLevelAdjustments(rawNode),
        interest_connections: this.generateInterestConnections(rawNode),
        cultural_contexts: this.generateCulturalContexts(rawNode),
        accessibility_features: this.generateAccessibilityFeatures(rawNode)
      },
      
      ai_analysis: {
        learning_analytics: this.generateLearningAnalytics(rawNode),
        predictive_indicators: this.generatePredictiveIndicators(rawNode),
        recommendation_weights: this.generateRecommendationWeights(rawNode),
        adaptive_parameters: this.generateAdaptiveParameters(rawNode),
        performance_benchmarks: this.generatePerformanceBenchmarks(rawNode)
      },
      
      technical_metadata: {
        data_version: "1.0",
        last_updated: new Date(),
        quality_score: this.calculateQualityScore(rawNode),
        validation_status: "validated",
        usage_statistics: this.generateInitialUsageStats()
      }
    };

    return convertedNode;
  }

  /**
   * 转换关系数据为完整结构
   */
  convertRelationship(rawRel, isIntraGrade = true) {
    const convertedRel = {
      id: rawRel.id,
      source_id: rawRel.source,
      target_id: rawRel.target,
      relationship_type: rawRel.type,
      strength: rawRel.strength,
      description: rawRel.description,
      
      cognitive_analysis: {
        cognitive_load: this.mapRelationshipCognitiveLoad(rawRel.strength),
        difficulty_progression: this.calculateDifficultyProgression(rawRel),
        mental_model_connection: this.generateMentalModelConnection(rawRel)
      },
      
      learning_guidance: {
        prerequisite_skills: this.generatePrerequisiteSkills(rawRel),
        learning_objectives: this.generateRelationshipObjectives(rawRel),
        teaching_strategies: this.generateTeachingStrategies(rawRel),
        common_difficulties: this.generateCommonDifficulties(rawRel),
        assessment_points: this.generateAssessmentPoints(rawRel)
      }
    };

    if (!isIntraGrade) {
      convertedRel.grade_span = this.calculateGradeSpan(rawRel.source, rawRel.target);
    }

    return convertedRel;
  }

  // 辅助函数
  estimateTimeByDifficulty(difficulty) {
    const timeMap = { easy: 40, medium: 50, hard: 60, expert: 75 };
    return timeMap[difficulty] || 45;
  }

  mapCognitiveLoad(difficulty) {
    const loadMap = { easy: 'low', medium: 'medium', hard: 'high', expert: 'high' };
    return loadMap[difficulty] || 'medium';
  }

  calculatePrerequisiteCount(nodeId) {
    return rawIntraGradeRelationships.filter(rel => 
      rel.target === nodeId && ['foundation', 'prerequisite'].includes(rel.type)
    ).length;
  }

  calculateComplexityScore(difficulty, level) {
    const baseScore = { easy: 0.2, medium: 0.5, hard: 0.7, expert: 0.9 }[difficulty] || 0.5;
    const levelMultiplier = level <= 6 ? 0.8 : level <= 9 ? 1.0 : 1.2;
    return Math.min(baseScore * levelMultiplier, 1.0);
  }

  mapDomain(chapter) {
    if (chapter.includes('数') || chapter.includes('运算') || chapter.includes('方程')) return '数与代数';
    if (chapter.includes('图形') || chapter.includes('几何') || chapter.includes('测量')) return '图形与几何';
    if (chapter.includes('统计') || chapter.includes('概率') || chapter.includes('可能性')) return '统计与概率';
    return '综合与实践';
  }

  mapSubdomain(section) {
    const domainMap = {
      '认识': '数的认识',
      '运算': '数的运算',
      '图形': '图形认识',
      '测量': '测量',
      '统计': '数据整理',
      '概率': '随机现象'
    };
    
    for (let key in domainMap) {
      if (section.includes(key)) return domainMap[key];
    }
    return '基础概念';
  }

  mapConceptType(name) {
    if (name.includes('认识') || name.includes('了解')) return '概念理解';
    if (name.includes('计算') || name.includes('运算')) return '计算技能';
    if (name.includes('解决') || name.includes('应用')) return '问题解决';
    return '基础技能';
  }

  generateLearningObjectives(node) {
    const objectives = [`掌握${node.name}的基本概念`];
    if (node.difficulty !== 'easy') {
      objectives.push(`能够运用${node.name}解决实际问题`);
    }
    return objectives;
  }

  extractKeyConcepts(description) {
    // 从描述中提取关键概念
    const concepts = [];
    if (description.includes('数')) concepts.push('数的概念');
    if (description.includes('计算') || description.includes('运算')) concepts.push('运算方法');
    if (description.includes('图形')) concepts.push('图形特征');
    return concepts.length > 0 ? concepts : ['基础概念'];
  }

  mapSkills(difficulty) {
    const skillMap = {
      easy: ['观察能力', '基础操作'],
      medium: ['逻辑思维', '计算能力', '应用能力'],
      hard: ['抽象思维', '推理能力', '综合分析'],
      expert: ['创新思维', '建模能力', '系统分析']
    };
    return skillMap[difficulty] || skillMap.medium;
  }

  generateKeyPoints(node) {
    return [
      `理解${node.name}的基本含义`,
      `掌握${node.name}的基本方法`,
      `能够应用${node.name}解决问题`
    ];
  }

  generateExamples(node) {
    const baseExamples = [`${node.name}的基础练习`, `${node.name}的实际应用`];
    if (node.level <= 3) {
      baseExamples.unshift('生活中的实际例子');
    }
    return baseExamples;
  }

  generateCommonMistakes(node) {
    return [
      '概念理解不准确',
      '方法运用错误',
      '计算过程出错'
    ];
  }

  generateTeachingTips(node) {
    const tips = ['循序渐进，由浅入深'];
    if (node.level <= 6) {
      tips.push('结合实物教学');
      tips.push('多用游戏化方式');
    }
    tips.push('及时反馈，强化练习');
    return tips;
  }

  // 更多辅助函数...
  generatePracticeTypes(node) {
    return ['基础练习', '提高练习', '综合应用'];
  }

  generateDifficultyLevels(difficulty) {
    const levels = ['基础', '标准'];
    if (difficulty !== 'easy') levels.push('提高');
    if (difficulty === 'expert') levels.push('拓展');
    return levels;
  }

  generateAssessmentCriteria(node) {
    return ['概念理解', '方法掌握', '应用能力'];
  }

  generateSampleProblems(node) {
    return ['基础练习题', '应用题', '综合题'];
  }

  generateSolutionStrategies(node) {
    return ['直观理解', '逻辑推理', '数形结合'];
  }

  // 批量处理函数
  processAllNodes() {
    console.log('开始处理知识点数据...');
    this.processedNodes = rawKnowledgeNodes.map(node => this.convertKnowledgeNode(node));
    console.log(`已处理 ${this.processedNodes.length} 个知识点`);
  }

  processAllRelationships() {
    console.log('开始处理关系数据...');
    
    this.processedRelationships.intra_grade_relationships = 
      rawIntraGradeRelationships.map(rel => this.convertRelationship(rel, true));
    
    this.processedRelationships.cross_grade_relationships = 
      rawCrossGradeRelationships.map(rel => this.convertRelationship(rel, false));
    
    console.log(`已处理 ${this.processedRelationships.intra_grade_relationships.length} 条同级关系`);
    console.log(`已处理 ${this.processedRelationships.cross_grade_relationships.length} 条跨级关系`);
  }

  // 文件生成函数
  generateKnowledgeNodesFile() {
    const fileContent = `/**
 * K12数学知识点完整数据
 * 自动生成于: ${new Date().toISOString()}
 * 数据来源: PostgreSQL knowledge_graph_nodes表
 * 总计: ${this.processedNodes.length} 个知识点
 */

const knowledgeNodes = ${JSON.stringify(this.processedNodes, null, 2)};

const knowledgeStatistics = {
  total_count: ${this.processedNodes.length},
  by_grade: ${JSON.stringify(this.calculateGradeStatistics(), null, 2)},
  by_difficulty: ${JSON.stringify(this.calculateDifficultyStatistics(), null, 2)},
  by_domain: ${JSON.stringify(this.calculateDomainStatistics(), null, 2)}
};

module.exports = {
  knowledgeNodes,
  knowledgeStatistics
};`;

    return fileContent;
  }

  generateRelationshipsFile() {
    const fileContent = `/**
 * K12数学知识点关系完整数据
 * 自动生成于: ${new Date().toISOString()}
 * 数据来源: PostgreSQL knowledge_graph_edges & knowledge_graph_cross_links表
 * 同级关系: ${this.processedRelationships.intra_grade_relationships.length} 条
 * 跨级关系: ${this.processedRelationships.cross_grade_relationships.length} 条
 */

const knowledgeRelationships = ${JSON.stringify(this.processedRelationships, null, 2)};

const relationshipStatistics = {
  total_count: ${this.processedRelationships.intra_grade_relationships.length + this.processedRelationships.cross_grade_relationships.length},
  intra_grade_count: ${this.processedRelationships.intra_grade_relationships.length},
  cross_grade_count: ${this.processedRelationships.cross_grade_relationships.length},
  by_type: ${JSON.stringify(this.calculateRelationshipTypeStatistics(), null, 2)}
};

module.exports = {
  knowledgeRelationships,
  relationshipStatistics
};`;

    return fileContent;
  }

  // 统计函数
  calculateGradeStatistics() {
    const stats = {};
    this.processedNodes.forEach(node => {
      const grade = node.educational_metadata.grade;
      stats[`grade_${grade}`] = (stats[`grade_${grade}`] || 0) + 1;
    });
    return stats;
  }

  calculateDifficultyStatistics() {
    const stats = {};
    this.processedNodes.forEach(node => {
      const difficulty = node.difficulty_info.level;
      stats[difficulty] = (stats[difficulty] || 0) + 1;
    });
    return stats;
  }

  calculateDomainStatistics() {
    const stats = {};
    this.processedNodes.forEach(node => {
      const domain = node.knowledge_classification.domain;
      stats[domain] = (stats[domain] || 0) + 1;
    });
    return stats;
  }

  calculateRelationshipTypeStatistics() {
    const stats = {};
    const allRels = [
      ...this.processedRelationships.intra_grade_relationships,
      ...this.processedRelationships.cross_grade_relationships
    ];
    
    allRels.forEach(rel => {
      const type = rel.relationship_type;
      stats[type] = (stats[type] || 0) + 1;
    });
    return stats;
  }

  // 其他必要的辅助函数...
  calculateQualityScore(node) {
    let score = 0.8; // 基础分
    if (node.description && node.description.length > 20) score += 0.1;
    if (node.difficulty) score += 0.1;
    return Math.min(score, 1.0);
  }

  generateInitialUsageStats() {
    return {
      access_count: 0,
      completion_rate: 0,
      satisfaction_score: 0
    };
  }

  findPrerequisiteLinks(nodeId) {
    return rawIntraGradeRelationships
      .filter(rel => rel.target === nodeId && ['foundation', 'prerequisite'].includes(rel.type))
      .map(rel => rel.source);
  }

  findSuccessorLinks(nodeId) {
    return rawIntraGradeRelationships
      .filter(rel => rel.source === nodeId)
      .map(rel => rel.target);
  }

  findParallelLinks(nodeId) {
    // 简化实现，实际应该基于同级别、同难度的知识点
    return [];
  }

  findApplicationLinks(nodeId) {
    return rawIntraGradeRelationships
      .filter(rel => rel.source === nodeId && rel.type === 'application')
      .map(rel => rel.target);
  }

  findCrossDomainLinks(nodeId) {
    return rawCrossGradeRelationships
      .filter(rel => rel.source === nodeId || rel.target === nodeId)
      .map(rel => rel.source === nodeId ? rel.target : rel.source);
  }

  // 更多生成函数的简化实现...
  generateStudyMethods(node) {
    const methods = ['理论学习', '实践练习'];
    if (node.level <= 6) methods.unshift('游戏学习');
    return methods;
  }

  generateTimeAllocation(difficulty) {
    const allocations = {
      easy: { theory: 15, practice: 20, review: 10 },
      medium: { theory: 20, practice: 25, review: 10 },
      hard: { theory: 25, practice: 30, review: 15 },
      expert: { theory: 30, practice: 35, review: 20 }
    };
    return allocations[difficulty] || allocations.medium;
  }

  generateReviewSchedule(difficulty) {
    const schedules = {
      easy: ['当日复习', '三日复习'],
      medium: ['当日复习', '三日复习', '周复习'],
      hard: ['当日复习', '三日复习', '周复习', '月复习'],
      expert: ['当日复习', '三日复习', '周复习', '月复习', '期末复习']
    };
    return schedules[difficulty] || schedules.medium;
  }

  generateExtensionActivities(node) {
    return [`${node.name}拓展练习`, '实际应用练习', '创新思维训练'];
  }

  generateRemedialStrategies(node) {
    return ['降低难度', '增加练习', '个别辅导', '同伴互助'];
  }

  generateMentalModels(node) {
    return ['基础概念模型', '操作程序模型'];
  }

  generateReasoningPatterns(node) {
    if (node.level <= 3) return ['直观思维', '具体操作'];
    if (node.level <= 6) return ['逻辑思维', '抽象操作'];
    return ['抽象思维', '演绎推理'];
  }

  calculateTransferPotential(node) {
    const baseScore = { easy: 0.6, medium: 0.7, hard: 0.8, expert: 0.9 }[node.difficulty] || 0.7;
    return baseScore;
  }

  mapAbstractionLevel(level) {
    if (level <= 3) return '具体操作';
    if (level <= 6) return '表象思维';
    if (level <= 9) return '抽象思维';
    return '形式思维';
  }

  generateCognitiveBridges(node) {
    return ['生活经验', '已有知识', '感性认识'];
  }

  generateEntryPoints(node) {
    return ['基础概念', '生活经验'];
  }

  generateMilestoneCheckpoints(node) {
    return [`理解${node.name}概念`, `掌握${node.name}方法`, `应用${node.name}解题`];
  }

  generateAlternativeRoutes(node) {
    return ['标准路径', '游戏路径', '项目路径'];
  }

  generateAccelerationOptions(node) {
    return ['跳跃学习', '深度学习', '拓展学习'];
  }

  generateSupportResources(node) {
    return ['教学视频', '练习题库', '学习工具'];
  }

  generateLearningStyleAdaptations(node) {
    return {
      visual: '图形化学习',
      auditory: '听觉学习',
      kinesthetic: '动手操作'
    };
  }

  generateAbilityLevelAdjustments(node) {
    return {
      advanced: '提高难度',
      standard: '标准教学',
      struggling: '降低难度'
    };
  }

  generateInterestConnections(node) {
    return ['游戏化', '生活化', '故事化'];
  }

  generateCulturalContexts(node) {
    return ['中国文化', '地方特色'];
  }

  generateAccessibilityFeatures(node) {
    return ['大字体', '语音提示', '触觉反馈'];
  }

  generateLearningAnalytics(node) {
    return {
      average_mastery_time: this.estimateTimeByDifficulty(node.difficulty),
      common_error_patterns: ['概念混淆', '计算错误'],
      success_indicators: ['准确性', '流畅性']
    };
  }

  generatePredictiveIndicators(node) {
    return ['前置技能', '学习动机', '认知能力'];
  }

  generateRecommendationWeights(node) {
    return {
      practice_frequency: 0.7,
      difficulty_progression: 0.8,
      interest_alignment: 0.6
    };
  }

  generateAdaptiveParameters(node) {
    return {
      error_tolerance: 0.3,
      progression_threshold: 0.8,
      review_frequency: 0.5
    };
  }

  generatePerformanceBenchmarks(node) {
    return ['年龄标准', '课程要求', '同伴比较'];
  }

  // 关系相关的生成函数
  mapRelationshipCognitiveLoad(strength) {
    if (strength >= 0.8) return 'low';
    if (strength >= 0.5) return 'medium';
    return 'high';
  }

  calculateDifficultyProgression(rel) {
    // 基于关系强度计算难度递进
    return Math.max(0.1, 1 - rel.strength);
  }

  generateMentalModelConnection(rel) {
    const typeMap = {
      foundation: 'foundational_connection',
      prerequisite: 'prerequisite_connection',
      application: 'application_connection',
      analogy: 'analogical_connection'
    };
    return typeMap[rel.type] || 'general_connection';
  }

  generatePrerequisiteSkills(rel) {
    return ['基础理解', '操作技能'];
  }

  generateRelationshipObjectives(rel) {
    return [`理解${rel.type}关系`, '建立知识连接'];
  }

  generateTeachingStrategies(rel) {
    return ['对比教学', '递进教学', '关联教学'];
  }

  generateCommonDifficulties(rel) {
    return ['关系理解困难', '知识迁移困难'];
  }

  generateAssessmentPoints(rel) {
    return ['关系理解', '应用能力'];
  }

  calculateGradeSpan(sourceId, targetId) {
    // 简化实现，实际应该查找源和目标的年级
    const sourceGrade = parseInt(sourceId.match(/\d+/)[0]);
    const targetGrade = parseInt(targetId.match(/\d+/)[0]);
    return `${sourceGrade}-${targetGrade}`;
  }

  // 主执行函数
  execute() {
    try {
      console.log('=== K12数学知识点数据导入脚本 ===');
      console.log('开始数据转换...\n');

      // 处理所有数据
      this.processAllNodes();
      this.processAllRelationships();

      // 生成文件内容
      console.log('\n生成JavaScript文件...');
      const nodesFileContent = this.generateKnowledgeNodesFile();
      const relationshipsFileContent = this.generateRelationshipsFile();

      // 注释掉文件写入操作，微信小程序环境不支持
      // const outputDir = path.join(__dirname);
      
      // fs.writeFileSync(
      //   path.join(outputDir, 'knowledge-nodes-full.js'), 
      //   nodesFileContent, 
      //   'utf8'
      // );
      
      // fs.writeFileSync(
      //   path.join(outputDir, 'knowledge-relationships-full.js'), 
      //   relationshipsFileContent, 
      //   'utf8'
      // );

      // 在微信小程序环境中，直接返回数据
      return {
        nodesFileContent,
        relationshipsFileContent,
        nodes: this.processedNodes,
        relationships: this.processedRelationships
      };

      console.log('\n=== 数据导入完成 ===');
      console.log(`✓ 知识点数据: ${this.processedNodes.length} 个`);
      console.log(`✓ 同级关系: ${this.processedRelationships.intra_grade_relationships.length} 条`);
      console.log(`✓ 跨级关系: ${this.processedRelationships.cross_grade_relationships.length} 条`);
      console.log('\n生成的文件:');
      console.log('- knowledge-nodes-full.js');
      console.log('- knowledge-relationships-full.js');
      console.log('\n导入脚本执行成功！');

    } catch (error) {
      console.error('数据导入过程中发生错误:', error);
      // 注释掉process.exit，微信小程序环境不支持
      // process.exit(1);
      throw error; // 改为抛出错误
    }
  }
}

// 脚本执行 - 适配微信小程序环境
// 注释掉Node.js特有的检查，改为直接导出
// if (require.main === module) {
//   const converter = new DataConverter();
//   converter.execute();
// }

// 微信小程序环境导出
if (typeof module !== 'undefined' && module.exports) {
  module.exports = DataConverter;
} else {
  // 如果在全局环境中，直接挂载到全局对象
  if (typeof window !== 'undefined') {
    window.DataConverter = DataConverter;
  } else if (typeof global !== 'undefined') {
    global.DataConverter = DataConverter;
  }
} 