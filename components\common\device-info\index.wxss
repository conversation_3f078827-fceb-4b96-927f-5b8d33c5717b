.device-info-container {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  transition: all 0.3s ease;
}

.device-info-container.show {
  transform: translateY(0);
}

.device-info-container.hide {
  transform: translateY(100%);
}

.device-info-panel {
  background-color: #ffffff;
  border-radius: 20rpx 20rpx 0 0;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
  padding: 30rpx;
  max-height: 80vh;
  overflow-y: auto;
}

.device-info-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.device-info-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}

.device-info-actions {
  display: flex;
  align-items: center;
}

.device-info-action {
  font-size: 28rpx;
  color: #3E7BFA;
  margin-left: 20rpx;
  padding: 6rpx 10rpx;
  border-radius: 4rpx;
}

.device-info-action:active {
  background-color: rgba(62, 123, 250, 0.1);
}

.device-info-section {
  margin-top: 24rpx;
  padding-bottom: 20rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 16rpx;
  position: relative;
  padding-left: 20rpx;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 8rpx;
  width: 8rpx;
  height: 28rpx;
  background-color: #3E7BFA;
  border-radius: 4rpx;
}

.info-item {
  display: flex;
  font-size: 26rpx;
  line-height: 1.8;
  margin-bottom: 8rpx;
}

.item-label {
  color: #666666;
  width: 220rpx;
  flex-shrink: 0;
}

.item-value {
  color: #333333;
  flex: 1;
  word-break: break-all;
}

.expand-btn {
  text-align: center;
  font-size: 26rpx;
  color: #3E7BFA;
  padding: 16rpx 0;
  margin-top: 10rpx;
}

.expand-btn:active {
  opacity: 0.8;
}

/* 高亮标记 */
.highlight {
  color: #F56C6C;
  font-weight: bold;
} 