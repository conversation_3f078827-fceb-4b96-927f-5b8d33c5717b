/* 日期选择器样式 */
.date-picker-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: flex-end;
}

.date-picker-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1001;
}

.date-picker-panel {
  position: relative;
  width: 100%;
  max-width: 750rpx;
  background-color: #fff;
  border-radius: 24rpx 24rpx 0 0;
  z-index: 1002;
  transform: translateY(0);
  animation: slide-up 0.3s ease;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.1);
}

@keyframes slide-up {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

.date-picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 30rpx;
  border-bottom: 1px solid #eee;
}

.picker-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.picker-btn {
  font-size: 28rpx;
  padding: 10rpx 20rpx;
  border-radius: 8rpx;
  transition: all 0.2s;
}

.picker-cancel {
  color: #666;
}

.picker-cancel:active {
  background-color: #f5f5f5;
}

.picker-confirm {
  color: #3E7BFA;
  font-weight: 500;
}

.picker-confirm:active {
  background-color: rgba(62, 123, 250, 0.1);
}

.date-picker-body {
  position: relative;
  height: 480rpx;
  overflow: hidden;
}

.date-picker-view {
  width: 100%;
  height: 100%;
}

.picker-item {
  line-height: 80rpx;
  font-size: 32rpx;
  color: #333;
  display: flex;
  justify-content: center;
  align-items: center;
}

.picker-indicator {
  border-radius: 8rpx;
  background-color: rgba(62, 123, 250, 0.05);
  border-top: 1px solid rgba(62, 123, 250, 0.1);
  border-bottom: 1px solid rgba(62, 123, 250, 0.1);
}

.picker-mask {
  background-image: linear-gradient(to bottom, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.6)), 
                    linear-gradient(to top, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.6));
}

/* 适配不同设备 */
@media screen and (min-height: 700px) {
  .date-picker-body {
    height: 520rpx;
  }
}

/* iPad适配 */
@media screen and (min-width: 768px) {
  .date-picker-panel {
    max-width: 600rpx;
    margin: 0 auto;
    border-radius: 24rpx;
  }
}

/* 安全区域适配 */
@supports (padding-bottom: constant(safe-area-inset-bottom)) or (padding-bottom: env(safe-area-inset-bottom)) {
  .date-picker-panel {
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
  }
} 