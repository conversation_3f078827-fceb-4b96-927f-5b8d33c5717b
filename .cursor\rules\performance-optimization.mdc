---
description: 
globs: 
alwaysApply: true
---
# 性能优化规范

本项目作为K12数学学习智能体，需要保证良好的性能体验。以下是性能优化的主要规范。

## 启动性能优化

### 分包加载
- 使用分包加载，减少主包体积
- 主包只保留首页和框架必需代码
- 非必要功能放入分包
- 分包预下载配置：
```json
{
  "preloadRule": {
    "pages/index/index": {
      "network": "all",
      "packages": ["packageStudy"]
    }
  }
}
```

### 首屏优化
- 减少首屏数据请求
- 优化首屏渲染节点数量
- 使用骨架屏提升体验
- 延迟加载非首屏内容

## 运行时性能

### 渲染性能
- 避免频繁setData
- 合并多个setData操作
- 仅传输必要数据
- 长列表使用虚拟列表
```javascript
// 优化前
this.setData({ 
  'item.value': value1,
  'item.label': value2
});

// 优化后
this.setData({
  item: {
    value: value1,
    label: value2
  }
});
```

### 事件优化
- 合理使用事件委托
- 防抖和节流处理
- 避免频繁触发重排重绘
```javascript
// 防抖示例
function debounce(fn, delay = 300) {
  let timer = null;
  return function() {
    clearTimeout(timer);
    timer = setTimeout(() => {
      fn.apply(this, arguments);
    }, delay);
  }
}
```

### 内存优化
- 及时销毁不需要的定时器
- 合理使用缓存
- 避免内存泄漏
- 大数据量分批处理

## 网络性能

### 请求优化
- 合并请求
- 使用缓存
- 避免重复请求
- 请求错误重试机制
```javascript
// 请求重试示例
async function requestWithRetry(url, retryCount = 3) {
  for(let i = 0; i < retryCount; i++) {
    try {
      return await request(url);
    } catch(err) {
      if(i === retryCount - 1) throw err;
    }
  }
}
```

### 资源优化
- 图片懒加载
- 使用webp格式图片
- 合理使用本地缓存
- CDN加速

## 数学公式渲染优化

### LaTeX渲染
- 使用缓存机制
- 分批渲染复杂公式
- 预加载常用公式
```javascript
// LaTeX渲染缓存
const formulaCache = new Map();
function renderFormula(latex) {
  if(formulaCache.has(latex)) {
    return formulaCache.get(latex);
  }
  const result = doRender(latex);
  formulaCache.set(latex, result);
  return result;
}
```

### 手写识别
- 优化识别算法
- 本地预处理
- 减少识别请求

## AI功能优化

### 响应优化
- 本地能力优先
- 请求排队机制
- 超时降级处理
```javascript
// AI请求超时处理
function aiRequest(prompt, timeout = 3000) {
  return Promise.race([
    doAIRequest(prompt),
    new Promise((_, reject) => 
      setTimeout(() => reject('timeout'), timeout)
    )
  ]).catch(err => {
    if(err === 'timeout') {
      return fallbackResponse(prompt);
    }
    throw err;
  });
}
```

### 智能降级
- 定义降级策略
- 保留基础功能
- 错误友好提示

## 性能监控

### 监控指标
- 页面加载时间
- 首屏渲染时间
- API响应时间
- 内存占用
- 帧率监控

### 监控实现
```javascript
// 性能监控示例
const performance = wx.getPerformance();
const observer = performance.createObserver((entryList) => {
  entryList.getEntries().forEach((entry) => {
    console.log('路由', entry.name);
    console.log('加载时间', entry.duration);
  });
});
observer.observe({ entryTypes: ['navigation', 'render'] });
```

## 优化工具

- 微信开发者工具性能面板
- Memory-leak-finder
- Page-performance-viewer
- API-performance-monitor

## 最佳实践

1. 定期进行性能评估
2. 建立性能指标基线
3. 性能优化和业务需求平衡
4. 持续监控和优化
5. 保持优化文档更新

## 实现参考

- 性能监控工具：[utils/performance.js](mdc:utils/performance.js)
- 优化配置：[app.json](mdc:app.json)
- 分包配置：[packageStudy/pages/learning-path/index.json](mdc:packageStudy/pages/learning-path/index.json)
