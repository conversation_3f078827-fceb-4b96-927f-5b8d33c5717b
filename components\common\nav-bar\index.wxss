/* 导航栏组件样式 */
.nav-bar {
  width: 100%;
  position: relative;
  background-color: #ffffff;
  color: #333333;
  box-sizing: border-box;
  z-index: 1000;
}

/* 固定定位 */
.nav-bar-fixed {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
}

/* 阴影效果 */
.nav-bar-shadow {
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

/* 精简导航栏样式 */
.nav-bar-plain .nav-bar-content {
  height: 30px !important; /* 减小导航栏高度 */
  padding: 0 16rpx; /* 减小左右内边距 */
}

.nav-bar-title-plain {
  font-size: 32rpx !important; /* 减小标题字体 */
  font-weight: 500 !important;
}

/* 状态栏 */
.nav-bar-status {
  width: 100%;
}

/* 导航栏内容区 */
.nav-bar-content {
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 0 24rpx;
  box-sizing: border-box;
}

/* 左侧区域 */
.nav-bar-left {
  display: flex;
  flex-direction: row;
  align-items: center;
  min-width: 80rpx;
}

/* 中间区域 */
.nav-bar-center {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 30rpx;
}

/* 标题文本 */
.nav-bar-title {
  font-size: 36rpx;
  font-weight: 600;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: #333333;
  max-width: 60%;
}

/* 右侧区域 */
.nav-bar-right {
  display: flex;
  flex-direction: row;
  align-items: center;
  min-width: 80rpx;
  justify-content: flex-end;
}

/* 按钮样式 */
.nav-bar-button {
  min-width: 72rpx;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10rpx;
  padding: 0 6rpx;
  background-color: transparent;
}

/* 精简模式下的按钮 */
.nav-bar-plain .nav-bar-button {
  min-width: 60rpx;
  height: 60rpx;
}

/* 按钮文本 */
.nav-bar-button-text {
  font-size: 28rpx;
  margin-left: 4rpx;
}

/* 导航栏图标样式 */
.icon-back, .icon-home {
  width: 40rpx;
  height: 40rpx;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.nav-bar-plain .icon-back,
.nav-bar-plain .icon-home {
  width: 36rpx;
  height: 36rpx;
}

.icon-back {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M20,11V13H8L13.5,18.5L12.08,19.92L4.16,12L12.08,4.08L13.5,5.5L8,11H20Z' fill='%233E7BFA'/%3E%3C/svg%3E");
}

.icon-home {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M10,20V14H14V20H19V12H22L12,3L2,12H5V20H10Z' fill='%23333333'/%3E%3C/svg%3E");
} 