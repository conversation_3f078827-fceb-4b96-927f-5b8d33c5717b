---
description: 
globs: 
alwaysApply: true
---
# K12全科智能学习平台 - 数据库表结构规则

## 🎯 项目核心特性
本项目是面向中国K12教育体系的**全科智能学习平台**，基于PostgreSQL 13+构建，支持：
- 语文、数学、英语、物理、化学、生物、历史、地理、政治等九大核心学科
- 高中文理科分流制度和新高考3+1+2模式
- 多版本教材体系（人教版、北师大版、苏教版等）
- AI个性化教学和跨学科关联分析
- 微信小程序原生支持

## 🏗️ 数据库8层架构系统

参考详细文档：[数据库表结构说明文档.md](mdc:database_scripts/postgresql/数据库表结构说明文档.md)

### Layer 0: 系统安装层
- **核心表**: `installation_log`、`installation_session`
- **职责**: 数据库系统自动化安装与配置管理、安装状态跟踪、错误恢复机制
- **特点**: 支持断点恢复、实时进度监控、智能错误诊断

### Layer 1: 基础配置层
- **核心表**: `system_config`、`publishers`、`curriculum_standards`、`textbook_editions`、`textbook_version_mappings`、`subject_domains`、`academic_track_syllabus`、`textbook_compatibility_matrix`、`installation_performance_benchmarks`
- **职责**: 全学科系统基础配置、枚举类型定义、教育元数据管理
- **特点**: 为K12全科学习平台提供标准化基础数据支撑

### Layer 2: 用户认证层
- **核心表**: `users`、`students`、`teachers`、`student_academic_track_history`、`user_mfa_settings`、`user_mfa_logs`、`user_trusted_devices`、`subject_selection_analysis`、`user_login_logs`、`user_sessions`
- **职责**: 用户身份管理、权限控制、文理科分流跟踪、多因素认证
- **特点**: 微信小程序无缝登录、全学科个性化学习支持

### Layer 3: 知识图谱层
- **核心表**: `knowledge_nodes`、`knowledge_relationships`、`student_knowledge_mastery`、`knowledge_content_details`、`academic_track_learning_paths`、`knowledge_node_track_variants`、`multimedia_resources`、`knowledge_multimedia_links`、`practice_questions`、`question_variants`、`cross_subject_competency_mapping`、`cross_subject_content_mapping`
- **职责**: K12全学科知识体系构建、跨学科智能关联、个性化学习路径规划
- **特点**: 支持AI个性化推荐、跨学科关联发现和自适应学习

### Layer 4: 学习跟踪层
- **核心表**: `learning_recommendations`、`student_statistics`、`learning_progress`、`learning_sessions`、`learning_behavior_analytics`、`learning_pattern_analysis`、`learning_effectiveness_metrics`、`adaptive_learning_adjustments`、`comprehensive_quality_assessment`、`multimodal_learning_analysis`
- **职责**: 全学科学习行为追踪、跨学科智能分析、五育并举素质评价
- **特点**: 构建完整的全学科学习数据闭环，支持多模态学习分析

### Layer 5: 班级协作层
- **核心表**: `classes`、`class_members`、`homeworks`、`homework_submissions`、`class_invitations`、`class_announcements`、`class_statistics`、`peer_learning_groups`、`peer_learning_group_members`、`learning_milestones`、`class_statistics_cache`、`class_realtime_metrics`
- **职责**: 班级组织管理、全学科作业管理、师生互动协作、跨学科项目协作
- **特点**: 支持现代化课堂教学模式和跨学科项目式学习

### Layer 6: 成就激励层
- **核心表**: `achievements`、`student_achievements`、`audit_logs`、`achievement_social_shares`、`share_interactions`、`student_influence_rankings`、`learning_community_feed`
- **职责**: 全学科成就体系管理、综合素养激励机制、学习行为数据记录
- **特点**: 构建完整的全学科学习激励体系，营造积极的学习社区氛围

### Layer 7: AI增强层
- **AI推荐模块**: `ai_recommendation_configs`、`recommendation_history`、`multi_engine_recommendation_system`、`real_time_personalization_service`、`recommendation_engine_benchmarks`
- **AI对话模块**: `ai_chat_sessions`、`ai_chat_messages`、`learning_topics`
- **数字教材模块**: `digital_textbook_content`、`textbook_knowledge_mapping`、`textbook_knowledge_mapping_history`、`mapping_conflicts`、`student_textbook_usage`
- **能力分析模块**: `student_ability_history`、`ability_milestones`、`class_ability_statistics`、`grade_ability_benchmarks`
- **智能题库模块**: `enhanced_questions`、`student_answer_records`、`weakness_analysis`
- **个性化学习模块**: `personalized_learning_plans`、`knowledge_path_cache`、`ai_model_performance_metrics`
- **职责**: 跨学科智能推荐算法、全科个性化学习路径、AI对话系统、数字教材智能解析
- **特点**: 融入先进AI技术，支撑自适应学习系统，实现多引擎融合的高精度个性化推荐

### Layer 8: 错题本系统
- **核心表**: `wrong_question_book`、`wrong_question_statistics`、`wrong_question_practice_log`、`wrong_question_collections`、`wrong_question_learning_paths`、`wrong_question_recommendations`
- **职责**: 全学科错题收集管理、跨学科错题智能分析、个性化错题推荐
- **特点**: 支持跨学科薄弱点关联分析，实现个性化错题复习和精准查漏补缺

## 📋 开发规范

### 🔧 数据库操作规范
1. **表命名**: 使用下划线分隔的英文命名，遵循业务语义
2. **索引策略**: 系统设计了150+专用索引，优先使用现有索引
3. **分区表设计**: 大数据量表采用分区设计，如按学科、年级分区
4. **事务安全**: 涉及多表操作必须使用事务，确保数据一致性
5. **性能优化**: 利用多层缓存策略，支持全学科并发访问

### 🎯 业务开发要点
1. **全学科支持**: 新功能开发需考虑九大学科的差异化需求
2. **文理科分流**: 高中阶段功能需区分文理科处理逻辑
3. **跨学科关联**: 充分利用知识图谱的跨学科关联能力
4. **AI增强**: 新功能优先考虑AI算法集成和个性化推荐
5. **微信小程序适配**: 接口设计需考虑小程序端的使用特点

### 📊 查询优化建议
1. **使用分层查询**: 根据8层架构进行分层查询，避免跨层直接关联
2. **利用缓存表**: 优先使用cache表进行统计查询
3. **分区查询**: 大数据量查询要指定分区条件
4. **索引利用**: 查询条件要充分利用现有索引结构
5. **批量操作**: 大量数据操作使用批量插入/更新方式

## 🔗 相关文件引用
- 数据库脚本目录: [database_scripts/postgresql/](mdc:database_scripts/postgresql)
- 云函数实现: [cloudfunctions/](mdc:cloudfunctions)
- 数据模型定义: [models/](mdc:models)
- 工具类库: [utils/](mdc:utils)

