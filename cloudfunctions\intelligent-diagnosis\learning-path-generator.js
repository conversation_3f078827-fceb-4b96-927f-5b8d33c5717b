// 智能学习路径生成器
const { RELATION_TYPES, RELATION_STRENGTH } = require('./graph-data-types');

class LearningPathGenerator {
  constructor(knowledgeGraph) {
    this.knowledgeGraph = knowledgeGraph;
    this.pathCache = new Map();
    this.initialized = false;
  }

  /**
   * 初始化学习路径生成器
   * @returns {Promise<boolean>} 初始化结果
   */
  async initialize() {
    try {
      console.log('正在初始化学习路径生成器...');
      
      // 预加载常用路径模板
      this.pathTemplates = new Map();
      
      // 初始化缓存
      this.pathCache.clear();
      
      // 初始化配置
      this.defaultConfig = {
        maxPathLength: 20,
        minMasteryThreshold: 0.7,
        defaultLearningSpeed: 'normal',
        enableAdaptiveAdjustment: true
      };
      
      this.initialized = true;
      console.log('学习路径生成器初始化完成');
      return true;
    } catch (error) {
      console.error('学习路径生成器初始化失败:', error);
      return false;
    }
  }

  /**
   * 生成学习路径 - 向后兼容方法
   * @param {Object} levelData - 水平数据
   * @param {Object} options - 选项
   * @returns {Object} 学习路径
   */
  async generateLearningPath(levelData, options) {
    console.log('调用兼容的学习路径生成方法...');
    
    const { currentLevel, targetLevel } = levelData;
    const { preferences, timeConstraints } = options;
    
    return await this.generatePersonalizedPath(
      { studentId: 'default', gradeLevel: currentLevel || 'grade6' },
      { knowledgePoints: {}, strengths: [], weaknesses: [] },
      targetLevel ? [targetLevel] : [],
      { ...preferences, timeConstraints }
    );
  }

  /**
   * 生成学习路径 - 简化兼容方法
   * @param {Object} data - 路径数据
   * @returns {Object} 学习路径
   */
  async generatePath(data) {
    console.log('调用简化的学习路径生成方法...');
    
    // 兼容不同的数据格式
    const studentProfile = {
      studentId: data.studentId || 'test_student',
      gradeLevel: data.gradeLevel || 6
    };
    
    const masteryData = data.masteryData || {
      knowledgePoints: data.knowledgePoints || {},
      strengths: data.strengths || [],
      weaknesses: data.weaknesses || []
    };
    
    const targetPoints = data.targetKnowledgePoints || data.targets || [];
    const preferences = data.preferences || {};
    
    return await this.generatePersonalizedPath(
      studentProfile,
      masteryData,
      targetPoints,
      preferences
    );
  }

  /**
   * 生成个性化学习路径
   * @param {Object} studentProfile - 学生档案
   * @param {Object} masteryData - 知识掌握情况
   * @param {Array} targetKnowledgePoints - 目标知识点
   * @param {Object} learningPreferences - 学习偏好
   * @returns {Object} 个性化学习路径
   */
  async generatePersonalizedPath(studentProfile, masteryData, targetKnowledgePoints, learningPreferences = {}) {
    console.log('开始生成个性化学习路径...');
    
    const pathConfig = {
      gradeLevel: studentProfile.gradeLevel,
      learningSpeed: learningPreferences.learningSpeed || 'normal',
      difficultyPreference: learningPreferences.difficultyPreference || 'gradual',
      timeConstraints: learningPreferences.timeConstraints || {},
      strengths: masteryData.strengths || [],
      weaknesses: masteryData.weaknesses || []
    };

    // 1. 分析当前知识状态
    const knowledgeState = this.analyzeKnowledgeState(masteryData, targetKnowledgePoints);
    
    // 2. 识别学习目标和前置依赖
    const learningGoals = await this.identifyLearningGoals(targetKnowledgePoints, knowledgeState);
    
    // 3. 构建知识依赖图
    const dependencyGraph = this.buildDependencyGraph(learningGoals, knowledgeState);
    
    // 4. 生成最优学习序列
    const optimalSequence = this.generateOptimalSequence(dependencyGraph, pathConfig);
    
    // 5. 个性化路径调整
    const personalizedPath = this.personalizePathSequence(optimalSequence, pathConfig);
    
    // 6. 添加学习资源和策略
    const enrichedPath = await this.enrichPathWithResources(personalizedPath, pathConfig);
    
    // 7. 生成路径元数据
    const pathMetadata = this.generatePathMetadata(enrichedPath, pathConfig);

    console.log('个性化学习路径生成完成');
    
    return {
      pathId: this.generatePathId(),
      studentId: studentProfile.studentId,
      generatedAt: new Date().toISOString(),
      metadata: pathMetadata,
      sequence: enrichedPath,
      estimatedCompletion: this.estimateCompletionTime(enrichedPath),
      adaptiveSettings: this.generateAdaptiveSettings(pathConfig)
    };
  }

  /**
   * 分析当前知识状态
   * @param {Object} masteryData - 掌握情况数据
   * @param {Array} targetPoints - 目标知识点
   * @returns {Object} 知识状态分析
   */
  analyzeKnowledgeState(masteryData, targetPoints) {
    const knowledgeState = {
      mastered: [],
      partiallyMastered: [],
      notMastered: [],
      prerequisites: new Set(),
      gaps: []
    };

    // 分析每个知识点的掌握状态
    for (const [nodeId, pointData] of Object.entries(masteryData.knowledgePoints || {})) {
      const masteryScore = pointData.masteryScore || 0;
      
      if (masteryScore >= 0.8) {
        knowledgeState.mastered.push(nodeId);
      } else if (masteryScore >= 0.6) {
        knowledgeState.partiallyMastered.push(nodeId);
      } else {
        knowledgeState.notMastered.push(nodeId);
      }
    }

    // 识别目标知识点的前置依赖
    for (const targetId of targetPoints) {
      const prerequisites = this.findPrerequisiteChain(targetId);
      prerequisites.forEach(prereq => knowledgeState.prerequisites.add(prereq));
    }

    // 识别知识缺口
    knowledgeState.gaps = this.identifyKnowledgeGaps(knowledgeState);

    return knowledgeState;
  }

  /**
   * 识别学习目标
   * @param {Array} targetPoints - 目标知识点
   * @param {Object} knowledgeState - 知识状态
   * @returns {Array} 学习目标列表
   */
  async identifyLearningGoals(targetPoints, knowledgeState) {
    const learningGoals = [];

    for (const targetId of targetPoints) {
      // 检查是否已掌握
      if (knowledgeState.mastered.includes(targetId)) {
        continue;
      }

      const goal = {
        targetNodeId: targetId,
        priority: this.calculateGoalPriority(targetId, knowledgeState),
        prerequisites: this.findDirectPrerequisites(targetId),
        estimatedDifficulty: this.getNodeDifficulty(targetId),
        learningPath: []
      };

      // 构建到达目标的学习路径
      goal.learningPath = this.buildPathToTarget(targetId, knowledgeState);
      
      learningGoals.push(goal);
    }

    // 按优先级排序
    return learningGoals.sort((a, b) => b.priority - a.priority);
  }

  /**
   * 构建知识依赖图
   * @param {Array} learningGoals - 学习目标
   * @param {Object} knowledgeState - 知识状态
   * @returns {Object} 依赖图
   */
  buildDependencyGraph(learningGoals, knowledgeState) {
    const dependencyGraph = {
      nodes: new Map(),
      edges: new Map(),
      levels: new Map()
    };

    // 收集所有需要学习的知识点
    const allNodes = new Set();
    learningGoals.forEach(goal => {
      goal.learningPath.forEach(nodeId => allNodes.add(nodeId));
    });

    // 构建节点信息
    allNodes.forEach(nodeId => {
      const node = this.knowledgeGraph.nodes[nodeId];
      if (node) {
        dependencyGraph.nodes.set(nodeId, {
          ...node,
          currentMastery: this.getNodeMastery(nodeId, knowledgeState),
          prerequisites: this.findDirectPrerequisites(nodeId),
          dependents: this.findDirectDependents(nodeId)
        });
      }
    });

    // 构建边信息（依赖关系）
    allNodes.forEach(nodeId => {
      const prerequisites = this.findDirectPrerequisites(nodeId);
      prerequisites.forEach(prereqId => {
        if (allNodes.has(prereqId)) {
          const edgeId = `${prereqId}->${nodeId}`;
          const relationship = this.getRelationship(prereqId, nodeId);
          
          dependencyGraph.edges.set(edgeId, {
            source: prereqId,
            target: nodeId,
            type: relationship?.type || RELATION_TYPES.PREREQUISITE,
            strength: relationship?.strength || RELATION_STRENGTH.MEDIUM,
            weight: this.calculateEdgeWeight(prereqId, nodeId, knowledgeState)
          });
        }
      });
    });

    // 计算拓扑层级
    dependencyGraph.levels = this.calculateTopologicalLevels(dependencyGraph);

    return dependencyGraph;
  }

  /**
   * 生成最优学习序列
   * @param {Object} dependencyGraph - 依赖图
   * @param {Object} pathConfig - 路径配置
   * @returns {Array} 最优学习序列
   */
  generateOptimalSequence(dependencyGraph, pathConfig) {
    console.log('生成最优学习序列...');

    // 使用改进的拓扑排序算法
    const sequence = [];
    const inDegree = new Map();
    const queue = [];

    // 计算入度
    dependencyGraph.nodes.forEach((node, nodeId) => {
      const prerequisites = node.prerequisites.filter(prereqId => 
        dependencyGraph.nodes.has(prereqId) && 
        node.currentMastery < 0.8
      );
      inDegree.set(nodeId, prerequisites.length);
      
      if (prerequisites.length === 0 && node.currentMastery < 0.8) {
        queue.push({
          nodeId,
          priority: this.calculateNodePriority(nodeId, pathConfig),
          estimatedTime: this.estimateStudyTime(nodeId)
        });
      }
    });

    // 按优先级排序队列
    queue.sort((a, b) => b.priority - a.priority);

    // 生成序列
    while (queue.length > 0) {
      const current = queue.shift();
      const currentNode = dependencyGraph.nodes.get(current.nodeId);
      
      // 添加到序列
      sequence.push({
        nodeId: current.nodeId,
        node: currentNode,
        sequenceIndex: sequence.length,
        estimatedStudyTime: current.estimatedTime,
        prerequisites: currentNode.prerequisites,
        learningObjectives: this.generateLearningObjectives(current.nodeId),
        assessmentMethods: this.generateAssessmentMethods(current.nodeId)
      });

      // 更新依赖节点的入度
      currentNode.dependents.forEach(dependentId => {
        if (dependencyGraph.nodes.has(dependentId)) {
          const newInDegree = inDegree.get(dependentId) - 1;
          inDegree.set(dependentId, newInDegree);
          
          if (newInDegree === 0) {
            const dependentNode = dependencyGraph.nodes.get(dependentId);
            if (dependentNode.currentMastery < 0.8) {
              queue.push({
                nodeId: dependentId,
                priority: this.calculateNodePriority(dependentId, pathConfig),
                estimatedTime: this.estimateStudyTime(dependentId)
              });
              
              // 重新排序队列
              queue.sort((a, b) => b.priority - a.priority);
            }
          }
        }
      });
    }

    return sequence;
  }

  /**
   * 个性化路径调整
   * @param {Array} sequence - 基础序列
   * @param {Object} pathConfig - 路径配置
   * @returns {Array} 个性化调整后的序列
   */
  personalizePathSequence(sequence, pathConfig) {
    console.log('个性化路径调整...');

    let personalizedSequence = [...sequence];

    // 1. 根据学习速度调整
    personalizedSequence = this.adjustForLearningSpeed(personalizedSequence, pathConfig.learningSpeed);

    // 2. 根据难度偏好调整
    personalizedSequence = this.adjustForDifficultyPreference(personalizedSequence, pathConfig.difficultyPreference);

    // 3. 考虑时间约束
    if (pathConfig.timeConstraints.maxDailyStudyTime) {
      personalizedSequence = this.adjustForTimeConstraints(personalizedSequence, pathConfig.timeConstraints);
    }

    // 4. 利用优势知识点
    personalizedSequence = this.leverageStrengths(personalizedSequence, pathConfig.strengths);

    // 5. 重点关注薄弱点
    personalizedSequence = this.addressWeaknesses(personalizedSequence, pathConfig.weaknesses);

    // 6. 添加复习节点
    personalizedSequence = this.insertReviewNodes(personalizedSequence);

    return personalizedSequence;
  }

  /**
   * 丰富路径资源
   * @param {Array} personalizedPath - 个性化路径
   * @param {Object} pathConfig - 路径配置
   * @returns {Array} 丰富资源后的路径
   */
  async enrichPathWithResources(personalizedPath, pathConfig) {
    console.log('丰富路径资源...');

    return personalizedPath.map(pathItem => ({
      ...pathItem,
      learningResources: this.generateLearningResources(pathItem.nodeId, pathConfig),
      practiceActivities: this.generatePracticeActivities(pathItem.nodeId, pathConfig),
      assessmentTools: this.generateAssessmentTools(pathItem.nodeId),
      teachingStrategies: this.recommendTeachingStrategies(pathItem.nodeId, pathConfig),
      parentGuidance: this.generateParentGuidance(pathItem.nodeId),
      motivationElements: this.generateMotivationElements(pathItem.nodeId, pathConfig)
    }));
  }

  /**
   * 找前置知识点链
   * @param {string} nodeId - 知识点ID
   * @returns {Array} 前置知识点链
   */
  findPrerequisiteChain(nodeId) {
    const visited = new Set();
    const chain = [];

    const dfs = (currentId) => {
      if (visited.has(currentId)) return;
      visited.add(currentId);

      const prerequisites = this.findDirectPrerequisites(currentId);
      prerequisites.forEach(prereqId => {
        dfs(prereqId);
        chain.push(prereqId);
      });
    };

    dfs(nodeId);
    return [...new Set(chain)]; // 去重
  }

  /**
   * 找直接前置知识点
   * @param {string} nodeId - 知识点ID
   * @returns {Array} 直接前置知识点
   */
  findDirectPrerequisites(nodeId) {
    const prerequisites = [];
    
    if (this.knowledgeGraph.links) {
      this.knowledgeGraph.links.forEach(link => {
        if (link.target === nodeId && 
            (link.type === RELATION_TYPES.PREREQUISITE || 
             link.type === RELATION_TYPES.FOUNDATION ||
             link.strength >= RELATION_STRENGTH.STRONG)) {
          prerequisites.push(link.source);
        }
      });
    }

    return prerequisites;
  }

  /**
   * 找直接依赖知识点
   * @param {string} nodeId - 知识点ID
   * @returns {Array} 直接依赖知识点
   */
  findDirectDependents(nodeId) {
    const dependents = [];
    
    if (this.knowledgeGraph.links) {
      this.knowledgeGraph.links.forEach(link => {
        if (link.source === nodeId && 
            (link.type === RELATION_TYPES.PREREQUISITE || 
             link.type === RELATION_TYPES.FOUNDATION ||
             link.strength >= RELATION_STRENGTH.STRONG)) {
          dependents.push(link.target);
        }
      });
    }

    return dependents;
  }

  /**
   * 获取关系信息
   * @param {string} sourceId - 源知识点ID
   * @param {string} targetId - 目标知识点ID
   * @returns {Object} 关系信息
   */
  getRelationship(sourceId, targetId) {
    if (this.knowledgeGraph.links) {
      return this.knowledgeGraph.links.find(link => 
        link.source === sourceId && link.target === targetId
      );
    }
    return null;
  }

  /**
   * 计算边权重
   * @param {string} sourceId - 源节点ID
   * @param {string} targetId - 目标节点ID
   * @param {Object} knowledgeState - 知识状态
   * @returns {number} 边权重
   */
  calculateEdgeWeight(sourceId, targetId, knowledgeState) {
    const relationship = this.getRelationship(sourceId, targetId);
    const baseWeight = relationship?.strength || RELATION_STRENGTH.MEDIUM;
    
    // 根据掌握情况调整权重
    const sourceMastery = this.getNodeMastery(sourceId, knowledgeState);
    const masteryFactor = sourceMastery > 0.8 ? 0.5 : 1.0; // 已掌握的降低权重
    
    return baseWeight * masteryFactor;
  }

  /**
   * 获取节点掌握度
   * @param {string} nodeId - 节点ID
   * @param {Object} knowledgeState - 知识状态
   * @returns {number} 掌握度
   */
  getNodeMastery(nodeId, knowledgeState) {
    if (knowledgeState.mastered.includes(nodeId)) return 1.0;
    if (knowledgeState.partiallyMastered.includes(nodeId)) return 0.7;
    if (knowledgeState.notMastered.includes(nodeId)) return 0.0;
    return 0.5; // 默认值
  }

  /**
   * 计算拓扑层级
   * @param {Object} dependencyGraph - 依赖图
   * @returns {Map} 节点层级映射
   */
  calculateTopologicalLevels(dependencyGraph) {
    const levels = new Map();
    const visited = new Set();

    const calculateLevel = (nodeId) => {
      if (levels.has(nodeId)) return levels.get(nodeId);
      if (visited.has(nodeId)) return 0; // 避免循环依赖

      visited.add(nodeId);
      const node = dependencyGraph.nodes.get(nodeId);
      
      if (!node || node.prerequisites.length === 0) {
        levels.set(nodeId, 0);
        return 0;
      }

      const maxPrereqLevel = Math.max(
        ...node.prerequisites.map(prereqId => calculateLevel(prereqId))
      );
      
      const level = maxPrereqLevel + 1;
      levels.set(nodeId, level);
      return level;
    };

    dependencyGraph.nodes.forEach((node, nodeId) => {
      calculateLevel(nodeId);
    });

    return levels;
  }

  // 辅助方法
  generatePathId() {
    return `path_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  calculateGoalPriority(nodeId, knowledgeState) {
    const node = this.knowledgeGraph.nodes[nodeId];
    if (!node) return 0;

    let priority = 0;
    
    // 基础优先级（根据年级和重要性）
    priority += (node.level || 1) * 10;
    
    // 前置依赖完成度影响
    const prerequisites = this.findDirectPrerequisites(nodeId);
    const completedPrereqs = prerequisites.filter(prereqId => 
      knowledgeState.mastered.includes(prereqId)
    ).length;
    priority += (completedPrereqs / Math.max(prerequisites.length, 1)) * 20;
    
    // 难度影响（简单的优先）
    const difficultyMap = { 'easy': 10, 'medium': 5, 'hard': 0, 'expert': -5 };
    priority += difficultyMap[node.difficulty] || 0;

    return priority;
  }

  getNodeDifficulty(nodeId) {
    const node = this.knowledgeGraph.nodes[nodeId];
    return node?.difficulty || 'medium';
  }

  buildPathToTarget(targetId, knowledgeState) {
    const path = [];
    const visited = new Set();

    const dfs = (nodeId) => {
      if (visited.has(nodeId)) return;
      visited.add(nodeId);

      const prerequisites = this.findDirectPrerequisites(nodeId);
      prerequisites.forEach(prereqId => {
        if (!knowledgeState.mastered.includes(prereqId)) {
          dfs(prereqId);
        }
      });

      if (!knowledgeState.mastered.includes(nodeId)) {
        path.push(nodeId);
      }
    };

    dfs(targetId);
    return path;
  }

  calculateNodePriority(nodeId, pathConfig) {
    let priority = 0;
    
    // 基础优先级
    priority += this.calculateGoalPriority(nodeId, {
      mastered: pathConfig.strengths,
      partiallyMastered: [],
      notMastered: pathConfig.weaknesses
    });

    // 学习偏好调整
    if (pathConfig.difficultyPreference === 'challenging') {
      const difficulty = this.getNodeDifficulty(nodeId);
      if (difficulty === 'hard' || difficulty === 'expert') {
        priority += 15;
      }
    }

    return priority;
  }

  estimateStudyTime(nodeId) {
    const node = this.knowledgeGraph.nodes[nodeId];
    if (!node) return 60; // 默认60分钟

    const difficultyTimeMap = {
      'easy': 30,
      'medium': 60,
      'hard': 90,
      'expert': 120
    };

    return difficultyTimeMap[node.difficulty] || 60;
  }

  generateLearningObjectives(nodeId) {
    const node = this.knowledgeGraph.nodes[nodeId];
    if (!node) return [];

    return [
      `理解${node.name}的基本概念`,
      `掌握${node.name}的解题方法`,
      `能够应用${node.name}解决实际问题`
    ];
  }

  generateAssessmentMethods(nodeId) {
    return [
      '概念理解测试',
      '练习题完成',
      '应用题解答',
      '口头表达能力'
    ];
  }

  identifyKnowledgeGaps(knowledgeState) {
    const gaps = [];
    
    knowledgeState.prerequisites.forEach(prereqId => {
      if (!knowledgeState.mastered.includes(prereqId) && 
          !knowledgeState.partiallyMastered.includes(prereqId)) {
        gaps.push(prereqId);
      }
    });

    return gaps;
  }

  // 个性化调整方法
  adjustForLearningSpeed(sequence, learningSpeed) {
    const speedMap = {
      'slow': 1.5,
      'normal': 1.0,
      'fast': 0.7
    };
    
    const multiplier = speedMap[learningSpeed] || 1.0;
    
    return sequence.map(item => ({
      ...item,
      estimatedStudyTime: Math.round(item.estimatedStudyTime * multiplier)
    }));
  }

  adjustForDifficultyPreference(sequence, difficultyPreference) {
    if (difficultyPreference === 'gradual') {
      // 按难度排序，简单的在前
      return sequence.sort((a, b) => {
        const difficultyOrder = { 'easy': 0, 'medium': 1, 'hard': 2, 'expert': 3 };
        return (difficultyOrder[a.node.difficulty] || 1) - (difficultyOrder[b.node.difficulty] || 1);
      });
    }
    
    return sequence;
  }

  adjustForTimeConstraints(sequence, timeConstraints) {
    const maxDailyTime = timeConstraints.maxDailyStudyTime || 120; // 默认120分钟
    
    // 将长时间学习项分解
    return sequence.map(item => {
      if (item.estimatedStudyTime > maxDailyTime) {
        item.splitSessions = Math.ceil(item.estimatedStudyTime / maxDailyTime);
        item.sessionDuration = Math.round(item.estimatedStudyTime / item.splitSessions);
      }
      return item;
    });
  }

  leverageStrengths(sequence, strengths) {
    // 在优势知识点附近安排相关学习内容
    return sequence.map(item => {
      const relatedStrengths = strengths.filter(strengthId => {
        const relationship = this.getRelationship(strengthId, item.nodeId);
        return relationship && relationship.strength >= RELATION_STRENGTH.MEDIUM;
      });

      if (relatedStrengths.length > 0) {
        item.leverageStrengths = relatedStrengths;
        item.connectionStrategy = '通过已掌握的相关知识点建立联系';
      }

      return item;
    });
  }

  addressWeaknesses(sequence, weaknesses) {
    // 为薄弱知识点添加额外支持
    return sequence.map(item => {
      if (weaknesses.includes(item.nodeId)) {
        item.isWeakness = true;
        item.extraSupport = true;
        item.estimatedStudyTime *= 1.3; // 增加30%学习时间
        item.additionalPractice = true;
      }
      return item;
    });
  }

  insertReviewNodes(sequence) {
    const reviewSequence = [];
    
    sequence.forEach((item, index) => {
      reviewSequence.push(item);
      
      // 每学习3个知识点插入一个复习节点
      if ((index + 1) % 3 === 0) {
        const reviewedNodes = sequence.slice(Math.max(0, index - 2), index + 1);
        reviewSequence.push({
          type: 'review',
          nodeIds: reviewedNodes.map(node => node.nodeId),
          estimatedStudyTime: 30,
          reviewObjectives: ['巩固前期学习内容', '检查理解程度', '建立知识联系']
        });
      }
    });

    return reviewSequence;
  }

  // 资源生成方法
  generateLearningResources(nodeId, pathConfig) {
    return {
      textbooks: [`教材第${this.getChapterInfo(nodeId)}章节`],
      videos: [`${this.getNodeName(nodeId)}视频讲解`],
      interactiveContent: [`${this.getNodeName(nodeId)}互动练习`],
      supplementaryMaterials: [`${this.getNodeName(nodeId)}补充资料`]
    };
  }

  generatePracticeActivities(nodeId, pathConfig) {
    return [
      '基础练习题',
      '应用题训练',
      '综合练习',
      '创新思维题'
    ];
  }

  generateAssessmentTools(nodeId) {
    return [
      '前置测试',
      '过程性评估',
      '结果性测试',
      '自我评价表'
    ];
  }

  recommendTeachingStrategies(nodeId, pathConfig) {
    const node = this.knowledgeGraph.nodes[nodeId];
    if (!node) return [];

    const strategies = ['直接教学法'];
    
    if (node.difficulty === 'easy') {
      strategies.push('示范教学', '反复练习');
    } else if (node.difficulty === 'hard') {
      strategies.push('探究式学习', '小组讨论', '案例分析');
    }

    return strategies;
  }

  generateParentGuidance(nodeId) {
    return {
      supportTips: [`如何在家支持${this.getNodeName(nodeId)}的学习`],
      commonMistakes: ['常见错误及纠正方法'],
      encouragementAdvice: ['鼓励策略和方法']
    };
  }

  generateMotivationElements(nodeId, pathConfig) {
    return {
      achievements: ['学习徽章', '进度里程碑'],
      rewards: ['完成奖励', '阶段性奖励'],
      socialElements: ['学习分享', '同伴鼓励']
    };
  }

  generatePathMetadata(enrichedPath, pathConfig) {
    const totalTime = enrichedPath.reduce((sum, item) => 
      sum + (item.estimatedStudyTime || 0), 0
    );

    return {
      totalKnowledgePoints: enrichedPath.filter(item => item.type !== 'review').length,
      estimatedTotalTime: totalTime,
      difficultyDistribution: this.calculateDifficultyDistribution(enrichedPath),
      prerequisitesCovered: this.countPrerequisitesCovered(enrichedPath),
      adaptivityLevel: 'high'
    };
  }

  estimateCompletionTime(enrichedPath) {
    const totalMinutes = enrichedPath.reduce((sum, item) => 
      sum + (item.estimatedStudyTime || 0), 0
    );
    
    const days = Math.ceil(totalMinutes / 60); // 假设每天1小时学习
    return {
      totalMinutes,
      estimatedDays: days,
      estimatedWeeks: Math.ceil(days / 7)
    };
  }

  generateAdaptiveSettings(pathConfig) {
    return {
      difficultyAdjustment: pathConfig.difficultyPreference,
      paceAdjustment: pathConfig.learningSpeed,
      supportLevel: 'adaptive',
      assessmentFrequency: 'regular'
    };
  }

  // 辅助方法
  getNodeName(nodeId) {
    const node = this.knowledgeGraph.nodes[nodeId];
    return node?.name || nodeId;
  }

  getChapterInfo(nodeId) {
    const node = this.knowledgeGraph.nodes[nodeId];
    return node?.chapterNumber || 'X';
  }

  calculateDifficultyDistribution(enrichedPath) {
    const distribution = { easy: 0, medium: 0, hard: 0, expert: 0 };
    
    enrichedPath.forEach(item => {
      if (item.node && item.node.difficulty) {
        distribution[item.node.difficulty]++;
      }
    });

    return distribution;
  }

  countPrerequisitesCovered(enrichedPath) {
    return enrichedPath.filter(item => 
      item.prerequisites && item.prerequisites.length > 0
    ).length;
  }
}

module.exports = LearningPathGenerator; 