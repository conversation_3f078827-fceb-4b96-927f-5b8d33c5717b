// 智能诊断系统最终状态检查器 v3.0.0
const fs = require('fs');
const path = require('path');

class FinalStatusChecker {
  constructor() {
    this.results = {
      coreModules: {},
      testFiles: {},
      deploymentFiles: {},
      documentation: {},
      configuration: {},
      statistics: {
        totalFiles: 0,
        codeFiles: 0,
        testFiles: 0,
        documentFiles: 0,
        totalLines: 0
      }
    };
    this.issues = [];
    this.recommendations = [];
  }

  /**
   * 执行完整状态检查
   */
  async performCompleteCheck() {
    console.log('🔍 开始最终状态检查...\n');

    try {
      // 1. 检查核心模块
      await this.checkCoreModules();
      
      // 2. 检查测试文件
      await this.checkTestFiles();
      
      // 3. 检查部署文件
      await this.checkDeploymentFiles();
      
      // 4. 检查文档
      await this.checkDocumentation();
      
      // 5. 检查配置文件
      await this.checkConfiguration();
      
      // 6. 统计项目规模
      await this.calculateProjectStatistics();
      
      // 7. 生成最终报告
      const report = this.generateFinalReport();
      
      console.log('\n✅ 最终状态检查完成');
      return report;
      
    } catch (error) {
      console.error('❌ 状态检查失败:', error);
      throw error;
    }
  }

  /**
   * 检查核心模块
   */
  async checkCoreModules() {
    console.log('📦 检查核心模块...');
    
    const coreModules = [
      'index.js',
      'diagnosis-engine.js',
      'ai-enhanced-analyzer.js',
      'learning-outcome-predictor.js',
      'real-time-behavior-analyzer.js',
      'adaptive-path-optimizer.js',
      'learning-path-generator.js',
      'report-generator.js',
      'config.js'
    ];

    for (const module of coreModules) {
      try {
        const filePath = path.join(__dirname, module);
        const stats = fs.statSync(filePath);
        const content = fs.readFileSync(filePath, 'utf8');
        
        // 检查模块大小和内容
        this.results.coreModules[module] = {
          exists: true,
          size: `${(stats.size / 1024).toFixed(2)}KB`,
          lines: content.split('\n').length,
          version: this.extractVersion(content),
          hasTests: this.checkIfHasTests(module),
          lastModified: stats.mtime.toISOString()
        };
        
        // 检查模块完整性
        if (stats.size < 1000) {
          this.issues.push(`⚠️ ${module} 文件过小，可能不完整`);
        }
        
        if (!content.includes('class ') && !content.includes('function ')) {
          this.issues.push(`⚠️ ${module} 可能不包含主要功能代码`);
        }
        
        console.log(`  ✅ ${module} - ${this.results.coreModules[module].size}`);
        
      } catch (error) {
        this.results.coreModules[module] = {
          exists: false,
          error: error.message
        };
        this.issues.push(`❌ ${module} 缺失或无法访问: ${error.message}`);
        console.log(`  ❌ ${module} - 缺失`);
      }
    }
  }

  /**
   * 检查测试文件
   */
  async checkTestFiles() {
    console.log('\n🧪 检查测试文件...');
    
    const testFiles = [
      'simple-test.js',
      'optimized-comprehensive-test.js',
      'demo-diagnosis-fixed.js',
      'test-diagnosis.js',
      'performance-monitor.js'
    ];

    for (const testFile of testFiles) {
      try {
        const filePath = path.join(__dirname, testFile);
        const stats = fs.statSync(filePath);
        const content = fs.readFileSync(filePath, 'utf8');
        
        this.results.testFiles[testFile] = {
          exists: true,
          size: `${(stats.size / 1024).toFixed(2)}KB`,
          lines: content.split('\n').length,
          testCases: this.countTestCases(content),
          coverage: this.estimateTestCoverage(testFile),
          lastModified: stats.mtime.toISOString()
        };
        
        console.log(`  ✅ ${testFile} - ${this.results.testFiles[testFile].testCases} 测试用例`);
        
      } catch (error) {
        this.results.testFiles[testFile] = {
          exists: false,
          error: error.message
        };
        console.log(`  ❌ ${testFile} - 缺失`);
      }
    }
  }

  /**
   * 检查部署文件
   */
  async checkDeploymentFiles() {
    console.log('\n🚀 检查部署文件...');
    
    const deploymentFiles = [
      '快速部署脚本.bat',
      'run-demo.bat',
      'package.json',
      'Git提交指南.md'
    ];

    for (const deployFile of deploymentFiles) {
      try {
        const filePath = path.join(__dirname, deployFile);
        const stats = fs.statSync(filePath);
        
        this.results.deploymentFiles[deployFile] = {
          exists: true,
          size: `${(stats.size / 1024).toFixed(2)}KB`,
          lastModified: stats.mtime.toISOString(),
          ready: true
        };
        
        if (deployFile === 'package.json') {
          const content = fs.readFileSync(filePath, 'utf8');
          const packageInfo = JSON.parse(content);
          this.results.deploymentFiles[deployFile].version = packageInfo.version;
          this.results.deploymentFiles[deployFile].scripts = Object.keys(packageInfo.scripts).length;
        }
        
        console.log(`  ✅ ${deployFile}`);
        
      } catch (error) {
        this.results.deploymentFiles[deployFile] = {
          exists: false,
          error: error.message
        };
        console.log(`  ❌ ${deployFile} - 缺失`);
      }
    }
  }

  /**
   * 检查文档
   */
  async checkDocumentation() {
    console.log('\n📚 检查文档...');
    
    const documentFiles = [
      'README.md',
      '部署指导文档.md',
      '项目总结.md'
    ];

    let totalDocLines = 0;
    
    for (const docFile of documentFiles) {
      try {
        const filePath = path.join(__dirname, docFile);
        const stats = fs.statSync(filePath);
        const content = fs.readFileSync(filePath, 'utf8');
        const lines = content.split('\n').length;
        totalDocLines += lines;
        
        this.results.documentation[docFile] = {
          exists: true,
          size: `${(stats.size / 1024).toFixed(2)}KB`,
          lines: lines,
          sections: this.countMarkdownSections(content),
          lastModified: stats.mtime.toISOString()
        };
        
        console.log(`  ✅ ${docFile} - ${lines} 行，${this.results.documentation[docFile].sections} 章节`);
        
      } catch (error) {
        this.results.documentation[docFile] = {
          exists: false,
          error: error.message
        };
        console.log(`  ❌ ${docFile} - 缺失`);
      }
    }
    
    this.results.statistics.documentationLines = totalDocLines;
  }

  /**
   * 检查配置文件
   */
  async checkConfiguration() {
    console.log('\n⚙️ 检查配置...');
    
    try {
      const packagePath = path.join(__dirname, 'package.json');
      const packageContent = fs.readFileSync(packagePath, 'utf8');
      const packageInfo = JSON.parse(packageContent);
      
      this.results.configuration.package = {
        name: packageInfo.name,
        version: packageInfo.version,
        scripts: Object.keys(packageInfo.scripts).length,
        dependencies: Object.keys(packageInfo.dependencies || {}).length,
        devDependencies: Object.keys(packageInfo.devDependencies || {}).length,
        files: (packageInfo.files || []).length
      };
      
      console.log(`  ✅ package.json - v${packageInfo.version}`);
      console.log(`  📝 ${this.results.configuration.package.scripts} npm scripts`);
      console.log(`  📦 ${this.results.configuration.package.dependencies} 依赖项`);
      
    } catch (error) {
      this.issues.push('❌ package.json 解析失败');
    }
  }

  /**
   * 计算项目统计
   */
  async calculateProjectStatistics() {
    console.log('\n📊 计算项目统计...');
    
    const allFiles = fs.readdirSync(__dirname);
    let totalLines = 0;
    let codeFiles = 0;
    let testFiles = 0;
    let documentFiles = 0;
    
    for (const file of allFiles) {
      try {
        const filePath = path.join(__dirname, file);
        const stats = fs.statSync(filePath);
        
        if (stats.isFile()) {
          this.results.statistics.totalFiles++;
          
          const ext = path.extname(file);
          if (ext === '.js') {
            const content = fs.readFileSync(filePath, 'utf8');
            const lines = content.split('\n').length;
            totalLines += lines;
            
            if (file.includes('test') || file.includes('demo')) {
              testFiles++;
            } else {
              codeFiles++;
            }
          } else if (ext === '.md') {
            const content = fs.readFileSync(filePath, 'utf8');
            totalLines += content.split('\n').length;
            documentFiles++;
          }
        }
      } catch (error) {
        // 忽略无法读取的文件
      }
    }
    
    this.results.statistics.totalLines = totalLines;
    this.results.statistics.codeFiles = codeFiles;
    this.results.statistics.testFiles = testFiles;
    this.results.statistics.documentFiles = documentFiles;
    
    console.log(`  📁 总文件数: ${this.results.statistics.totalFiles}`);
    console.log(`  📄 代码文件: ${codeFiles}`);
    console.log(`  🧪 测试文件: ${testFiles}`);
    console.log(`  📚 文档文件: ${documentFiles}`);
    console.log(`  📝 总行数: ${totalLines.toLocaleString()}`);
  }

  /**
   * 生成最终报告
   */
  generateFinalReport() {
    const report = {
      timestamp: new Date().toISOString(),
      projectStatus: this.issues.length === 0 ? 'READY' : 'WARNING',
      version: '3.0.0',
      completeness: this.calculateCompleteness(),
      results: this.results,
      issues: this.issues,
      recommendations: this.generateRecommendations(),
      summary: this.generateSummary()
    };
    
    return report;
  }

  /**
   * 计算完整度
   */
  calculateCompleteness() {
    const coreModuleCount = Object.keys(this.results.coreModules).length;
    const existingCoreModules = Object.values(this.results.coreModules).filter(m => m.exists).length;
    
    const testFileCount = Object.keys(this.results.testFiles).length;
    const existingTestFiles = Object.values(this.results.testFiles).filter(t => t.exists).length;
    
    const docFileCount = Object.keys(this.results.documentation).length;
    const existingDocFiles = Object.values(this.results.documentation).filter(d => d.exists).length;
    
    const coreCompleteness = (existingCoreModules / coreModuleCount) * 100;
    const testCompleteness = (existingTestFiles / testFileCount) * 100;
    const docCompleteness = (existingDocFiles / docFileCount) * 100;
    
    return {
      overall: Math.round((coreCompleteness + testCompleteness + docCompleteness) / 3),
      core: Math.round(coreCompleteness),
      tests: Math.round(testCompleteness),
      documentation: Math.round(docCompleteness)
    };
  }

  /**
   * 生成建议
   */
  generateRecommendations() {
    const recommendations = [];
    
    if (this.issues.length > 0) {
      recommendations.push('解决检测到的问题以确保系统稳定运行');
    }
    
    const completeness = this.calculateCompleteness();
    if (completeness.overall < 95) {
      recommendations.push('完善缺失的文件和功能以提高系统完整性');
    }
    
    if (this.results.statistics.testFiles < 3) {
      recommendations.push('增加更多测试用例以提高代码覆盖率');
    }
    
    if (recommendations.length === 0) {
      recommendations.push('系统状态良好，可以进行生产环境部署');
      recommendations.push('建议定期进行性能监控和系统维护');
      recommendations.push('考虑添加更多高级功能和优化');
    }
    
    return recommendations;
  }

  /**
   * 生成摘要
   */
  generateSummary() {
    const completeness = this.calculateCompleteness();
    
    return {
      description: 'K12数学智能诊断系统 - 完整功能版本',
      status: this.issues.length === 0 ? '✅ 准备就绪' : '⚠️ 需要注意',
      completeness: `${completeness.overall}% 完整`,
      codeQuality: completeness.core > 90 ? '优秀' : '良好',
      testCoverage: completeness.tests > 80 ? '充分' : '基本',
      documentation: completeness.documentation > 90 ? '完整' : '基本',
      deploymentReady: this.issues.length < 3,
      productionReady: this.issues.length === 0 && completeness.overall > 95
    };
  }

  // 辅助方法
  extractVersion(content) {
    const versionMatch = content.match(/version.*['"`]([^'"`]+)['"`]/i);
    return versionMatch ? versionMatch[1] : 'unknown';
  }

  checkIfHasTests(module) {
    const testFiles = ['simple-test.js', 'optimized-comprehensive-test.js'];
    return testFiles.some(testFile => {
      try {
        const testContent = fs.readFileSync(path.join(__dirname, testFile), 'utf8');
        return testContent.includes(module.replace('.js', ''));
      } catch {
        return false;
      }
    });
  }

  countTestCases(content) {
    const testMatches = content.match(/test|describe|it\(/gi);
    return testMatches ? testMatches.length : 0;
  }

  estimateTestCoverage(testFile) {
    if (testFile.includes('comprehensive')) return '高覆盖率';
    if (testFile.includes('simple')) return '基本覆盖';
    if (testFile.includes('performance')) return '性能测试';
    return '未知';
  }

  countMarkdownSections(content) {
    const sectionMatches = content.match(/^#+\s/gm);
    return sectionMatches ? sectionMatches.length : 0;
  }
}

// 导出模块
module.exports = FinalStatusChecker;

// 注释掉不兼容的代码，云函数环境中不支持 require.main === module
// 如果需要执行状态检查，可以在云函数入口中直接调用
/*
// 如果直接运行此文件，执行状态检查
if (require.main === module) {
  async function runStatusCheck() {
    const checker = new FinalStatusChecker();
    
    try {
      const report = await checker.performCompleteCheck();
      
      console.log('\n' + '='.repeat(60));
      console.log('📋 最终状态报告');
      console.log('='.repeat(60));
      
      console.log(`🚀 项目状态: ${report.summary.status}`);
      console.log(`📊 完整度: ${report.summary.completeness}`);
      console.log(`💯 代码质量: ${report.summary.codeQuality}`);
      console.log(`🧪 测试覆盖: ${report.summary.testCoverage}`);
      console.log(`📚 文档状态: ${report.summary.documentation}`);
      console.log(`🎯 部署就绪: ${report.summary.deploymentReady ? '✅ 是' : '❌ 否'}`);
      console.log(`🏭 生产就绪: ${report.summary.productionReady ? '✅ 是' : '❌ 否'}`);
      
      if (report.issues.length > 0) {
        console.log('\n⚠️ 发现的问题:');
        report.issues.forEach(issue => console.log(`  ${issue}`));
      }
      
      if (report.recommendations.length > 0) {
        console.log('\n💡 建议:');
        report.recommendations.forEach((rec, index) => {
          console.log(`  ${index + 1}. ${rec}`);
        });
      }
      
      console.log('\n📈 项目统计:');
      console.log(`  📁 总文件: ${report.results.statistics.totalFiles}`);
      console.log(`  📝 总行数: ${report.results.statistics.totalLines.toLocaleString()}`);
      console.log(`  📦 核心模块: ${Object.keys(report.results.coreModules).length}`);
      console.log(`  🧪 测试文件: ${Object.keys(report.results.testFiles).length}`);
      
      console.log('\n✨ 状态检查完成!');
      
      // 保存详细报告
      const reportFile = `final-status-report-${Date.now()}.json`;
      fs.writeFileSync(reportFile, JSON.stringify(report, null, 2));
      console.log(`📄 详细报告已保存: ${reportFile}`);
      
    } catch (error) {
      console.error('状态检查失败:', error);
      process.exit(1);
    }
  }

  runStatusCheck();
}
*/ 