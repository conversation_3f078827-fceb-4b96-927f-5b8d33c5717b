{"version": "1.0.0", "description": "K12数学学习智能体微信小程序 Cursor Rules 配置索引", "lastUpdated": "2024-12-19", "rules": {"always_apply": {"description": "自动应用的规则文件，每次 AI 交互时都会生效", "files": [{"file": "code-standards.mdc", "description": "项目代码标准规范", "size": "7.2KB", "scope": "编码规范、组件开发、图标使用、样式命名"}, {"file": "design-style.mdc", "description": "设计风格指南", "size": "3.2KB", "scope": "色彩系统、字体规范、布局规范、交互设计"}, {"file": "database-structure.mdc", "description": "数据库结构规范", "size": "6.6KB", "scope": "数据库设计、字段规范、SQL语法"}, {"file": "device-adaptation.mdc", "description": "设备适配规范", "size": "3.4KB", "scope": "响应式设计、多设备兼容"}, {"file": "error-handling.mdc", "description": "错误处理规范", "size": "2.9KB", "scope": "异常处理、错误提示、容错机制"}, {"file": "iconfont-guide.mdc", "description": "图标字体使用指南", "size": "3.3KB", "scope": "图标管理、SVG使用、样式规范"}, {"file": "performance-optimization.mdc", "description": "性能优化规范", "size": "3.8KB", "scope": "加载优化、渲染性能、内存管理"}]}, "manual_apply": {"description": "手动应用的规则文件，在特定场景下触发", "files": [{"file": "project-structure.mdc", "description": "项目结构指南", "size": "4.4KB", "scope": "目录结构、文件组织、命名规范"}, {"file": "miniprogram-development.mdc", "description": "微信小程序开发规范", "size": "3.2KB", "scope": "页面跳转、分包加载、性能优化"}, {"file": "mathematical-content-formatting.mdc", "description": "数学内容格式化规范", "size": "79KB", "scope": "KaTeX语法、数学公式、PostgreSQL转义"}, {"file": "wechat-miniprogram-compatibility.mdc", "description": "微信小程序兼容性", "size": "3.4KB", "scope": "版本兼容、API适配、平台差异"}, {"file": "page-navigation.mdc", "description": "页面导航规范", "size": "8.6KB", "scope": "路由管理、页面跳转、参数传递"}, {"file": "svg-base64-display-rule.mdc", "description": "SVG显示规范", "size": "2.2KB", "scope": "SVG处理、Base64编码、图像显示"}, {"file": "feature-preservation.mdc", "description": "功能保护规范", "size": "4.1KB", "scope": "向前兼容、功能稳定性、变更管理"}, {"file": "package-structure.mdc", "description": "分包结构规范", "size": "4.8KB", "scope": "分包策略、模块划分、依赖管理"}, {"file": "data-models.mdc", "description": "数据模型规范", "size": "1.9KB", "scope": "数据结构、模型定义、字段规范"}, {"file": "math-learning-path.mdc", "description": "数学学习路径规范", "size": "1.5KB", "scope": "学习路径、知识图谱、教育逻辑"}]}}, "usage": {"auto_rules": "标记为 alwaysApply: true 的规则会在每次 AI 对话时自动应用", "manual_rules": "需要在特定场景下手动引用或通过 description 字段触发", "file_location": ".cursor/rules/ 目录下的 .mdc 文件", "modification": "修改规则时请同步更新此索引文件"}, "statistics": {"total_rules": 17, "always_apply_rules": 7, "manual_apply_rules": 10, "total_size": "约120KB"}}