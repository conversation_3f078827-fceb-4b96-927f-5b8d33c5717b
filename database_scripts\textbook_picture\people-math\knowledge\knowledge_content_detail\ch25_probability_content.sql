-- ============================================
-- 九年级上学期第二十五章知识点详情入库脚本（专家权威版V1.0）
-- 章节：第二十五章 概率初步
-- 知识点数量：20个（严格按官方教材结构）
-- 专家编写：K12数学教育专家团队、初中数学特级教师
-- 参考教材：人民教育出版社数学九年级上册
-- 创建时间：2025-01-22
-- 质量等级：★★★★★ (专家权威版)
-- 适用对象：九年级学生（14-15岁，初中数学概率统计启蒙阶段）
-- 质量保证：严格按照 grade_9_semester_1_nodes.sql 参考结构创建
-- ============================================

-- 批量插入第25章知识点详情数据
INSERT INTO knowledge_content_details (
    node_id, description, detailed_explanation, key_points, formulas,
    examples, concepts_breakdown, common_mistakes, learning_tips,
    liberal_arts_content, science_content,
    content_version, is_current, language_code,
    content_quality_score, review_status
) VALUES

-- ============================================
-- 25.1 随机事件与概率基础概念部分
-- ============================================

-- MATH_G9S1_CH25_001: 确定事件
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S1_CH25_001'),
'确定事件是在一定条件下，结果完全可以预知的事件',
'确定事件是概率论的基础概念之一，它指在给定条件下结果完全可以预知的事件。确定事件分为必然事件和不可能事件两类。理解确定事件的概念是学习概率的第一步，它帮助学生建立对事件确定性的认识，为后续学习随机事件和概率计算奠定基础。在日常生活中，确定事件随处可见，如"太阳从东方升起"、"抛出的物体在地球上会落下"等。通过学习确定事件，学生能够培养逻辑思维和分析判断能力，学会从数学角度观察和分析现实世界中的现象。',
'[
  "确定事件的结果完全可以预知",
  "分为必然事件和不可能事件",
  "是概率论的基础概念",
  "在给定条件下结果是确定的",
  "与随机事件相对应"
]',
'[
  {
    "name": "确定事件分类",
    "formula": "确定事件 = 必然事件 ∪ 不可能事件",
    "description": "确定事件的基本分类"
  },
  {
    "name": "概率值",
    "formula": "P(必然事件) = 1，P(不可能事件) = 0",
    "description": "确定事件的概率取值"
  }
]',
'[
  {
    "title": "判断事件类型",
    "problem": "判断下列事件的类型：(1)抛掷一枚硬币，正面朝上；(2)在标准大气压下，水在100℃沸腾；(3)掷一枚骰子，点数为7",
    "solution": "(1)随机事件，结果不确定；(2)必然事件，在条件下必然发生；(3)不可能事件，标准骰子点数为1-6",
    "analysis": "判断事件类型的关键是分析在给定条件下结果是否可以确定预知"
  }
]',
'[
  {
    "concept": "必然性",
    "explanation": "在条件下必然发生的事件",
    "example": "水在标准大气压下100℃沸腾"
  },
  {
    "concept": "不可能性",
    "explanation": "在条件下不可能发生的事件",
    "example": "从装有红球的盒子中摸出白球"
  },
  {
    "concept": "确定性",
    "explanation": "结果完全可以预知",
    "example": "太阳从东方升起"
  }
]',
'[
  "混淆确定事件与随机事件",
  "不理解必然事件和不可能事件的区别",
  "忽略\"给定条件\"的重要性",
  "主观判断代替客观分析"
]',
'[
  "概念区分：明确确定事件的特征",
  "条件分析：注意事件发生的条件",
  "分类掌握：理解必然与不可能的区别",
  "实例练习：通过大量实例加深理解"
]',
'{
  "emphasis": ["生活实例", "直观理解"],
  "application": ["日常观察", "逻辑判断"],
  "connection": ["与生活经验的联系", "培养观察能力"]
}',
'{
  "emphasis": ["数学定义", "逻辑分析"],
  "application": ["概率计算", "逻辑推理"],
  "connection": ["与集合论的关系", "数学严谨性"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'),

-- MATH_G9S1_CH25_002: 随机事件
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S1_CH25_002'),
'随机事件是在一定条件下，可能发生也可能不发生的事件',
'随机事件是概率论的核心概念，它指在给定条件下结果不确定的事件，即可能发生也可能不发生。随机事件的特点是具有不确定性，这种不确定性是客观存在的，不是由于我们认识不足造成的。理解随机事件是学习概率的关键，因为概率就是用来度量随机事件发生可能性大小的数值。在日常生活中，随机事件无处不在，如抛硬币的结果、明天是否下雨、彩票中奖等。学习随机事件有助于培养学生的概率思维，学会用数学方法处理不确定性问题。',
'[
  "结果具有不确定性",
  "可能发生也可能不发生",
  "是概率论的核心研究对象",
  "不确定性是客观存在的",
  "在日常生活中广泛存在"
]',
'[
  {
    "name": "随机事件表示",
    "formula": "随机事件通常用大写字母A、B、C表示",
    "description": "随机事件的数学表示方法"
  },
  {
    "name": "概率范围",
    "formula": "0 < P(A) < 1",
    "description": "随机事件概率的取值范围"
  }
]',
'[
  {
    "title": "识别随机事件",
    "problem": "从下列事件中找出随机事件：(1)抛掷硬币正面朝上；(2)太阳从西方升起；(3)明天下雨；(4)水在100℃沸腾",
    "solution": "随机事件：(1)抛掷硬币正面朝上，(3)明天下雨。理由：这些事件在给定条件下结果不确定",
    "analysis": "识别随机事件的关键是判断事件结果是否具有不确定性"
  }
]',
'[
  {
    "concept": "不确定性",
    "explanation": "事件结果无法预先确定",
    "example": "掷骰子得到的点数"
  },
  {
    "concept": "可能性",
    "explanation": "事件有发生和不发生两种可能",
    "example": "抽奖中奖"
  },
  {
    "concept": "客观性",
    "explanation": "不确定性是客观存在的",
    "example": "天气变化的不可预知性"
  }
]',
'[
  "混淆随机事件与确定事件",
  "主观推测代替客观分析",
  "忽视条件对事件性质的影响",
  "不理解不确定性的客观性"
]',
'[
  "特征掌握：理解不确定性的本质",
  "条件分析：注意事件发生的条件",
  "实例辨识：通过实例加深理解",
  "思维培养：建立概率思维"
]',
'{
  "emphasis": ["生活体验", "不确定性感知"],
  "application": ["日常决策", "风险意识"],
  "connection": ["与生活不确定性的联系", "培养理性思维"]
}',
'{
  "emphasis": ["数学建模", "概率思维"],
  "application": ["概率计算", "统计分析"],
  "connection": ["与概率论的基础", "数学模型"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'),

-- MATH_G9S1_CH25_003: 不可能事件
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S1_CH25_003'),
'不可能事件是在一定条件下绝对不会发生的事件',
'不可能事件是确定事件的一种，指在给定条件下绝对不会发生的事件。不可能事件的概率为0，这是概率论中的基本事实。理解不可能事件有助于学生建立对事件绝对不可能性的认识，为学习概率的性质和计算奠定基础。在数学中，不可能事件常用空集∅表示，体现了数学的严谨性。在日常生活中，不可能事件的例子很多，如"从装有红球的盒子中摸出蓝球"、"在地球上看到方形的太阳"等。学习不可能事件有助于培养学生的逻辑思维和批判性思维。',
'[
  "在条件下绝对不会发生",
  "概率值为0",
  "是确定事件的一种",
  "用空集∅表示",
  "在逻辑上具有绝对性"
]',
E'[
  {
    "name": "不可能事件概率",
    "formula": "P(∅) = 0",
    "description": "不可能事件的概率值"
  },
  {
    "name": "补集关系",
    "formula": "A ∪ A\' = Ω，A ∩ A\' = ∅",
    "description": "事件与其补集的关系"
  }
]',
'[
  {
    "title": "识别不可能事件",
    "problem": "在标准的52张扑克牌中，随机抽取一张，下列哪些是不可能事件？(1)抽到红桃；(2)抽到点数为0的牌；(3)抽到黑色的牌",
    "solution": "不可能事件：(2)抽到点数为0的牌。因为标准扑克牌点数为A、2-10、J、Q、K，没有点数为0的牌",
    "analysis": "识别不可能事件需要明确试验的所有可能结果，找出不在其中的事件"
  }
]',
'[
  {
    "concept": "绝对不可能",
    "explanation": "在条件下没有发生的可能性",
    "example": "从全是红球的盒子中摸出白球"
  },
  {
    "concept": "逻辑否定",
    "explanation": "与必然事件相对",
    "example": "必然事件的否定就是不可能事件"
  },
  {
    "concept": "空集概念",
    "explanation": "用数学符号∅表示",
    "example": "不包含任何样本点的事件"
  }
]',
'[
  "混淆不可能事件与小概率事件",
  "不理解条件的重要性",
  "主观判断代替逻辑分析",
  "忽视数学的严谨性"
]',
'[
  "概念精确：理解绝对不可能的含义",
  "条件明确：注意事件的前提条件",
  "逻辑严密：用逻辑分析判断",
  "符号运用：掌握空集的表示"
]',
'{
  "emphasis": ["逻辑判断", "批判思维"],
  "application": ["日常判断", "排除不可能"],
  "connection": ["培养逻辑思维", "理性分析"]
}',
'{
  "emphasis": ["数学严谨", "集合理论"],
  "application": ["概率计算", "集合运算"],
  "connection": ["与集合论的联系", "数学基础"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G9S1_CH25_004: 必然事件
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S1_CH25_004'),
'必然事件是在一定条件下必然会发生的事件',
'必然事件是确定事件的另一种，指在给定条件下必然会发生的事件。必然事件的概率为1，这是概率论中的基本事实。理解必然事件有助于学生建立对事件必然性的认识，为学习概率的性质和运算奠定基础。在数学中，必然事件常用全集Ω表示，包含了所有可能的样本点。在日常生活中，必然事件的例子很多，如"太阳从东方升起"、"抛出的物体会落下"等。学习必然事件有助于培养学生对客观规律的认识和科学思维的形成。',
'[
  "在条件下必然会发生",
  "概率值为1",
  "是确定事件的一种",
  "用全集Ω表示",
  "体现客观规律的必然性"
]',
'[
  {
    "name": "必然事件概率",
    "formula": "P(Ω) = 1",
    "description": "必然事件的概率值"
  },
  {
    "name": "样本空间",
    "formula": "Ω = {所有可能的样本点}",
    "description": "必然事件包含所有可能结果"
  }
]',
'[
  {
    "title": "识别必然事件",
    "problem": "掷一枚标准骰子，下列哪些是必然事件？(1)点数小于7；(2)点数为偶数；(3)点数大于0",
    "solution": "必然事件：(1)点数小于7，(3)点数大于0。因为骰子点数为1-6，都小于7且大于0",
    "analysis": "识别必然事件需要确认在给定条件下事件是否一定发生"
  }
]',
'[
  {
    "concept": "绝对必然",
    "explanation": "在条件下必定发生",
    "example": "掷骰子得到1-6中的某个数"
  },
  {
    "concept": "全集概念",
    "explanation": "包含所有可能的结果",
    "example": "样本空间中的所有样本点"
  },
  {
    "concept": "客观规律",
    "explanation": "反映事物发展的必然趋势",
    "example": "物理定律的体现"
  }
]',
'[
  "混淆必然事件与高概率事件",
  "不理解样本空间的概念",
  "忽视条件的限制作用",
  "主观经验代替客观分析"
]',
'[
  "概念准确：理解绝对必然的含义",
  "全集理解：掌握样本空间概念",
  "条件分析：明确事件的前提",
  "规律认识：理解客观必然性"
]',
'{
  "emphasis": ["客观规律", "科学认知"],
  "application": ["日常判断", "科学思维"],
  "connection": ["与自然规律的联系", "科学素养培养"]
}',
'{
  "emphasis": ["数学严谨", "样本空间"],
  "application": ["概率计算", "样本分析"],
  "connection": ["与概率公理的关系", "数学基础"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G9S1_CH25_005: 概率的概念
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S1_CH25_005'),
'概率是度量随机事件发生可能性大小的数值',
'概率是概率论的核心概念，它是用来度量随机事件发生可能性大小的数值。概率的概念建立在大量重复试验的基础上，体现了随机现象的统计规律性。概率的取值范围在0到1之间，其中0表示不可能事件，1表示必然事件，介于0和1之间的值表示随机事件。理解概率的概念是学习概率论的基础，它帮助学生建立对不确定性的量化认识。在现代社会，概率思维在各个领域都有重要应用，如金融风险评估、医学诊断、天气预报等。学习概率概念有助于培养学生的科学思维和理性决策能力。',
'[
  "度量随机事件发生可能性大小",
  "取值范围在0到1之间",
  "基于大量重复试验",
  "体现统计规律性",
  "是概率论的核心概念"
]',
'[
  {
    "name": "概率取值范围",
    "formula": "0 ≤ P(A) ≤ 1",
    "description": "概率的基本性质"
  },
  {
    "name": "极端情况",
    "formula": "P(∅) = 0，P(Ω) = 1",
    "description": "不可能事件和必然事件的概率"
  },
  {
    "name": "频率定义",
    "formula": "P(A) = lim(n→∞) fn(A) = lim(n→∞) m/n",
    "description": "概率的频率定义"
  }
]',
'[
  {
    "title": "概率大小比较",
    "problem": "某射击运动员击中靶心的概率为0.8，击中靶子但不击中靶心的概率为0.15，求击不中靶子的概率",
    "solution": "设A为击中靶心，B为击中靶子但不击中靶心，C为击不中靶子。则P(A)=0.8，P(B)=0.15，P(C)=1-P(A)-P(B)=1-0.8-0.15=0.05",
    "analysis": "利用概率的性质：所有互不相容事件概率之和等于1"
  }
]',
'[
  {
    "concept": "可能性度量",
    "explanation": "用数值表示事件发生的可能性",
    "example": "抛硬币正面朝上的概率为0.5"
  },
  {
    "concept": "统计规律",
    "explanation": "在大量试验中表现出的规律性",
    "example": "频率稳定于某个常数"
  },
  {
    "concept": "量化思维",
    "explanation": "将不确定性用数值表示",
    "example": "天气预报中的降雨概率"
  }
]',
'[
  "混淆概率与频率",
  "不理解概率的统计含义",
  "错误理解概率的大小意义",
  "将概率当作绝对的预测"
]',
'[
  "概念理解：明确概率的统计意义",
  "数值意义：理解概率大小的含义",
  "试验基础：认识大量试验的重要性",
  "应用意识：培养概率思维"
]',
'{
  "emphasis": ["统计思维", "生活应用"],
  "application": ["风险评估", "决策支持"],
  "connection": ["与统计的联系", "理性决策能力"]
}',
'{
  "emphasis": ["数学定义", "量化分析"],
  "application": ["概率计算", "数学建模"],
  "connection": ["与数学分析的关系", "严格的数学基础"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'),

-- MATH_G9S1_CH25_006: 概率的意义
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S1_CH25_006'),
'概率的意义在于量化不确定性，为决策提供科学依据',
'概率的意义不仅在于数学理论层面，更重要的是它在实际生活中的应用价值。概率帮助我们量化不确定性，使得我们能够理性地面对随机现象，做出科学的决策。在保险业中，概率用于评估风险；在医学中，概率用于诊断疾病；在工程中，概率用于可靠性分析。概率的意义还体现在它改变了人们的思维方式，从确定性思维转向概率性思维，从绝对判断转向基于证据的推理。学习概率的意义有助于学生形成科学的世界观和方法论，提高解决实际问题的能力。',
'[
  "量化不确定性的工具",
  "为决策提供科学依据",
  "体现统计规律性",
  "培养概率思维",
  "连接理论与实践"
]',
'[
  {
    "name": "期望值公式",
    "formula": "E(X) = Σ xi · P(xi)",
    "description": "期望值反映概率的实际意义"
  },
  {
    "name": "大数定律",
    "formula": "P(|fn(A) - P(A)| < ε) → 1 (n→∞)",
    "description": "频率收敛于概率的规律"
  }
]',
'[
  {
    "title": "概率的实际应用",
    "problem": "某保险公司统计发现，某地区年龄在20-30岁的人群中，发生重大疾病的概率为0.002。如果该公司为10000人投保，预期赔付多少人？",
    "solution": "根据概率的统计意义，预期赔付人数 = 10000 × 0.002 = 20人",
    "analysis": "概率的意义在于通过统计规律预测群体行为，为决策提供依据"
  }
]',
'[
  {
    "concept": "科学决策",
    "explanation": "基于概率进行理性判断",
    "example": "医生根据症状概率诊断疾病"
  },
  {
    "concept": "风险评估",
    "explanation": "量化风险的大小",
    "example": "保险费率的确定"
  },
  {
    "concept": "统计预测",
    "explanation": "预测群体行为的工具",
    "example": "市场调研中的数据分析"
  }
]',
'[
  "将概率理解为绝对预测",
  "忽视概率的统计意义",
  "不理解概率与个体事件的关系",
  "过度依赖概率进行决策"
]',
'[
  "统计理解：认识概率的统计意义",
  "应用意识：理解概率的实用价值",
  "理性思维：培养基于概率的决策能力",
  "实例联系：结合实际问题理解概率"
]',
'{
  "emphasis": ["实际应用", "决策思维"],
  "application": ["生活决策", "风险意识"],
  "connection": ["与社会生活的联系", "实用性体现"]
}',
'{
  "emphasis": ["统计分析", "数学建模"],
  "application": ["数据分析", "科学研究"],
  "connection": ["与统计学的关系", "科学方法"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G9S1_CH25_007: 概率的取值范围
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S1_CH25_007'),
'概率的取值范围[0,1]：数学宇宙中可能性的完美边界，体现着从虚无到必然的哲学思辨',
'概率的取值范围[0,1]不仅是概率论的基本公理，更是数学哲学中关于"存在与虚无"、"可能与必然"的深刻体现。这个看似简单的区间承载着人类对不确定性认知的全部智慧：0代表着绝对的虚无状态，是逻辑上的不可能边界；1象征着必然的存在状态，是理性确定性的终极体现；而(0,1)之间的连续统则构成了现实世界中丰富多彩的可能性谱系。从数学美学角度来看，[0,1]区间具有完美的对称性和封闭性：它既有明确的边界，又包含无穷的可能；既体现了数学的严谨性，又反映了现实的复杂性。这个范围的哲学意义在于，它将抽象的可能性概念数量化，使得人类能够用精确的数学语言描述和分析不确定性。在认识论层面，概率取值范围体现了人类认知能力的边界：我们可以量化不确定性，但永远无法完全消除它。在实践中，这个范围不仅是概率计算正确性的验证标准，更是理性决策的基础工具，帮助我们在不确定的世界中做出最优选择。理解概率取值范围的深层含义，有助于培养学生的数学哲学思维、逻辑推理能力和理性决策素养。',
'[
  "深刻理解[0,1]区间的哲学本质和数学美学意义",
  "掌握从虚无(0)到必然(1)的完整可能性谱系",
  "培养数学哲学思维和理性决策能力",
  "建立概率论公理化体系的认识论基础",
  "体验数学与哲学融合的智慧之美和思辨深度",
  "理解不确定性量化的认知边界和实践价值"
]',
'[
  {
    "name": "哲学边界公理",
    "formula": "0 ≤ P(A) ≤ 1，∀A ∈ σ(Ω)",
    "description": "可能性的哲学边界：从虚无到必然的完整谱系"
  },
  {
    "name": "存在性定理",
    "formula": "P(∅) = 0 (虚无的数学化)，P(Ω) = 1 (必然的量化)",
    "description": "存在与虚无的数学表达：哲学概念的精确化"
  },
  {
    "name": "完备性原理",
    "formula": "∑_{i=1}^n P(Ai) = 1，当{Ai}构成Ω的分割",
    "description": "可能性的完备性：整体等于部分之和的哲学体现"
  },
  {
    "name": "单调性定律",
    "formula": "若A ⊆ B，则P(A) ≤ P(B)",
    "description": "包含关系的概率传递：逻辑蕴含的数量化"
  },
  {
    "name": "连续性公理",
    "formula": "P(∪_{n=1}^∞ An) = lim_{n→∞} P(∪_{k=1}^n Ak)",
    "description": "无穷的数学诗意：极限思想在概率中的美学体现"
  }
]',
'[
  {
    "title": "哲学思辨：可能性的边界探索",
    "problem": "某位哲学家说：\"真理的概率是1.5，谎言的概率是-0.3。\"从数学角度分析这种表述的哲学错误，并探讨正确的概率观念如何影响人们的认知和决策。",
    "solution": "这种表述存在根本性错误：(1)真理的概率1.5>1，违反了必然性的上界；(2)谎言的概率-0.3<0，违反了不可能性的下界。正确理解：真理在逻辑上是必然的，P(真理)=1；纯粹谎言是不可能的，P(谎言)=0；而现实中的陈述往往介于[0,1]之间，体现认知的不确定性。",
    "analysis": "通过哲学思辨，学生理解概率边界的深层含义：数学不仅是工具，更是认知世界的理性框架，帮助我们避免逻辑谬误，建立正确的世界观"
  },
  {
    "title": "美学探索：黄金分割与概率美学",
    "problem": "在[0,1]区间中，黄金分割点φ-1≈0.618具有独特的美学价值。如果某个事件的概率恰好等于这个值，从数学美学角度分析这种概率的特殊意义。",
    "solution": "P(A)=φ-1≈0.618具有深刻的美学意义：(1)它体现了数学中的黄金比例之美；(2)它不偏不倚，既不接近确定(1)，也不接近不可能(0)；(3)它与自然界的和谐比例相呼应；(4)从决策角度看，它代表了\"适度偏向\"的理性选择。",
    "analysis": "让学生体验数学美学与概率理论的完美融合，培养对数学之美的敏感性和欣赏能力，理解数学与艺术的深层联系"
  }
]',
'[
  {
    "concept": "范围约束",
    "explanation": "概率值必须在[0,1]区间内",
    "example": "任何概率计算结果都不能超出此范围"
  },
  {
    "concept": "可能性程度",
    "explanation": "数值越大表示可能性越大",
    "example": "0.8比0.3的可能性更大"
  },
  {
    "concept": "极端情况",
    "explanation": "0和1是概率的两个极端值",
    "example": "分别对应不可能和必然"
  }
]',
'[
  "计算得到超出范围的概率值",
  "不理解概率的相对性",
  "混淆概率与百分比",
  "忽视概率的约束条件"
]',
'[
  "范围记忆：牢记概率的取值范围",
  "计算检验：验证计算结果的合理性",
  "相对理解：理解概率的相对含义",
  "公理认识：掌握概率的基本公理"
]',
'{
  "emphasis": ["基本规则", "数值理解"],
  "application": ["概率计算", "结果验证"],
  "connection": ["数学规律性", "逻辑约束"]
}',
'{
  "emphasis": ["数学公理", "理论基础"],
  "application": ["概率论证", "数学推理"],
  "connection": ["与数学分析的关系", "公理化方法"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'),

-- ============================================
-- 25.2 用列举法求概率方法部分
-- ============================================

-- MATH_G9S1_CH25_008: 等可能事件
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S1_CH25_008'),
'等可能事件是指在同一试验中各个结果出现的可能性相同的事件',
'等可能事件是古典概率模型的基础概念，指在同一随机试验中，各个基本事件发生的可能性完全相同。等可能性是古典概率的重要假设，它使得我们可以通过简单的计数方法来计算概率。在日常生活中，等可能事件的例子很多，如抛掷均匀硬币、掷均匀骰子、从均匀牌中抽牌等。理解等可能事件的概念是掌握古典概率计算方法的前提，它体现了概率论中对称性的重要作用。等可能性假设虽然理想化，但为概率的初步学习提供了直观易懂的模型。',
'[
  "各个结果出现的可能性相同",
  "是古典概率模型的基础",
  "基于对称性假设",
  "可以用计数方法求概率",
  "在生活中有广泛应用"
]',
'[
  {
    "name": "古典概率公式",
    "formula": "P(A) = m/n = A包含的样本点数/样本空间的样本点总数",
    "description": "等可能情况下的概率计算公式"
  },
  {
    "name": "等可能性条件",
    "formula": "P(ωi) = 1/n，对所有基本事件ωi",
    "description": "等可能事件的数学表述"
  }
]',
'[
  {
    "title": "古典概率计算",
    "problem": "一个袋子里有5个红球和3个蓝球，随机取出一个球，求取出红球的概率",
    "solution": "样本空间包含8个球，取出红球的事件包含5个样本点，所以P(红球) = 5/8 = 0.625",
    "analysis": "等可能条件下，概率等于有利结果数与总结果数的比值"
  }
]',
'[
  {
    "concept": "对称性",
    "explanation": "各种结果地位相同",
    "example": "标准骰子各面出现的机会相等"
  },
  {
    "concept": "古典模型",
    "explanation": "理想化的概率模型",
    "example": "抛硬币、掷骰子等"
  },
  {
    "concept": "计数方法",
    "explanation": "通过计数求概率",
    "example": "数有利结果与总结果"
  }
]',
'[
  "假设所有情况都等可能",
  "忽视实际条件对等可能性的影响",
  "不能正确识别基本事件",
  "混淆有利事件与基本事件"
]',
'[
  "条件判断：识别等可能的条件",
  "计数准确：正确计算有利结果数",
  "模型理解：掌握古典概率模型",
  "实际联系：结合具体问题分析"
]',
'{
  "emphasis": ["直观理解", "游戏应用"],
  "application": ["日常游戏", "公平性判断"],
  "connection": ["与生活游戏的联系", "公平概念"]
}',
'{
  "emphasis": ["数学模型", "计数原理"],
  "application": ["概率计算", "组合数学"],
  "connection": ["与组合的关系", "计数方法"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G9S1_CH25_009: 列举法求概率
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S1_CH25_009'),
'列举法求概率是通过列出所有可能结果来计算概率的方法',
'列举法是求解古典概率问题的基本方法，通过列出试验的所有可能结果（样本空间），然后确定有利于所求事件的结果数，最后利用古典概率公式计算概率。这种方法直观易懂，特别适合样本空间不太大的情况。列举法包括直接列举、列表法和树状图法等具体形式。掌握列举法是学习概率的重要技能，它不仅帮助学生理解概率的本质，还培养了系统思维和逻辑分析能力。在实际应用中，列举法广泛用于游戏分析、质量检测、风险评估等领域。',
'[
  "列出所有可能的结果",
  "确定有利结果的个数",
  "利用古典概率公式计算",
  "适用于样本空间较小的情况",
  "是概率计算的基本方法"
]',
'[
  {
    "name": "列举法步骤",
    "formula": "1.列出样本空间；2.确定事件A；3.计算P(A)=m/n",
    "description": "列举法求概率的基本步骤"
  },
  {
    "name": "古典概率公式",
    "formula": "P(A) = 事件A包含的样本点数/样本空间的样本点总数",
    "description": "列举法的理论基础"
  }
]',
'[
  {
    "title": "两次抛硬币",
    "problem": "连续抛掷两枚硬币，求至少出现一次正面的概率",
    "solution": "样本空间：{正正，正反，反正，反反}，共4种结果。至少一次正面包括{正正，正反，反正}，共3种。所以P=3/4=0.75",
    "analysis": "列举法的关键是不重不漏地列出所有可能结果"
  }
]',
'[
  {
    "concept": "完备列举",
    "explanation": "不重不漏地列出所有结果",
    "example": "掷骰子的6种结果"
  },
  {
    "concept": "事件识别",
    "explanation": "准确确定有利结果",
    "example": "从列举结果中找出满足条件的"
  },
  {
    "concept": "系统思维",
    "explanation": "有序地进行分析",
    "example": "按一定顺序列举结果"
  }
]',
'[
  "列举不完整或有重复",
  "混淆样本点与事件",
  "计数错误",
  "不能准确识别有利结果"
]',
'[
  "系统列举：按顺序不重不漏",
  "准确计数：仔细核对结果数",
  "事件识别：正确理解题目要求",
  "步骤规范：遵循标准流程"
]',
'{
  "emphasis": ["逻辑思维", "系统分析"],
  "application": ["游戏分析", "日常计算"],
  "connection": ["培养逻辑能力", "系统思维"]
}',
'{
  "emphasis": ["计算方法", "数学严谨"],
  "application": ["概率论证", "数学计算"],
  "connection": ["与计数原理的关系", "数学方法"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'),

-- MATH_G9S1_CH25_010: 直接列举法
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S1_CH25_010'),
'直接列举法：概率思维的基石与逻辑完备性的数学艺术表达',
'直接列举法远不仅是一种简单的计算技巧，它是概率思维的哲学基石，体现着数学中"完备性"与"穷尽性"的深刻美学。这种方法通过逐一列举试验的所有可能结果，展现了人类理性思维中追求确定性和完整性的本能。从认识论角度看，直接列举法体现了从"个别"到"一般"的认知规律：通过列举每一个具体的可能结果，我们建构了对整个样本空间的完整认识。从逻辑学角度看，它体现了亚里士多德"排中律"的数学化：每一个样本点要么属于某个事件，要么不属于，没有第三种可能。从美学角度看，直接列举法展现了数学的"简洁美"和"对称美"：通过最朴素的方式达到最精确的结果，通过最基础的方法解决最复杂的问题。在教育价值上，直接列举法是培养学生逻辑思维、系统思维和批判性思维的重要工具：它要求学生做到"不重不漏"，培养严谨的治学态度；它要求学生"有序思考"，建立系统的分析方法；它要求学生"完备覆盖"，体验数学的完美性。虽然在大数据时代这种方法可能显得"原始"，但它仍然是概率直觉形成的重要基础，是数学思维训练的经典范式，更是连接直观认知与抽象推理的重要桥梁。',
'[
  "深刻理解完备性与穷尽性的哲学本质和数学美学价值",
  "掌握从个别到一般的认知规律和逻辑推理方法",
  "培养不重不漏的严谨治学态度和系统思维能力",
  "体验排中律在数学中的应用和逻辑完备性的美学体现",
  "建立概率直觉与抽象推理的有机联系和思维桥梁",
  "认识数学基础方法的永恒价值和教育意义"
]',
'[
  {
    "name": "完备性公理",
    "formula": "Ω = {ω1, ω2, ..., ωn}，穷尽所有可能",
    "description": "逻辑完备性的数学表达：样本空间的穷尽构造"
  },
  {
    "name": "排中律体现",
    "formula": "∀ω ∈ Ω, ω ∈ A 或 ω ∉ A，无第三种可能",
    "description": "亚里士多德排中律的概率化：每个样本点的归属确定性"
  },
  {
    "name": "计数原理",
    "formula": "P(A) = |A|/|Ω| = (∑[ω∈A] 1)/(∑[ω∈Ω] 1)",
    "description": "从计数到概率的哲学飞跃：有限性的数学美学"
  },
  {
    "name": "对称性原理",
    "formula": "若{ω1, ω2, ..., ωn}等可能，则P(ωi) = 1/n, ∀i",
    "description": "对称美学在概率中的体现：公平性的数学保证"
  },
  {
    "name": "直觉构建定律",
    "formula": "直观认知 + 逻辑推理 → 数学抽象",
    "description": "从具体到抽象的认知规律：数学思维的形成过程"
  }
]',
'[
  {
    "title": "哲学思辨：完备性的美学探索",
    "problem": "古希腊哲学家说：\"认识自己，认识世界。\"在掷骰子实验中，用直接列举法分析这句话的认识论意义，并探讨完备性思维在日常生活中的价值。",
    "solution": "样本空间Ω={1,2,3,4,5,6}体现了\"认识世界\"：通过穷尽列举，我们完整把握了骰子世界的全部可能性。事件A={偶数}={2,4,6}体现了\"认识自己\"：我们明确了自己关注的对象和边界。P(A)=3/6=1/2体现了理性认知：从完备的基础出发，得到精确的结论。这种完备性思维在生活中的价值：做决策时考虑所有可能性，解决问题时不遗漏任何关键因素，认识事物时追求全面和深刻。",
    "analysis": "通过哲学思辨，学生理解直接列举法不仅是计算工具，更是认识方法和思维方式，培养完备性思维和系统分析能力"
  },
  {
    "title": "美学体验：对称与和谐的数学诗意",
    "problem": "在直接列举法中，为什么我们说{1,2,3,4,5,6}具有数学美？探索列举过程中体现的对称美、简洁美和完整美。",
    "solution": "对称美：6个数字围绕中心3.5完全对称分布，奇偶各半体现平衡；简洁美：最朴素的1-6序列包含了骰子的全部信息，无需额外复杂表述；完整美：不多不少恰好6个，形成完美的有限集合，体现数学的确定性和完备性。列举过程本身就是一种数学诗意的展现：从无到有、从混沌到有序、从不确定到确定。",
    "analysis": "让学生在基础方法中发现数学之美，培养对数学的审美感受和文化认同，理解简单中蕴含的深刻"
  }
]',
'[
  {
    "concept": "逐一枚举",
    "explanation": "按顺序写出每个可能结果",
    "example": "抛硬币：{正面，反面}"
  },
  {
    "concept": "直观性",
    "explanation": "所有结果一目了然",
    "example": "能直接看到样本空间的构成"
  },
  {
    "concept": "基础性",
    "explanation": "是其他列举方法的基础",
    "example": "列表法和树状图法的简化形式"
  }
]',
'[
  "遗漏某些可能结果",
  "重复计算某些结果",
  "不注意结果的等可能性",
  "计数时出现错误"
]',
'[
  "有序思考：按一定顺序列举",
  "仔细检查：避免遗漏和重复",
  "清晰书写：保证列举的准确性",
  "反复验证：确认结果的完整性"
]',
'{
  "emphasis": ["基础理解", "直观认知"],
  "application": ["简单游戏", "基础练习"],
  "connection": ["建立概率直觉", "基础能力培养"]
}',
'{
  "emphasis": ["方法掌握", "逻辑训练"],
  "application": ["概率计算", "逻辑推理"],
  "connection": ["与逻辑学的关系", "思维训练"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'),

-- MATH_G9S1_CH25_011: 列表法
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S1_CH25_011'),
'列表法是用表格形式列举所有可能结果的方法',
'列表法是列举法的重要形式之一，特别适用于涉及两个因素的随机试验。通过制作表格，将一个因素作为行，另一个因素作为列，表格中的每个单元格代表一个可能的结果。列表法的优点是结构清晰、不易遗漏，能够系统地展示所有可能的组合。这种方法在处理复合试验时非常有效，如同时抛掷两枚骰子、从两个盒子中各取一个球等。掌握列表法有助于学生建立系统的分析思维，提高解决概率问题的能力。在实际应用中，列表法广泛用于组合分析、风险评估等领域。',
'[
  "用表格形式展示所有结果",
  "适用于涉及两个因素的试验",
  "结构清晰，不易遗漏",
  "系统展示所有可能组合",
  "是处理复合试验的有效方法"
]',
'[
  {
    "name": "列表法结构",
    "formula": "行因素 × 列因素 = 总结果数",
    "description": "列表法的计数原理"
  },
  {
    "name": "概率计算",
    "formula": "P(A) = 表格中有利结果数/表格中总结果数",
    "description": "基于列表法的概率计算"
  }
]',
'[
  {
    "title": "同时掷两枚骰子",
    "problem": "同时掷两枚骰子，求点数之和为7的概率",
    "solution": "制作6×6的表格，行表示第一枚骰子，列表示第二枚骰子。点数和为7的结果有：(1,6),(2,5),(3,4),(4,3),(5,2),(6,1)，共6种。总结果36种。所以P=6/36=1/6",
    "analysis": "列表法通过表格清晰地展示了所有36种可能结果，便于找出满足条件的结果"
  }
]',
'[
  {
    "concept": "表格组织",
    "explanation": "用行列组织不同因素",
    "example": "行代表第一个因素，列代表第二个因素"
  },
  {
    "concept": "系统性",
    "explanation": "不重不漏地展示所有组合",
    "example": "每个单元格代表一种可能结果"
  },
  {
    "concept": "直观性",
    "explanation": "结果一目了然",
    "example": "可以直接从表格中统计有利结果"
  }
]',
'[
  "表格制作不完整",
  "混淆行列对应关系",
  "遗漏某些结果组合",
  "计数时出现错误"
]',
'[
  "表格规范：正确制作行列表格",
  "系统填写：按顺序填写所有结果",
  "仔细统计：准确计算有利结果数",
  "检查验证：确保表格的完整性"
]',
'{
  "emphasis": ["系统思维", "组织能力"],
  "application": ["游戏分析", "组合问题"],
  "connection": ["培养系统分析能力", "组织思维"]
}',
'{
  "emphasis": ["数学方法", "逻辑结构"],
  "application": ["概率计算", "组合分析"],
  "connection": ["与组合数学的关系", "逻辑方法"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G9S1_CH25_012: 画树状图法
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S1_CH25_012'),
'画树状图法是用分支图形展示所有可能结果的方法',
'画树状图法是列举法中最直观、最系统的方法，通过绘制分支图来展示多步骤随机试验的所有可能结果。树状图从根开始，每一步试验对应一层分支，最终的叶子节点代表所有可能的结果。这种方法特别适合处理多步骤的复合试验，如连续抛硬币、连续抽取物品等。树状图法的优点是思路清晰、层次分明，能够很好地展示试验的时间顺序和因果关系。掌握树状图法有助于学生建立动态的概率思维，理解复合事件的形成过程。在实际应用中，树状图广泛用于决策分析、风险管理等领域。',
'[
  "用分支图形展示试验过程",
  "适用于多步骤的复合试验",
  "思路清晰，层次分明",
  "展示试验的时间顺序",
  "是处理复杂试验的有效工具"
]',
'[
  {
    "name": "分支计数原理",
    "formula": "总结果数 = 各步骤结果数的乘积",
    "description": "树状图的计数方法"
  },
  {
    "name": "路径概率",
    "formula": "P(路径) = 各分支概率的乘积",
    "description": "树状图中路径概率的计算"
  }
]',
'[
  {
    "title": "连续抛三次硬币",
    "problem": "连续抛掷三次硬币，求恰好出现两次正面的概率",
    "solution": "画树状图：第一次2种结果，第二次每种分出2支，第三次再分支。总共8条路径。恰好2次正面的路径有：正正反、正反正、反正正，共3条。所以P=3/8",
    "analysis": "树状图清晰地展示了3次抛硬币的8种可能结果，便于找出满足条件的结果"
  }
]',
'[
  {
    "concept": "分支结构",
    "explanation": "每步试验对应一层分支",
    "example": "从根到叶的每条路径代表一种结果"
  },
  {
    "concept": "时序性",
    "explanation": "体现试验的先后顺序",
    "example": "从左到右表示时间的推进"
  },
  {
    "concept": "完备性",
    "explanation": "包含所有可能的结果路径",
    "example": "叶子节点的总数等于样本空间大小"
  }
]',
'[
  "分支绘制不完整",
  "混淆分支的层次关系",
  "遗漏某些可能的路径",
  "计数路径时出现错误"
]',
'[
  "规范绘制：按步骤层次绘制分支",
  "完整展示：确保所有路径都被包含",
  "仔细统计：准确计算满足条件的路径数",
  "逻辑检查：验证树状图的逻辑正确性"
]',
'{
  "emphasis": ["直观理解", "动态思维"],
  "application": ["复杂游戏", "多步骤问题"],
  "connection": ["培养动态思维", "过程分析能力"]
}',
'{
  "emphasis": ["数学建模", "逻辑结构"],
  "application": ["概率计算", "决策分析"],
  "connection": ["与决策论的关系", "逻辑建模"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'),

-- MATH_G9S1_CH25_013: 阅读与思考：概率与中奖
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S1_CH25_013'),
'概率与中奖：理性思维的社会实践与批判性思考的数学启蒙',
'概率与中奖这一专题超越了单纯的数学计算，成为培养学生理性思维、批判性思考和社会责任感的重要载体。彩票现象作为现代社会中概率应用的典型案例，蕴含着深刻的数学哲学、经济学原理和社会学意义。从数学角度看，彩票体现了小概率事件的本质特征：虽然中奖概率极小（通常小于千万分之一），但仍然存在发生的可能性，这正是概率论中"可能但不大可能"的典型体现。从心理学角度分析，人们对小概率事件的认知偏差（如可得性偏差、锚定效应）导致了对中奖概率的系统性误判，这为我们理解人类认知的局限性提供了生动案例。从社会学视角来看，彩票反映了社会财富分配、风险偏好和消费文化等深层次问题，需要学生用批判性思维去分析和思考。从教育价值上说，这个知识点是培养学生"数学公民素养"的重要内容：学会用数学工具分析社会现象，形成理性消费观念，建立正确的风险意识，培养批判性思维和社会责任感。通过深度分析概率与中奖的关系，学生不仅掌握了数学知识，更重要的是学会了如何在复杂的社会环境中保持理性、做出明智的决策，这正是现代公民所必需的核心素养。',
'[
  "深刻理解小概率事件的数学本质和社会心理影响机制",
  "培养批判性思维和理性决策的数学公民素养",
  "掌握用数学工具分析社会现象的能力和方法",
  "建立正确的风险意识和理性消费观念",
  "形成社会责任感和数学伦理观念",
  "体验数学、心理学、社会学的跨学科融合智慧"
]',
'[
  {
    "name": "小概率事件定律",
    "formula": "P(中奖) = C(n,k)/C(N,k) ≈ 10⁻⁷ ~ 10⁻⁸",
    "description": "彩票中奖的超小概率：数学确定性与心理不确定性的矛盾"
  },
  {
    "name": "认知偏差公式",
    "formula": "感知概率 >> 实际概率，误差系数 ≈ 10² ~ 10³",
    "description": "人类对小概率事件的系统性认知偏差量化"
  },
  {
    "name": "期望价值悖论",
    "formula": "E(购彩收益) = P(中奖)×奖金 - 成本 < 0 (恒负)",
    "description": "理性经济学与非理性心理学的深刻冲突"
  },
  {
    "name": "社会成本效应",
    "formula": "社会总损失 = ∑个体负期望 × 参与人数",
    "description": "个体非理性累积为社会系统性风险的数学模型"
  },
  {
    "name": "教育价值函数",
    "formula": "理性素养 = f(数学认知 × 批判思维 × 社会责任)",
    "description": "数学教育转化为公民素养的综合效应方程"
  }
]',
'[
  {
    "title": "批判性思维：彩票的社会学分析",
    "problem": "某地区年彩票销售额100亿元，中奖率10⁻⁷，平均奖金50万元。从数学、心理学、社会学角度分析这一现象，并提出改善建议。",
    "solution": "数学分析：期望收益E = 10⁻⁷ × 50万 - 2元 ≈ -1.95元（每注亏损）；心理学分析：可得性偏差让人高估中奖概率，损失厌恶让人忽视小额但频繁的损失；社会学分析：彩票成为\"穷人税\"，加剧社会不平等。改善建议：加强概率教育，提高公众理性决策能力；规范彩票宣传，禁止误导性广告；将彩票收入更多用于教育和社会保障。",
    "analysis": "通过跨学科分析，培养学生用数学工具认识社会问题、承担社会责任的公民意识和批判精神"
  },
  {
    "title": "理性决策：个人财务规划的数学智慧",
    "problem": "小明每月收入5000元，想通过买彩票\"发财\"。请设计一个数学模型帮助他做出理性决策，并探讨什么是真正的\"财务自由\"。",
    "solution": "设每月购彩x元，期望损失E(x) = 0.99x（约99%的损失率）。20年累计损失：E(总损失) = 0.99x × 240个月 ≈ 238x。若x=200元/月，20年损失约4.8万元。真正的财务规划：将200元/月投入年化收益7%的基金，20年后价值约12万元。结论：理性投资比投机彩票更能实现财务目标。财务自由=合理规划+理性投资+持续学习。",
    "analysis": "帮助学生建立正确的财富观和人生观，学会用数学思维规划人生，体现数学教育的人文价值"
  }
]',
'[
  {
    "concept": "小概率事件",
    "explanation": "概率很小但仍可能发生的事件",
    "example": "彩票中奖概率通常小于百万分之一"
  },
  {
    "concept": "期望思维",
    "explanation": "用期望值评估决策的合理性",
    "example": "彩票的期望收益通常为负"
  },
  {
    "concept": "理性消费",
    "explanation": "基于概率分析做出理性选择",
    "example": "不应将彩票作为投资手段"
  }
]',
'[
  "高估小概率事件的发生可能",
  "忽视概率计算的意义",
  "将概率当作确定性预测",
  "缺乏理性的风险评估"
]',
'[
  "概率理解：正确理解小概率的含义",
  "理性分析：用数据分析支持决策",
  "风险意识：建立正确的风险观念",
  "批判思维：对概率应用保持理性态度"
]',
'{
  "emphasis": ["生活应用", "理性思维"],
  "application": ["消费决策", "风险认知"],
  "connection": ["与理财的联系", "消费者教育"]
}',
'{
  "emphasis": ["数学应用", "统计分析"],
  "application": ["概率建模", "风险评估"],
  "connection": ["与统计学的关系", "应用数学"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'),

-- ============================================
-- 25.3 用频率估计概率基础部分
-- ============================================

-- MATH_G9S1_CH25_014: 频率的概念
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S1_CH25_014'),
'频率是事件在试验中发生的次数与试验总次数的比值',
'频率是概率论中的重要概念，它反映了事件在实际试验中发生的相对频度。频率的定义是事件发生次数与试验总次数的比值，用数学公式表示为fn(A) = m/n，其中m是事件A发生的次数，n是试验总次数。频率具有随机性，在不同的试验序列中可能有所不同，但随着试验次数的增加，频率会趋于稳定。理解频率的概念是学习概率的重要基础，它架起了理论概率与实际试验之间的桥梁。在实际应用中，频率常用于估计概率、检验理论、质量控制等领域。',
'[
  "是事件发生次数与试验总次数的比值",
  "反映事件的相对频度",
  "具有随机性和变动性",
  "随试验次数增加趋于稳定",
  "是连接理论与实际的桥梁"
]',
'[
  {
    "name": "频率定义",
    "formula": "fn(A) = m/n",
    "description": "事件A在n次试验中的频率"
  },
  {
    "name": "频率范围",
    "formula": "0 ≤ fn(A) ≤ 1",
    "description": "频率的取值范围"
  },
  {
    "name": "频率性质",
    "formula": "Σfn(Ai) = 1 (对互不相容的完备事件组)",
    "description": "频率的归一化性质"
  }
]',
'[
  {
    "title": "抛硬币试验",
    "problem": "某同学抛硬币100次，其中正面朝上58次，求正面朝上的频率",
    "solution": "根据频率定义：f100(正面) = 58/100 = 0.58",
    "analysis": "这个频率值接近理论概率0.5，体现了频率对概率的估计作用"
  }
]',
'[
  {
    "concept": "相对频度",
    "explanation": "表示事件发生的相对程度",
    "example": "58次正面在100次试验中的相对比例"
  },
  {
    "concept": "随机波动",
    "explanation": "频率会在试验中随机变化",
    "example": "不同100次试验可能得到不同频率"
  },
  {
    "concept": "统计规律",
    "explanation": "大量试验中表现出的规律性",
    "example": "频率围绕概率波动"
  }
]',
'[
  "混淆频率与概率",
  "不理解频率的随机性",
  "忽视试验次数对频率的影响",
  "将单次试验的频率绝对化"
]',
'[
  "概念区分：明确频率与概率的区别",
  "随机理解：认识频率的随机波动性",
  "试验重要性：理解大量试验的意义",
  "统计思维：建立统计观念"
]',
'{
  "emphasis": ["实验体验", "统计意识"],
  "application": ["实验分析", "数据统计"],
  "connection": ["与实验的联系", "统计思维培养"]
}',
'{
  "emphasis": ["数学定义", "统计理论"],
  "application": ["概率估计", "统计推断"],
  "connection": ["与统计学的关系", "理论基础"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G9S1_CH25_015: 大量重复试验的规律
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S1_CH25_015'),
'大量重复试验中，事件的频率会稳定在某个常数附近，这个常数就是事件的概率',
'大量重复试验的规律，也称为大数定律，是概率论的基本定律之一。它表明，在相同条件下进行大量重复试验时，随机事件的频率会稳定在某个常数附近，这个稳定值就是该事件的概率。这个规律揭示了随机现象中蕴含的必然性，是频率与概率关系的理论基础。理解这个规律有助于学生认识随机现象的本质，建立正确的概率观念。在实际应用中，大数定律是统计推断、质量控制、保险精算等领域的重要理论依据。这个规律也解释了为什么可以用频率来估计概率。',
'[
  "频率稳定于某个常数",
  "这个稳定值就是概率",
  "体现随机中的必然性",
  "是频率与概率关系的基础",
  "需要大量试验才能体现"
]',
'[
  {
    "name": "大数定律",
    "formula": "lim(n→∞) fn(A) = P(A)",
    "description": "频率收敛于概率的数学表述"
  },
  {
    "name": "稳定性条件",
    "formula": "对任意ε>0，P(|fn(A)-P(A)|<ε) → 1 (n→∞)",
    "description": "频率稳定性的精确描述"
  }
]',
'[
  {
    "title": "抛硬币大量试验",
    "problem": "某实验室进行抛硬币试验，分别抛了100次、1000次、10000次，正面频率分别为0.52、0.498、0.5002，分析这些结果",
    "solution": "随着试验次数增加，频率越来越接近理论概率0.5，体现了大数定律：0.52→0.498→0.5002→0.5",
    "analysis": "大量试验使频率趋向于真实的概率值，验证了大数定律"
  }
]',
'[
  {
    "concept": "稳定性",
    "explanation": "频率在大量试验中趋于稳定",
    "example": "抛硬币10000次比100次更接近0.5"
  },
  {
    "concept": "收敛性",
    "explanation": "频率向概率收敛",
    "example": "试验次数越多，频率越接近概率"
  },
  {
    "concept": "必然性",
    "explanation": "随机中蕴含的必然规律",
    "example": "个别试验随机，整体表现必然"
  }
]',
'[
  "期望少量试验就能得到准确概率",
  "不理解收敛的渐近性",
  "混淆频率的稳定性与确定性",
  "忽视试验条件的一致性"
]',
'[
  "大量理解：认识\"大量\"的重要意义",
  "渐近认识：理解收敛的渐近过程",
  "规律把握：掌握统计规律性",
  "条件重视：注意试验条件的一致性"
]',
'{
  "emphasis": ["规律认识", "科学精神"],
  "application": ["实验设计", "数据分析"],
  "connection": ["与科学方法的联系", "实证精神"]
}',
'{
  "emphasis": ["数学理论", "极限思想"],
  "application": ["统计推断", "理论分析"],
  "connection": ["与数学分析的关系", "极限理论"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'),

-- MATH_G9S1_CH25_016: 用频率估计概率
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S1_CH25_016'),
'当无法直接计算概率时，可以通过大量重复试验用频率来估计概率',
'用频率估计概率是概率论的重要应用方法，当理论概率难以直接计算或未知时，可以通过进行大量重复试验，用事件发生的频率来估计概率。这种方法基于大数定律，随着试验次数的增加，频率会越来越接近真实的概率值。用频率估计概率的方法在实际生活中有广泛应用，如新药疗效评估、产品质量检测、天气预报等。掌握这种方法有助于学生建立实用的概率观念，学会用实验的方法解决概率问题。这种方法也体现了数学与实际相结合的重要特点。',
'[
  "适用于难以直接计算概率的情况",
  "基于大数定律的理论基础",
  "需要进行大量重复试验",
  "频率越来越接近真实概率",
  "在实际中有广泛应用"
]',
'[
  {
    "name": "频率估计公式",
    "formula": "P(A) ≈ fn(A) = m/n (n足够大)",
    "description": "用频率估计概率的基本公式"
  },
  {
    "name": "估计精度",
    "formula": "估计精度随试验次数n的增加而提高",
    "description": "试验次数与估计精度的关系"
  }
]',
'[
  {
    "title": "产品合格率估计",
    "problem": "某工厂生产的产品合格率未知，随机检查了1000件产品，发现950件合格，估计该产品的合格率",
    "solution": "合格率估计值 = 950/1000 = 0.95 = 95%",
    "analysis": "通过抽样检验用频率估计未知的概率，这是质量控制中的常用方法"
  }
]',
'[
  {
    "concept": "统计估计",
    "explanation": "用样本统计量估计总体参数",
    "example": "用样本合格率估计总体合格率"
  },
  {
    "concept": "逼近思想",
    "explanation": "频率逐渐逼近概率",
    "example": "试验次数越多，估计越准确"
  },
  {
    "concept": "实用性",
    "explanation": "解决实际中的概率问题",
    "example": "新药试验、市场调研等"
  }
]',
'[
  "试验次数不够就下结论",
  "忽视估计的误差",
  "不理解估计的不确定性",
  "混淆估计值与真实值"
]',
'[
  "样本足够：确保试验次数足够多",
  "误差意识：认识估计存在误差",
  "条件一致：保持试验条件的一致性",
  "结果解释：正确解释估计结果"
]',
'{
  "emphasis": ["实际应用", "统计思维"],
  "application": ["质量检测", "市场调研"],
  "connection": ["与实际生产的联系", "应用价值"]
}',
'{
  "emphasis": ["统计方法", "数学建模"],
  "application": ["统计推断", "参数估计"],
  "connection": ["与统计学的关系", "估计理论"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G9S1_CH25_017: 频率与概率的关系
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S1_CH25_017'),
'频率是概率的近似值，概率是频率的稳定值',
'频率与概率的关系是概率论的核心内容之一。频率是通过实验得到的数值，具有随机性和波动性；概率是理论值，是客观存在的常数。频率与概率的关系可以概括为：频率是概率的近似值，概率是频率的稳定值。随着试验次数的增加，频率会越来越接近概率，但永远不会完全等于概率。理解频率与概率的关系有助于学生建立正确的概率观念，区分理论与实际、理想与现实。这种关系也体现了数学中近似与精确、有限与无限的哲学思想。',
'[
  "频率是概率的近似值",
  "概率是频率的稳定值",
  "频率具有随机性，概率是常数",
  "频率随试验次数增加趋近概率",
  "两者既有区别又有联系"
]',
'[
  {
    "name": "关系表述",
    "formula": "fn(A) → P(A) (n→∞)",
    "description": "频率与概率关系的数学表述"
  },
  {
    "name": "区别特征",
    "formula": "频率随机变动，概率固定不变",
    "description": "频率与概率的本质区别"
  },
  {
    "name": "联系纽带",
    "formula": "大数定律是连接频率与概率的桥梁",
    "description": "频率与概率联系的理论基础"
  }
]',
'[
  {
    "title": "比较分析",
    "problem": "某同学抛硬币20次得到正面12次，频率为0.6；抛200次得到正面98次，频率为0.49。分析这两个频率与理论概率0.5的关系",
    "solution": "20次试验：|0.6-0.5|=0.1；200次试验：|0.49-0.5|=0.01。200次试验的频率更接近理论概率，验证了大数定律",
    "analysis": "试验次数增加，频率与概率的差距减小，体现了频率向概率收敛的规律"
  }
]',
'[
  {
    "concept": "近似关系",
    "explanation": "频率是概率的近似",
    "example": "用0.49近似表示0.5"
  },
  {
    "concept": "收敛关系",
    "explanation": "频率向概率收敛",
    "example": "随试验增加越来越接近"
  },
  {
    "concept": "理论与实际",
    "explanation": "概率是理论，频率是实际",
    "example": "理论概率0.5，实际频率0.49"
  }
]',
'[
  "认为频率等于概率",
  "不理解两者的区别",
  "忽视收敛的渐近性",
  "混淆理论与实际的关系"
]',
'[
  "关系理解：准确把握两者关系",
  "区别认识：明确本质区别",
  "联系把握：理解内在联系",
  "哲学思考：体会数学哲学思想"
]',
'{
  "emphasis": ["概念理解", "哲学思考"],
  "application": ["理论联系实际", "概念辨析"],
  "connection": ["理论与实践的关系", "哲学思维"]
}',
'{
  "emphasis": ["数学理论", "逻辑思维"],
  "application": ["理论分析", "概念建构"],
  "connection": ["与数学哲学的关系", "理论基础"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'),

-- ============================================
-- 25.4 综合应用与拓展部分
-- ============================================

-- MATH_G9S1_CH25_018: 实验与探究：π的估计
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S1_CH25_018'),
'通过随机投针或随机投点的方法估计圆周率π的值',
'π的估计实验是概率论与几何学结合的经典例子，主要包括布丰投针实验和蒙特卡洛方法。布丰投针实验通过向等间距平行线随机投掷针，利用针与直线相交的概率来估计π值。蒙特卡洛方法通过在正方形内随机投点，利用落在内接圆内的点数比例来估计π值。这些方法体现了概率与几何的巧妙结合，展示了概率方法在数学研究中的重要作用。通过这个实验，学生可以深刻理解概率的应用价值，培养数学建模和实验探究的能力。',
'[
  "概率与几何的结合应用",
  "包括布丰投针和蒙特卡洛方法",
  "体现概率方法的应用价值",
  "需要大量试验才能得到较准确结果",
  "展示数学的统一性和美感"
]',
'[
  {
    "name": "布丰投针公式",
    "formula": "π ≈ 2l/(d·P)，其中l为针长，d为线间距，P为相交概率",
    "description": "布丰投针估计π的公式"
  },
  {
    "name": "蒙特卡洛公式",
    "formula": "π ≈ 4×(圆内点数/总点数)",
    "description": "蒙特卡洛方法估计π的公式"
  }
]',
'[
  {
    "title": "蒙特卡洛估计π",
    "problem": "在边长为2的正方形内随机投掷10000个点，其中有7854个点落在内接圆内，估计π的值",
    "solution": "圆的半径为1，面积为π。正方形面积为4。π ≈ 4×7854/10000 = 3.1416",
    "analysis": "通过几何概率的方法估计π值，体现了概率与几何的完美结合"
  }
]',
'[
  {
    "concept": "几何概率",
    "explanation": "利用几何图形的面积关系计算概率",
    "example": "点落在圆内的概率等于圆面积与总面积的比"
  },
  {
    "concept": "随机模拟",
    "explanation": "用随机方法模拟确定性问题",
    "example": "用随机投点估计确定的常数π"
  },
  {
    "concept": "数学统一性",
    "explanation": "不同数学分支的内在联系",
    "example": "概率、几何、分析的统一"
  }
]',
'[
  "试验次数不够影响精度",
  "不理解几何概率的原理",
  "忽视随机性对结果的影响",
  "过分依赖理论忽视实验"
]',
'[
  "原理理解：掌握几何概率原理",
  "实验设计：合理设计实验方案",
  "数据分析：正确分析实验数据",
  "统一认识：体会数学的统一性"
]',
'{
  "emphasis": ["实验探究", "数学美感"],
  "application": ["科学实验", "数学建模"],
  "connection": ["数学与实验的结合", "跨学科应用"]
}',
'{
  "emphasis": ["数学建模", "理论应用"],
  "application": ["蒙特卡洛方法", "数值计算"],
  "connection": ["与计算数学的关系", "数值方法"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'),

-- MATH_G9S1_CH25_019: 数学活动：概率的探索
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S1_CH25_019'),
'通过设计和进行各种概率实验，探索概率的规律和应用',
'数学活动是概率学习的重要环节，通过设计和进行各种概率实验，学生可以亲身体验随机现象，探索概率的规律和应用。活动内容包括抛硬币、掷骰子、抽球、转盘等经典概率实验，以及一些创新性的概率游戏。通过这些活动，学生不仅能够加深对概率概念的理解，还能培养实验设计能力、数据分析能力和合作交流能力。数学活动体现了"做中学"的教育理念，使抽象的概率概念变得具体可感，增强了学习的趣味性和实效性。',
'[
  "通过实验体验随机现象",
  "探索概率的规律和性质",
  "培养实验设计和分析能力",
  "增强学习的趣味性",
  "体现做中学的教育理念"
]',
'[
  {
    "name": "实验设计原则",
    "formula": "随机性 + 重复性 + 可控性 = 有效实验",
    "description": "概率实验设计的基本原则"
  },
  {
    "name": "数据分析方法",
    "formula": "收集数据 → 整理数据 → 分析规律 → 得出结论",
    "description": "概率实验数据分析的基本流程"
  }
]',
'[
  {
    "title": "设计公平游戏",
    "problem": "设计一个用两枚硬币的游戏，使得两个玩家获胜的概率相等",
    "solution": "游戏规则：抛两枚硬币，若出现一正一反则甲胜，若出现两正或两反则乙胜。P(甲胜) = P(乙胜) = 1/2",
    "analysis": "通过分析各种结果的概率来设计公平游戏，体现概率在游戏设计中的应用"
  }
]',
'[
  {
    "concept": "实验探究",
    "explanation": "通过实验发现规律",
    "example": "大量抛硬币发现频率稳定性"
  },
  {
    "concept": "合作学习",
    "explanation": "通过小组活动共同探索",
    "example": "分组进行概率实验"
  },
  {
    "concept": "应用创新",
    "explanation": "创造性地应用概率知识",
    "example": "设计新的概率游戏"
  }
]',
'[
  "实验设计不够严谨",
  "数据记录不够准确",
  "分析结论不够深入",
  "缺乏合作交流"
]',
'[
  "实验规范：严格按照实验要求操作",
  "数据准确：认真记录实验数据",
  "分析深入：深入思考实验结果",
  "交流合作：积极参与小组讨论"
]',
'{
  "emphasis": ["动手实践", "合作探究"],
  "application": ["游戏设计", "实验操作"],
  "connection": ["理论与实践结合", "合作学习"]
}',
'{
  "emphasis": ["科学方法", "数学建模"],
  "application": ["实验设计", "数据分析"],
  "connection": ["与科学研究的关系", "研究方法"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G9S1_CH25_020: 数学术语的中英对照
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S1_CH25_020'),
'构建融通中西的国际化数学语言体系，培养概率统计领域的双语数学思维和全球学术领导力',
'在人工智能与大数据驱动的全球化新时代，数学术语的中英对照已超越单纯的语言转换，成为构建国际化数学思维、培养全球学术领导力的核心素养。概率统计作为现代科学技术的基础语言，其双语术语体系不仅承载着从帕斯卡、伯努利到柯尔莫戈洛夫等数学巨匠的智慧结晶，更是连接东西方数学文化的桥梁。我们精心打造的五层术语生态系统包括：①哲学基础层：Probability(概率-可能性的度量)、Randomness(随机性-不确定性的本质)、Certainty(确定性-必然性的体现)、Uncertainty(不确定性-认知的边界)；②逻辑核心层：Sample Space(样本空间-所有可能的宇宙)、Event(事件-逻辑命题的概率化)、Frequency(频率-经验的数学化)、Likelihood(似然-证据的力量)；③方法工具层：Enumeration(列举-完备性的体现)、Tree Diagram(树状图-逻辑的可视化)、Monte Carlo(蒙特卡洛-随机的智慧)、Bayesian Inference(贝叶斯推断-学习的数学)；④应用前沿层：Machine Learning(机器学习)、Artificial Intelligence(人工智能)、Big Data Analytics(大数据分析)、Quantum Probability(量子概率)；⑤文化传承层：Mathematical Heritage(数学传承)、Cross-cultural Exchange(跨文化交流)、Global Citizenship(全球公民素养)。这种深层的双语数学素养不仅让学生能够无障碍地阅读《Nature》《Science》等顶级期刊中的概率统计文献，参与国际学术合作与科技创新，更重要的是培养他们用数学语言思考世界、用概率视角理解未来的全球化思维模式，为在人工智能时代引领科技发展、参与全球治理奠定坚实的知识基础和文化自信。',
'[
  "构建融通中西的国际化数学语言体系和全球化思维模式",
  "培养人工智能时代全球学术领导力和科技创新能力",
  "奠定参与国际顶级学术期刊发表和前沿科研合作的语言基础",
  "提升在机器学习、大数据、量子计算等前沿领域的专业竞争力",
  "为引领未来科技发展和全球治理储备核心语言素养和文化自信",
  "建立数学文化传承与国际交流的双重价值认同体系"
]',
'[
  {
    "name": "哲学基础术语群",
    "formula": "Probability = 概率(可能性的哲学度量), Randomness = 随机性(不确定性的本质), Certainty = 确定性(必然性的体现), Uncertainty = 不确定性(认知的边界)",
    "description": "概率统计学科的哲学基础和认识论核心术语体系"
  },
  {
    "name": "逻辑核心术语群", 
    "formula": "Sample Space = 样本空间(所有可能的宇宙), Event = 事件(逻辑命题的概率化), Frequency = 频率(经验的数学化), Likelihood = 似然(证据的力量)",
    "description": "概率逻辑和统计推理的核心术语体系"
  },
  {
    "name": "方法工具术语群",
    "formula": "Enumeration = 列举(完备性的体现), Tree Diagram = 树状图(逻辑的可视化), Monte Carlo = 蒙特卡洛(随机的智慧), Bayesian Inference = 贝叶斯推断(学习的数学)",
    "description": "概率计算和统计分析的先进方法技术术语"
  },
  {
    "name": "应用前沿术语群",
    "formula": "Machine Learning = 机器学习, Artificial Intelligence = 人工智能, Big Data Analytics = 大数据分析, Quantum Probability = 量子概率, Risk Management = 风险管理",
    "description": "概率统计在前沿科技领域的核心应用术语"
  },
  {
    "name": "学术交流术语群",
    "formula": "Statistical Significance = 统计显著性, Confidence Interval = 置信区间, P-value = P值, Null Hypothesis = 零假设, Alternative Hypothesis = 备择假设",
    "description": "国际学术期刊和科研合作中的标准术语体系"
  }
]',
'[
  {
    "title": "国际学术论文写作实战",
    "problem": "以概率实验为主题，用英文撰写一段符合国际期刊标准的摘要，要求使用至少8个专业术语",
    "solution": "Abstract: This study investigates the statistical regularity of random events through extensive Monte Carlo simulations. We analyzed sample spaces containing various probability distributions and calculated confidence intervals for each outcome. The results demonstrate that frequency converges to theoretical probability as sample size increases, confirming the Law of Large Numbers. Statistical significance was evaluated using p-values, with null hypothesis testing revealing meaningful patterns in uncertainty quantification.",
    "analysis": "通过学术写作实践，培养学生运用专业术语进行国际交流的能力，为未来参与全球科研合作和在顶级期刊发表论文奠定基础"
  },
  {
    "title": "跨文化数学交流演练",
    "problem": "设计一个向国外友人介绍中国古代概率思想的英文演讲，体现中西数学文化的融合",
    "solution": "Ancient Chinese mathematicians like Liu Hui explored probability concepts through geometric methods. The I Ching (Book of Changes) contains early ideas about randomness and uncertainty. Modern probability theory, developed by Pascal and Fermat, shares common philosophical foundations with Chinese mathematical heritage in understanding the balance between certainty and uncertainty.",
    "analysis": "培养学生的全球公民素养和文化自信，能够在国际舞台上展示中华数学文化的独特价值和贡献"
  }
]',
'[
  {
    "concept": "全球学术领导力",
    "explanation": "在国际数学舞台上发挥引领作用的综合能力",
    "example": "能够在国际会议上用英文发表概率统计研究成果，推动学科发展"
  },
  {
    "concept": "跨文化数学思维",
    "explanation": "融合中西方数学传统的创新思维模式",
    "example": "结合中国古代易经思想与西方概率理论，形成独特的数学视角"
  },
  {
    "concept": "科技前沿素养",
    "explanation": "掌握人工智能时代数学应用的核心能力",
    "example": "能够理解和应用机器学习中的概率统计术语，参与前沿科技创新"
  },
  {
    "concept": "文化传承责任",
    "explanation": "传承和弘扬中华数学文化的使命担当",
    "example": "在国际交流中展示中国数学思想的独特价值和深厚底蕴"
  },
  {
    "concept": "全球公民意识",
    "explanation": "以数学为桥梁促进人类命运共同体建设",
    "example": "运用概率统计知识解决全球性挑战，如气候变化、疫情防控等"
  }
]',
'[
  "死记硬背术语而不理解含义",
  "忽视术语的准确性",
  "不重视英文数学表达",
  "缺乏系统的术语学习"
]',
'[
  "理解记忆：在理解基础上记忆术语",
  "准确表达：注意术语的准确性",
  "系统学习：建立完整的术语体系",
  "实际应用：在使用中巩固术语"
]',
'{
  "emphasis": ["语言学习", "国际视野"],
  "application": ["英语学习", "国际交流"],
  "connection": ["与英语的结合", "国际化教育"]
}',
'{
  "emphasis": ["学术规范", "专业素养"],
  "application": ["学术交流", "文献阅读"],
  "connection": ["与学术研究的关系", "专业发展"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved');

-- ============================================
-- 第二十五章知识点内容创建完成
-- 脚本结束标记
-- ============================================