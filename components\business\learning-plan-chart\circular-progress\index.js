// 圆形进度组件
Component({
  properties: {
    // 进度百分比（0-100）
    progress: {
      type: Number,
      value: 0,
      observer(newVal) {
        this.drawProgress(newVal);
      }
    },
    // 组件大小（rpx）
    size: {
      type: Number,
      value: 160
    },
    // 圆环宽度（rpx）
    strokeWidth: {
      type: Number,
      value: 8
    },
    // 单色模式下的颜色
    color: {
      type: String,
      value: '#3E7BFA'
    },
    // 背景色
    backgroundColor: {
      type: String,
      value: '#E5E5E5'
    },
    // 是否使用渐变色
    useGradient: {
      type: Boolean,
      value: true
    },
    // 渐变开始颜色
    startColor: {
      type: String,
      value: '#7B72FB'
    },
    // 渐变结束颜色
    endColor: {
      type: String,
      value: '#5D54FA'
    },
    // 中心标签文本
    label: {
      type: String,
      value: '完成率'
    },
    // 文字颜色
    textColor: {
      type: String,
      value: '#333333'
    },
    // 进度文字大小
    valueSize: {
      type: Number,
      value: 36
    },
    // 标签文字大小
    labelSize: {
      type: Number,
      value: 24
    },
    // 是否显示动画
    animated: {
      type: Boolean,
      value: true
    },
    // 动画持续时间(ms)
    animationDuration: {
      type: Number,
      value: 1000
    }
  },

  data: {
    ctx: null,
    sizeInPx: 0,
    currentProgress: 0,
    isAnimating: false,
    animationTimer: null
  },

  lifetimes: {
    ready: function() {
      this.initCanvas();
    },
    detached: function() {
      if (this.animationTimer) {
        clearTimeout(this.animationTimer);
        this.animationTimer = null;
      }
    }
  },

  methods: {
    // 初始化Canvas
    initCanvas: function() {
      const query = this.createSelectorQuery().in(this);
      query.select('#progressCanvas')
        .fields({ node: true, size: true })
        .exec((res) => {
          if (!res[0] || !res[0].node) {
            console.error('找不到Canvas节点');
            return;
          }

          const canvas = res[0].node;
          const ctx = canvas.getContext('2d');
          
          // 获取设备像素比
          const dpr = wx.getSystemInfoSync().pixelRatio;
          
          // 设置canvas大小，考虑像素比
          const sizeInPx = this.rpxToPx(this.properties.size);
          canvas.width = sizeInPx * dpr;
          canvas.height = sizeInPx * dpr;
          
          // 缩放上下文以适应像素比
          ctx.scale(dpr, dpr);
          
          this.setData({
            ctx: ctx,
            sizeInPx: sizeInPx
          });
          
          // 绘制初始进度
          this.drawProgress(this.properties.progress);
        });
    },
    
    // 将rpx转换为px
    rpxToPx: function(rpx) {
      const systemInfo = wx.getSystemInfoSync();
      return rpx * systemInfo.windowWidth / 750;
    },
    
    // 绘制进度
    drawProgress: function(progress) {
      const { ctx, sizeInPx, isAnimating } = this.data;
      if (!ctx || isAnimating) return;
      
      const {
        strokeWidth, backgroundColor, color, 
        useGradient, startColor, endColor,
        animated, animationDuration
      } = this.properties;
      
      const strokeWidthPx = this.rpxToPx(strokeWidth);
      const radius = (sizeInPx - strokeWidthPx) / 2;
      const centerX = sizeInPx / 2;
      const centerY = sizeInPx / 2;
      
      // 限制进度范围在0-100
      progress = Math.min(100, Math.max(0, progress));
      
      if (animated) {
        // 清除之前的计时器
        if (this.animationTimer) {
          clearTimeout(this.animationTimer);
          this.animationTimer = null;
        }
        
        // 设置动画状态
        this.setData({ isAnimating: true });
        
        // 记录动画开始时间和起始进度
        const startTime = Date.now();
        const startProgress = this.data.currentProgress;
        const endProgress = progress;
        
        // 动画函数
        const animate = () => {
          const now = Date.now();
          const elapsed = now - startTime;
          const progress = Math.min(elapsed / animationDuration, 1);
          
          // 使用缓动函数计算当前进度
          const currentProgress = this.easeOutQuad(
            progress, startProgress, endProgress - startProgress, 1
          );
          
          // 更新内部变量而不是调用setData
          this.data.currentProgress = currentProgress;
          
          // 绘制当前进度
          this.drawProgressFrame(currentProgress);
          
          // 继续动画或结束
          if (progress < 1) {
            // 使用setTimeout代替requestAnimationFrame
            this.animationTimer = setTimeout(() => {
              animate();
            }, 16); // 大约60fps
          } else {
            // 动画结束时更新状态
            this.setData({ 
              currentProgress: endProgress,
              isAnimating: false
            });
            this.animationTimer = null;
          }
        };
        
        // 开始动画
        animate();
      } else {
        // 不使用动画，直接绘制
        this.setData({ currentProgress: progress });
        this.drawProgressFrame(progress);
      }
    },
    
    // 绘制单帧进度
    drawProgressFrame: function(progress) {
      const { ctx, sizeInPx } = this.data;
      const {
        strokeWidth, backgroundColor, color, 
        useGradient, startColor, endColor
      } = this.properties;
      
      const strokeWidthPx = this.rpxToPx(strokeWidth);
      const radius = (sizeInPx - strokeWidthPx) / 2;
      const centerX = sizeInPx / 2;
      const centerY = sizeInPx / 2;
      
      // 清除画布
      ctx.clearRect(0, 0, sizeInPx, sizeInPx);
      
      // 绘制背景圆环
      ctx.beginPath();
      ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
      ctx.strokeStyle = backgroundColor;
      ctx.lineWidth = strokeWidthPx;
      ctx.stroke();
      
      // 如果进度为0，不绘制进度圆环
      if (progress === 0) return;
      
      // 计算进度弧度
      const endAngle = (progress / 100) * 2 * Math.PI - 0.5 * Math.PI;
      
      // 绘制进度圆环
      ctx.beginPath();
      ctx.arc(centerX, centerY, radius, -0.5 * Math.PI, endAngle);
      ctx.lineCap = 'round';
      
      // 设置渐变或单色
      if (useGradient) {
        const gradient = ctx.createLinearGradient(0, 0, sizeInPx, sizeInPx);
        gradient.addColorStop(0, startColor);
        gradient.addColorStop(1, endColor);
        ctx.strokeStyle = gradient;
      } else {
        ctx.strokeStyle = color;
      }
      
      ctx.lineWidth = strokeWidthPx;
      ctx.stroke();
    },
    
    // 缓动函数 - easeOutQuad
    easeOutQuad: function(t, b, c, d) {
      return -c * (t /= d) * (t - 2) + b;
    }
  }
}); 