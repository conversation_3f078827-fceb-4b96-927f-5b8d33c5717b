-- 诊断DELETE语句问题的调试脚本

-- 1. 首先检查要删除的数据是否存在
SELECT 'Step 1: 检查二年级知识点是否存在' as step;
SELECT COUNT(*) as total_grade2_nodes
FROM knowledge_nodes 
WHERE node_code LIKE 'MATH_G2S1_%' OR node_code LIKE 'MATH_G2S2_%';

-- 2. 检查涉及二年级知识点的关系数量
SELECT 'Step 2: 检查涉及二年级知识点的关系数量' as step;
SELECT COUNT(*) as total_relationships_to_delete
FROM knowledge_relationships 
WHERE grade_span=0 AND (
    (source_node_id IN (SELECT id FROM knowledge_nodes WHERE node_code LIKE 'MATH_G2S1_%' OR node_code LIKE 'MATH_G2S2_%'))
    OR (target_node_id IN (SELECT id FROM knowledge_nodes WHERE node_code LIKE 'MATH_G2S1_%' OR node_code LIKE 'MATH_G2S2_%'))
);

-- 3. 检查表结构和约束
SELECT 'Step 3: 检查knowledge_relationships表结构' as step;
\d knowledge_relationships;

-- 4. 检查是否有外键约束阻止删除
SELECT 'Step 4: 检查外键约束' as step;
SELECT 
    tc.constraint_name, 
    tc.table_name, 
    kcu.column_name, 
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name 
FROM 
    information_schema.table_constraints AS tc 
    JOIN information_schema.key_column_usage AS kcu
      ON tc.constraint_name = kcu.constraint_name
      AND tc.table_schema = kcu.table_schema
    JOIN information_schema.constraint_column_usage AS ccu
      ON ccu.constraint_name = tc.constraint_name
      AND ccu.table_schema = tc.table_schema
WHERE tc.constraint_type = 'FOREIGN KEY' 
    AND tc.table_name='knowledge_relationships';

-- 5. 尝试分步删除 - 先删除作为source的关系
SELECT 'Step 5: 准备删除作为source的关系' as step;
SELECT COUNT(*) as source_relationships
FROM knowledge_relationships 
WHERE grade_span=0 AND source_node_id IN (
    SELECT id FROM knowledge_nodes WHERE node_code LIKE 'MATH_G2S1_%' OR node_code LIKE 'MATH_G2S2_%'
);

-- 6. 尝试分步删除 - 再删除作为target的关系
SELECT 'Step 6: 准备删除作为target的关系' as step;
SELECT COUNT(*) as target_relationships
FROM knowledge_relationships 
WHERE grade_span=0 AND target_node_id IN (
    SELECT id FROM knowledge_nodes WHERE node_code LIKE 'MATH_G2S1_%' OR node_code LIKE 'MATH_G2S2_%'
);

-- 7. 检查具体的二年级知识点ID
SELECT 'Step 7: 二年级知识点详情' as step;
SELECT id, node_code, node_name 
FROM knowledge_nodes 
WHERE node_code LIKE 'MATH_G2S1_%' OR node_code LIKE 'MATH_G2S2_%'
ORDER BY node_code
LIMIT 10;

-- 8. 安全的分步删除方案
SELECT 'Step 8: 安全删除方案' as step;

-- 方案A: 先删除source关系
-- DELETE FROM knowledge_relationships 
-- WHERE grade_span=0 AND source_node_id IN (
--     SELECT id FROM knowledge_nodes WHERE node_code LIKE 'MATH_G2S1_%' OR node_code LIKE 'MATH_G2S2_%'
-- );

-- 方案B: 再删除target关系
-- DELETE FROM knowledge_relationships 
-- WHERE grade_span=0 AND target_node_id IN (
--     SELECT id FROM knowledge_nodes WHERE node_code LIKE 'MATH_G2S1_%' OR node_code LIKE 'MATH_G2S2_%'
-- );

-- 方案C: 使用EXISTS子查询（更安全）
-- DELETE FROM knowledge_relationships kr
-- WHERE kr.grade_span = 0 
--   AND (
--     EXISTS (SELECT 1 FROM knowledge_nodes kn WHERE kn.id = kr.source_node_id AND (kn.node_code LIKE 'MATH_G2S1_%' OR kn.node_code LIKE 'MATH_G2S2_%'))
--     OR 
--     EXISTS (SELECT 1 FROM knowledge_nodes kn WHERE kn.id = kr.target_node_id AND (kn.node_code LIKE 'MATH_G2S1_%' OR kn.node_code LIKE 'MATH_G2S2_%'))
--   );
