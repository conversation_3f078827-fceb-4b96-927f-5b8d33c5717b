-- ============================================
-- 九年级上学期第二十三章知识点详情入库脚本（专家权威修正版V2.0）
-- 章节：第二十三章 旋转
-- 知识点数量：20个（严格按官方教材结构）
-- 专家编写：K12数学教育专家团队、初中数学特级教师
-- 参考教材：人民教育出版社数学九年级上册
-- 创建时间：2025-01-22
-- 质量等级：★★★★★ (专家权威修正版)
-- 适用对象：九年级学生（14-15岁，初中数学几何综合阶段）
-- 质量保证：严格按照 grade_9_semester_1_nodes.sql 参考结构创建
-- ============================================

-- 批量插入第23章知识点详情数据
INSERT INTO knowledge_content_details (
    node_id, description, detailed_explanation, key_points, formulas,
    examples, concepts_breakdown, common_mistakes, learning_tips,
    liberal_arts_content, science_content,
    content_version, is_current, language_code,
    content_quality_score, review_status
) VALUES

-- ============================================
-- 23.1 图形的旋转基础概念部分
-- ============================================

-- MATH_G9S1_CH23_001: 旋转的概念
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S1_CH23_001'),
'旋转是在平面内，把一个图形绕着某一点转动一个角度的图形变换',
'旋转是几何变换中的重要概念，它描述了图形在平面内绕固定点转动的过程。旋转变换具有保持图形形状和大小不变的特性，只改变图形的位置和方向。理解旋转概念对于培养学生的空间想象力和几何直观具有重要意义。旋转在日常生活中随处可见，如钟表指针的转动、车轮的滚动、地球的自转等，这些现象都体现了旋转的数学本质。',
'[
  "旋转是图形在平面内的运动变换",
  "旋转过程中图形的形状和大小保持不变",
  "旋转涉及旋转中心、旋转角度和旋转方向",
  "旋转是刚体运动的基本形式",
  "旋转广泛存在于自然界和日常生活中"
]',
'[
  {
    "name": "旋转定义",
    "formula": "在平面内，把图形绕某一点转动一个角度",
    "description": "旋转的基本定义"
  },
  {
    "name": "旋转的特征",
    "formula": "保持图形的形状、大小不变，改变位置",
    "description": "旋转变换的本质特征"
  }
]',
'[
  {
    "title": "识别旋转现象",
    "problem": "下列现象中，哪些属于旋转：(1)钟表指针的转动 (2)电梯的上下运动 (3)汽车方向盘的转动 (4)推拉门的开关",
    "solution": "(1)钟表指针绕中心轴转动，属于旋转 (2)电梯上下运动是平移，不属于旋转 (3)方向盘绕中心转动，属于旋转 (4)推拉门沿直线运动，属于平移",
    "analysis": "判断旋转的关键是看图形是否绕某一点转动"
  }
]',
'[
  {
    "concept": "图形变换",
    "explanation": "改变图形位置、大小或形状的操作",
    "example": "平移、旋转、轴对称都是图形变换"
  },
  {
    "concept": "刚体运动",
    "explanation": "物体在运动过程中形状和大小保持不变",
    "example": "旋转是刚体运动的典型例子"
  },
  {
    "concept": "几何直观",
    "explanation": "通过图形感知和理解数学概念",
    "example": "观察旋转现象培养空间想象力"
  }
]',
'[
  "混淆旋转与平移",
  "不理解旋转中心的作用",
  "忽视旋转方向的重要性",
  "认为旋转会改变图形大小"
]',
'[
  "观察生活：从日常现象中理解旋转",
  "动手操作：用实物演示旋转过程",
  "对比分析：区分旋转与其他图形变换",
  "空间想象：培养几何直观能力"
]',
'{
  "emphasis": ["生活现象", "概念理解"],
  "application": ["几何变换", "空间认知"],
  "connection": ["与物理运动的联系", "几何直观培养"]
}',
'{
  "emphasis": ["数学模型", "抽象思维"],
  "application": ["工程设计", "计算机图形学"],
  "connection": ["与线性代数的关系", "变换矩阵"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G9S1_CH23_002: 旋转的三要素
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S1_CH23_002'),
'确定一个旋转需要三个要素：旋转中心、旋转角度、旋转方向',
'旋转的三要素是完整描述旋转变换的必要条件。旋转中心是图形绕其转动的固定点；旋转角度是图形转动的角度大小；旋转方向通常规定顺时针为负方向，逆时针为正方向。这三个要素缺一不可，它们共同决定了旋转的具体效果。掌握旋转三要素对于准确描述和实施旋转变换至关重要，也是学习后续旋转性质和作图的基础。',
'[
  "旋转中心：图形绕其转动的固定点",
  "旋转角度：图形转动的角度大小",
  "旋转方向：通常以逆时针为正方向",
  "三要素缺一不可，共同确定旋转变换",
  "是描述旋转的完整信息"
]',
'[
  {
    "name": "旋转三要素",
    "formula": "旋转中心 + 旋转角度 + 旋转方向",
    "description": "完整确定旋转变换的三个条件"
  },
  {
    "name": "角度规定",
    "formula": "逆时针方向为正角，顺时针方向为负角",
    "description": "旋转方向的数学规定"
  }
]',
'[
  {
    "title": "确定旋转三要素",
    "problem": "描述钟表分针从12点到3点的旋转：确定其旋转中心、旋转角度和旋转方向",
    "solution": "旋转中心：钟表的中心点；旋转角度：90°；旋转方向：顺时针（负方向）",
    "analysis": "完整描述旋转必须明确三个要素"
  }
]',
'[
  {
    "concept": "旋转中心",
    "explanation": "旋转过程中保持不动的点",
    "example": "钟表中心、地球自转轴心"
  },
  {
    "concept": "旋转角度",
    "explanation": "从初始位置到终止位置的角度",
    "example": "90°、180°、270°等"
  },
  {
    "concept": "旋转方向",
    "explanation": "顺时针或逆时针的转动方向",
    "example": "逆时针为正，顺时针为负"
  }
]',
'[
  "忽略旋转中心的重要性",
  "混淆旋转角度与方向",
  "不理解正负角度的规定",
  "描述旋转时要素不完整"
]',
'[
  "要素记忆：中心、角度、方向三要素",
  "方向规定：逆时针正，顺时针负",
  "完整描述：三要素缺一不可",
  "实例练习：用三要素描述具体旋转"
]',
'{
  "emphasis": ["要素识别", "完整描述"],
  "application": ["旋转描述", "图形分析"],
  "connection": ["与角度概念的联系", "几何语言表达"]
}',
'{
  "emphasis": ["参数化描述", "精确定义"],
  "application": ["程序设计", "机械控制"],
  "connection": ["与坐标变换的关系", "参数方程"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'),

-- MATH_G9S1_CH23_003: 旋转中心
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S1_CH23_003'),
'旋转中心是旋转过程中保持不动的定点，所有点都绕此中心转动',
'旋转中心是旋转变换中的核心概念，它是整个旋转过程的"支点"。旋转中心可以在图形内部、外部或边界上，不同位置的旋转中心会产生不同的旋转效果。理解旋转中心的作用机制对于掌握旋转性质、进行旋转作图都有重要意义。旋转中心的选择往往根据具体问题的需要或图形的对称性来确定。',
'[
  "旋转中心是旋转过程中的不动点",
  "图形上所有点都绕旋转中心转动",
  "旋转中心可能在图形内部、外部或边界上",
  "旋转中心的位置影响旋转效果",
  "是旋转变换的基准点"
]',
'[
  {
    "name": "旋转中心性质",
    "formula": "旋转中心在旋转过程中位置不变",
    "description": "旋转中心的不变性"
  },
  {
    "name": "距离关系",
    "formula": "任意点到旋转中心的距离在旋转前后不变",
    "description": "保距性的体现"
  }
]',
E'[
  {
    "title": "确定旋转中心",
    "problem": "正方形ABCD绕某点旋转90°后得到正方形A\'B\'C\'D\'，如何确定旋转中心？",
    "solution": "连接对应点AA\'、BB\'，作它们的垂直平分线，两条垂直平分线的交点就是旋转中心",
    "analysis": "旋转中心到对应点的距离相等，位于对应点连线的垂直平分线上"
  }
]',
'[
  {
    "concept": "不动点",
    "explanation": "在变换过程中位置不变的点",
    "example": "旋转中心是旋转变换的不动点"
  },
  {
    "concept": "垂直平分线",
    "explanation": "到线段两端距离相等的点的轨迹",
    "example": "旋转中心在对应点连线的垂直平分线上"
  },
  {
    "concept": "中心位置",
    "explanation": "旋转中心相对于图形的位置关系",
    "example": "内心、外心、边上都可能是旋转中心"
  }
]',
'[
  "忽略旋转中心的唯一性",
  "混淆旋转中心与图形中心",
  "不理解旋转中心的确定方法",
  "认为旋转中心必须在图形内部"
]',
'[
  "唯一确定：旋转中心是唯一的",
  "作图方法：利用垂直平分线确定",
  "位置灵活：可在图形内外任意位置",
  "距离性质：到对应点距离相等"
]',
'{
  "emphasis": ["中心概念", "作图技能"],
  "application": ["几何作图", "图形分析"],
  "connection": ["与垂直平分线的联系", "几何性质"]
}',
'{
  "emphasis": ["几何理论", "精确定位"],
  "application": ["工程制图", "坐标几何"],
  "connection": ["与解析几何的关系", "坐标变换"]
}',
'1.0', true, 'zh-CN', 4.7, 'approved'),

-- MATH_G9S1_CH23_004: 旋转角度
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S1_CH23_004'),
'旋转角度是指图形从初始位置转动到终止位置所经过的角度大小',
'旋转角度是旋转变换的重要参数，它决定了图形旋转的程度。旋转角度可以是任意大小，通常用度数或弧度来表示。角度的正负有明确规定：逆时针旋转为正角，顺时针旋转为负角。理解旋转角度的概念有助于学生准确描述旋转变换，为后续学习旋转的性质和应用奠定基础。',
'[
  "旋转角度决定图形旋转的程度",
  "可以用度数或弧度表示",
  "逆时针旋转为正角，顺时针为负角",
  "角度大小可以是任意值",
  "是旋转变换的重要参数"
]',
'[
  {
    "name": "角度表示",
    "formula": "度数：0°到360°；弧度：0到2π",
    "description": "旋转角度的表示方法"
  },
  {
    "name": "角度正负",
    "formula": "逆时针为正（+），顺时针为负（-）",
    "description": "旋转方向与角度符号的对应"
  }
]',
'[
  {
    "title": "计算旋转角度",
    "problem": "时针从2点位置转到5点位置，求其旋转角度",
    "solution": "时针每小时转动30°，从2点到5点共转动3小时，所以旋转角度为3×30°=90°。由于是顺时针转动，角度为-90°",
    "analysis": "根据转动时间和转速计算角度，注意方向的正负"
  }
]',
'[
  {
    "concept": "角度单位",
    "explanation": "度数和弧度的关系与转换",
    "example": "360°=2π弧度，180°=π弧度"
  },
  {
    "concept": "角度范围",
    "explanation": "旋转角度可以超过360°",
    "example": "连续旋转两圈为720°"
  },
  {
    "concept": "方向约定",
    "explanation": "数学中角度方向的标准规定",
    "example": "逆时针为正方向是数学约定"
  }
]',
'[
  "混淆角度大小与方向",
  "不理解负角度的含义",
  "角度单位换算错误",
  "忽略旋转方向的重要性"
]',
'[
  "方向记忆：逆时针正，顺时针负",
  "单位掌握：度数与弧度的转换",
  "范围理解：角度可以大于360°",
  "符号注意：根据方向确定正负"
]',
'{
  "emphasis": ["角度概念", "方向规定"],
  "application": ["角度计算", "图形旋转"],
  "connection": ["与角的概念联系", "三角函数基础"]
}',
'{
  "emphasis": ["数学严谨性", "量化分析"],
  "application": ["精确计算", "科学测量"],
  "connection": ["与物理学的关系", "角速度概念"]
}',
'1.0', true, 'zh-CN', 4.6, 'approved'),

-- MATH_G9S1_CH23_005: 旋转的性质
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S1_CH23_005'),
'旋转变换具有保持图形形状、大小、对应线段长度和对应角度大小不变的性质',
'旋转的性质是旋转变换的核心内容，体现了旋转作为刚体运动的本质特征。这些性质保证了图形在旋转过程中只改变位置而不改变几何特征，这是区分旋转与其他变换的重要依据。理解和掌握旋转性质对于解决旋转相关问题、进行几何推理和证明都具有重要意义。旋转性质也是研究图形对称性和几何不变量的重要基础。',
'[
  "旋转保持图形的形状和大小不变",
  "对应线段的长度相等",
  "对应角的大小相等",
  "图形的面积和周长保持不变",
  "是刚体运动的典型特征"
]',
E'[
  {
    "name": "保形性质",
    "formula": "旋转前后图形全等：形状、大小完全相同",
    "description": "旋转的基本性质"
  },
  {
    "name": "保距性质",
    "formula": "对应点间距离相等：|AB| = |A\'B\'|",
    "description": "线段长度的不变性"
  },
  {
    "name": "保角性质",
    "formula": "对应角相等：∠ABC = ∠A\'B\'C\'",
    "description": "角度大小的不变性"
  }
]',
E'[
  {
    "title": "验证旋转性质",
    "problem": "△ABC绕点O旋转60°得到△A\'B\'C\'，验证旋转的性质",
    "solution": "测量对应线段：AB = A\'B\'，BC = B\'C\'，AC = A\'C\'；测量对应角：∠A = ∠A\'，∠B = ∠B\'，∠C = ∠C\'；面积和周长也相等",
    "analysis": "旋转保持所有几何量不变，只改变位置"
  }
]',
'[
  {
    "concept": "全等变换",
    "explanation": "保持图形形状和大小不变的变换",
    "example": "旋转是全等变换的一种"
  },
  {
    "concept": "几何不变量",
    "explanation": "在变换过程中保持不变的几何量",
    "example": "长度、角度、面积都是不变量"
  },
  {
    "concept": "刚体运动",
    "explanation": "物体各部分相对位置不变的运动",
    "example": "旋转是平面刚体运动"
  }
]',
'[
  "认为旋转会改变图形大小",
  "忽略角度的保持性",
  "混淆旋转与形变",
  "不理解全等的概念"
]',
'[
  "性质记忆：形状、大小、角度都不变",
  "对比理解：与缩放、剪切等变换对比",
  "实验验证：通过测量验证性质",
  "全等概念：理解旋转产生全等图形"
]',
'{
  "emphasis": ["几何性质", "不变量理解"],
  "application": ["几何证明", "图形分析"],
  "connection": ["与全等概念的联系", "几何推理"]
}',
'{
  "emphasis": ["数学严谨性", "变换理论"],
  "application": ["理论数学", "计算几何"],
  "connection": ["与群论的关系", "变换群"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G9S1_CH23_006: 对应点到旋转中心的距离相等
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S1_CH23_006'),
'旋转过程中，图形上任意一点与其对应点到旋转中心的距离相等',
'这是旋转变换的重要性质之一，体现了旋转中心的特殊地位。由于旋转是绕固定点的圆周运动，图形上每个点都在以旋转中心为圆心的圆上运动，因此点到旋转中心的距离（即半径）保持不变。这个性质为寻找旋转中心、验证旋转关系提供了重要依据，也是进行旋转作图的理论基础。',
'[
  "对应点到旋转中心的距离相等",
  "体现了旋转的圆周运动特征",
  "是寻找旋转中心的重要依据",
  "反映了旋转的保距性质",
  "为旋转作图提供理论基础"
]',
E'[
  {
    "name": "距离相等性质",
    "formula": "|OA| = |OA\'|，其中O是旋转中心",
    "description": "对应点到旋转中心距离相等"
  },
  {
    "name": "圆周运动",
    "formula": "每个点在以旋转中心为圆心的圆上运动",
    "description": "旋转的几何本质"
  }
]',
E'[
  {
    "title": "利用距离性质寻找旋转中心",
    "problem": "已知点A旋转后得到点A\'，点B旋转后得到点B\'，如何确定旋转中心O？",
    "solution": "旋转中心O满足|OA| = |OA\'|且|OB| = |OB\'|，所以O在AA\'的垂直平分线上，也在BB\'的垂直平分线上，两条垂直平分线的交点就是旋转中心",
    "analysis": "利用对应点到旋转中心距离相等的性质来确定旋转中心"
  }
]',
'[
  {
    "concept": "垂直平分线",
    "explanation": "到线段两端点距离相等的点的轨迹",
    "example": "旋转中心在对应点连线的垂直平分线上"
  },
  {
    "concept": "等距性",
    "explanation": "保持距离不变的性质",
    "example": "旋转保持点到中心的距离不变"
  },
  {
    "concept": "圆周运动",
    "explanation": "绕固定点的圆形轨迹运动",
    "example": "旋转中每个点的运动轨迹是圆弧"
  }
]',
'[
  "不理解距离相等的几何意义",
  "混淆对应点与任意点",
  "忽略旋转中心的特殊作用",
  "作图时不利用距离性质"
]',
'[
  "距离理解：深刻理解等距的几何意义",
  "作图应用：利用垂直平分线找旋转中心",
  "性质验证：通过测量验证距离相等",
  "圆周思维：将旋转理解为圆周运动"
]',
'{
  "emphasis": ["距离概念", "几何性质"],
  "application": ["旋转中心确定", "几何作图"],
  "connection": ["与圆的性质联系", "垂直平分线"]
}',
'{
  "emphasis": ["数学严谨性", "几何证明"],
  "application": ["理论推导", "计算验证"],
  "connection": ["与解析几何的关系", "距离公式"]
}',
'1.0', true, 'zh-CN', 4.7, 'approved'),

-- MATH_G9S1_CH23_007: 对应角相等
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S1_CH23_007'),
'旋转过程中，图形中任意角与其对应角的大小相等',
'角度的保持是旋转变换保形性质的重要体现。无论是图形内的角还是由对应点构成的角，在旋转前后都保持大小不变。这个性质确保了图形的形状在旋转过程中完全保持，是判断两个图形是否为旋转关系的重要依据。理解这个性质对于进行几何推理、解决旋转相关问题具有重要意义。',
'[
  "图形中任意角与其对应角相等",
  "体现了旋转的保形性质",
  "确保图形形状完全保持",
  "是判断旋转关系的重要依据",
  "适用于所有类型的角"
]',
E'[
  {
    "name": "角度保持性质",
    "formula": "∠ABC = ∠A\'B\'C\'，其中A\'B\'C\'是ABC的对应点",
    "description": "对应角相等的数学表达"
  },
  {
    "name": "旋转角等式",
    "formula": "∠AOA\' = ∠BOB\' = 旋转角（O为旋转中心）",
    "description": "对应点与旋转中心构成的角等于旋转角"
  }
]',
E'[
  {
    "title": "验证对应角相等",
    "problem": "正方形ABCD绕点O旋转90°得到正方形A\'B\'C\'D\'，验证∠ABC = ∠A\'B\'C\'",
    "solution": "测量得∠ABC = 90°，∠A\'B\'C\' = 90°，确实相等。这验证了旋转保持角度不变的性质",
    "analysis": "旋转保持图形中所有角的大小不变"
  }
]',
E'[
  {
    "concept": "对应角",
    "explanation": "旋转前后对应位置的角",
    "example": "△ABC中的∠A对应△A\'B\'C\'中的∠A\'"
  },
  {
    "concept": "保形变换",
    "explanation": "保持图形形状不变的变换",
    "example": "旋转是典型的保形变换"
  },
  {
    "concept": "角度不变性",
    "explanation": "角度在变换中保持大小不变",
    "example": "直角仍为直角，锐角仍为锐角"
  }
]',
'[
  "混淆对应角与旋转角",
  "不理解角度保持的重要性",
  "忽略角度测量的准确性",
  "认为旋转会改变角度大小"
]',
'[
  "角度识别：准确识别对应角",
  "性质理解：深刻理解保形的含义",
  "测量验证：通过实际测量验证性质",
  "推理应用：在几何推理中应用此性质"
]',
'{
  "emphasis": ["角度概念", "保形性质"],
  "application": ["几何推理", "图形分析"],
  "connection": ["与角的性质联系", "全等判定"]
}',
'{
  "emphasis": ["变换不变量", "几何理论"],
  "application": ["理论证明", "高等几何"],
  "connection": ["与群论的关系", "不变量理论"]
}',
'1.0', true, 'zh-CN', 4.6, 'approved'),

-- MATH_G9S1_CH23_008: 作旋转图形
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S1_CH23_008'),
'根据给定的旋转中心和旋转角度，用圆规和直尺作出图形旋转后的图形',
'作旋转图形是将旋转理论转化为实践操作的重要技能。作图过程需要运用旋转的基本性质，特别是对应点到旋转中心距离相等和旋转角相等的性质。掌握旋转作图不仅能加深对旋转概念的理解，还能培养学生的几何作图能力和空间想象力。这是几何学习中理论与实践相结合的重要环节。',
'[
  "根据旋转中心和角度作旋转图形",
  "需要运用圆规和直尺等作图工具",
  "基于旋转的基本性质进行作图",
  "培养几何作图能力和空间想象力",
  "是理论与实践相结合的重要技能"
]',
E'[
  {
    "name": "作图步骤",
    "formula": "连线→作角→截距→连接",
    "description": "旋转作图的基本步骤"
  },
  {
    "name": "作图原理",
    "formula": "利用|OA| = |OA\'|和∠AOA\' = 旋转角",
    "description": "基于旋转性质的作图原理"
  }
]',
E'[
  {
    "title": "作旋转图形",
    "problem": "已知△ABC和点O，作△ABC绕点O逆时针旋转90°后的图形",
    "solution": "步骤：1)连接OA，以O为顶点，OA为一边作90°角，在另一边上截取OA\' = OA，得到A\'；2)同理作出B\'、C\'；3)连接A\'B\'、B\'C\'、C\'A\'得到△A\'B\'C\'",
    "analysis": "关键是利用距离相等和角度相等的性质逐点作图"
  }
]',
E'[
  {
    "concept": "几何作图",
    "explanation": "用圆规和直尺进行精确绘图",
    "example": "作角、截线段、作垂线等基本作图"
  },
  {
    "concept": "逐点作图",
    "explanation": "逐个确定对应点的位置",
    "example": "先作A\'，再作B\'，最后作C\'"
  },
  {
    "concept": "作图验证",
    "explanation": "通过测量验证作图的准确性",
    "example": "检查距离和角度是否符合要求"
  }
]',
'[
  "作图步骤不清晰",
  "忽略旋转方向",
  "角度测量不准确",
  "线段长度把握不当"
]',
'[
  "步骤记忆：连线、作角、截距、连接",
  "工具使用：熟练使用圆规和量角器",
  "精度控制：保证作图的准确性",
  "验证习惯：作图后进行验证"
]',
'{
  "emphasis": ["作图技能", "实践操作"],
  "application": ["几何作图", "图形设计"],
  "connection": ["与基本作图的联系", "空间想象"]
}',
'{
  "emphasis": ["精确制图", "工程应用"],
  "application": ["工程制图", "建筑设计"],
  "connection": ["与CAD的关系", "计算机绘图"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G9S1_CH23_009: 确定旋转中心和旋转角
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S1_CH23_009'),
'已知图形及其旋转后的图形，通过几何作图确定旋转中心和旋转角度',
'这是旋转问题的逆向思考，即从结果推断条件。确定旋转要素需要综合运用旋转的各种性质，是对旋转理论的深入应用。掌握这项技能对于分析复杂的几何图形、解决实际问题具有重要意义。这个过程不仅考查学生的几何推理能力，还培养了逆向思维和问题解决能力。',
'[
  "从已知图形和旋转图形确定旋转要素",
  "需要综合运用旋转的各种性质",
  "体现逆向思维和推理能力",
  "是旋转理论的深入应用",
  "培养问题分析和解决能力"
]',
E'[
  {
    "name": "确定旋转中心",
    "formula": "作对应点连线的垂直平分线，交点为旋转中心",
    "description": "利用等距性质确定旋转中心"
  },
  {
    "name": "确定旋转角",
    "formula": "∠AOA\' = 旋转角，其中O是旋转中心",
    "description": "通过对应点与中心的夹角确定旋转角"
  }
]',
E'[
  {
    "title": "确定旋转要素",
    "problem": "已知△ABC和△A\'B\'C\'是旋转关系，确定旋转中心和旋转角",
    "solution": "1)作AA\'和BB\'的垂直平分线，交点O为旋转中心；2)测量∠AOA\'的大小，即为旋转角；3)根据A到A\'的转向确定旋转方向",
    "analysis": "综合运用距离相等和角度关系确定旋转要素"
  }
]',
'[
  {
    "concept": "逆向推理",
    "explanation": "从结果推断原因或条件",
    "example": "从旋转图形推断旋转要素"
  },
  {
    "concept": "垂直平分线性质",
    "explanation": "垂直平分线上的点到线段两端距离相等",
    "example": "旋转中心的确定依据"
  },
  {
    "concept": "角度测量",
    "explanation": "用量角器测量角度大小",
    "example": "确定旋转角的具体数值"
  }
]',
'[
  "垂直平分线作图不准确",
  "角度测量存在误差",
  "忽略旋转方向的判断",
  "不能综合运用多个性质"
]',
'[
  "作图准确：垂直平分线要准确",
  "测量精确：角度测量要仔细",
  "方向判断：注意顺逆时针方向",
  "综合分析：运用多种性质相互验证"
]',
'{
  "emphasis": ["逆向思维", "综合应用"],
  "application": ["问题分析", "几何推理"],
  "connection": ["与作图的联系", "推理能力培养"]
}',
'{
  "emphasis": ["分析能力", "数学建模"],
  "application": ["工程分析", "逆向工程"],
  "connection": ["与解析几何的关系", "参数确定"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'),

-- MATH_G9S1_CH23_010: 中心对称的概念
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S1_CH23_010'),
'中心对称是指图形绕某一点旋转180°后能与原图形重合',
'中心对称是旋转的特殊情况，当旋转角为180°时就形成中心对称。中心对称图形具有独特的美感和对称性，在自然界和人工设计中广泛存在。理解中心对称概念有助于学生认识图形的对称美，培养审美意识，同时也为学习点对称、图形设计等内容奠定基础。中心对称体现了数学中的和谐与平衡。',
'[
  "中心对称是旋转180°的特殊情况",
  "图形绕某点旋转180°后与原图形重合",
  "体现了图形的对称美和平衡感",
  "是自然界和设计中常见的对称形式",
  "为后续学习点对称奠定基础"
]',
'[
  {
    "name": "中心对称定义",
    "formula": "图形绕某点旋转180°后与原图形重合",
    "description": "中心对称的数学定义"
  },
  {
    "name": "对称中心",
    "formula": "使图形中心对称的特殊点",
    "description": "中心对称的关键要素"
  }
]',
'[
  {
    "title": "识别中心对称",
    "problem": "下列图形中哪些具有中心对称性：正方形、等边三角形、平行四边形、正五边形",
    "solution": "正方形：有中心对称性（绕中心180°重合）；等边三角形：无中心对称性；平行四边形：有中心对称性；正五边形：无中心对称性",
    "analysis": "判断中心对称的关键是看绕某点旋转180°是否重合"
  }
]',
'[
  {
    "concept": "180°旋转",
    "explanation": "半圆旋转，相当于关于点对称",
    "example": "时针从12点转到6点"
  },
  {
    "concept": "图形重合",
    "explanation": "旋转后图形完全覆盖原图形",
    "example": "所有对应点完全重合"
  },
  {
    "concept": "对称美",
    "explanation": "对称带来的美感和平衡",
    "example": "中国传统图案、现代设计"
  }
]',
'[
  "混淆中心对称与轴对称",
  "不理解180°旋转的特殊性",
  "忽略对称中心的重要性",
  "判断对称性时不够仔细"
]',
'[
  "概念辨析：区分中心对称与轴对称",
  "特殊角度：理解180°的特殊意义",
  "中心定位：准确找到对称中心",
  "美感体验：感受对称的美学价值"
]',
'{
  "emphasis": ["对称概念", "美学认知"],
  "application": ["图形识别", "美术设计"],
  "connection": ["与轴对称的区别", "美学教育"]
}',
'{
  "emphasis": ["数学结构", "抽象思维"],
  "application": ["群论基础", "对称性研究"],
  "connection": ["与代数结构的关系", "对称群"]
}',
'1.0', true, 'zh-CN', 4.7, 'approved'),

-- MATH_G9S1_CH23_011: 中心对称图形
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S1_CH23_011'),
'中心对称图形是指具有中心对称性质的图形，绕对称中心旋转180°后与自身重合',
'中心对称图形广泛存在于生活中，如正方形、矩形、平行四边形、圆等都是常见的中心对称图形。认识和理解中心对称图形有助于学生建立空间观念，培养几何直觉。这些图形在建筑设计、艺术创作、工程制图中都有重要应用，体现了数学与美学、实用性的完美结合。',
'[
  "具有中心对称性质的图形",
  "绕对称中心旋转180°与自身重合",
  "在生活中广泛存在",
  "培养空间观念和几何直觉",
  "体现数学与美学的结合"
]',
'[
  {
    "name": "中心对称图形定义",
    "formula": "绕某点旋转180°后与自身重合的图形",
    "description": "中心对称图形的判定标准"
  },
  {
    "name": "常见对称图形",
    "formula": "正方形、矩形、平行四边形、圆、正偶数边形",
    "description": "具有中心对称性的基本图形"
  }
]',
'[
  {
    "title": "判断中心对称图形",
    "problem": "判断下列图形是否为中心对称图形：(1)线段 (2)角 (3)等腰三角形 (4)矩形",
    "solution": "(1)线段：是，以中点为对称中心；(2)角：不是（除平角外）；(3)等腰三角形：不是；(4)矩形：是，以对角线交点为对称中心",
    "analysis": "判断方法是看能否找到一个点使图形绕此点旋转180°后重合"
  }
]',
'[
  {
    "concept": "对称中心位置",
    "explanation": "使图形对称的特殊点的位置",
    "example": "矩形的对称中心在对角线交点"
  },
  {
    "concept": "几何图形分类",
    "explanation": "按对称性质对图形进行分类",
    "example": "对称图形与非对称图形"
  },
  {
    "concept": "生活应用",
    "explanation": "中心对称在生活中的体现",
    "example": "标志设计、建筑装饰、器物造型"
  }
]',
'[
  "遗漏对称中心的考虑",
  "混淆不同类型的对称",
  "判断时不够全面系统",
  "忽略实际应用价值"
]',
'[
  "系统判断：建立完整的判断流程",
  "中心定位：准确找到对称中心",
  "分类记忆：记住常见对称图形",
  "生活联系：观察生活中的对称现象"
]',
'{
  "emphasis": ["图形分类", "生活应用"],
  "application": ["图形识别", "设计欣赏"],
  "connection": ["与生活实际的联系", "美学应用"]
}',
'{
  "emphasis": ["几何结构", "数学分类"],
  "application": ["几何研究", "结构分析"],
  "connection": ["与群论的关系", "对称性理论"]
}',
'1.0', true, 'zh-CN', 4.6, 'approved'),

-- MATH_G9S1_CH23_012: 中心对称的性质
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S1_CH23_012'),
'中心对称图形的性质：对应点连线都过对称中心，且被对称中心平分',
'中心对称的性质是理解和应用中心对称的重要基础。这些性质不仅体现了中心对称的几何特征，也为解决相关问题提供了重要工具。理解这些性质有助于学生更好地分析几何图形，进行几何推理，解决实际问题。这些性质在坐标几何、图形变换等领域都有广泛应用。',
'[
  "对应点连线都过对称中心",
  "对称中心平分对应点连线",
  "对应线段平行且相等",
  "对应角相等",
  "图形面积、周长等保持不变"
]',
E'[
  {
    "name": "对应点性质",
    "formula": "A与A\'对应 ⟺ O是AA\'中点",
    "description": "对应点与对称中心的关系"
  },
  {
    "name": "对应线段性质",
    "formula": "AB ∥ A\'B\'且|AB| = |A\'B\'|",
    "description": "对应线段平行且相等"
  }
]',
'[
  {
    "title": "利用中心对称性质解题",
    "problem": "四边形ABCD是中心对称图形，对称中心为O，若∠A = 70°，求∠C的度数",
    "solution": "由于ABCD是中心对称图形，A与C是对应顶点，根据中心对称性质，对应角相等，所以∠C = ∠A = 70°",
    "analysis": "利用中心对称图形对应角相等的性质"
  }
]',
E'[
  {
    "concept": "对应元素",
    "explanation": "中心对称中相互对应的点、线段、角",
    "example": "点A对应点A\'，线段AB对应线段A\'B\'"
  },
  {
    "concept": "几何不变量",
    "explanation": "在变换中保持不变的几何量",
    "example": "长度、角度、面积等"
  },
  {
    "concept": "平行关系",
    "explanation": "对应线段保持平行关系",
    "example": "原图形中的平行线段在对称图形中仍平行"
  }
]',
'[
  "混淆对应关系",
  "忽略性质的应用条件",
  "不能灵活运用性质解题",
  "对性质理解不够深入"
]',
'[
  "对应识别：准确识别对应元素",
  "性质理解：深刻理解各项性质",
  "灵活应用：在解题中灵活运用",
  "综合运用：多个性质综合使用"
]',
'{
  "emphasis": ["性质理解", "应用技巧"],
  "application": ["几何推理", "问题解决"],
  "connection": ["与全等的关系", "几何证明"]
}',
'{
  "emphasis": ["理论深度", "逻辑推理"],
  "application": ["几何证明", "理论研究"],
  "connection": ["与群论的关系", "变换理论"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'),

-- MATH_G9S1_CH23_013: 关于原点对称的点的坐标
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S1_CH23_013'),
'在坐标系中，点(x,y)关于原点对称的点坐标为(-x,-y)',
'坐标系中的中心对称为我们提供了代数化研究几何问题的工具。当对称中心为原点时，对称变换具有简洁的代数形式，这为解决几何问题提供了有力的工具。理解坐标系中的中心对称不仅能加深对对称概念的理解，还能培养学生的数形结合思想，为后续学习函数的对称性奠定基础。',
'[
  "在坐标系中研究中心对称",
  "关于原点对称的点坐标规律",
  "提供代数化研究几何的工具",
  "培养数形结合思想",
  "为函数对称性学习奠定基础"
]',
'[
  {
    "name": "关于原点对称",
    "formula": "点(x,y)关于原点的对称点为(-x,-y)",
    "description": "坐标系中关于原点对称的坐标变换"
  },
  {
    "name": "关于任意点对称",
    "formula": "点(x,y)关于点(a,b)的对称点为(2a-x,2b-y)",
    "description": "关于任意点对称的坐标公式"
  }
]',
E'[
  {
    "title": "求对称点坐标",
    "problem": "点A(3,-2)关于原点O的对称点A\'的坐标是什么？点B(1,4)关于点P(2,1)的对称点B\'的坐标是什么？",
    "solution": "A关于原点的对称点A\'(-3,2)；B关于P(2,1)的对称点B\'，设B\'(x,y)，则P是BB\'的中点，所以(1+x)/2=2，(4+y)/2=1，解得B\'(3,-2)",
    "analysis": "利用对称点坐标公式或中点公式求解"
  }
]',
'[
  {
    "concept": "坐标变换",
    "explanation": "通过改变坐标来实现几何变换",
    "example": "(x,y) → (-x,-y)表示关于原点对称"
  },
  {
    "concept": "代数方法",
    "explanation": "用代数运算解决几何问题",
    "example": "通过坐标计算确定对称关系"
  },
  {
    "concept": "数形结合",
    "explanation": "数量关系与图形性质的结合",
    "example": "坐标变化反映几何变换"
  }
]',
'[
  "坐标符号搞错",
  "公式记忆不准确",
  "计算过程出现错误",
  "不理解坐标与几何的对应关系"
]',
'[
  "符号准确：注意坐标的正负号",
  "公式熟记：熟练掌握对称公式",
  "计算仔细：避免计算错误",
  "数形对应：理解坐标与图形的关系"
]',
'{
  "emphasis": ["坐标运算", "数形结合"],
  "application": ["解析几何", "函数图像"],
  "connection": ["与函数对称性的关系", "坐标几何"]
}',
'{
  "emphasis": ["代数方法", "变换理论"],
  "application": ["线性代数", "几何变换"],
  "connection": ["与矩阵变换的关系", "线性变换理论"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G9S1_CH23_014: 中心对称图形的识别
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S1_CH23_014'),
'掌握识别中心对称图形的方法，能够判断给定图形是否具有中心对称性',
'识别中心对称图形是应用中心对称概念的重要技能。这需要学生能够综合运用中心对称的定义和性质，通过观察、分析、验证等方法来判断图形的对称性。掌握识别方法不仅有助于解决几何问题，还能培养学生的观察能力和逻辑推理能力。这项技能在实际生活中也有广泛应用，如建筑设计、艺术创作等领域。',
'[
  "掌握识别中心对称图形的方法",
  "综合运用定义和性质进行判断",
  "培养观察能力和逻辑推理能力",
  "在实际生活中有广泛应用",
  "是中心对称概念的重要应用"
]',
'[
  {
    "name": "识别方法",
    "formula": "寻找对称中心，验证180°旋转重合",
    "description": "中心对称图形识别的基本方法"
  },
  {
    "name": "验证步骤",
    "formula": "找中心 → 作对应点 → 验证重合",
    "description": "系统化的识别验证流程"
  }
]',
'[
  {
    "title": "识别实例分析",
    "problem": "判断正六边形是否为中心对称图形",
    "solution": "正六边形是中心对称图形。对称中心为正六边形的中心，将图形绕中心旋转180°后，每个顶点都有对应的顶点重合，整个图形与原图形完全重合",
    "analysis": "正偶数边形都具有中心对称性"
  }
]',
'[
  {
    "concept": "系统判断",
    "explanation": "建立完整的判断标准和流程",
    "example": "先找中心，再验证对应关系"
  },
  {
    "concept": "特殊图形",
    "explanation": "掌握常见图形的对称性规律",
    "example": "正偶数边形有中心对称性"
  },
  {
    "concept": "实际应用",
    "explanation": "在设计和艺术中的应用",
    "example": "标志设计、建筑装饰的对称美"
  }
]',
'[
  "缺乏系统的判断方法",
  "忽略对称中心的寻找",
  "验证过程不够严谨",
  "对常见图形规律不熟悉"
]',
'[
  "方法系统：建立完整的识别流程",
  "中心寻找：准确定位对称中心",
  "严谨验证：仔细验证对应关系",
  "规律掌握：记住常见图形的性质"
]',
'{
  "emphasis": ["识别技能", "系统方法"],
  "application": ["图形分析", "实际应用"],
  "connection": ["与几何性质的联系", "应用能力"]
}',
'{
  "emphasis": ["逻辑推理", "严谨分析"],
  "application": ["理论研究", "问题解决"],
  "connection": ["与数学证明的关系", "推理能力"]
}',
'1.0', true, 'zh-CN', 4.7, 'approved'),

-- MATH_G9S1_CH23_015: 作关于点对称的图形
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S1_CH23_015'),
'根据给定的对称中心，作出图形关于该点的中心对称图形',
'作中心对称图形是将中心对称理论转化为实践操作的重要技能。通过逐点作对称点的方法，可以精确地作出整个图形的中心对称图形。这个过程不仅加深了对中心对称概念的理解，还培养了学生的作图能力和空间想象力。掌握这项技能对于几何作图和图形设计具有重要意义。',
'[
  "根据对称中心作中心对称图形",
  "通过逐点作对称点完成",
  "将理论转化为实践操作",
  "培养作图能力和空间想象力",
  "对几何作图和设计有重要意义"
]',
E'[
  {
    "name": "作图原理",
    "formula": "逐点作对称，连线成图",
    "description": "中心对称图形的作图方法"
  },
  {
    "name": "对称点作法",
    "formula": "延长AO到A\'，使|OA\'| = |OA|",
    "description": "作关于点O的对称点的方法"
  }
]',
E'[
  {
    "title": "作中心对称三角形",
    "problem": "已知△ABC和点O，作△ABC关于点O的中心对称图形",
    "solution": "步骤：1)延长AO，在延长线上取A\'使OA\' = OA；2)同理作出B\'、C\'；3)连接A\'B\'、B\'C\'、C\'A\'得到△A\'B\'C\'",
    "analysis": "关键是准确作出各顶点的对称点，然后按原图形连接方式连线"
  }
]',
E'[
  {
    "concept": "逐点对称",
    "explanation": "逐个作出关键点的对称点",
    "example": "先作顶点对称，再连接成图形"
  },
  {
    "concept": "线段延长",
    "explanation": "在射线上截取相等线段",
    "example": "延长AO到A\'，使OA\' = OA"
  },
  {
    "concept": "图形还原",
    "explanation": "按原图形结构连接对称点",
    "example": "保持原有的连接关系"
  }
]',
'[
  "对称点位置不准确",
  "遗漏某些关键点",
  "连线关系错误",
  "忽略作图验证"
]',
'[
  "准确作点：确保对称点位置正确",
  "完整作图：不遗漏任何关键点",
  "关系保持：保持原有连接关系",
  "验证习惯：作图后进行检验"
]',
'{
  "emphasis": ["作图技能", "实践操作"],
  "application": ["几何作图", "图形设计"],
  "connection": ["与基本作图的联系", "空间想象"]
}',
'{
  "emphasis": ["精确制图", "工程应用"],
  "application": ["工程制图", "建筑设计"],
  "connection": ["与CAD的关系", "数字化绘图"]
}',
'1.0', true, 'zh-CN', 4.7, 'approved'),

-- MATH_G9S1_CH23_016: 信息技术应用：探索旋转的性质
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S1_CH23_016'),
'运用信息技术工具探索和验证旋转的基本性质，增强对旋转概念的理解',
'信息技术为探索旋转性质提供了强有力的工具。通过动态几何软件，学生可以直观地观察旋转过程，验证旋转的各种性质，发现图形变换的规律。这种探索方式不仅使抽象的几何概念变得生动具体，还培养了学生的探究能力和创新思维。掌握运用信息技术学习数学的方法，对学生的终身学习和未来发展具有重要意义。',
'[
  "运用信息技术探索旋转性质",
  "通过动态软件观察旋转过程",
  "验证和发现旋转规律",
  "培养探究能力和创新思维",
  "掌握信息技术学习方法"
]',
'[
  {
    "name": "动态探索",
    "formula": "拖动-观察-总结",
    "description": "利用动态几何软件探索的基本流程"
  },
  {
    "name": "验证性质",
    "formula": "测量-比较-验证",
    "description": "用软件验证旋转性质的方法"
  }
]',
E'[
  {
    "title": "软件探索旋转性质",
    "problem": "用几何画板探索：将△ABC绕点O旋转60°得到△A\'B\'C\'，观察对应点的连线有什么特点？",
    "solution": "通过拖动改变三角形位置和旋转中心，观察发现：1)对应点与旋转中心的距离相等；2)∠AOA\' = ∠BOB\' = ∠COC\' = 60°；3)旋转角度保持不变",
    "analysis": "动态演示帮助学生直观理解旋转的不变性质"
  }
]',
'[
  {
    "concept": "动态几何",
    "explanation": "可以拖动和变化的几何图形",
    "example": "几何画板、GeoGebra等软件"
  },
  {
    "concept": "探索学习",
    "explanation": "通过观察发现规律的学习方式",
    "example": "拖动点观察图形变化规律"
  },
  {
    "concept": "技术整合",
    "explanation": "将信息技术与数学学习相结合",
    "example": "用软件验证数学理论"
  }
]',
'[
  "过分依赖技术忽略理论",
  "观察不够仔细深入",
  "缺乏有效的探索策略",
  "不能从现象中总结规律"
]',
'[
  "理技并重：技术服务于理论学习",
  "仔细观察：注意细节和变化规律",
  "策略探索：有目的有方法地探索",
  "规律总结：从现象提升到理论"
]',
'{
  "emphasis": ["技术应用", "探索学习"],
  "application": ["数学实验", "可视化学习"],
  "connection": ["与现代技术的结合", "创新学习方式"]
}',
'{
  "emphasis": ["数字化教育", "计算思维"],
  "application": ["教育技术", "数据分析"],
  "connection": ["与STEM教育的关系", "计算机科学"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G9S1_CH23_017: 项目学习：图案设计
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S1_CH23_017'),
'运用旋转变换设计美丽的图案，体验数学在艺术设计中的应用',
'项目学习通过图案设计让学生将旋转理论应用于实际创作中。这种学习方式不仅巩固了旋转的概念和性质，还培养了学生的创造能力和审美情趣。学生在设计过程中体验到数学与艺术的结合，认识到数学的实用价值和美学价值。这种跨学科的学习方式有助于培养学生的综合素养和创新精神。',
'[
  "运用旋转变换设计图案",
  "将理论应用于实际创作",
  "培养创造能力和审美情趣",
  "体验数学与艺术的结合",
  "培养综合素养和创新精神"
]',
'[
  {
    "name": "设计原理",
    "formula": "基本单元 + 旋转规律 = 美丽图案",
    "description": "图案设计的基本方法"
  },
  {
    "name": "旋转对称",
    "formula": "n次旋转对称 ⟺ 旋转角度 = 360°/n",
    "description": "具有旋转对称性的图案特点"
  }
]',
'[
  {
    "title": "设计旋转对称图案",
    "problem": "设计一个具有4次旋转对称的花朵图案",
    "solution": "1)绘制基本单元（四分之一花瓣）；2)以图案中心为旋转中心，依次旋转90°、180°、270°；3)将四个单元组合形成完整花朵；4)调整细节，美化图案",
    "analysis": "关键是确定合适的基本单元和旋转中心，保证图案的对称美"
  }
]',
'[
  {
    "concept": "项目学习",
    "explanation": "以项目为载体的学习方式",
    "example": "完成一个完整的图案设计项目"
  },
  {
    "concept": "跨学科融合",
    "explanation": "数学与艺术的有机结合",
    "example": "用数学原理创作艺术作品"
  },
  {
    "concept": "创造性思维",
    "explanation": "在设计中发挥创造力",
    "example": "设计独特的对称图案"
  }
]',
'[
  "缺乏设计思路和创意",
  "对旋转对称理解不深",
  "忽略美学要素",
  "项目执行不够系统"
]',
'[
  "创意激发：多观察生活中的美",
  "理论应用：灵活运用旋转知识",
  "美学融合：注重图案的美感",
  "系统实施：按步骤完成项目"
]',
'{
  "emphasis": ["创意设计", "美学体验"],
  "application": ["艺术创作", "设计实践"],
  "connection": ["与艺术的融合", "审美教育"]
}',
'{
  "emphasis": ["设计理论", "创新应用"],
  "application": ["工业设计", "建筑装饰"],
  "connection": ["与设计学的关系", "创新创业"]
}',
'1.0', true, 'zh-CN', 4.7, 'approved'),

-- MATH_G9S1_CH23_018: 利用旋转设计图案
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S1_CH23_018'),
'掌握利用旋转变换设计各种对称图案的具体方法和技巧',
'这是图案设计的深化和拓展，学生需要掌握更多的设计方法和技巧。通过学习不同的旋转角度、组合方式和设计元素，可以创作出更加丰富多彩的图案。这个过程不仅提高了学生的设计能力，还加深了对旋转概念的理解和应用。学生在实践中体验到数学的创造性和实用性，激发学习兴趣和探索精神。',
'[
  "掌握具体的设计方法和技巧",
  "学习不同角度和组合方式",
  "创作丰富多彩的图案",
  "提高设计能力和数学理解",
  "体验数学的创造性和实用性"
]',
'[
  {
    "name": "多层旋转",
    "formula": "内层图案 + 外层图案 + 不同旋转角度",
    "description": "复杂图案的层次设计方法"
  },
  {
    "name": "组合变换",
    "formula": "旋转 + 平移 + 轴对称 = 复合图案",
    "description": "多种变换的组合应用"
  }
]',
'[
  {
    "title": "设计复合对称图案",
    "problem": "设计一个同时具有中心对称和轴对称的复合图案",
    "solution": "1)设计具有中心对称的基本图案；2)确保图案也具有轴对称性；3)可采用十字形、菱形等基本元素；4)通过旋转和反射组合形成复合图案",
    "analysis": "需要同时满足多种对称性要求，体现几何变换的综合应用"
  }
]',
'[
  {
    "concept": "设计技巧",
    "explanation": "提高图案质量的具体方法",
    "example": "色彩搭配、线条流畅、比例协调"
  },
  {
    "concept": "复合变换",
    "explanation": "多种几何变换的组合应用",
    "example": "旋转与轴对称的组合"
  },
  {
    "concept": "创作过程",
    "explanation": "从构思到完成的完整流程",
    "example": "草图-设计-修改-完善"
  }
]',
'[
  "设计过于复杂失去美感",
  "变换应用不够准确",
  "缺乏系统的设计思路",
  "忽略实际制作的可行性"
]',
'[
  "简洁美观：保持设计的简洁美",
  "变换准确：准确应用几何变换",
  "思路清晰：建立系统的设计流程",
  "实用考虑：考虑实际制作的要求"
]',
'{
  "emphasis": ["设计技能", "实用创作"],
  "application": ["图案制作", "装饰设计"],
  "connection": ["与工艺美术的联系", "实用技能"]
}',
'{
  "emphasis": ["设计原理", "技术实现"],
  "application": ["计算机图形学", "工业设计"],
  "connection": ["与数字艺术的关系", "技术创新"]
}',
'1.0', true, 'zh-CN', 4.6, 'approved'),

-- MATH_G9S1_CH23_019: 阅读与思考：旋转对称
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S1_CH23_019'),
'通过阅读拓展对旋转对称概念的理解，思考旋转对称在自然和人文中的体现',
'阅读与思考环节引导学生从更广阔的视角理解旋转对称。通过阅读相关材料，学生了解旋转对称在自然界中的普遍存在，如花朵、雪花、星系等的对称结构，以及在人类文明中的应用，如建筑、艺术、文字等。这种学习方式培养了学生的阅读理解能力、批判思维和跨学科视野，让学生认识到数学与自然、文化的深刻联系。',
'[
  "拓展对旋转对称的理解",
  "了解自然界中的对称现象",
  "认识人类文明中的对称应用",
  "培养阅读理解和批判思维",
  "建立跨学科视野"
]',
'[
  {
    "name": "自然对称",
    "formula": "生物结构的n次旋转对称",
    "description": "自然界中普遍存在的对称现象"
  },
  {
    "name": "文化对称",
    "formula": "艺术与建筑中的对称美学",
    "description": "人类文明中的对称表现"
  }
]',
'[
  {
    "title": "分析自然中的旋转对称",
    "problem": "观察向日葵花盘的种子排列，分析其中的旋转对称性",
    "solution": "向日葵花盘中的种子按斐波那契螺旋排列，具有近似的旋转对称性。种子排列遵循黄金角约137.5°，这种排列使种子密度最大化，体现了自然的数学智慧",
    "analysis": "自然界的对称往往与优化原理相关，体现了数学在自然中的深层作用"
  }
]',
'[
  {
    "concept": "自然数学",
    "explanation": "自然界中蕴含的数学原理",
    "example": "花瓣数、蜂巢结构、雪花形状"
  },
  {
    "concept": "文化数学",
    "explanation": "人类文化中的数学元素",
    "example": "建筑对称、艺术图案、文字结构"
  },
  {
    "concept": "审美数学",
    "explanation": "数学与美学的关系",
    "example": "对称美、比例美、规律美"
  }
]',
'[
  "阅读不够深入细致",
  "思考缺乏批判性",
  "不能建立跨学科联系",
  "忽略背后的数学原理"
]',
'[
  "深度阅读：仔细阅读理解材料",
  "独立思考：形成自己的见解",
  "跨界联系：建立学科间的联系",
  "原理探索：寻找背后的数学原理"
]',
'{
  "emphasis": ["文化理解", "审美体验"],
  "application": ["文化欣赏", "生活观察"],
  "connection": ["与自然科学的联系", "人文素养"]
}',
'{
  "emphasis": ["理论深度", "跨学科研究"],
  "application": ["学术研究", "理论探索"],
  "connection": ["与哲学的关系", "科学方法论"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G9S1_CH23_020: 数学活动：旋转的探究
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S1_CH23_020'),
'通过动手操作和合作探究，深入理解旋转的本质和应用',
'数学活动是本章学习的综合实践环节。通过设计的探究活动，学生能够在动手操作中体验旋转的过程，在合作交流中深化理解，在问题解决中应用知识。这种活动式学习充分调动学生的多种感官，激发学习兴趣，培养探究能力和合作精神。活动的开展也为评价学生的综合能力提供了丰富的素材。',
'[
  "通过动手操作体验旋转过程",
  "在合作交流中深化理解",
  "在问题解决中应用知识",
  "调动多种感官激发兴趣",
  "培养探究能力和合作精神"
]',
'[
  {
    "name": "探究活动",
    "formula": "操作-观察-猜想-验证-总结",
    "description": "数学探究活动的基本流程"
  },
  {
    "name": "合作学习",
    "formula": "分工-讨论-整合-展示",
    "description": "小组合作学习的组织方式"
  }
]',
'[
  {
    "title": "探究钟表指针的旋转",
    "problem": "设计活动探究时钟指针的旋转规律：时针、分针、秒针的旋转速度关系",
    "solution": "活动设计：1)观察实际时钟运行；2)记录不同时间点指针位置；3)计算各指针的旋转角度；4)发现旋转速度关系：秒针：分针：时针 = 720:12:1；5)建立数学模型",
    "analysis": "通过实际观察建立数学模型，体现数学与生活的联系"
  }
]',
'[
  {
    "concept": "活动设计",
    "explanation": "根据学习目标设计合适的活动",
    "example": "观察-实验-探究-总结"
  },
  {
    "concept": "合作学习",
    "explanation": "通过小组合作完成学习任务",
    "example": "分工合作、优势互补"
  },
  {
    "concept": "成果展示",
    "explanation": "展示和交流学习成果",
    "example": "汇报发现、分享经验"
  }
]',
'[
  "活动组织不够有序",
  "合作流于形式",
  "探究不够深入",
  "缺乏有效的成果展示"
]',
'[
  "有序组织：合理安排活动流程",
  "有效合作：发挥每个人的作用",
  "深入探究：不满足于表面现象",
  "充分展示：展示探究过程和结果"
]',
'{
  "emphasis": ["动手实践", "合作探究"],
  "application": ["实践活动", "团队协作"],
  "connection": ["与实践能力的联系", "社会技能"]
}',
'{
  "emphasis": ["研究方法", "学术探究"],
  "application": ["科学研究", "学术合作"],
  "connection": ["与科学方法的关系", "研究能力"]
}',
'1.0', true, 'zh-CN', 4.7, 'approved');

