/* 遮罩层 */
.formula-editor-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 1000;
}

/* 公式编辑器容器 */
.formula-editor {
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100%;
  background-color: #fff;
  border-radius: 24rpx 24rpx 0 0;
  box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.1);
  z-index: 1001;
  transform: translateY(100%);
  transition: transform 0.3s ease;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}

.formula-editor-show {
  transform: translateY(0);
}

/* 编辑器头部 */
.formula-editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 30rpx;
  border-bottom: 1rpx solid #eee;
}

.formula-editor-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.formula-editor-actions {
  display: flex;
}

.action-btn {
  display: flex;
  align-items: center;
  margin-left: 24rpx;
  padding: 0 12rpx;
}

.action-icon {
  width: 36rpx;
  height: 36rpx;
  margin-right: 8rpx;
}

.icon-eye {
  background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23333' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z'/%3E%3Ccircle cx='12' cy='12' r='3'/%3E%3C/svg%3E") no-repeat center;
  background-size: contain;
}

.icon-eye-off {
  background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23333' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24'/%3E%3Cline x1='1' y1='1' x2='23' y2='23'/%3E%3C/svg%3E") no-repeat center;
  background-size: contain;
}

.icon-delete {
  background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23333' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M3 6h18'/%3E%3Cpath d='M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2'/%3E%3C/svg%3E") no-repeat center;
  background-size: contain;
}

/* 公式输入区域 */
.formula-editor-input-area {
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #eee;
}

.formula-input {
  width: 100%;
  height: 80rpx;
  font-size: 28rpx;
  color: #333;
  line-height: 80rpx;
  padding: 0 16rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  box-sizing: border-box;
}

.input-error {
  border-color: #ff4d4f;
}

.formula-error {
  font-size: 24rpx;
  color: #ff4d4f;
  margin-top: 8rpx;
}

/* 公式预览区域 */
.formula-editor-preview {
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #eee;
}

.preview-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 16rpx;
}

.preview-content {
  min-height: 88rpx;
  padding: 16rpx;
  background-color: #f9f9f9;
  border-radius: 8rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.preview-formula {
  max-width: 100%;
  overflow-x: auto;
}

.preview-placeholder {
  font-size: 26rpx;
  color: #999;
}

/* 符号选择器 - 新样式 */
.formula-tabs {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 标签栏 */
.tabs-header {
  display: flex;
  border-bottom: 1rpx solid #eee;
  overflow-x: auto;
  white-space: nowrap;
  background-color: #f9f9f9;
}

.tab-item {
  padding: 20rpx 30rpx;
  font-size: 28rpx;
  color: #666;
  position: relative;
}

.tab-active {
  color: #6c5ce7;
  font-weight: 500;
  position: relative;
}

.tab-active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 4rpx;
  background-color: #6c5ce7;
}

/* 符号内容区 */
.tabs-content {
  flex: 1;
  overflow: hidden;
}

.symbols-container {
  height: 100%;
  padding: 10rpx 0;
}

.symbols-grid {
  display: flex;
  flex-wrap: wrap;
  padding: 20rpx 10rpx;
}

.symbol-item {
  width: 25%;
  height: 100rpx;
  padding: 10rpx;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.symbol-text {
  font-size: 32rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.symbol-desc {
  font-size: 22rpx;
  color: #999;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 100%;
}

/* 算术运算符专区 */
.arithmetic-section {
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #eee;
}

/* 底部按钮区 */
.formula-editor-footer {
  display: flex;
  padding: 24rpx 30rpx;
  border-top: 1rpx solid #eee;
}

.footer-btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  font-size: 28rpx;
  border-radius: 40rpx;
  margin: 0 10rpx;
}

.cancel-btn {
  background-color: #f5f5f5;
  color: #666;
}

.confirm-btn {
  background-color: #6c5ce7;
  color: #fff;
}

/* 适配小屏幕设备 */
@media screen and (max-height: 600px) {
  .formula-editor {
    height: 95%;
  }
  
  .formula-editor-input-area,
  .formula-editor-preview {
    flex: 0 0 100px;
  }
  
  .formula-input {
    height: 80px;
  }
  
  .preview-content {
    min-height: 60px;
  }
} 