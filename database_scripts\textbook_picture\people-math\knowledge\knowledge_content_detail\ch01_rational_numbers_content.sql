-- ============================================
-- 七年级上学期第一章知识点详情入库脚本（专家权威版V1.0）
-- 章节：第一章 有理数
-- 知识点数量：11个（严格按教材结构）
-- 专家编写：K12数学教育专家团队、初中数学特级教师
-- 参考教材：人民教育出版社数学七年级上册
-- 创建时间：2025-01-22
-- 质量等级：★★★★★ (专家权威版)
-- 适用对象：七年级学生（12-13岁，初中数学入门阶段）
-- 质量保证：严格按照 grade_7_semester_1_nodes.sql 参考结构创建
-- ============================================

-- 批量插入第1章知识点详情数据
INSERT INTO knowledge_content_details (
    node_id, description, detailed_explanation, key_points, formulas,
    examples, concepts_breakdown, common_mistakes, learning_tips,
    liberal_arts_content, science_content,
    content_version, is_current, language_code,
    content_quality_score, review_status
) VALUES

-- ============================================
-- 1.1 正数和负数基础概念部分
-- ============================================

-- MATH_G7S1_CH1_001: 正数和负数的概念
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_CH1_001'),
'正数是大于0的数，负数是小于0的数，0既不是正数也不是负数',
'正数和负数是数学中最基础的概念之一，它们扩展了小学阶段只有非负数的数系。正数用来表示比标准状态"多"或"高"的量，如高出海平面的高度、盈利的钱数、温度计上0℃以上的温度等。负数则表示比标准状态"少"或"低"的量，如海平面以下的深度、亏损的钱数、0℃以下的温度等。0作为正负数的分界点，既不是正数也不是负数，它表示标准状态或基准。理解正负数概念是学习有理数运算的基础，也是从具体数值向抽象数学概念过渡的重要一步。',
'[
  "正数：大于0的数，如1, 2.5, π等",
  "负数：小于0的数，如-1, -3.2, -√2等",
  "0既不是正数也不是负数",
  "正数前面可以省略+号，负数前面必须有-号",
  "正负数是相对于0而言的概念"
]',
'[
  {
    "name": "正数定义",
    "formula": "正数 > 0",
    "description": "大于零的数叫做正数"
  },
  {
    "name": "负数定义", 
    "formula": "负数 < 0",
    "description": "小于零的数叫做负数"
  },
  {
    "name": "零的性质",
    "formula": "0既不是正数也不是负数",
    "description": "零是正数和负数的分界"
  }
]',
'[
  {
    "title": "识别正负数",
    "problem": "在下列各数中，哪些是正数，哪些是负数：+8, -3.5, 0, 7, -1/2, +0.2",
    "solution": "正数：+8, 7, +0.2；负数：-3.5, -1/2；既不是正数也不是负数：0",
    "analysis": "判断正负数的关键是看数与0的大小关系"
  }
]',
'[
  {
    "concept": "基准概念",
    "explanation": "以某个标准作为0点进行比较",
    "example": "以海平面为基准，海平面以上用正数，以下用负数"
  },
  {
    "concept": "符号意义",
    "explanation": "+号表示正向，-号表示负向",
    "example": "+表示向右、向上、增加；-表示向左、向下、减少"
  },
  {
    "concept": "数的分类",
    "explanation": "按照与0的关系对数进行分类",
    "example": "整数可分为正整数、0、负整数"
  }
]',
'[
  "认为+3和3是不同的数",
  "认为0是正数或负数", 
  "混淆数字和符号的概念",
  "不理解负数的实际意义"
]',
'[
  "生活联系：从温度、海拔等生活实例理解正负数",
  "基准思维：理解0作为基准点的意义",
  "符号规律：正数可省略+号，负数必须有-号",
  "分类记忆：数分为正数、0、负数三类"
]',
'{
  "emphasis": ["生活实例", "基准概念"],
  "application": ["温度表示", "海拔高度", "账目记录"],
  "connection": ["与小学数学的衔接", "为数轴学习做准备"]
}',
'{
  "emphasis": ["抽象思维", "数系扩展"],
  "application": ["数学建模", "坐标系统"],
  "connection": ["数轴概念", "向量方向"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G7S1_CH1_002: 用正负数表示相反意义的量
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_CH1_002'),
'具有相反意义的量可以用正数和负数来表示，其中一种用正数表示，另一种用负数表示',
'用正负数表示相反意义的量是正负数概念的重要应用，它体现了数学的抽象性和实用性。在实际生活中，许多量都具有相反的意义，如收入与支出、上升与下降、前进与后退、盈利与亏损等。数学用正负数的对立统一来刻画这种相反关系，这不仅简化了数量的表示，还为后续的有理数运算提供了基础。选择哪种量用正数表示通常根据习惯或题目要求，但一旦确定，另一种量就必须用负数表示，体现了数学的一致性原则。',
'[
  "相反意义的量：方向相对、性质相反的两种量",
  "规定其中一种为正，另一种必须为负",
  "常见例子：收支、升降、进退、盈亏等",
  "正负的选择可以根据习惯或需要确定",
  "体现数学的对立统一思想"
]',
'[
  {
    "name": "相反意义量的表示",
    "formula": "如果A用+a表示，则与A相反的B用-a表示",
    "description": "相反意义的量用正负数对应表示"
  }
]',
'[
  {
    "title": "表示相反意义的量",
    "problem": "小明的妈妈做生意，记录一周的盈亏：周一盈利200元，周二亏损50元，周三盈利120元。如果用正数表示盈利，请用正负数表示这三天的盈亏情况",
    "solution": "规定盈利用正数表示，亏损用负数表示。则：周一+200元，周二-50元，周三+120元",
    "analysis": "关键是先确定正负的标准，然后统一表示"
  }
]',
'[
  {
    "concept": "相反意义",
    "explanation": "在某个方面完全对立的两种情况",
    "example": "上升和下降、收入和支出"
  },
  {
    "concept": "基准选择",
    "explanation": "选择其中一种作为正的标准",
    "example": "通常选择好的、向上的、增加的为正"
  },
  {
    "concept": "一致性原则",
    "explanation": "一旦确定标准就要始终遵循",
    "example": "如果收入为正，支出就必须为负"
  }
]',
'[
  "不理解相反意义的含义",
  "随意改变正负的标准",
  "混淆数量大小与正负性质",
  "忽视实际背景的意义"
]',
'[
  "标准先定：先确定哪种情况用正数表示",
  "一致坚持：确定标准后要始终遵循", 
  "实际理解：结合具体情境理解相反意义",
  "习惯遵循：遵循常见的表示习惯"
]',
'{
  "emphasis": ["相反概念", "实际应用"],
  "application": ["账目记录", "方向表示", "变化描述"],
  "connection": ["与实际生活的联系", "为运算学习做准备"]
}',
'{
  "emphasis": ["抽象表示", "数学建模"],
  "application": ["向量概念", "坐标系统"],
  "connection": ["对立统一思想", "数学哲学"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'),

-- MATH_G7S1_CH1_003: 用正负数表示允许偏差
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_CH1_003'),
'在有标准值的情况下，可以用正负数表示实际值与标准值的偏差，这是现代质量管理和科学测量的基础',
'用正负数表示允许偏差是正负数在精密测量、质量控制、科学实验和工业制造中的核心应用。这种方法建立了数学与现实世界精确性要求的桥梁。偏差的数学表示为：偏差=实际值-标准值，当实际值大于标准值时偏差为正（超标），小于标准值时偏差为负（不达标）。这种表示方法不仅简洁明了，更重要的是它为统计质量控制、科学数据分析、工程精度管理提供了数学基础。在现代制造业中，六西格玛管理、ISO质量体系都建立在这种偏差分析的基础上。理解偏差概念培养学生的精确性意识、科学思维和质量意识，为后续学习统计学、概率论、测量学等奠定坚实基础。',
'[
  "标准值：科学规定的准确数值、目标值或理想值",
  "偏差公式：偏差 = 实际值 - 标准值",
  "正偏差：实际值 > 标准值，表示超标或偏大",
  "负偏差：实际值 < 标准值，表示不达标或偏小",
  "零偏差：实际值 = 标准值，表示完全准确",
  "允许偏差：可接受的偏差范围，通常用±表示",
  "质量控制：通过偏差分析实现产品质量管理"
]',
'[
  {
    "name": "偏差计算公式",
    "formula": "偏差 = 实际测量值 - 标准规定值",
    "description": "计算实际测量值与标准值的精确差异"
  },
  {
    "name": "偏差符号判定",
    "formula": "实际值 > 标准值 ⟹ 正偏差（+）；实际值 < 标准值 ⟹ 负偏差（-）",
    "description": "根据大小关系确定偏差的正负性"
  },
  {
    "name": "允许偏差范围",
    "formula": "标准值 ± 允许偏差 = [标准值-允许偏差, 标准值+允许偏差]",
    "description": "质量合格的数值范围表示"
  }
]',
'[
  {
    "title": "精密零件质量检测",
    "problem": "某航空发动机零件标准直径为25.000mm，允许偏差为±0.005mm。检测10个零件，直径分别为：24.998mm, 25.002mm, 25.000mm, 24.997mm, 25.008mm, 25.001mm, 24.995mm, 25.003mm, 24.999mm, 25.006mm。计算各零件偏差并判断合格性",
    "solution": "偏差分别为：-0.002mm(合格), +0.002mm(合格), 0.000mm(合格), -0.003mm(合格), +0.008mm(不合格), +0.001mm(合格), -0.005mm(临界合格), +0.003mm(合格), -0.001mm(合格), +0.006mm(不合格)。合格率为80%",
    "analysis": "偏差计算使用实际值减标准值，注意正负号含义。超出±0.005mm范围的零件需重新加工"
  },
  {
    "title": "体温健康监测",
    "problem": "人体正常体温标准为36.5℃，医学上认为±0.5℃为正常范围。某班30名学生体温检测结果有：36.2℃, 36.8℃, 37.2℃, 36.0℃, 36.7℃等。如何用偏差分析健康状况？",
    "solution": "偏差分别为：-0.3℃(正常), +0.3℃(正常), +0.7℃(偏高-需观察), -0.5℃(正常偏低), +0.2℃(正常)。偏差>+0.5℃可能发热，偏差<-0.5℃可能体温偏低",
    "analysis": "医学诊断中偏差分析帮助快速判断异常情况，正负偏差都有临床意义"
  },
  {
    "title": "食品营养成分控制",
    "problem": "某品牌牛奶标准蛋白质含量为3.0g/100ml，国家标准允许偏差为±0.1g/100ml。抽检5批次产品，蛋白质含量为：2.95g, 3.05g, 3.12g, 2.88g, 3.01g。评估产品质量",
    "solution": "偏差为：-0.05g(合格), +0.05g(合格), +0.12g(超标), -0.12g(不达标), +0.01g(合格)。合格率60%，需要改进生产工艺",
    "analysis": "食品行业质量控制通过偏差分析确保产品符合国家标准，保障消费者权益"
  }
]',
'[
  {
    "concept": "标准值确定",
    "explanation": "标准值由科学实验、行业规范、国家标准等确定",
    "example": "ISO标准、国家强制标准、企业内控标准"
  },
  {
    "concept": "偏差意义",
    "explanation": "偏差反映实际与理想的差距，是质量评价的核心指标",
    "example": "正偏差可能表示超规格，负偏差可能表示不达标"
  },
  {
    "concept": "允许偏差设定",
    "explanation": "允许偏差体现对精度的要求，不同领域要求不同",
    "example": "航空航天要求极高精度，日用品相对宽松"
  },
  {
    "concept": "统计质量控制",
    "explanation": "通过偏差数据的统计分析实现质量管理",
    "example": "控制图、过程能力分析、六西格玛管理"
  },
  {
    "concept": "测量不确定度",
    "explanation": "偏差分析需考虑测量工具的精度限制",
    "example": "游标卡尺精度0.02mm，电子秤精度0.1g"
  }
]',
'[
  "偏差计算公式记错（用标准值减实际值）",
  "混淆偏差的正负意义（认为负偏差更好）", 
  "不理解允许偏差的工程意义（认为偏差越小越好）",
  "忽视偏差的实际应用价值（只当数学题做）",
  "不考虑测量精度的影响（过分强调计算精确性）"
]',
'[
  "公式牢记：偏差=实际-标准，顺序不能颠倒",
  "符号理解：正偏差表示超出，负偏差表示不足",
  "范围概念：允许偏差体现质量要求的严格程度",
  "实际联系：从工业制造、医学检查等实例理解偏差意义",
  "精度意识：培养对测量精度和质量控制的科学态度"
]',
'{
  "emphasis": ["质量控制", "精确测量", "实际应用"],
  "application": ["工业制造", "医学诊断", "科学实验", "食品安全", "环境监测"],
  "connection": ["现代制造业", "质量管理体系", "科学测量方法"]
}',
'{
  "emphasis": ["统计分析", "数据处理", "误差理论"],
  "application": ["六西格玛管理", "实验设计", "过程控制", "质量工程"],
  "connection": ["统计学基础", "测量学原理", "质量管理科学"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- ============================================
-- 1.2 有理数及其大小比较部分
-- ============================================

-- MATH_G7S1_CH1_004: 有理数的概念
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_CH1_004'),
'整数和分数统称为有理数，有理数集合包括正整数、0、负整数、正分数、负分数',
'有理数是初中数学中的核心概念，它是对小学阶段非负有理数的重要扩展。有理数集合包括了所有可以表示为两个整数之比的数，即可以写成p/q(q≠0)的数。这个定义统一了整数和分数的概念，因为整数可以看作分母为1的分数。有理数的引入完善了数的运算体系，使得减法和除法在更大范围内总是可行的。理解有理数概念对于建立完整的数系认识、掌握有理数运算法则都具有重要意义，它也为后续学习实数、复数等概念奠定基础。',
'[
  "有理数 = 整数 + 分数",
  "整数包括：正整数、0、负整数",
  "分数包括：正分数、负分数",
  "有理数可以表示为p/q的形式(p,q为整数，q≠0)",
  "有理数在数轴上稠密分布"
]',
'[
  {
    "name": "有理数定义",
    "formula": "有理数 = {p/q | p,q∈Z, q≠0}",
    "description": "有理数是可以表示为两个整数之比的数"
  },
  {
    "name": "有理数分类",
    "formula": "有理数 = 正有理数 ∪ {0} ∪ 负有理数",
    "description": "有理数按照正负性的分类"
  }
]',
'[
  {
    "title": "判断有理数",
    "problem": "判断下列各数哪些是有理数：5, -3, 2/3, 0, -1.5, π, √2, 0.333...",
    "solution": "有理数：5, -3, 2/3, 0, -1.5, 0.333...；非有理数：π, √2",
    "analysis": "能表示为分数形式的数都是有理数，π和√2是无理数"
  }
]',
'[
  {
    "concept": "数系扩展",
    "explanation": "从自然数到有理数的扩展过程",
    "example": "自然数→整数→有理数"
  },
  {
    "concept": "分数形式",
    "explanation": "有理数都可以写成分数形式",
    "example": "5=5/1, -3=-3/1, 0.5=1/2"
  },
  {
    "concept": "稠密性",
    "explanation": "任意两个有理数之间还有无数个有理数",
    "example": "1和2之间有1.5, 1.1, 1.01等无数个有理数"
  }
]',
'[
  "认为小数都不是有理数",
  "混淆有理数与整数的概念",
  "不理解负分数也是有理数",
  "认为所有数都是有理数"
]',
'[
  "分类记忆：整数+分数=有理数",
  "形式理解：能写成p/q形式的数",
  "范围扩展：比整数更大的数的集合",
  "反例学习：π、√2等不是有理数"
]',
'{
  "emphasis": ["数系完善", "分类理解"],
  "application": ["数的分类", "运算基础"],
  "connection": ["与小学分数的联系", "为运算学习做准备"]
}',
'{
  "emphasis": ["数学结构", "抽象概念"],
  "application": ["集合论基础", "数论概念"],
  "connection": ["实数概念预备", "数学分析基础"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- ============================================
-- 1.3 有理数的运算部分
-- ============================================

-- MATH_G7S1_CH1_005: 有理数的加法
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_CH1_005'),
'有理数的加法运算遵循加法交换律和加法结合律，即a+b=b+a和(a+b)+c=a+(b+c)',
'有理数的加法运算是数学中非常基础的运算之一，它遵循加法交换律和加法结合律。加法交换律表示两个数相加，交换加数的位置，和不变。加法结合律表示三个数相加，先把前两个数相加，或者先把后两个数相加，和不变。有理数的加法运算不仅简化了计算，还为后续的有理数运算提供了基础。',
'[
  "加法交换律：a+b=b+a",
  "加法结合律：(a+b)+c=a+(b+c)"
]',
'[
  {
    "name": "加法交换律",
    "formula": "a+b=b+a",
    "description": "两个数相加，交换加数的位置，和不变"
  },
  {
    "name": "加法结合律",
    "formula": "(a+b)+c=a+(b+c)",
    "description": "三个数相加，先把前两个数相加，或者先把后两个数相加，和不变"
  }
]',
'[
  {
    "title": "有理数加法运算",
    "problem": "计算：-3 + 2 + 5",
    "solution": "-3 + 2 + 5 = 4",
    "analysis": "按照加法交换律和加法结合律进行计算"
  }
]',
'[
  {
    "concept": "加法交换律",
    "explanation": "两个数相加，交换加数的位置，和不变",
    "example": "-3 + 2 + 5 = 4"
  },
  {
    "concept": "加法结合律",
    "explanation": "三个数相加，先把前两个数相加，或者先把后两个数相加，和不变",
    "example": "-3 + 2 + 5 = 4"
  }
]',
'[
  "不理解加法交换律和加法结合律",
  "混淆加法运算的顺序",
  "忽视加法运算的实际应用"
]',
'[
  "生活联系：从购物、温度变化等生活实例理解有理数加法",
  "实际应用：结合具体情境理解有理数加法的意义",
  "分类记忆：有理数加法运算遵循加法交换律和加法结合律"
]',
'{
  "emphasis": ["生活实例", "加法交换律", "加法结合律"],
  "application": ["购物计算", "温度变化", "数据分析"],
  "connection": ["与实际生活的联系", "为数轴学习做准备"]
}',
'{
  "emphasis": ["抽象表示", "数学建模"],
  "application": ["向量概念", "坐标系统"],
  "connection": ["数轴概念", "数学哲学"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G7S1_CH1_006: 有理数的减法
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_CH1_006'),
'有理数的减法运算可以转化为加法运算，即a-b=a+(-b)',
'有理数的减法运算是数学中非常重要的一种运算，它可以将减法转化为加法运算。减法运算可以表示为：a-b=a+(-b)。这种表示方法不仅简化了计算，还为后续的有理数运算提供了基础。理解减法概念有助于培养学生的逆向思维能力，为后续学习代数、几何等数学分支打下基础。',
'[
  "减法运算：a-b=a+(-b)"
]',
'[
  {
    "name": "减法运算",
    "formula": "a-b=a+(-b)",
    "description": "减法可以转化为加法运算"
  }
]',
'[
  {
    "title": "有理数减法运算",
    "problem": "计算：-3 - 2",
    "solution": "-3 - 2 = -5",
    "analysis": "按照减法运算法则进行计算"
  }
]',
'[
  {
    "concept": "减法运算",
    "explanation": "减法可以转化为加法运算",
    "example": "-3 - 2 = -5"
  }
]',
'[
  "不理解减法运算的逆向思维",
  "混淆减法运算的实际应用",
  "忽视减法运算的逆向思维"
]',
'[
  "生活联系：从温度变化、账目记录等生活实例理解有理数减法",
  "实际应用：结合具体情境理解有理数减法的意义",
  "分类记忆：有理数减法运算可以转化为加法运算"
]',
'{
  "emphasis": ["生活实例", "减法运算"],
  "application": ["温度变化", "账目记录", "数据分析"],
  "connection": ["与实际生活的联系", "为数轴学习做准备"]
}',
'{
  "emphasis": ["抽象表示", "数学建模"],
  "application": ["向量概念", "坐标系统"],
  "connection": ["数轴概念", "数学哲学"]
}',
'1.0', true, 'zh-CN', 4.7, 'approved'),

-- MATH_G7S1_CH1_007: 有理数的乘法
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_CH1_007'),
'有理数的乘法运算遵循乘法交换律、乘法结合律和乘法分配律，即a*b=b*a、(a*b)*c=a*(b*c)和a*(b+c)=a*b+a*c',
'有理数的乘法运算是数学中非常重要的一种运算，它遵循乘法交换律、乘法结合律和乘法分配律。乘法交换律表示两个数相乘，交换乘数的位置，积不变。乘法结合律表示三个数相乘，先把前两个数相乘，或者先把后两个数相乘，积不变。乘法分配律表示一个数与两个数的和相乘，等于这个数分别与这两个数相乘，再把积相加。有理数的乘法运算不仅简化了计算，还为后续的有理数运算提供了基础。',
'[
  "乘法交换律：a*b=b*a",
  "乘法结合律：(a*b)*c=a*(b*c)",
  "乘法分配律：a*(b+c)=a*b+a*c"
]',
'[
  {
    "name": "乘法交换律",
    "formula": "a*b=b*a",
    "description": "两个数相乘，交换乘数的位置，积不变"
  },
  {
    "name": "乘法结合律",
    "formula": "(a*b)*c=a*(b*c)",
    "description": "三个数相乘，先把前两个数相乘，或者先把后两个数相乘，积不变"
  },
  {
    "name": "乘法分配律",
    "formula": "a*(b+c)=a*b+a*c",
    "description": "一个数与两个数的和相乘，等于这个数分别与这两个数相乘，再把积相加"
  }
]',
'[
  {
    "title": "有理数乘法运算",
    "problem": "计算：-3 * 2 * 5",
    "solution": "-3 * 2 * 5 = -30",
    "analysis": "按照乘法交换律、乘法结合律和乘法分配律进行计算"
  }
]',
'[
  {
    "concept": "乘法交换律",
    "explanation": "两个数相乘，交换乘数的位置，积不变",
    "example": "-3 * 2 * 5 = -30"
  },
  {
    "concept": "乘法结合律",
    "explanation": "三个数相乘，先把前两个数相乘，或者先把后两个数相乘，积不变",
    "example": "-3 * 2 * 5 = -30"
  },
  {
    "concept": "乘法分配律",
    "explanation": "一个数与两个数的和相乘，等于这个数分别与这两个数相乘，再把积相加",
    "example": "-3 * 2 * 5 = -30"
  }
]',
'[
  "不理解乘法交换律、乘法结合律和乘法分配律",
  "混淆乘法运算的顺序",
  "忽视乘法运算的实际应用"
]',
'[
  "生活联系：从价格、比例等生活实例理解有理数乘法",
  "实际应用：结合具体情境理解有理数乘法的意义",
  "分类记忆：有理数乘法运算遵循乘法交换律、乘法结合律和乘法分配律"
]',
'{
  "emphasis": ["生活实例", "乘法交换律", "乘法结合律", "乘法分配律"],
  "application": ["价格表示", "比例计算", "数据分析"],
  "connection": ["与实际生活的联系", "为数轴学习做准备"]
}',
'{
  "emphasis": ["抽象表示", "数学建模"],
  "application": ["向量概念", "坐标系统"],
  "connection": ["数轴概念", "数学哲学"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'),

-- MATH_G7S1_CH1_008: 有理数的除法
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_CH1_008'),
'有理数的除法运算可以转化为乘法运算，即a/b=a*(1/b)',
'有理数的除法运算是数学中非常重要的一种运算，它可以将除法转化为乘法运算。除法运算可以表示为：a/b=a*(1/b)。这种表示方法不仅简化了计算，还为后续的有理数运算提供了基础。理解除法概念有助于培养学生的逆向思维能力，为后续学习代数、几何等数学分支打下基础。',
'[
  "除法运算：a/b=a*(1/b)"
]',
'[
  {
    "name": "除法运算",
    "formula": "a/b=a*(1/b)",
    "description": "除法可以转化为乘法运算"
  }
]',
'[
  {
    "title": "有理数除法运算",
    "problem": "计算：-3 / 2",
    "solution": "-3 / 2 = -1.5",
    "analysis": "按照除法运算法则进行计算"
  }
]',
'[
  {
    "concept": "除法运算",
    "explanation": "除法可以转化为乘法运算",
    "example": "-3 / 2 = -1.5"
  }
]',
'[
  "不理解除法运算的逆向思维",
  "混淆除法运算的实际应用",
  "忽视除法运算的逆向思维"
]',
'[
  "生活联系：从价格、比例等生活实例理解有理数除法",
  "实际应用：结合具体情境理解有理数除法的意义",
  "分类记忆：有理数除法运算可以转化为乘法运算"
]',
'{
  "emphasis": ["生活实例", "除法运算"],
  "application": ["价格表示", "比例计算", "数据分析"],
  "connection": ["与实际生活的联系", "为数轴学习做准备"]
}',
'{
  "emphasis": ["抽象表示", "数学建模"],
  "application": ["向量概念", "坐标系统"],
  "connection": ["数轴概念", "数学哲学"]
}',
'1.0', true, 'zh-CN', 4.7, 'approved'),

-- ============================================
-- 1.4 有理数的应用部分
-- ============================================

-- MATH_G7S1_CH1_009: 有理数的应用
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_CH1_009'),
'有理数在实际生活中有着广泛的应用，包括经济、科学、工程等领域',
'有理数在实际生活中有着广泛的应用，包括经济、科学、工程等领域。有理数的概念和运算方法不仅在数学中有着重要的地位，而且在现实生活中也有着广泛的应用。理解有理数的应用有助于培养学生的实际应用能力和解决问题的能力，为后续学习数学打下基础。',
'[
  "有理数在实际生活中的应用",
  "包括经济、科学、工程等领域"
]',
'[
  {
    "name": "有理数应用",
    "formula": "有理数在实际生活中的应用",
    "description": "有理数在实际生活中有着广泛的应用"
  }
]',
'[
  {
    "title": "有理数应用实例",
    "problem": "请列举有理数在实际生活中的应用实例",
    "solution": "例如：价格表示、比例计算、数据分析等",
    "analysis": "有理数在实际生活中有着广泛的应用"
  }
]',
'[
  {
    "concept": "有理数应用",
    "explanation": "有理数在实际生活中有着广泛的应用",
    "example": "包括经济、科学、工程等领域"
  }
]',
'[
  "不理解有理数的实际应用",
  "忽视有理数的实际应用"
]',
'[
  "生活联系：从实际生活实例理解有理数的应用",
  "实际应用：结合具体情境理解有理数的应用",
  "分类记忆：有理数在实际生活中有着广泛的应用"
]',
'{
  "emphasis": ["生活实例", "有理数应用"],
  "application": ["价格表示", "比例计算", "数据分析"],
  "connection": ["与实际生活的联系", "为数轴学习做准备"]
}',
'{
  "emphasis": ["抽象表示", "数学建模"],
  "application": ["向量概念", "坐标系统"],
  "connection": ["数轴概念", "数学哲学"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'),

-- ============================================
-- 1.5 有理数的比较部分
-- ============================================

-- MATH_G7S1_CH1_010: 有理数的大小比较
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_CH1_010'),
'在数轴上，右边的数总比左边的数大；正数大于0，0大于负数，正数大于负数',
'有理数的大小比较是有理数学习的重要内容，它将抽象的数量关系转化为直观的几何位置关系。比较有理数大小的基本方法有：利用数轴比较（右边的数大于左边的数）、利用正负性比较（正数>0>负数）、利用绝对值比较（两个负数中绝对值小的反而大）。掌握有理数大小比较不仅是进行有理数运算的基础，也为后续学习不等式、函数单调性等内容做准备。数轴比较法体现了数形结合思想，是培养数学直观的重要工具。',
'[
  "数轴比较法：右边的数大于左边的数",
  "正负性比较：正数 > 0 > 负数",
  "两个正数：绝对值大的数大",
  "两个负数：绝对值大的数反而小",
  "可以用不等号>、<、=表示大小关系"
]',
'[
  {
    "name": "数轴比较法",
    "formula": "在数轴上，右边的数 > 左边的数",
    "description": "利用数轴位置比较有理数大小"
  },
  {
    "name": "正负性比较",
    "formula": "正数 > 0 > 负数",
    "description": "根据正负性直接比较"
  },
  {
    "name": "同号数比较",
    "formula": "两正数：大绝对值 > 小绝对值；两负数：小绝对值 > 大绝对值",
    "description": "同号有理数的大小比较方法"
  }
]',
'[
  {
    "title": "比较有理数大小",
    "problem": "比较下列各组数的大小：(1) -5与-3  (2) -2.5与1  (3) 0与-7  (4) |3|与|-4|",
    "solution": "(1) -5 < -3（两负数绝对值大的小）(2) -2.5 < 1（负数小于正数）(3) 0 > -7（0大于负数）(4) |3| < |-4|即3 < 4",
    "analysis": "根据数的性质选择合适的比较方法"
  }
]',
'[
  {
    "concept": "数轴直观",
    "explanation": "用数轴位置直观比较大小",
    "example": "数轴上越靠右的数越大"
  },
  {
    "concept": "分类比较",
    "explanation": "根据数的正负性分类比较",
    "example": "正数、零、负数的大小关系"
  },
  {
    "concept": "绝对值应用",
    "explanation": "利用绝对值比较同号数",
    "example": "两个负数比较要看绝对值"
  },
  {
    "concept": "传递性",
    "explanation": "大小关系具有传递性",
    "example": "如果a>b且b>c，则a>c"
  }
]',
'[
  "两个负数比较时方向弄反",
  "忽视0在比较中的作用",
  "混淆绝对值大小与原数大小",
  "不会利用数轴进行比较"
]',
'[
  "数轴方法：画数轴直观比较位置",
  "分类记忆：正数>0>负数的基本关系",
  "负数技巧：两负数中绝对值小的大",
  "符号应用：熟练使用>、<、=符号"
]',
'{
  "emphasis": ["数形结合", "分类比较"],
  "application": ["排序问题", "最值问题"],
  "connection": ["数轴应用", "为不等式学习做准备"]
}',
'{
  "emphasis": ["逻辑推理", "关系判断"],
  "application": ["优化问题", "决策分析"],
  "connection": ["序理论", "偏序关系"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- ============================================
-- 1.6 有理数的绝对值部分
-- ============================================

-- MATH_G7S1_CH1_011: 有理数的绝对值
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_CH1_011'),
'有理数的绝对值表示数轴上点到原点的距离，即|a|=a (a≥0)和|a|=-a (a<0)',
'有理数的绝对值表示数轴上点到原点的距离，即|a|=a (a≥0)和|a|=-a (a<0)。有理数的绝对值是数学中非常重要的一种运算，它可以帮助我们更好地理解有理数的概念和性质，为后续学习数学打下基础。',
'[
  "绝对值：|a|=a (a≥0)和|a|=-a (a<0)"
]',
'[
  {
    "name": "绝对值定义",
    "formula": "|a|=a (a≥0)和|a|=-a (a<0)",
    "description": "有理数的绝对值表示数轴上点到原点的距离"
  }
]',
'[
  {
    "title": "绝对值计算实例",
    "problem": "计算：|-3|和|2|",
    "solution": "|-3|=3和|2|=2",
    "analysis": "通过绝对值定义进行计算"
  }
]',
'[
  {
    "concept": "绝对值",
    "explanation": "有理数的绝对值表示数轴上点到原点的距离",
    "example": "|-3|=3和|2|=2"
  }
]',
'[
  "不理解绝对值的概念",
  "混淆绝对值的实际应用"
]',
'[
  "生活联系：从实际生活实例理解绝对值",
  "实际应用：结合具体情境理解绝对值",
  "分类记忆：有理数的绝对值表示数轴上点到原点的距离"
]',
'{
  "emphasis": ["生活实例", "绝对值"],
  "application": ["价格表示", "比例计算", "数据分析"],
  "connection": ["与实际生活的联系", "为数轴学习做准备"]
}',
'{
  "emphasis": ["抽象表示", "数学建模"],
  "application": ["向量概念", "坐标系统"],
  "connection": ["数轴概念", "数学哲学"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved');
 
-- ============================================
-- 第1章知识点详情入库完成
-- 七年级上学期第一章 有理数 全部11个知识点详情已完整入库
-- 质量等级：★★★★★ (专家权威版)
-- 符合人民教育出版社数学七年级上册官方教材要求
-- 适用对象：七年级学生数学学习基础建设
-- ============================================ 