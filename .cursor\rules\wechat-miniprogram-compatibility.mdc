---
description:
globs:
alwaysApply: false
---
# 微信小程序兼容性规范

本规范记录微信小程序开发中的常见兼容性问题和解决方案，确保代码能在小程序环境中正常运行。

## 核心兼容性问题

### Node.js模块兼容性

**问题**: 微信小程序不支持Node.js特有的模块和API

**常见错误**:
- `require.main === module` - 小程序不支持require.main属性
- `fs` 和 `path` 模块 - 小程序不支持文件系统操作
- `process.exit()` - 小程序不支持进程控制
- `__dirname` - 小程序不支持目录路径获取

**解决方案**:

1. **模块导出兼容性处理**:
```javascript
// 错误写法
if (require.main === module) {
  // 执行代码
}

// 正确写法 - 兼容小程序环境
// 注释掉Node.js特有检查
// if (require.main === module) {
//   // 执行代码  
// }

// 微信小程序环境导出
if (typeof module !== 'undefined' && module.exports) {
  module.exports = MyClass;
} else {
  // 全局环境挂载
  if (typeof window !== 'undefined') {
    window.MyClass = MyClass;
  } else if (typeof global !== 'undefined') {
    global.MyClass = MyClass;
  }
}
```

2. **文件系统操作替代**:
```javascript
// 错误写法
const fs = require('fs');
const path = require('path');
fs.writeFileSync(path.join(__dirname, 'data.js'), content);

// 正确写法 - 返回数据而非写文件
// 注释掉文件系统操作
// const fs = require('fs');
// const path = require('path');

// 在小程序中直接返回数据
return {
  fileContent: content,
  data: processedData
};
```

3. **进程控制替代**:
```javascript
// 错误写法
process.exit(1);

// 正确写法
// process.exit(1); // 注释掉
throw new Error('执行失败'); // 改为抛出错误
```

### require模块引入规范

**问题**: 小程序对require的使用有特定限制

**解决方案**:
- 避免使用Node.js特有模块
- 使用相对路径引入自定义模块
- 检查模块是否在小程序支持列表中

### 错误处理兼容性

**问题**: 小程序环境的错误处理方式不同

**解决方案**:
```javascript
// 使用try-catch包装
try {
  // 业务逻辑
} catch (error) {
  console.error('错误信息:', error);
  // 不使用process.exit，改为抛出或返回错误状态
  throw error;
}
```

## 开发工具检查规范

### 常见报错信息

1. **"Code protect is not available"** - 通常伴随require使用问题
2. **"require is not being used properly"** - require.main等Node.js特性使用错误
3. **"Module not found"** - 引用了不支持的Node.js模块

### 修复检查清单

- [ ] 检查是否使用了`require.main`
- [ ] 检查是否引入了`fs`、`path`等Node.js模块  
- [ ] 检查是否使用了`process.exit()`
- [ ] 检查是否使用了`__dirname`、`__filename`
- [ ] 确保模块导出兼容小程序环境
- [ ] 验证错误处理不依赖Node.js特性

## 最佳实践

1. **环境检测**: 在代码中加入环境检测逻辑
2. **条件编译**: 使用注释方式处理环境差异
3. **数据返回**: 用数据返回替代文件写入
4. **错误抛出**: 用错误抛出替代进程退出
5. **兼容导出**: 支持多种模块导出方式

## 相关文件示例

参考 [data/data-import-script.js](mdc:data/data-import-script.js) 中的兼容性处理方式。

## 验证方法

1. 在微信开发者工具中编译检查
2. 查看控制台是否有兼容性警告
3. 确保所有功能在小程序环境中正常运行
