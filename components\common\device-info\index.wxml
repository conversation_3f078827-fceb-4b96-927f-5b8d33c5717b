<view class="device-info-container {{showDeviceInfo ? 'show' : 'hide'}}">
  <view class="device-info-panel">
    <view class="device-info-header">
      <view class="device-info-title">设备信息</view>
      <view class="device-info-actions">
        <view class="device-info-action" bindtap="copyDeviceInfo">复制</view>
        <view class="device-info-action" bindtap="hideDeviceInfo">关闭</view>
      </view>
    </view>
    
    <!-- 基本信息 -->
    <view class="device-info-section">
      <view class="section-title">基本信息</view>
      <view class="info-item">
        <text class="item-label">设备品牌：</text>
        <text class="item-value">{{deviceInfo.brand}}</text>
      </view>
      <view class="info-item">
        <text class="item-label">设备型号：</text>
        <text class="item-value">{{deviceInfo.model}}</text>
      </view>
      <view class="info-item">
        <text class="item-label">操作系统：</text>
        <text class="item-value">{{deviceInfo.system}}</text>
      </view>
      <view class="info-item">
        <text class="item-label">设备平台：</text>
        <text class="item-value">{{deviceInfo.platform}}</text>
      </view>
    </view>
    
    <!-- 屏幕信息 -->
    <view class="device-info-section">
      <view class="section-title">屏幕信息</view>
      <view class="info-item">
        <text class="item-label">屏幕宽度：</text>
        <text class="item-value">{{deviceInfo.screenWidth}}px</text>
      </view>
      <view class="info-item">
        <text class="item-label">屏幕高度：</text>
        <text class="item-value">{{deviceInfo.screenHeight}}px</text>
      </view>
      <view class="info-item">
        <text class="item-label">可用宽度：</text>
        <text class="item-value">{{deviceInfo.windowWidth}}px</text>
      </view>
      <view class="info-item">
        <text class="item-label">可用高度：</text>
        <text class="item-value">{{deviceInfo.windowHeight}}px</text>
      </view>
      <view class="info-item">
        <text class="item-label">设备像素比：</text>
        <text class="item-value">{{deviceInfo.pixelRatio}}</text>
      </view>
    </view>
    
    <!-- 安全区域 -->
    <view class="device-info-section">
      <view class="section-title">安全区域</view>
      <view class="info-item">
        <text class="item-label">状态栏高度：</text>
        <text class="item-value">{{deviceInfo.statusBarHeight}}px</text>
      </view>
      <view class="info-item">
        <text class="item-label">底部安全高度：</text>
        <text class="item-value">{{deviceInfo.safeAreaBottom}}px</text>
      </view>
      <view class="info-item" wx:if="{{expandDetails}}">
        <text class="item-label">安全区域左：</text>
        <text class="item-value">{{deviceInfo.safeArea.left}}px</text>
      </view>
      <view class="info-item" wx:if="{{expandDetails}}">
        <text class="item-label">安全区域右：</text>
        <text class="item-value">{{deviceInfo.safeArea.right}}px</text>
      </view>
      <view class="info-item" wx:if="{{expandDetails}}">
        <text class="item-label">安全区域上：</text>
        <text class="item-value">{{deviceInfo.safeArea.top}}px</text>
      </view>
      <view class="info-item" wx:if="{{expandDetails}}">
        <text class="item-label">安全区域下：</text>
        <text class="item-value">{{deviceInfo.safeArea.bottom}}px</text>
      </view>
    </view>
    
    <!-- 设备特性 -->
    <view class="device-info-section">
      <view class="section-title">设备特性</view>
      <view class="info-item">
        <text class="item-label">刘海屏设备：</text>
        <text class="item-value">{{deviceInfo.isIPhoneX ? '是' : '否'}}</text>
      </view>
      <view class="info-item">
        <text class="item-label">iPad设备：</text>
        <text class="item-value">{{deviceInfo.isIPad ? '是' : '否'}}</text>
      </view>
    </view>
    
    <!-- 展开/收起按钮 -->
    <view class="expand-btn" bindtap="toggleDetails">
      {{expandDetails ? '收起详情' : '查看更多'}}
    </view>
  </view>
</view> 