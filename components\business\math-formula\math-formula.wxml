<view class="math-formula {{mode}} {{clickable ? 'clickable' : ''}}" bindtap="handleTap" style="font-size: {{fontSize}}px;">
  <view class="formula-content">{{renderedFormula}}</view>
  
  <view class="steps-button" wx:if="{{showStepsButton && steps.length > 0}}" catchtap="toggleSteps">
    <view class="icon {{showSteps ? 'icon-up' : 'icon-down'}} icon-sm"></view>
  </view>
  
  <view class="steps-container" wx:if="{{showSteps && steps.length > 0}}">
    <view class="step-content">
      <view class="step-title">解题步骤 {{currentStep + 1}}/{{steps.length}}</view>
      <view class="step-formula">{{steps[currentStep].formula}}</view>
      <view class="step-explanation">{{steps[currentStep].explanation}}</view>
    </view>
    
    <view class="step-controls">
      <view class="step-control-button" catchtap="prevStep" wx:if="{{currentStep > 0}}">
        <view class="icon icon-left icon-sm"></view>
      </view>
      
      <view class="step-control-button" catchtap="toggleAnimation" wx:if="{{enableAnimation}}">
        <view class="icon {{isPlaying ? 'icon-pause' : 'icon-play'}} icon-sm"></view>
      </view>
      
      <view class="step-control-button" catchtap="speakExplanation">
        <view class="icon icon-voice icon-sm"></view>
      </view>
      
      <view class="step-control-button" catchtap="nextStep" wx:if="{{currentStep < steps.length - 1}}">
        <view class="icon icon-right icon-sm"></view>
      </view>
    </view>
  </view>
</view> 