// 雷达图组件
Component({
  properties: {
    // 雷达图数据
    chartData: {
      type: Object,
      value: null,
      observer: function(newVal) {
        if (newVal) {
          // 延迟一下再绘制，确保组件已完全渲染
          setTimeout(() => {
            this.drawRadarChart();
          }, 50);
        }
      }
    },
    // 对比数据（班级平均）
    compareData: {
      type: Array,
      value: []
    },
    // 图表宽度
    width: {
      type: Number,
      value: 300
    },
    // 图表高度
    height: {
      type: Number,
      value: 300
    },
    // 主题色
    primaryColor: {
      type: String,
      value: '#3E7BFA'
    },
    // 对比色
    secondaryColor: {
      type: String,
      value: '#F5A623'
    },
    // 图表标题
    title: {
      type: String,
      value: ''
    },
    // 图例位置：top, bottom, none
    legendPosition: {
      type: String,
      value: 'top'
    },
    // 是否启用动画
    enableAnimation: {
      type: Boolean,
      value: false
    }
  },

  data: {
    svgContent: '',
    svgBase64: '',
    imageLoaded: false,
    axisLabels: [],
    chartReady: false
  },

  lifetimes: {
    attached() {
      // 组件被附加时的处理
    },
    ready() {
      // 推迟绘制操作，确保组件DOM结构完全准备好
      setTimeout(() => {
        if (this.data.chartData) {
          this.drawRadarChart();
        }
      }, 100);
    },
    detached() {
      // 组件卸载时清理相关数据，避免内存泄漏
      this.setData({
        svgContent: '',
        svgBase64: '',
        imageLoaded: false,
        axisLabels: [],
        chartReady: false
      });
    }
  },

  methods: {
    /**
     * 绘制雷达图
     */
    drawRadarChart() {
      const chartData = this.properties.chartData;
      const compareData = this.properties.compareData;
      
      if (!chartData) {
        console.warn('雷达图数据为空');
        return;
      }

      try {
        // 解析数据
        const data = this.processChartData(chartData, compareData);
        if (!data) return;

        // 使用setTimeout确保DOM已完全渲染
        setTimeout(() => {
          // 生成SVG
          const svgString = this.generateSvg(data);
          this.setData({ svgContent: svgString });

          // 转换为Base64
          this.convertSvgToBase64(svgString);

          // 触发渲染完成事件
          this.triggerEvent('rendered');
        }, 50);
        
      } catch (error) {
        console.error('绘制雷达图失败:', error);
      }
    },

    /**
     * 处理图表数据
     */
    processChartData(chartData, compareData) {
      // 尝试适配不同格式的数据
      let labels = [];
      let userValues = [];
      let avgValues = [];

      // 优先使用传入的对比数据
      if (compareData && compareData.length > 0) {
        avgValues = compareData;
      }

      // 先尝试标准格式
      if (chartData.labels && chartData.data) {
        labels = chartData.labels;
        userValues = chartData.data;
        
        // 如果没有单独传入compareData，尝试从chartData获取
        if (avgValues.length === 0 && chartData.avgData && chartData.avgData.length === labels.length) {
          avgValues = chartData.avgData;
        }
      } 
      // 尝试维度格式
      else if (chartData.dimensions && (chartData.userData || chartData.values)) {
        labels = chartData.dimensions;
        userValues = chartData.userData || chartData.values;
        
        // 如果没有单独传入compareData，尝试从chartData获取
        if (avgValues.length === 0 && chartData.averageData && chartData.averageData.length === labels.length) {
          avgValues = chartData.averageData;
        }
      }
      // 尝试轴格式
      else if (chartData.axes && chartData.datasets) {
        labels = chartData.axes.map(axis => axis.label || axis.name);
        
        if (chartData.datasets.length > 0) {
          const firstDataset = chartData.datasets[0];
          userValues = labels.map(label => {
            const axisName = chartData.axes.find(axis => axis.label === label || axis.name === label)?.name;
            return axisName ? (firstDataset.values[axisName] || 0) : 0;
          });
          
          // 如果没有单独传入compareData，尝试从datasets获取
          if (avgValues.length === 0 && chartData.datasets.length > 1) {
            const secondDataset = chartData.datasets[1];
            avgValues = labels.map(label => {
              const axisName = chartData.axes.find(axis => axis.label === label || axis.name === label)?.name;
              return axisName ? (secondDataset.values[axisName] || 0) : 0;
            });
          }
        }
      }

      // 检查数据有效性
      if (labels.length < 3 || userValues.length !== labels.length) {
        console.error('雷达图数据不足或不匹配', {
          labelsLength: labels.length,
          userValuesLength: userValues.length
        });
        return null;
      }

      this.setData({ axisLabels: labels });

      return {
        labels,
        userValues,
        avgValues: avgValues.length === labels.length ? avgValues : []
      };
    },

    /**
     * 生成SVG字符串
     */
    generateSvg(data) {
      const { labels, userValues, avgValues } = data;
      const { width, height, primaryColor, secondaryColor, enableAnimation } = this.properties;
      
      // 设置边距和半径
      const margin = { top: 40, right: 40, bottom: 40, left: 40 };
      const radius = Math.min(width - margin.left - margin.right, height - margin.top - margin.bottom) / 2;
      
      // 计算中心点
      const centerX = width / 2;
      const centerY = height / 2;
      
      // 计算角度步长
      const angleStep = (Math.PI * 2) / labels.length;
      
      // 级别数量
      const levels = 4;
      
      // 开始生成SVG内容
      let svgString = `<svg xmlns="http://www.w3.org/2000/svg" width="${width}" height="${height}" viewBox="0 0 ${width} ${height}">`;
      
      // 添加白色背景（透明度为0，预留可能的样式需求）
      svgString += `<rect width="${width}" height="${height}" fill="white" opacity="0" />`;

      // 添加标题
      if (this.properties.title) {
        svgString += `<text x="${width/2}" y="20" text-anchor="middle" font-size="16" font-weight="500" fill="#333">${this.properties.title}</text>`;
      }
      
      // 绘制网格线 - 同心圆
      svgString += `<g class="radar-grid">`;
      for (let i = 1; i <= levels; i++) {
        const levelRadius = radius * (i / levels);
        svgString += `<circle cx="${centerX}" cy="${centerY}" r="${levelRadius}" fill="none" stroke="#EEEEEE" stroke-width="1" />`;
      }
      svgString += `</g>`;
      
      // 绘制轴线
      svgString += `<g class="radar-axis">`;
      for (let i = 0; i < labels.length; i++) {
        const angle = i * angleStep - Math.PI / 2; // 从12点钟方向开始
        const x = centerX + radius * Math.cos(angle);
        const y = centerY + radius * Math.sin(angle);
        
        svgString += `<line x1="${centerX}" y1="${centerY}" x2="${x}" y2="${y}" stroke="#DDDDDD" stroke-width="1" />`;
        
        // 添加标签 - 根据位置细化调整
        let labelRadius = radius * 1.10; // 基础距离
        
        // 针对不同位置的标签添加适当的偏移调整
        const labelOffset = {dx: 0, dy: 0};
        
        // 顶部标签稍微上移
        if (Math.abs(angle + Math.PI/2) < 0.3) { // 接近顶部
          labelOffset.dy = -10;
        }
        // 底部标签稍微下移
        else if (Math.abs(angle - Math.PI/2) < 0.3) { // 接近底部
          labelOffset.dy = 10;
        }
        // 左侧标签向左偏移
        else if (Math.abs(angle - Math.PI) < 0.5) { // 靠左侧
          labelOffset.dx = -10;
        }
        // 右侧标签向右偏移
        else if (Math.abs(angle) < 0.5) { // 靠右侧
          labelOffset.dx = 10;
        }
        
        const labelX = centerX + labelRadius * Math.cos(angle) + labelOffset.dx;
        const labelY = centerY + labelRadius * Math.sin(angle) + labelOffset.dy;
        
        // 根据角度调整文本对齐方式
        let textAnchor = "middle";
        
        // 更精细的文本对齐调整
        if (Math.cos(angle) > 0.5) {
          textAnchor = "start";
        } else if (Math.cos(angle) < -0.5) {
          textAnchor = "end";
        }
        
        // 标签文本
        svgString += `<text x="${labelX}" y="${labelY}" text-anchor="${textAnchor}" 
          fill="#333333" font-size="13" font-weight="500">${labels[i]}</text>`;
      }
      svgString += `</g>`;
      
      // 如果有平均值数据，绘制平均值多边形
      if (avgValues.length > 0) {
        svgString += this.generatePolygon(avgValues, centerX, centerY, radius, angleStep, secondaryColor, 0.4, "radar-shape-secondary", enableAnimation);
      }
      
      // 绘制用户数据多边形
      svgString += this.generatePolygon(userValues, centerX, centerY, radius, angleStep, primaryColor, 0.6, "radar-shape-primary", enableAnimation);
      
      // 结束SVG
      svgString += `</svg>`;
      
      return svgString;
    },
    
    /**
     * 生成多边形SVG
     */
    generatePolygon(values, centerX, centerY, radius, angleStep, color, opacity, className, animate) {
      let svg = '';
      
      // 计算多边形的点
      const points = values.map((value, i) => {
        const angle = i * angleStep - Math.PI / 2;
        // 最大值假设为100
        const normalizedValue = Math.min(value, 100) / 100;
        const distance = normalizedValue * radius;
        return {
          x: centerX + distance * Math.cos(angle),
          y: centerY + distance * Math.sin(angle)
        };
      });
      
      // 生成多边形点字符串
      const pointsString = points.map(p => `${p.x},${p.y}`).join(' ');
      
      // 添加多边形区域
      svg += `<polygon points="${pointsString}" fill="${color}" fill-opacity="${opacity}" stroke="${color}" stroke-width="2" stroke-opacity="0.9"`;
      
      // 如果启用动画，添加动画效果
      if (animate) {
        svg += ` opacity="0">
          <animate attributeName="opacity" from="0" to="1" dur="0.8s" fill="freeze" />
        </polygon>`;
      } else {
        svg += ` />`;
      }
      
      // 添加数据点
      points.forEach((p, i) => {
        svg += `<circle cx="${p.x}" cy="${p.y}" r="4" fill="white" stroke="${color}" stroke-width="2"`;
        
        if (animate) {
          svg += ` opacity="0">
            <animate attributeName="opacity" from="0" to="1" dur="1s" fill="freeze" begin="0.8s" />
          </circle>`;
        } else {
          svg += ` />`;
        }
      });
      
      return svg;
    },
    
    /**
     * 将SVG字符串转换为Base64编码
     */
    convertSvgToBase64(svgString) {
      try {
        // 避免使用encodeURIComponent，直接转为utf-8字节数组
        const bytes = [];
        for (let i = 0; i < svgString.length; i++) {
          const code = svgString.charCodeAt(i);
          if (code < 128) {
            bytes.push(code);
          } else if (code < 2048) {
            bytes.push(192 + (code >> 6), 128 + (code & 63));
          } else if (code < 65536) {
            bytes.push(224 + (code >> 12), 128 + ((code >> 6) & 63), 128 + (code & 63));
          }
        }
        
        // 使用微信API转换为Base64
        const base64Svg = `data:image/svg+xml;base64,${wx.arrayBufferToBase64(new Uint8Array(bytes))}`;
        
        this.setData({
          svgBase64: base64Svg,
          imageLoaded: true,
          chartReady: true
        });
        
        return base64Svg;
      } catch (error) {
        console.error('SVG转Base64失败:', error);
        return null;
      }
    },
    
    /**
     * 导出图片（供外部调用）
     */
    exportImage() {
      return new Promise((resolve, reject) => {
        if (this.data.svgBase64) {
          // 已经有Base64编码的SVG
          resolve(this.data.svgBase64);
        } else if (this.data.svgContent) {
          // 有SVG内容但未转换为Base64
          try {
            const base64Svg = this.convertSvgToBase64(this.data.svgContent);
            if (base64Svg) {
              resolve(base64Svg);
            } else {
              reject(new Error('SVG转Base64失败'));
            }
          } catch (error) {
            console.error('导出图片失败:', error);
            reject(error);
          }
        } else {
          // 尝试重新绘制
          this.drawRadarChart();
          
          // 等待绘制完成
          setTimeout(() => {
            if (this.data.svgBase64) {
              resolve(this.data.svgBase64);
            } else {
              reject(new Error('雷达图绘制失败'));
            }
          }, 300);
        }
      });
    },

    /**
     * 图片加载完成回调
     */
    onImageLoad: function(e) {
      // 通知图片加载完成
      this.triggerEvent('imageload', e.detail);
    }
  }
}); 