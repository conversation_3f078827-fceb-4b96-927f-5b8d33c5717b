// 智能诊断引擎 - 优化版核心模块
const AIEnhancedAnalyzer = require('./ai-enhanced-analyzer');
const LearningOutcomePredictor = require('./learning-outcome-predictor');
const RealTimeBehaviorAnalyzer = require('./real-time-behavior-analyzer');
const AdaptivePathOptimizer = require('./adaptive-path-optimizer');
const LearningPathGenerator = require('./learning-path-generator');

/**
 * 智能诊断引擎主类 - 优化版
 * 整合多个分析模块，提供统一的诊断接口
 * 增强了错误处理、性能监控和缓存机制
 */
class DiagnosisEngine {
  constructor() {
    this.initialized = false;
    this.modules = {};
    this.cache = new Map();
    this.metrics = {
      totalDiagnosis: 0,
      successfulDiagnosis: 0,
      failedDiagnosis: 0,
      avgResponseTime: 0,
      cacheHitRate: 0,
      lastResetTime: Date.now()
    };
    this.config = {
      cacheTimeout: 5 * 60 * 1000, // 5分钟缓存
      maxCacheSize: 100,
      enableCache: true,
      enableMetrics: true,
      enableDetailedLogging: true,
      maxRetries: 3,
      retryDelay: 1000
    };
  }

  /**
   * 初始化诊断引擎 - 优化版
   */
  async initialize() {
    if (this.initialized) return;

    const startTime = Date.now();
    let initErrors = [];

    try {
      console.log('正在初始化智能诊断引擎...');
      
      // 初始化知识图谱（模拟数据）
      this.knowledgeGraph = await this.initializeKnowledgeGraph();
      
      // 初始化各个分析模块 - 增加错误处理
      const moduleInitializations = [
        this.initializeModule('aiAnalyzer', () => new AIEnhancedAnalyzer(this.knowledgeGraph)),
        this.initializeModule('outcomePredictor', () => new LearningOutcomePredictor(this.knowledgeGraph)),
        this.initializeModule('behaviorAnalyzer', () => new RealTimeBehaviorAnalyzer(this.knowledgeGraph)),
        this.initializeModule('pathOptimizer', () => new AdaptivePathOptimizer(this.knowledgeGraph)),
        this.initializeModule('pathGenerator', () => new LearningPathGenerator(this.knowledgeGraph))
      ];

      const results = await Promise.allSettled(moduleInitializations);
      
      // 检查初始化结果
      results.forEach((result, index) => {
        if (result.status === 'rejected') {
          const moduleNames = ['aiAnalyzer', 'outcomePredictor', 'behaviorAnalyzer', 'pathOptimizer', 'pathGenerator'];
          initErrors.push(`${moduleNames[index]}: ${result.reason}`);
        }
      });
      
      if (initErrors.length > 0) {
        console.warn('部分模块初始化失败:', initErrors);
        // 如果关键模块失败，抛出错误
        if (initErrors.length > 2) {
          throw new Error(`关键模块初始化失败: ${initErrors.join(', ')}`);
        }
      }
      
      this.initialized = true;
      const initTime = Date.now() - startTime;
      console.log(`智能诊断引擎初始化完成，用时: ${initTime}ms`);
      
      if (this.config.enableMetrics) {
        this.logMetrics('initialization', { duration: initTime, errors: initErrors.length });
      }
    } catch (error) {
      console.error('诊断引擎初始化失败:', error);
      throw new Error(`诊断引擎初始化失败: ${error.message}`);
    }
  }

  /**
   * 初始化单个模块 - 新增
   * @param {string} moduleName - 模块名称
   * @param {Function} moduleFactory - 模块工厂函数
   * @returns {Promise} 初始化Promise
   */
  async initializeModule(moduleName, moduleFactory) {
    try {
      console.log(`正在初始化模块: ${moduleName}`);
      this.modules[moduleName] = moduleFactory();
      
      // 如果模块有初始化方法，调用它
      if (typeof this.modules[moduleName].initialize === 'function') {
        await this.modules[moduleName].initialize();
      }
      
      console.log(`模块 ${moduleName} 初始化成功`);
      return { moduleName, status: 'success' };
    } catch (error) {
      console.error(`模块 ${moduleName} 初始化失败:`, error);
      // 设置空的模块以防止调用错误
      this.modules[moduleName] = null;
      throw new Error(`${moduleName} 初始化失败: ${error.message}`);
    }
  }

  /**
   * 综合智能诊断 - 优化版
   * @param {Object} studentData - 学生数据
   * @param {Object} learningData - 学习数据
   * @param {Object} testResults - 测试结果
   * @param {Object} learningProgress - 学习进度
   * @param {Object} behaviorData - 行为数据
   * @returns {Object} 综合诊断结果
   */
  async comprehensiveDiagnosis(studentData, learningData, testResults, learningProgress = {}, behaviorData = {}) {
    const startTime = Date.now();
    this.ensureInitialized();
    
    // 输入验证
    this.validateInputData(studentData, 'studentData');
    this.validateInputData(testResults, 'testResults');
    
    const cacheKey = this.generateCacheKey('comprehensive', studentData.studentId, testResults);
    
    // 检查缓存
    if (this.config.enableCache && this.cache.has(cacheKey)) {
      const cachedResult = this.cache.get(cacheKey);
      if (Date.now() - cachedResult.timestamp < this.config.cacheTimeout) {
        console.log('返回缓存的诊断结果');
        this.updateMetrics('cache_hit', Date.now() - startTime);
        return cachedResult.data;
      }
    }

    try {
      console.log(`开始为学生 ${studentData.studentId} 进行综合诊断...`);
      
      // 并行执行多个分析模块
      const analysisPromises = [];
      
      // 1. AI增强分析
      if (this.modules.aiAnalyzer) {
        analysisPromises.push(
          this.executeWithRetry(
            () => this.modules.aiAnalyzer.enhancedAnalysis(studentData, learningData, testResults, behaviorData),
            'AI增强分析'
          ).catch(error => ({ error: error.message, module: 'aiAnalyzer' }))
        );
      }
      
      // 2. 学习结果预测
      if (this.modules.outcomePredictor) {
        analysisPromises.push(
          this.executeWithRetry(
            () => this.modules.outcomePredictor.predictLearningOutcomes(studentData, learningProgress, {}, {}),
            '学习结果预测'
          ).catch(error => ({ error: error.message, module: 'outcomePredictor' }))
        );
      }
      
      // 3. 实时行为分析
      if (this.modules.behaviorAnalyzer) {
        analysisPromises.push(
          this.executeWithRetry(
            () => this.modules.behaviorAnalyzer.analyzeRealtimeBehavior(behaviorData, { currentNodeId: 'current' }, studentData),
            '实时行为分析'
          ).catch(error => ({ error: error.message, module: 'behaviorAnalyzer' }))
        );
      }
      
      // 4. 薄弱点识别
      analysisPromises.push(
        this.executeWithRetry(
          () => this.identifyWeaknesses({ testResults, learningHistory: learningData }),
          '薄弱点识别'
        ).catch(error => ({ error: error.message, module: 'weaknessAnalysis' }))
      );
      
      // 5. 生成学习路径推荐
      analysisPromises.push(
        this.executeWithRetry(
          () => this.generateOptimalLearningPath({
            currentLevel: this.calculateCurrentLevel(testResults),
            targetLevel: studentData.gradeLevel + 1,
            learningPreferences: this.extractLearningPreferences(behaviorData)
          }),
          '学习路径推荐'
        ).catch(error => ({ error: error.message, module: 'pathRecommendation' }))
      );

      // 等待所有分析完成
      const analysisResults = await Promise.all(analysisPromises);
      
      // 处理分析结果
      const [aiAnalysis, outcomePrediction, behaviorAnalysis, weaknessAnalysis, pathRecommendation] = analysisResults;
      
      // 检查是否有关键模块失败
      const failedModules = analysisResults.filter(result => result && result.error);
      if (failedModules.length > 0) {
        console.warn('部分分析模块执行失败:', failedModules);
      }

      const result = {
        timestamp: new Date().toISOString(),
        studentId: studentData.studentId,
        diagnosisType: 'comprehensive',
        executionTime: Date.now() - startTime,
        analysisResults: {
          aiEnhancedAnalysis: aiAnalysis?.error ? null : aiAnalysis,
          outcomePrediction: outcomePrediction?.error ? null : outcomePrediction,
          behaviorAnalysis: behaviorAnalysis?.error ? null : behaviorAnalysis,
          weaknessAnalysis: weaknessAnalysis?.error ? null : weaknessAnalysis,
          pathRecommendation: pathRecommendation?.error ? null : pathRecommendation
        },
        errors: failedModules,
        overallScore: this.calculateOverallScore(
          aiAnalysis?.error ? null : aiAnalysis, 
          outcomePrediction?.error ? null : outcomePrediction
        ),
        recommendations: this.generateOverallRecommendations(
          aiAnalysis?.error ? null : aiAnalysis, 
          weaknessAnalysis?.error ? null : weaknessAnalysis
        ),
        nextSteps: this.generateNextSteps(pathRecommendation?.error ? null : pathRecommendation),
        confidence: this.calculateDiagnosisConfidence(analysisResults, failedModules.length)
      };

      // 缓存结果
      if (this.config.enableCache) {
        this.setCache(cacheKey, result);
      }

      // 更新指标
      this.updateMetrics('success', Date.now() - startTime);
      
      console.log(`综合诊断完成，用时: ${Date.now() - startTime}ms`);
      return result;
    } catch (error) {
      this.updateMetrics('error', Date.now() - startTime);
      console.error('综合诊断失败:', error);
      throw new Error(`综合诊断失败: ${error.message}`);
    }
  }

  /**
   * 输入数据验证 - 新增
   * @param {Object} data - 数据对象
   * @param {string} dataType - 数据类型
   */
  validateInputData(data, dataType) {
    if (!data || typeof data !== 'object') {
      throw new Error(`${dataType} 必须是一个有效的对象`);
    }
    
    if (dataType === 'studentData') {
      if (!data.studentId || !data.gradeLevel) {
        throw new Error('学生数据必须包含 studentId 和 gradeLevel');
      }
    }
    
    if (dataType === 'testResults') {
      if (Object.keys(data).length === 0) {
        throw new Error('测试结果不能为空');
      }
    }
  }

  /**
   * 重试执行 - 新增
   * @param {Function} operation - 操作函数
   * @param {string} operationName - 操作名称
   * @returns {Promise} 执行结果
   */
  async executeWithRetry(operation, operationName) {
    let lastError;
    
    for (let attempt = 1; attempt <= this.config.maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error;
        console.warn(`${operationName} 第${attempt}次尝试失败:`, error.message);
        
        if (attempt < this.config.maxRetries) {
          await this.delay(this.config.retryDelay * attempt);
        }
      }
    }
    
    throw lastError;
  }

  /**
   * 延迟函数 - 新增
   * @param {number} ms - 延迟毫秒数
   * @returns {Promise} 延迟Promise
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 计算诊断置信度 - 新增
   * @param {Array} results - 分析结果数组
   * @param {number} errorCount - 错误数量
   * @returns {number} 置信度分数
   */
  calculateDiagnosisConfidence(results, errorCount) {
    const totalModules = results.length;
    const successModules = totalModules - errorCount;
    const baseConfidence = successModules / totalModules;
    
    // 根据数据质量调整置信度
    return Math.max(0.1, Math.min(1.0, baseConfidence * 0.9 + 0.1));
  }

  /**
   * 更新性能指标 - 新增
   * @param {string} operation - 操作类型
   * @param {number} responseTime - 响应时间
   */
  updateMetrics(operation, responseTime) {
    if (!this.config.enableMetrics) return;
    
    this.metrics.totalDiagnosis++;
    
    switch (operation) {
      case 'success':
        this.metrics.successfulDiagnosis++;
        break;
      case 'error':
        this.metrics.failedDiagnosis++;
        break;
      case 'cache_hit':
        // 缓存命中率计算在其他地方处理
        break;
    }
    
    // 更新平均响应时间
    this.metrics.avgResponseTime = (this.metrics.avgResponseTime + responseTime) / 2;
    
    // 计算缓存命中率
    const cacheHits = this.metrics.totalDiagnosis - this.metrics.successfulDiagnosis - this.metrics.failedDiagnosis;
    this.metrics.cacheHitRate = cacheHits / this.metrics.totalDiagnosis;
  }

  /**
   * 记录指标日志 - 新增
   * @param {string} event - 事件类型
   * @param {Object} data - 数据
   */
  logMetrics(event, data) {
    if (this.config.enableDetailedLogging) {
      console.log(`[METRICS] ${event}:`, {
        ...data,
        metrics: this.metrics,
        timestamp: new Date().toISOString()
      });
    }
  }



  /**
   * 薄弱点识别
   * @param {Object} data - 分析数据
   * @returns {Object} 薄弱点分析结果
   */
  async identifyWeaknesses(data) {
    this.ensureInitialized();
    
    const { testResults, learningHistory } = data;
    const weaknesses = [];
    const threshold = 0.7; // 掌握度阈值

    // 分析测试结果找出薄弱知识点
    for (const [nodeId, score] of Object.entries(testResults)) {
      if (score < threshold) {
        weaknesses.push({
          nodeId,
          currentScore: score,
          severity: this.calculateWeaknessSeverity(score),
          relatedNodes: this.findRelatedNodes(nodeId),
          recommendedActions: this.getWeaknessRecommendations(nodeId, score)
        });
      }
    }

    return {
      weaknessPoints: weaknesses,
      overallWeaknessLevel: this.calculateOverallWeakness(weaknesses),
      priorityOrder: this.prioritizeWeaknesses(weaknesses),
      improvementPlan: this.generateImprovementPlan(weaknesses)
    };
  }

  /**
   * 根本原因分析
   * @param {Array} weaknesses - 薄弱点列表
   * @returns {Object} 根本原因分析结果
   */
  async analyzeRootCauses(weaknesses) {
    const causes = [];
    
    for (const weakness of weaknesses.weaknessPoints || []) {
      // 分析前置知识点掌握情况
      const prerequisites = this.getPrerequisites(weakness.nodeId);
      const prerequisiteIssues = prerequisites.filter(prereq => 
        this.getNodeScore(prereq) < 0.8
      );
      
      if (prerequisiteIssues.length > 0) {
        causes.push({
          type: 'prerequisite_gap',
          weakness: weakness.nodeId,
          rootCause: prerequisiteIssues,
          priority: 'high'
        });
      }
      
      // 分析学习方法问题
      if (weakness.currentScore < 0.5) {
        causes.push({
          type: 'learning_method',
          weakness: weakness.nodeId,
          rootCause: '学习方法可能不适合',
          priority: 'medium'
        });
      }
    }
    
    return {
      identifiedCauses: causes,
      primaryCause: this.identifyPrimaryCause(causes),
      secondaryCauses: this.identifySecondaryCauses(causes)
    };
  }

  /**
   * 生成薄弱点改进建议
   * @param {Array} weaknesses - 薄弱点列表
   * @returns {Array} 改进建议
   */
  async generateWeaknessRecommendations(weaknesses) {
    const recommendations = [];
    
    for (const weakness of weaknesses.weaknessPoints || []) {
      recommendations.push({
        nodeId: weakness.nodeId,
        currentScore: weakness.currentScore,
        targetScore: 0.8,
        actions: [
          {
            type: 'review',
            description: `复习 ${this.getNodeName(weakness.nodeId)} 相关概念`,
            estimatedTime: '30分钟',
            priority: 'high'
          },
          {
            type: 'practice',
            description: `针对性练习 ${this.getNodeName(weakness.nodeId)} 题目`,
            estimatedTime: '45分钟',
            priority: 'high'
          },
          {
            type: 'prerequisite_check',
            description: '检查前置知识点掌握情况',
            estimatedTime: '20分钟',
            priority: 'medium'
          }
        ]
      });
    }
    
    return recommendations;
  }

  /**
   * 生成最优学习路径
   * @param {Object} options - 路径生成选项
   * @returns {Object} 学习路径
   */
  async generateOptimalLearningPath(options) {
    this.ensureInitialized();
    
    const { currentLevel, targetLevel, learningPreferences, timeConstraints } = options;
    
    return await this.modules.pathGenerator.generatePersonalizedPath(
      { studentId: 'default', gradeLevel: currentLevel || 'grade6' },
      { knowledgePoints: {}, strengths: [], weaknesses: [] },
      targetLevel ? [targetLevel] : [],
      { learningSpeed: 'normal', ...learningPreferences, timeConstraints }
    );
  }

  /**
   * 学习进度评估
   * @param {Object} data - 评估数据
   * @returns {Object} 进度评估结果
   */
  async evaluateProgress(data) {
    this.ensureInitialized();
    
    const { startDate, endDate, learningActivities } = data;
    
    return {
      overallProgress: this.calculateProgressPercentage(learningActivities),
      progressRate: this.calculateProgressRate(startDate, endDate, learningActivities),
      milestones: this.identifyMilestones(learningActivities),
      areas: {
        improved: this.getImprovedAreas(learningActivities),
        stagnant: this.getStagnantAreas(learningActivities),
        declined: this.getDeclinedAreas(learningActivities)
      },
      nextTargets: this.suggestNextTargets(learningActivities)
    };
  }

  /**
   * AI增强诊断分析
   * @param {Object} studentData - 学生数据
   * @param {Object} learningData - 学习数据
   * @param {Object} testResults - 测试结果
   * @param {Object} behaviorData - 行为数据
   * @returns {Object} AI分析结果
   */
  async performAIEnhancedAnalysis(studentData, learningData, testResults, behaviorData) {
    this.ensureInitialized();
    
    return await this.modules.aiAnalyzer.enhancedAnalysis(
      studentData, learningData, testResults, behaviorData
    );
  }

  // ==================== 辅助方法 ====================

  /**
   * 确保引擎已初始化
   */
  ensureInitialized() {
    if (!this.initialized) {
      throw new Error('诊断引擎未初始化，请先调用 initialize() 方法');
    }
  }

  /**
   * 初始化知识图谱
   */
  initializeKnowledgeGraph() {
    // 这里应该从数据库或配置文件加载真实的知识图谱
    return {
      nodes: new Map(),
      edges: new Map(),
      // 模拟一些基础节点
      getNode: (id) => ({ id, name: `知识点_${id}`, level: 1 }),
      getRelatedNodes: (id) => [`${id}_related1`, `${id}_related2`],
      getPrerequisites: (id) => [`${id}_pre1`, `${id}_pre2`]
    };
  }

  /**
   * 生成缓存键
   */
  generateCacheKey(type, ...params) {
    return `${type}_${params.join('_')}_${Date.now()}`;
  }

  /**
   * 设置缓存
   */
  setCache(key, value) {
    if (this.cache.size >= this.config.maxCacheSize) {
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }
    
    this.cache.set(key, {
      data: value,
      timestamp: Date.now()
    });
    
    // 设置过期清理
    setTimeout(() => {
      this.cache.delete(key);
    }, this.config.cacheTimeout);
  }

  /**
   * 计算当前水平
   */
  calculateCurrentLevel(testResults) {
    const scores = Object.values(testResults);
    return scores.reduce((sum, score) => sum + score, 0) / scores.length;
  }

  /**
   * 提取学习偏好
   */
  extractLearningPreferences(behaviorData) {
    return {
      learningStyle: 'visual', // 从行为数据推断
      pace: 'normal',
      difficultyPreference: 'moderate'
    };
  }

  /**
   * 计算综合分数
   */
  calculateOverallScore(aiAnalysis, outcomePrediction) {
    return {
      overall: 0.75,
      cognitive: 0.8,
      behavioral: 0.7,
      predictive: 0.72
    };
  }

  /**
   * 生成总体建议
   */
  generateOverallRecommendations(aiAnalysis, weaknessAnalysis) {
    return [
      {
        type: 'immediate',
        priority: 'high',
        description: '加强薄弱知识点练习',
        timeframe: '1-2周'
      },
      {
        type: 'medium_term',
        priority: 'medium', 
        description: '建立知识点间的连接',
        timeframe: '1个月'
      }
    ];
  }

  /**
   * 生成下一步行动
   */
  generateNextSteps(pathRecommendation) {
    return [
      {
        step: 1,
        action: '复习基础概念',
        estimatedTime: '30分钟',
        resources: ['视频教程', '练习题']
      },
      {
        step: 2,
        action: '完成针对性练习',
        estimatedTime: '45分钟',
        resources: ['练习册', '在线测试']
      }
    ];
  }

  // 其他辅助方法的简化实现
  calculateWeaknessSeverity(score) { return score < 0.5 ? 'high' : 'medium'; }
  findRelatedNodes(nodeId) { return this.knowledgeGraph.getRelatedNodes(nodeId); }
  getWeaknessRecommendations(nodeId, score) { return [`针对 ${nodeId} 的改进建议`]; }
  calculateOverallWeakness(weaknesses) { return weaknesses.length > 3 ? 'high' : 'medium'; }
  prioritizeWeaknesses(weaknesses) { return weaknesses.sort((a, b) => a.currentScore - b.currentScore); }
  generateImprovementPlan(weaknesses) { return { phases: ['基础强化', '综合提升'] }; }
  getPrerequisites(nodeId) { return this.knowledgeGraph.getPrerequisites(nodeId); }
  getNodeScore(nodeId) { return Math.random() * 0.5 + 0.5; }
  identifyPrimaryCause(causes) { return causes[0] || null; }
  identifySecondaryCauses(causes) { return causes.slice(1); }
  getNodeName(nodeId) { return this.knowledgeGraph.getNode(nodeId)?.name || nodeId; }
  calculateProgressPercentage(activities) { return 0.65; }
  calculateProgressRate(start, end, activities) { return 0.1; }
  identifyMilestones(activities) { return ['完成基础学习', '通过中级测试']; }
  getImprovedAreas(activities) { return ['计算能力']; }
  getStagnantAreas(activities) { return ['几何理解']; }
  getDeclinedAreas(activities) { return []; }
  suggestNextTargets(activities) { return ['代数应用', '函数概念']; }
}

module.exports = DiagnosisEngine; 