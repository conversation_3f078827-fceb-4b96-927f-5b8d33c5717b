-- ===================================================================
-- 人民教育出版社 九年级数学下册 第29章：投影与视图 - 知识点内容详情
-- 严格按照节点定义编写，共21个知识点
-- ===================================================================

INSERT INTO knowledge_content_details (
    node_id, description, detailed_explanation, key_points, formulas,
    examples, concepts_breakdown, common_mistakes, learning_tips,
    liberal_arts_content, science_content,
    content_version, is_current, language_code,
    content_quality_score, review_status
) VALUES

-- MATH_G9S2_CH29_001: 投影的概念
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S2_CH29_001'),
'投影是一种将空间图形映射到平面上的几何变换方法',
'投影是几何学中的重要概念，它描述了在光线照射下，空间中的点、线、面在某个平面上形成影像的过程。投影现象在日常生活中随处可见，如阳光下物体的影子、灯光照射产生的投影等。数学上的投影概念从这些自然现象中抽象而来，为描述三维空间与二维平面之间的关系提供了重要工具。投影由三个基本要素构成：投影中心（光源）、被投影的图形、投影面。根据投影线的性质，投影可分为平行投影和中心投影两大类。掌握投影概念是学习工程制图、建筑设计、计算机图形学的基础，也是培养空间想象能力的重要途径。',
'[
  "是空间图形到平面的映射变换",
  "来源于日常生活中的影子现象", 
  "由投影中心、被投影图形、投影面三要素构成",
  "分为平行投影和中心投影两大类",
  "是工程制图和空间想象的基础"
]',
'[
  {
    "name": "投影基本定义",
    "formula": "投影 = 光线照射下物体在平面上的影像",
    "description": "投影的数学定义"
  },
  {
    "name": "投影三要素",
    "formula": "投影中心 + 被投影图形 + 投影面",
    "description": "构成投影的基本要素"
  },
  {
    "name": "投影分类",
    "formula": "投影 = 平行投影 ∪ 中心投影",
    "description": "投影的基本分类"
  }
]',
'[
  {
    "title": "观察日常投影现象",
    "problem": "在阳光下，一根垂直于地面的木棒产生影子，分析这个投影现象的构成要素",
    "solution": "投影中心：太阳（可视为无穷远处的点光源）；被投影图形：木棒；投影面：地面；投影结果：木棒在地面上的影子。这是平行投影的典型例子。",
    "analysis": "通过日常现象理解投影的基本构成和分类"
  }
]',
'[
  {
    "concept": "空间映射",
    "explanation": "三维空间到二维平面的映射关系",
    "example": "立体物体在平面上的影像表示"
  },
  {
    "concept": "光学原理", 
    "explanation": "光线传播形成投影的物理原理",
    "example": "光源、物体、投影面的几何关系"
  },
  {
    "concept": "几何变换",
    "explanation": "投影作为几何变换的数学本质",
    "example": "保持某些性质、改变某些性质的变换"
  }
]',
'[
  "混淆投影与图形本身的区别",
  "不理解投影三要素的作用",
  "无法区分不同类型的投影",
  "忽视投影与光学的联系"
]',
'[
  "观察体验法：通过观察生活中的投影现象加深理解",
  "要素分析法：明确投影三要素在投影形成中的作用",
  "分类比较法：通过比较理解不同投影类型的特点",
  "原理联系法：建立投影与光学原理的联系"
]',
'{
  "emphasis": ["生活应用", "视觉艺术"],
  "application": ["摄影艺术", "舞台灯光设计"],
  "connection": ["艺术创作", "视觉表达"]
}',
'{
  "emphasis": ["几何原理", "数学抽象"],
  "application": ["几何学研究", "数学建模"],
  "connection": ["几何学", "变换理论"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G9S2_CH29_002: 平行投影
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S2_CH29_002'),
'平行投影是投影线互相平行的投影方式，广泛应用于工程制图',
'平行投影是一种重要的投影类型，其特征是所有投影线都互相平行。可以想象光源位于无穷远处，使得所有光线都平行射向投影面。平行投影具有重要的几何性质：能较好地保持图形的比例关系，平行线在投影后仍保持平行，适合精确的工程技术表达。平行投影在工程制图中应用极为广泛，是技术图纸的标准表达方式。根据投影线与投影面的关系，平行投影又分为正投影（投影线垂直于投影面）和斜投影（投影线倾斜于投影面）。掌握平行投影对理解工程图纸、建筑设计图具有重要意义。',
'[
  "投影线互相平行的投影方式",
  "能较好保持图形的比例关系",
  "在工程制图中应用极为广泛",
  "分为正投影和斜投影两种",
  "是技术图纸的标准表达方式"
]',
E'[
  {
    "name": "平行投影定义",
    "formula": "投影线 ∥ 投影线（所有投影线平行）",
    "description": "平行投影的基本特征"
  },
  {
    "name": "平行性保持",
    "formula": "若 a ∥ b，则 a\' ∥ b\'（投影保持平行性）",
    "description": "平行投影的重要性质"
  },
  {
    "name": "正投影条件", 
    "formula": "投影线 ⊥ 投影面",
    "description": "正投影的特殊条件"
  }
]',
'[
  {
    "title": "分析建筑平面图的投影原理",
    "problem": "建筑师绘制房屋平面图时采用什么投影方式？为什么？",
    "solution": "建筑平面图采用正投影方式（平行投影的特例）。原因：①能真实反映房屋的平面尺寸和比例②保持平行线的平行性，便于标注尺寸③垂直投影消除变形，确保测量精度④符合工程制图标准，便于施工使用。",
    "analysis": "平行投影在建筑工程中的应用体现了其保持几何性质的重要价值"
  }
]',
'[
  {
    "concept": "平行性保持",
    "explanation": "平行投影保持原图形平行关系的性质",
    "example": "建筑图纸中平行墙面的表达"
  },
  {
    "concept": "比例保持",
    "explanation": "平行于投影面的图形保持原比例",
    "example": "工程图纸的尺寸标注准确性"
  },
  {
    "concept": "工程标准",
    "explanation": "平行投影作为工程制图的标准方法",
    "example": "国际通用的技术图纸表达规范"
  }
]',
'[
  "不理解平行投影的几何条件",
  "混淆平行投影与中心投影",
  "忽视平行投影的应用价值",
  "不能正确分析投影结果"
]',
'[
  "条件理解法：准确掌握平行投影的几何条件",
  "性质归纳法：系统总结平行投影的重要性质", 
  "应用分析法：通过工程实例理解应用价值",
  "对比学习法：与中心投影对比加深理解"
]',
'{
  "emphasis": ["工程制图", "技术标准"],
  "application": ["建筑工程图", "机械制图"],
  "connection": ["工程技术", "制造业"]
}',
'{
  "emphasis": ["几何性质", "数学严谨性"],
  "application": ["几何学研究", "投影理论"],
  "connection": ["几何学", "工程数学"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'),

-- MATH_G9S2_CH29_003: 中心投影
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S2_CH29_003'),
'中心投影是投影线汇聚于一点的投影方式，产生透视效果',
'中心投影是另一种重要的投影类型，其特点是所有投影线都汇聚于一个点——投影中心。中心投影更接近人眼观察物体的方式，也是照相机成像和绘画透视的基本原理。在中心投影中，距离投影中心近的物体投影较大，距离远的物体投影较小，产生"近大远小"的透视效果。中心投影虽然不能保持平行性和长度比例，但能产生强烈的立体感和真实感，在艺术创作、摄影、影视制作、游戏开发等领域有重要应用。理解中心投影有助于培养观察能力和艺术欣赏能力。',
'[
  "投影线汇聚于投影中心的投影方式",
  "更接近人眼观察物体的方式",
  "产生近大远小的透视效果",
  "在艺术和摄影中应用广泛",
  "能产生强烈的立体感和真实感"
]',
'[
  {
    "name": "中心投影定义",
    "formula": "所有投影线汇聚于投影中心O",
    "description": "中心投影的基本特征"
  },
  {
    "name": "透视效果",
    "formula": "投影大小 ∝ 1/距离（近大远小）",
    "description": "中心投影的透视规律"
  },
  {
    "name": "投影中心",
    "formula": "O为所有投影线的汇聚点",
    "description": "中心投影的关键要素"
  }
]',
'[
  {
    "title": "分析透视摄影的投影原理",
    "problem": "摄影师拍摄一条笔直的公路，为什么在照片中公路会呈现汇聚的效果？",
    "solution": "这是中心投影产生的透视效果。相机镜头相当于投影中心，感光器件相当于投影面。虽然公路两侧是平行的，但在中心投影下：①近处的公路宽度投影较大②远处的公路宽度投影较小③视觉上形成向远方汇聚的效果。这种\"近大远小\"的透视现象是中心投影的典型特征，符合人眼的视觉规律。",
    "analysis": "通过摄影实例理解中心投影的透视原理和视觉效果"
  }
]',
'[
  {
    "concept": "透视原理",
    "explanation": "中心投影产生透视效果的几何原理",
    "example": "绘画中的一点透视、两点透视"
  },
  {
    "concept": "视觉真实性",
    "explanation": "中心投影符合人眼视觉规律",
    "example": "摄影成像、人眼观察的相似性"
  },
  {
    "concept": "艺术表现力",
    "explanation": "中心投影在艺术创作中的表现力",
    "example": "绘画、摄影、影视中的透视效果"
  }
]',
'[
  "不理解投影中心的作用",
  "混淆透视效果与实际大小",
  "不能正确分析中心投影结果",
  "忽视中心投影的应用领域"
]',
'[
  "中心理解法：深入理解投影中心的作用机制",
  "透视分析法：通过透视现象理解投影规律",
  "实例观察法：通过摄影等实例加深理解",
  "艺术联系法：结合艺术作品理解表现力"
]',
'{
  "emphasis": ["艺术创作", "视觉表现"],
  "application": ["绘画艺术", "摄影技术"],
  "connection": ["艺术美学", "视觉文化"]
}',
'{
  "emphasis": ["几何原理", "光学理论"],
  "application": ["光学研究", "几何分析"],
  "connection": ["光学", "几何学"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'),

-- MATH_G9S2_CH29_004: 平行投影的性质
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S2_CH29_004'),
'平行投影具有保持平行性、共线性等重要几何性质',
'平行投影具有一系列重要的几何性质，这些性质使其在工程技术中具有重要应用价值。主要性质包括：①平行性保持：平行线的投影仍为平行线（或重合）②共线性保持：共线点的投影仍共线③比例保持：平行于投影面的线段长度比例保持不变④简比性保持：平行于投影面的图形保持相似性。这些性质使得平行投影能够较为真实地反映物体的几何特征，特别适合需要精确测量和标注的工程图纸。理解这些性质有助于正确读图和绘图，是掌握工程制图的理论基础。',
'[
  "保持平行线的平行性质",
  "保持点的共线性关系", 
  "保持平行于投影面的长度比例",
  "保持平行于投影面图形的相似性",
  "适合精确测量和工程标注"
]',
E'[
  {
    "name": "平行性保持定理",
    "formula": "若 AB ∥ CD，则 A\'B\' ∥ C\'D\' 或重合",
    "description": "平行投影保持平行性的数学表达"
  },
  {
    "name": "比例保持定理",
    "formula": "AB ∥ 投影面 ⟹ |A\'B\'| = |AB|",
    "description": "平行于投影面的线段保持长度"
  },
  {
    "name": "共线性保持定理",
    "formula": "A、B、C共线 ⟹ A\'、B\'、C\'共线",
    "description": "共线点的投影保持共线性"
  }
]',
'[
  {
    "title": "工程图纸中的平行投影性质应用",
    "problem": "在建筑平面图中，为什么能直接在图纸上测量房间的长度和宽度？",
    "solution": "因为建筑平面图采用正投影（平行投影的特例），房屋平面平行于投影面。根据平行投影的比例保持性质：①平行于投影面的线段长度保持不变②图纸按比例缩小，但比例关系保持③平行线在投影中仍保持平行④可以直接在图纸上按比例测量实际尺寸。这就是工程图纸能够指导施工的理论基础。",
    "analysis": "平行投影性质在工程实践中的重要应用价值"
  }
]',
'[
  {
    "concept": "几何不变性",
    "explanation": "平行投影保持某些几何性质不变",
    "example": "平行性、共线性、比例关系的保持"
  },
  {
    "concept": "测量准确性",
    "explanation": "平行投影支持精确测量的数学基础",
    "example": "工程图纸的尺寸标注和测量"
  },
  {
    "concept": "工程实用性",
    "explanation": "平行投影性质的工程应用价值",
    "example": "建筑设计、机械制图的理论基础"
  }
]',
'[
  "不理解性质的几何意义",
  "混淆不同性质的适用条件",
  "不能应用性质分析实际问题",
  "忽视性质与工程应用的联系"
]',
'[
  "性质理解法：深入理解各性质的几何意义",
  "条件分析法：明确各性质的适用条件",
  "应用练习法：通过实例练习性质应用", 
  "联系实际法：结合工程实例理解价值"
]',
'{
  "emphasis": ["工程精度", "技术标准"],
  "application": ["工程测量", "制图标准"],
  "connection": ["工程技术", "质量控制"]
}',
'{
  "emphasis": ["几何理论", "数学严谨性"],
  "application": ["几何证明", "理论分析"],
  "connection": ["几何学", "数学原理"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G9S2_CH29_005: 中心投影的性质
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S2_CH29_005'),
'中心投影具有透视变换、不保持平行性等特殊性质',
'中心投影具有与平行投影不同的几何性质，这些性质决定了其独特的应用领域。主要性质包括：①不保持平行性：除过投影中心的直线外，平行线的投影一般会相交②保持共线性：共线点的投影仍共线③不保持长度比例：投影长度与距离投影中心的远近有关④透视变换：产生近大远小的视觉效果⑤交比不变性：交比是中心投影的不变量。这些性质使得中心投影虽然不适合精确测量，但能产生强烈的立体感和真实感，特别适合艺术表现和视觉传达。',
'[
  "不保持平行线的平行性（一般相交）",
  "保持点的共线性关系",
  "不保持长度比例关系",
  "产生近大远小的透视效果",
  "交比在中心投影下保持不变"
]',
E'[
  {
    "name": "平行性变换",
    "formula": "AB ∥ CD ⟹ A\'B\' ∩ C\'D\' = P（一般情况）",
    "description": "中心投影一般不保持平行性"
  },
  {
    "name": "透视比例",
    "formula": "|A\'B\'|/|AB| = d/(d+Δd)（距离相关）",
    "description": "投影大小与距离投影中心远近相关"
  },
  {
    "name": "交比不变性",
    "formula": "(A,B;C,D) = (A\',B\';C\',D\')",
    "description": "交比是中心投影的重要不变量"
  }
]',
E'[
  {
    "title": "透视绘画中的平行线处理",
    "problem": "在透视绘画中，画家如何表现铁轨延伸到远方的效果？",
    "solution": "利用中心投影的性质：①铁轨两条边线本来平行②在中心投影（透视）下，平行线会汇聚于一个消失点③近处铁轨间距离投影较大④远处铁轨间距离投影较小⑤视觉上形成向远方汇聚的效果。这种处理方法基于中心投影\'不保持平行性\'的性质，创造了深度感和空间感。",
    "analysis": "中心投影性质在艺术创作中的巧妙应用"
  }
]',
'[
  {
    "concept": "透视变换特性",
    "explanation": "中心投影产生的透视变换规律",
    "example": "近大远小的视觉效果机制"
  },
  {
    "concept": "几何性质变化",
    "explanation": "中心投影改变某些几何性质的规律",
    "example": "平行性丧失、比例关系改变"
  },
  {
    "concept": "艺术表现优势",
    "explanation": "中心投影在艺术表现中的独特优势",
    "example": "立体感、空间感、真实感的创造"
  }
]',
'[
  "不理解透视变换的原理",
  "混淆中心投影与平行投影的性质",
  "不能正确应用性质分析问题",
  "忽视性质在艺术中的应用"
]',
'[
  "透视理解法：深入理解透视变换的原理",
  "对比分析法：与平行投影性质对比学习",
  "艺术联系法：结合艺术实例理解应用",
  "性质应用法：练习性质在实际中的应用"
]',
'{
  "emphasis": ["艺术表现", "视觉效果"],
  "application": ["绘画技法", "设计表现"],
  "connection": ["视觉艺术", "美学原理"]
}',
'{
  "emphasis": ["几何变换", "投影理论"],
  "application": ["投影几何", "变换分析"],
  "connection": ["射影几何", "变换群论"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G9S2_CH29_006: 正投影  
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S2_CH29_006'),
'正投影是投影线垂直于投影面的特殊平行投影，是工程制图的基础',
'正投影是平行投影的重要特例，其特征是投影线垂直于投影面。正投影在工程制图中占据核心地位，因为它能最真实地反映物体在特定方向上的形状和尺寸。正投影具有重要性质：①形状保持：平行于投影面的图形投影后保持原形状和原大小②点投影：垂直于投影面的直线投影为一个点③准确测量：可以直接在投影图上测量实际尺寸。正投影是三视图系统的理论基础，通过在三个互相垂直的方向上进行正投影，可以完整表达三维物体的几何信息。掌握正投影原理是理解工程图纸的关键。',
'[
  "投影线垂直于投影面的特殊平行投影",
  "是工程制图中的核心投影方法",
  "能最真实反映物体的形状和尺寸",
  "是三视图系统的理论基础",
  "支持在投影图上直接测量尺寸"
]',
'[
  {
    "name": "正投影定义",
    "formula": "投影线 ⊥ 投影面",
    "description": "正投影的基本几何条件"
  },
  {
    "name": "形状保持定理",
    "formula": "图形 ∥ 投影面 ⟹ 投影保持原形状原大小",
    "description": "正投影的形状保持性质"
  },
  {
    "name": "点投影定理",
    "formula": "直线 ⊥ 投影面 ⟹ 投影为点",
    "description": "垂直线段的正投影特征"
  }
]',
'[
  {
    "title": "建筑立面图的正投影原理",
    "problem": "建筑师绘制建筑立面图时为什么要采用正投影方法？",
    "solution": "建筑立面图采用正投影的原因：①真实反映：正投影能真实反映建筑立面的形状、比例和尺寸②测量精确：可以直接在图纸上按比例测量实际尺寸③标注准确：便于标注门窗尺寸、层高等重要参数④施工指导：为施工提供准确的几何信息⑤标准规范：符合国际工程制图标准。正投影的垂直性质消除了斜投影可能产生的变形，确保了图纸的准确性。",
    "analysis": "正投影在建筑工程中的重要应用价值"
  }
]',
'[
  {
    "concept": "垂直投影几何",
    "explanation": "投影线与投影面垂直的几何关系",
    "example": "垂直投影产生的精确映射"
  },
  {
    "concept": "尺寸真实性",
    "explanation": "正投影保持尺寸真实性的原理",
    "example": "工程图纸的精确测量基础"
  },
  {
    "concept": "工程制图标准",
    "explanation": "正投影作为工程制图的国际标准",
    "example": "技术图纸的统一表达规范"
  }
]',
'[
  "不理解垂直投影的几何条件",
  "混淆正投影与一般平行投影",
  "不能正确判断正投影的结果",
  "忽视正投影在工程中的重要性"
]',
'[
  "几何理解法：深入理解垂直投影的几何原理",
  "性质掌握法：系统掌握正投影的重要性质",
  "应用分析法：通过工程实例理解应用价值",
  "标准学习法：学习工程制图的标准规范"
]',
'{
  "emphasis": ["工程制图精度", "技术标准化"],
  "application": ["建筑工程图", "机械制图"],
  "connection": ["工程技术", "制造业"]
}',
'{
  "emphasis": ["几何理论严谨性", "投影数学"],
  "application": ["几何学研究", "投影理论"],
  "connection": ["几何学", "工程数学"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'),

-- MATH_G9S2_CH29_007: 斜投影
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S2_CH29_007'),
'斜投影是投影线倾斜于投影面的平行投影，具有特殊的表现效果',
'斜投影是平行投影的另一种重要形式，其特征是投影线与投影面成一定角度倾斜。斜投影介于正投影和中心投影之间，既保持了平行投影的某些几何性质，又能产生一定的立体效果。斜投影的特点：①保持平行性：平行线的投影仍平行②产生变形：不平行于投影面的图形会发生变形③立体感：能表现物体的立体效果④应用灵活：可通过调整投影角度获得不同效果。斜投影在建筑表现图、产品设计展示、艺术创作等领域有重要应用，特别适合需要兼顾准确性和表现力的场合。',
'[
  "投影线倾斜于投影面的平行投影",
  "介于正投影和中心投影之间",
  "保持平行性但产生一定变形",
  "能产生一定的立体表现效果",
  "在建筑表现和设计展示中应用广泛"
]',
E'[
  {
    "name": "斜投影定义",
    "formula": "投影线与投影面成角度θ (0° < θ < 90°)",
    "description": "斜投影的基本几何条件"
  },
  {
    "name": "平行性保持",
    "formula": "AB ∥ CD ⟹ A\'B\' ∥ C\'D\'",
    "description": "斜投影保持平行性的性质"
  },
  {
    "name": "变形规律",
    "formula": "变形程度与投影角度和图形方向相关",
    "description": "斜投影的变形特征"
  }
]',
'[
  {
    "title": "建筑效果图的斜投影应用",
    "problem": "建筑师在制作建筑效果图时，为什么常常采用斜投影而不是正投影？",
    "solution": "建筑效果图采用斜投影的优势：①立体表现：斜投影能同时展现建筑的多个面，产生立体效果②空间感：通过适当的投影角度表现建筑的空间关系③视觉效果：比正投影更生动，比中心投影更准确④设计展示：便于展示建筑的整体设计理念⑤沟通效果：易于向非专业人士展示设计方案。斜投影在保持一定准确性的同时，增强了图形的表现力。",
    "analysis": "斜投影在建筑表现中平衡准确性与表现力的价值"
  }
]',
'[
  {
    "concept": "倾斜投影几何",
    "explanation": "投影线倾斜产生的几何效果",
    "example": "角度变化对投影结果的影响"
  },
  {
    "concept": "立体表现平衡",
    "explanation": "斜投影在准确性与表现力间的平衡",
    "example": "工程准确性与视觉效果的兼顾"
  },
  {
    "concept": "设计表现应用",
    "explanation": "斜投影在设计表现中的独特价值",
    "example": "建筑表现图、产品展示图的应用"
  }
]',
'[
  "不理解斜投影的几何原理",
  "混淆斜投影与其他投影类型",
  "不能正确控制斜投影的角度",
  "忽视斜投影的应用场合"
]',
'[
  "角度理解法：理解投影角度对结果的影响",
  "效果分析法：分析斜投影的表现效果",
  "应用比较法：比较不同投影方式的应用场合",
  "实践练习法：通过绘制练习掌握技巧"
]',
'{
  "emphasis": ["设计表现", "视觉效果"],
  "application": ["建筑表现图", "产品设计展示"],
  "connection": ["设计艺术", "视觉传达"]
}',
'{
  "emphasis": ["投影几何", "变换分析"],
  "application": ["几何研究", "投影理论"],
  "connection": ["几何学", "变换理论"]
}',
'1.0', true, 'zh-CN', 4.7, 'approved'),

-- MATH_G9S2_CH29_008: 三视图的概念
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S2_CH29_008'),
'三视图是用三个互相垂直的正投影表达立体物体的标准方法',
'三视图是工程制图中最重要的表达方法，通过在三个互相垂直的投影面上进行正投影，完整地表达三维物体的形状、大小和位置关系。三视图包括主视图（正面投影）、俯视图（水平投影）和左视图（侧面投影）。这三个视图相互关联，遵循"长对正、高平齐、宽相等"的投影规律。三视图系统基于正投影原理，能够准确、完整地传达物体的几何信息，是工程设计、制造加工、质量检验的重要依据。掌握三视图是理解工程图纸、培养空间思维能力的关键，也是从平面图形想象立体形状的重要桥梁。',
'[
  "用三个互相垂直的正投影表达立体物体",
  "包括主视图、俯视图和左视图三个基本视图",
  "遵循长对正、高平齐、宽相等的投影规律",
  "是工程制图中最重要的表达方法",
  "能够准确完整地传达物体的几何信息"
]',
'[
  {
    "name": "三视图组成",
    "formula": "三视图 = 主视图 + 俯视图 + 左视图",
    "description": "三视图的基本组成"
  },
  {
    "name": "投影关系",
    "formula": "长对正：主视图与俯视图长度对应",
    "description": "主视图与俯视图的投影关系"
  },
  {
    "name": "高度关系",
    "formula": "高平齐：主视图与左视图高度相等",
    "description": "主视图与左视图的投影关系"
  },
  {
    "name": "宽度关系",
    "formula": "宽相等：俯视图与左视图宽度相等",
    "description": "俯视图与左视图的投影关系"
  }
]',
'[
  {
    "title": "手机设计的三视图表达",
    "problem": "设计师如何用三视图完整表达一款手机的外观设计？",
    "solution": "手机的三视图表达：①主视图：显示手机正面，包括屏幕、按键、接口的位置和大小②俯视图：显示手机顶部，展现摄像头、扬声器等顶部元素的布局③左视图：显示手机侧面，体现厚度、按键、接口的侧面特征。三个视图遵循投影规律：长对正（主视图与俯视图的长度对应），高平齐（主视图与左视图的高度相等），宽相等（俯视图与左视图的宽度相等）。通过三视图，制造商可以准确理解设计意图，进行精确加工。",
    "analysis": "三视图在现代产品设计中完整表达设计信息的重要作用"
  }
]',
'[
  {
    "concept": "正投影系统",
    "explanation": "三视图基于正投影的系统性表达方法",
    "example": "三个互相垂直投影面的协调使用"
  },
  {
    "concept": "投影规律",
    "explanation": "三视图间的几何对应关系",
    "example": "长对正、高平齐、宽相等的具体体现"
  },
  {
    "concept": "工程表达标准",
    "explanation": "三视图作为工程表达的国际标准",
    "example": "技术图纸的统一表达规范"
  }
]',
'[
  "不理解三视图的投影原理",
  "混淆三个视图的对应关系",
  "不能正确应用投影规律",
  "忽视三视图的系统性特征"
]',
'[
  "系统理解法：理解三视图作为系统的整体性",
  "规律掌握法：熟练掌握三视图的投影规律",
  "实物对照法：通过实物模型理解投影关系",
  "应用练习法：通过绘制练习巩固理解"
]',
'{
  "emphasis": ["工程制图标准", "设计表达"],
  "application": ["产品设计图纸", "工程技术图"],
  "connection": ["工业设计", "制造业"]
}',
'{
  "emphasis": ["投影几何", "空间关系"],
  "application": ["几何学研究", "空间分析"],
  "connection": ["立体几何", "工程数学"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'),

-- MATH_G9S2_CH29_009: 主视图
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S2_CH29_009'),
'主视图是观察者正对物体主要特征方向得到的正投影视图',
'主视图是三视图中最重要的视图，它反映物体正面的形状特征，通常选择能最好体现物体主要特征和功能的方向作为主视方向。主视图的投影面称为正面投影面（V面），投影方向垂直于V面。主视图能够反映物体的高度和长度信息，但不能直接反映宽度信息。在工程制图中，主视图通常放置在图纸的左上方或中央位置，作为其他视图的基准。选择合适的主视方向对于清晰表达物体特征至关重要，应选择形状特征明显、功能结构清晰、便于加工测量的方向。',
'[
  "反映物体正面形状特征的正投影视图",
  "通常选择最能体现主要特征的观察方向",
  "反映物体的高度和长度信息",
  "是三视图系统的基准视图",
  "在工程制图中位置重要、作用关键"
]',
'[
  {
    "name": "主视图投影",
    "formula": "物体正面 → V面正投影 → 主视图",
    "description": "主视图的形成原理"
  },
  {
    "name": "尺寸反映",
    "formula": "主视图反映：高度H + 长度L",
    "description": "主视图反映的尺寸信息"
  },
  {
    "name": "观察方向",
    "formula": "观察方向 ⊥ 正面投影面V",
    "description": "主视图的观察方向"
  }
]',
'[
  {
    "title": "汽车主视图的方向选择",
    "problem": "汽车设计师为什么通常选择侧面作为汽车的主视方向？",
    "solution": "汽车选择侧面作为主视方向的原因：①特征明显：侧面能最好地体现汽车的整体轮廓和造型特征②功能清晰：能清楚显示车门、车窗、轮胎等主要功能部件③比例协调：侧面视角能完整展现汽车的长度和高度比例④识别性强：侧面轮廓是汽车最具识别性的视角⑤设计表达：便于表达汽车的设计理念和美学特征。这个选择使主视图能够最有效地传达汽车的设计信息。",
    "analysis": "主视方向选择在产品设计表达中的重要意义"
  }
]',
'[
  {
    "concept": "主要特征表达",
    "explanation": "主视图优先表达物体最重要特征",
    "example": "选择最具代表性的观察角度"
  },
  {
    "concept": "基准视图作用",
    "explanation": "主视图作为其他视图的参照基准",
    "example": "其他视图的位置和比例参考"
  },
  {
    "concept": "功能导向选择",
    "explanation": "主视方向选择以功能表达为导向",
    "example": "突出物体的主要功能特征"
  }
]',
'[
  "主视方向选择不当",
  "不理解主视图的基准作用",
  "混淆主视图与其他视图的区别",
  "忽视主视图的重要性"
]',
'[
  "特征分析法：分析物体主要特征选择主视方向",
  "功能导向法：以功能表达为导向选择视角",
  "基准理解法：理解主视图的基准作用",
  "实例分析法：通过实际产品分析主视图选择"
]',
'{
  "emphasis": ["设计表达", "产品特征"],
  "application": ["产品设计", "工业设计"],
  "connection": ["设计美学", "产品开发"]
}',
'{
  "emphasis": ["投影理论", "几何基准"],
  "application": ["几何分析", "投影研究"],
  "connection": ["立体几何", "工程几何"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G9S2_CH29_010: 左视图  
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S2_CH29_010'),
'左视图是从物体左侧观察得到的正投影视图，反映物体的侧面特征',
'左视图是三视图系统中的重要组成部分，它从物体的左侧方向进行观察，将观察到的形状投影到侧面投影面（W面）上。左视图主要反映物体的高度和宽度信息，补充主视图和俯视图无法表达的侧面结构特征。在工程制图中，左视图通常位于主视图的右侧，与主视图保持"高平齐"的投影关系。左视图对于理解物体的完整形状特别重要，特别是当物体在宽度方向有重要特征变化时。通过左视图，可以清楚地看到物体在侧面的轮廓、孔洞、凸起等结构特征。',
'[
  "从物体左侧观察得到的正投影视图",
  "反映物体的高度和宽度信息",
  "补充表达侧面结构特征",
  "与主视图保持高平齐的投影关系",
  "位于主视图右侧，完善物体表达"
]',
'[
  {
    "name": "左视图投影",
    "formula": "物体左侧 → W面正投影 → 左视图",
    "description": "左视图的形成原理"
  },
  {
    "name": "尺寸反映",
    "formula": "左视图反映：高度H + 宽度W",
    "description": "左视图反映的尺寸信息"
  },
  {
    "name": "高平齐关系",
    "formula": "左视图高度 = 主视图高度",
    "description": "左视图与主视图的投影关系"
  }
]',
'[
  {
    "title": "笔记本电脑的左视图分析",
    "problem": "笔记本电脑的左视图能够提供哪些主视图和俯视图无法表达的信息？",
    "solution": "笔记本电脑左视图的独特信息：①厚度特征：清楚显示笔记本合盖后的整体厚度②接口布局：展现左侧面USB、电源、音频等接口的位置③散热设计：显示左侧散热孔或散热槽的设计④结构细节：体现左侧面的线条、切角、标识等设计元素⑤开合角度：如果是展开状态，显示屏幕与键盘的角度关系。这些信息对于使用者了解产品功能、制造商进行生产都是必需的。",
    "analysis": "左视图在完整表达产品信息中的重要补充作用"
  }
]',
'[
  {
    "concept": "侧面特征表达",
    "explanation": "左视图专门表达物体侧面的特征信息",
    "example": "侧面轮廓、接口、结构的清晰展现"
  },
  {
    "concept": "宽度信息补充",
    "explanation": "左视图提供主视图缺失的宽度信息",
    "example": "物体在宽度方向的尺寸和变化"
  },
  {
    "concept": "投影协调性",
    "explanation": "左视图与其他视图的协调配合",
    "example": "高平齐关系保证信息的一致性"
  }
]',
'[
  "不理解左视图的观察方向",
  "混淆左视图与右视图",
  "忽视左视图与主视图的高度关系",
  "不能正确识别左视图的作用"
]',
'[
  "方向确定法：准确确定左视的观察方向",
  "关系理解法：理解左视图与其他视图的关系",
  "特征分析法：分析左视图表达的特殊信息",
  "实物对照法：通过实物观察理解左视图"
]',
'{
  "emphasis": ["产品细节", "功能接口"],
  "application": ["产品设计细节", "使用说明图"],
  "connection": ["用户体验", "产品设计"]
}',
'{
  "emphasis": ["空间关系", "几何补充"],
  "application": ["几何分析", "空间研究"],
  "connection": ["立体几何", "投影分析"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G9S2_CH29_011: 俯视图
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S2_CH29_011'),
'俯视图是从物体上方向下观察得到的正投影视图，反映物体的平面布局',
'俯视图是三视图中第三个重要视图，它从物体的上方垂直向下观察，将观察到的形状投影到水平投影面（H面）上。俯视图主要反映物体的长度和宽度信息，展现物体的平面布局、横断面特征和顶面结构。在工程制图中，俯视图通常位于主视图的下方，与主视图保持"长对正"的投影关系，与左视图保持"宽相等"的投影关系。俯视图对于理解物体的平面分布、内部结构、对称关系等特别重要，是建筑平面图、机械零件平面图的基础视图。',
'[
  "从物体上方向下观察的正投影视图",
  "反映物体的长度和宽度信息",
  "展现平面布局和横断面特征",
  "与主视图保持长对正的投影关系",
  "是建筑和机械平面图的基础"
]',
'[
  {
    "name": "俯视图投影",
    "formula": "物体顶面 → H面正投影 → 俯视图",
    "description": "俯视图的形成原理"
  },
  {
    "name": "尺寸反映",
    "formula": "俯视图反映：长度L + 宽度W",
    "description": "俯视图反映的尺寸信息"
  },
  {
    "name": "长对正关系",
    "formula": "俯视图长度 = 主视图长度",
    "description": "俯视图与主视图的投影关系"
  },
  {
    "name": "宽相等关系",
    "formula": "俯视图宽度 = 左视图宽度",
    "description": "俯视图与左视图的投影关系"
  }
]',
'[
  {
    "title": "智能手机俯视图的设计价值",
    "problem": "智能手机的俯视图在产品设计和用户使用中有什么重要作用？",
    "solution": "智能手机俯视图的重要作用：①布局设计：清楚显示摄像头、闪光灯、指纹识别等元素的平面布局②对称美学：体现产品设计的对称性和美学比例③功能分区：展现不同功能区域的平面分布和相互关系④制造指导：为生产制造提供精确的平面尺寸和定位信息⑤用户指导：帮助用户理解产品的平面特征和操作区域。俯视图结合主视图和左视图，完整表达了产品的三维特征。",
    "analysis": "俯视图在现代产品设计中平面信息表达的核心价值"
  }
]',
'[
  {
    "concept": "平面布局表达",
    "explanation": "俯视图专门表达物体的平面布局信息",
    "example": "元件分布、功能区域、对称关系"
  },
  {
    "concept": "横断面特征",
    "explanation": "俯视图反映物体在水平方向的断面特征",
    "example": "内部结构、孔洞分布、厚度变化"
  },
  {
    "concept": "投影完整性",
    "explanation": "俯视图完善三视图系统的完整性",
    "example": "提供长度和宽度的完整信息"
  }
]',
'[
  "不理解俯视的观察方向",
  "混淆俯视图与其他视图的关系",
  "不能正确分析俯视图的特征",
  "忽视俯视图的平面表达价值"
]',
'[
  "方向理解法：明确俯视的垂直向下观察方向",
  "关系掌握法：掌握俯视图与其他视图的投影关系",
  "平面分析法：重点分析俯视图的平面特征",
  "应用联系法：联系实际应用理解俯视图价值"
]',
'{
  "emphasis": ["平面设计", "布局规划"],
  "application": ["建筑平面图", "产品布局设计"],
  "connection": ["空间规划", "平面设计"]
}',
'{
  "emphasis": ["几何关系", "平面几何"],
  "application": ["几何分析", "平面研究"],
  "connection": ["平面几何", "空间分析"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G9S2_CH29_012: 画三视图
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S2_CH29_012'),
'画三视图是根据立体物体绘制三个标准正投影视图的技能方法',
'画三视图是工程制图的基本技能，需要遵循严格的投影原理和制图规范。绘制过程包括：①确定主视方向：选择最能表达物体特征的观察方向②建立投影关系：确保三视图间的"长对正、高平齐、宽相等"关系③逐一绘制：按照正投影原理分别绘制主视图、俯视图、左视图④检查校核：验证三视图的一致性和完整性。绘制三视图需要较强的空间想象能力和对投影原理的深刻理解。熟练掌握画三视图的方法，有助于提高空间思维能力，为学习工程制图奠定基础。',
'[
  "根据立体物体绘制三个正投影视图",
  "遵循长对正、高平齐、宽相等的投影关系",
  "需要确定合适的主视方向",
  "要求较强的空间想象能力",
  "是工程制图的基本技能要求"
]',
'[
  {
    "name": "投影关系原则",
    "formula": "长对正 ∧ 高平齐 ∧ 宽相等",
    "description": "三视图绘制的基本原则"
  },
  {
    "name": "主视方向确定",
    "formula": "选择最能体现物体特征的观察方向",
    "description": "主视图方向的选择原则"
  },
  {
    "name": "投影线垂直",
    "formula": "投影线 ⊥ 各投影面",
    "description": "正投影的基本条件"
  }
]',
'[
  {
    "title": "绘制简单几何体的三视图",
    "problem": "如何绘制一个圆锥的三视图？",
    "solution": "圆锥三视图的绘制步骤：①选择主视方向：选择圆锥轴线垂直于投影面的方向，使圆锥的特征最明显②绘制主视图：圆锥在主视图中呈等腰三角形③绘制俯视图：从上向下看，圆锥呈圆形，圆心标出④绘制左视图：与主视图相同，也呈等腰三角形⑤检查关系：确保主视图与俯视图长对正，主视图与左视图高平齐，俯视图与左视图宽相等。通过这样的绘制，完整表达了圆锥的几何特征。",
    "analysis": "通过具体实例理解三视图绘制的方法和要求"
  }
]',
'[
  {
    "concept": "投影原理应用",
    "explanation": "正投影原理在三视图绘制中的具体应用",
    "example": "每个视图都是严格的正投影结果"
  },
  {
    "concept": "空间关系表达",
    "explanation": "通过三视图完整表达物体的空间关系",
    "example": "三个视图协调配合表达立体信息"
  },
  {
    "concept": "技能方法训练",
    "explanation": "画三视图作为重要的技能训练方法",
    "example": "培养空间想象和制图能力"
  }
]',
'[
  "主视方向选择不当",
  "不能正确保持投影关系",
  "空间想象能力不足",
  "绘制过程缺乏系统性"
]',
'[
  "步骤化方法：按步骤系统地绘制三视图",
  "关系检查法：绘制过程中不断检查投影关系",
  "实物参照法：结合实物模型练习绘制",
  "反复练习法：通过大量练习提高熟练程度"
]',
'{
  "emphasis": ["制图技能", "实际应用"],
  "application": ["工程制图", "技术教育"],
  "connection": ["职业技能", "工程素养"]
}',
'{
  "emphasis": ["几何方法", "空间思维"],
  "application": ["几何训练", "思维发展"],
  "connection": ["空间几何", "数学思维"]
}',
'1.0', true, 'zh-CN', 4.7, 'approved'),

-- MATH_G9S2_CH29_013: 由三视图想象立体图形
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S2_CH29_013'),
'由三视图想象立体图形是根据三个平面视图重构三维物体的逆向思维能力',
'由三视图想象立体图形是空间思维的重要体现，要求学生能够从三个二维的投影视图中还原出三维物体的空间形状。这一过程需要综合运用投影原理、空间想象能力和逻辑推理能力。基本方法包括：①投影关系分析：确认三视图的投影关系是否正确②特征点识别：找出关键的点、线、面在各视图中的对应关系③空间重构：逐步在脑海中构建三维形状④验证检查：检查构建的立体图形是否符合所有视图。这种能力对于理解工程图纸、进行产品设计、发展空间智能都具有重要意义。',
'[
  "根据三个平面视图重构三维物体形状",
  "是空间思维和逆向思维的重要体现",
  "需要综合运用投影原理和空间想象",
  "要求识别特征点在各视图中的对应关系",
  "是理解工程图纸的关键能力"
]',
'[
  {
    "name": "投影逆变换",
    "formula": "三视图 → 空间想象 → 立体图形",
    "description": "从平面视图到立体形状的逆向过程"
  },
  {
    "name": "特征对应",
    "formula": "点、线、面在三视图中的对应关系",
    "description": "几何特征的多视图对应分析"
  },
  {
    "name": "空间重构",
    "formula": "主视图 ∩ 俯视图 ∩ 左视图 = 立体形状",
    "description": "三个视图信息的空间综合"
  }
]',
'[
  {
    "title": "从手机三视图想象实际产品",
    "problem": "给定一个手机的三视图，如何在脑海中构建出手机的立体形状？",
    "solution": "从手机三视图构建立体形状的方法：①分析主视图：理解手机的正面外观，屏幕、按键的位置和比例②分析俯视图：了解手机的平面形状，是矩形还是有圆角，摄像头的布局③分析左视图：确定手机的厚度和侧面特征④综合分析：将三个视图的信息在脑海中叠加，形成立体印象⑤细节完善：根据各视图的细节特征，完善立体形状的具体特征⑥验证检查：检查构建的立体形状是否与三个视图完全吻合。",
    "analysis": "通过现代产品实例理解空间重构的思维过程"
  }
]',
'[
  {
    "concept": "逆向空间思维",
    "explanation": "从二维信息还原三维形状的思维能力",
    "example": "平面图纸到立体物体的思维转换"
  },
  {
    "concept": "信息综合能力",
    "explanation": "综合多个视图信息形成统一认知",
    "example": "三个视图信息的协调整合"
  },
  {
    "concept": "空间智能发展",
    "explanation": "通过视图想象发展空间智能",
    "example": "空间认知能力的培养和提升"
  }
]',
'[
  "缺乏空间想象的基本方法",
  "不能正确分析投影关系",
  "无法综合多个视图的信息",
  "构建的立体形状与视图不符"
]',
'[
  "分步想象法：逐步从简单到复杂构建立体形状",
  "对应分析法：重点分析特征在各视图中的对应",
  "实物验证法：用实物模型验证想象结果",
  "反复练习法：通过大量练习提高想象能力"
]',
'{
  "emphasis": ["空间想象", "产品认知"],
  "application": ["产品理解", "空间教育"],
  "connection": ["认知发展", "智能培养"]
}',
'{
  "emphasis": ["空间思维", "逻辑推理"],
  "application": ["几何研究", "思维训练"],
  "connection": ["空间几何", "认知科学"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'),

-- MATH_G9S2_CH29_014: 简单几何体的三视图
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S2_CH29_014'),
'简单几何体的三视图是基本立体图形在三个投影面上的标准表达',
'简单几何体包括长方体、正方体、圆柱、圆锥、球体、棱锥、棱柱等基本立体图形。学习这些几何体的三视图有助于理解投影原理，培养空间想象能力，为学习复杂物体的三视图奠定基础。每种几何体的三视图都有其特征：长方体的三视图都是矩形；圆柱的主视图和左视图是矩形，俯视图是圆；圆锥的主视图和左视图是三角形，俯视图是圆；球体的三视图都是圆。掌握简单几何体的三视图规律，能够为理解复杂物体的投影表达打下坚实基础。',
'[
  "基本立体图形的标准三视图表达",
  "包括长方体、圆柱、圆锥、球体等几何体",
  "每种几何体都有特定的视图特征",
  "是学习复杂物体三视图的基础",
  "有助于理解投影原理和培养空间想象"
]',
'[
  {
    "name": "长方体三视图",
    "formula": "主视图：矩形，俯视图：矩形，左视图：矩形",
    "description": "长方体的三视图特征"
  },
  {
    "name": "圆柱三视图",
    "formula": "主视图：矩形，俯视图：圆，左视图：矩形",
    "description": "圆柱的三视图特征"
  },
  {
    "name": "圆锥三视图",
    "formula": "主视图：三角形，俯视图：圆，左视图：三角形",
    "description": "圆锥的三视图特征"
  },
  {
    "name": "球体三视图",
    "formula": "主视图：圆，俯视图：圆，左视图：圆",
    "description": "球体的三视图特征"
  }
]',
'[
  {
    "title": "分析水杯的几何构成和三视图",
    "problem": "一个普通的圆柱形水杯，其三视图有什么特征？如何体现圆柱的几何性质？",
    "solution": "圆柱形水杯的三视图特征：①主视图：呈现矩形形状，高度为杯子高度，宽度为杯子直径，体现了侧面观察的轮廓②俯视图：呈现圆形，半径为杯子的半径，如果有杯把会显示为圆外的附加形状③左视图：与主视图相同，也是矩形。这种三视图完美体现了圆柱的几何性质：②底面是圆形（俯视图）③侧面是矩形（主视图、左视图）③高度一致（主视图与左视图高度相等）。",
    "analysis": "通过日常用品理解简单几何体三视图的实际应用"
  }
]',
'[
  {
    "concept": "基础几何认知",
    "explanation": "通过三视图深化对基本几何体的认知",
    "example": "立体几何知识的投影表达"
  },
  {
    "concept": "投影规律掌握",
    "explanation": "掌握不同几何体的投影规律和特征",
    "example": "圆形、矩形、三角形在投影中的表现"
  },
  {
    "concept": "空间思维基础",
    "explanation": "简单几何体是空间思维训练的基础材料",
    "example": "从简单到复杂的认知发展路径"
  }
]',
'[
  "不理解几何体的基本性质",
  "混淆不同几何体的三视图特征",
  "不能正确分析投影结果",
  "缺乏几何体与三视图的对应能力"
]',
'[
  "分类学习法：按几何体类型分别学习三视图特征",
  "对比分析法：对比不同几何体三视图的异同",
  "实物观察法：通过实际模型观察理解投影",
  "规律总结法：总结各类几何体三视图的规律"
]',
'{
  "emphasis": ["基础教育", "几何认知"],
  "application": ["几何教学", "空间教育"],
  "connection": ["基础教育", "认知发展"]
}',
'{
  "emphasis": ["立体几何", "投影理论"],
  "application": ["几何学研究", "数学教育"],
  "connection": ["立体几何", "数学基础"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'),

-- MATH_G9S2_CH29_015: 组合体的三视图
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S2_CH29_015'),
'组合体的三视图是由多个简单几何体组合而成的复杂物体的投影表达',
'组合体是由两个或多个简单几何体通过叠加、切割、挖空等方式组合而成的复杂立体图形。组合体的三视图表达需要综合考虑各组成部分的投影效果，处理好遮挡关系、连接关系和整体关系。绘制组合体三视图的方法：①分析组成：识别组合体由哪些简单几何体组成②确定关系：分析各部分的相对位置和连接方式③逐一投影：将各部分的投影效果叠加④处理遮挡：正确表达可见线和不可见线⑤整体协调：确保三视图的投影关系正确。组合体三视图是工程实际中最常见的情况，掌握其绘制和识读方法具有重要的实用价值。',
'[
  "由多个简单几何体组合的复杂立体图形",
  "需要综合考虑各组成部分的投影效果",
  "要正确处理遮挡关系和连接关系",
  "是工程实际中最常见的表达情况",
  "需要较强的空间分析和综合能力"
]',
'[
  {
    "name": "组合体分析",
    "formula": "组合体 = 几何体₁ + 几何体₂ + ... + 几何体ₙ",
    "description": "组合体的构成分析"
  },
  {
    "name": "投影叠加",
    "formula": "组合体投影 = 各部分投影的叠加",
    "description": "组合体投影的形成原理"
  },
  {
    "name": "遮挡处理",
    "formula": "可见线：实线，不可见线：虚线",
    "description": "组合体投影中的遮挡关系表达"
  }
]',
'[
  {
    "title": "智能音箱的组合体三视图分析",
    "problem": "一个圆柱形底座加半球形顶部的智能音箱，如何分析其三视图特征？",
    "solution": "智能音箱组合体的三视图分析：①结构分析：底部圆柱 + 顶部半球的组合结构②主视图：下半部分为矩形（圆柱侧面），上半部分为半圆弧（半球轮廓）③俯视图：整体呈圆形，因为从上往下看是半球的圆形底面④左视图：与主视图相同，体现了轴对称特征⑤细节处理：连接处要平滑过渡，体现一体化设计。这种组合既保持了各部分的几何特征，又形成了协调的整体效果。",
    "analysis": "通过现代产品理解组合体三视图的复杂性和实用性"
  }
]',
'[
  {
    "concept": "复杂结构分析",
    "explanation": "将复杂物体分解为简单几何体组合",
    "example": "系统分析方法的应用"
  },
  {
    "concept": "投影综合处理",
    "explanation": "综合处理多个几何体的投影效果",
    "example": "空间关系的复杂表达"
  },
  {
    "concept": "工程实用技能",
    "explanation": "组合体三视图的工程实际应用价值",
    "example": "现代产品设计和制造的基础"
  }
]',
'[
  "不能正确分析组合体的构成",
  "无法处理复杂的遮挡关系",
  "组合体各部分投影关系混乱",
  "缺乏整体协调的概念"
]',
'[
  "分解分析法：将组合体分解为简单几何体",
  "逐步合成法：逐步将各部分投影合成整体",
  "关系理清法：理清各部分的空间关系",
  "实例练习法：通过实际产品练习分析"
]',
'{
  "emphasis": ["产品设计", "工程应用"],
  "application": ["工业设计", "产品开发"],
  "connection": ["现代制造", "设计工程"]
}',
'{
  "emphasis": ["复杂几何", "空间综合"],
  "application": ["几何综合", "空间分析"],
  "connection": ["立体几何", "系统分析"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G9S2_CH29_016: 阅读与思考：视图的产生与应用
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S2_CH29_016'),
'视图系统从古代绘画发展到现代工程制图的历史演进与广泛应用',
'视图的产生与发展体现了人类对空间表达需求的历史演进。古代工匠通过经验传承制作工艺品，文艺复兴时期画家发展了透视法，18世纪法国数学家蒙日创立了画法几何学，奠定了现代投影理论基础。现代视图系统广泛应用于：①工程制图：机械、建筑、电子等领域的技术图纸②产品设计：从概念设计到制造生产的全过程③数字化应用：CAD软件、3D建模、虚拟现实④教育培养：空间思维能力和工程素养的培养。视图系统的发展反映了人类认识空间、表达空间、改造空间能力的不断提升，是技术文明发展的重要标志。',
'[
  "从古代绘画到现代工程制图的历史演进",
  "体现人类对空间表达需求的发展",
  "蒙日画法几何学奠定了投影理论基础",
  "在现代工程和数字化中广泛应用",
  "是技术文明发展的重要标志"
]',
'[
  {
    "name": "历史发展脉络",
    "formula": "古代经验 → 透视法 → 画法几何 → 现代制图",
    "description": "视图系统的历史发展过程"
  },
  {
    "name": "应用领域拓展",
    "formula": "工程制图 + 产品设计 + 数字化应用 + 教育培养",
    "description": "现代视图系统的应用范围"
  },
  {
    "name": "技术文明标志",
    "formula": "空间认识 → 空间表达 → 空间改造",
    "description": "视图系统的文明意义"
  }
]',
'[
  {
    "title": "从古代建筑图到现代BIM技术",
    "problem": "中国古代建筑师如何传承建筑技艺？现代BIM技术如何革新建筑设计？",
    "solution": "古代与现代建筑表达的对比：①古代方式：通过木制模型、经验传承、口述记录传递建筑信息，精度有限但文化传承深厚②现代BIM：通过三维数字模型、精确投影图纸、参数化设计实现精确表达③技术进步：从经验性表达到科学化表达，从模糊传递到精确传递④应用价值：现代技术在保持文化传承的同时，大大提高了设计精度和施工效率。这种发展体现了人类空间表达能力的巨大进步。",
    "analysis": "通过古今对比理解视图技术发展的文明价值"
  }
]',
'[
  {
    "concept": "历史文化传承",
    "explanation": "视图技术承载的历史文化内涵",
    "example": "从古代工艺到现代技术的传承"
  },
  {
    "concept": "科学技术发展",
    "explanation": "视图系统的科学技术发展脉络",
    "example": "数学理论支撑的技术进步"
  },
  {
    "concept": "现代应用价值",
    "explanation": "视图技术在现代社会的重要应用",
    "example": "工程、设计、教育等领域的广泛应用"
  }
]',
'[
  "不了解视图技术的历史发展",
  "忽视视图系统的文化价值",
  "不能理解视图技术的现代意义",
  "缺乏对技术发展的整体认识"
]',
'[
  "历史学习法：了解视图技术的历史发展过程",
  "文化理解法：理解视图技术的文化内涵",
  "应用分析法：分析视图技术的现代应用价值",
  "发展展望法：展望视图技术的未来发展"
]',
'{
  "emphasis": ["文化传承", "技术发展"],
  "application": ["历史教育", "文化理解"],
  "connection": ["技术史", "文明发展"]
}',
'{
  "emphasis": ["数学发展", "理论基础"],
  "application": ["数学史", "理论研究"],
  "connection": ["数学发展", "科学进步"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G9S2_CH29_017: 课题学习：制作立体模型
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S2_CH29_017'),
'制作立体模型是通过动手实践深化投影与视图理解的综合学习活动',
'制作立体模型是投影与视图学习的重要实践环节，通过动手制作加深对空间关系和投影原理的理解。活动内容包括：①材料准备：选择合适的制作材料如纸板、泡沫、黏土等②设计规划：根据三视图设计制作方案③制作过程：按照投影关系制作立体模型④测量验证：测量模型尺寸验证投影关系⑤展示交流：展示作品并交流制作心得。这种学习方式能够：强化空间想象能力、验证投影理论、培养动手能力、增进合作交流。制作立体模型将抽象的投影理论转化为具体的实践体验，是理论联系实际的有效途径。',
'[
  "通过动手实践深化投影理论理解",
  "包括设计、制作、验证、展示等环节",
  "能够强化空间想象和动手能力",
  "是理论联系实际的有效学习方式",
  "培养合作交流和创新思维"
]',
'[
  {
    "name": "实践学习循环",
    "formula": "理论学习 → 设计制作 → 验证反思 → 深化理解",
    "description": "制作立体模型的学习循环"
  },
  {
    "name": "能力培养目标",
    "formula": "空间想象 + 动手能力 + 合作交流 + 创新思维",
    "description": "立体模型制作的能力培养目标"
  },
  {
    "name": "学习效果验证",
    "formula": "理论验证 + 实践体验 + 成果展示",
    "description": "立体模型制作的学习效果"
  }
]',
'[
  {
    "title": "制作建筑模型验证三视图",
    "problem": "学生小组如何通过制作简单建筑模型来验证三视图的投影关系？",
    "solution": "建筑模型制作的学习过程：①选择目标：选择一个简单的建筑物如小房子②绘制三视图：先在纸上绘制准确的三视图③材料准备：准备纸板、胶水、尺子等制作工具④分组制作：按三视图尺寸制作模型的各个面⑤组装验证：组装成立体模型，检查是否符合三视图⑥测量对比：测量模型实际尺寸与三视图是否一致⑦反思改进：总结制作过程中的问题和收获。这个过程让学生在实践中深刻理解投影关系。",
    "analysis": "通过具体项目理解实践学习在投影教学中的重要价值"
  }
]',
'[
  {
    "concept": "实践性学习",
    "explanation": "通过动手实践加深理论理解",
    "example": "理论知识的实际应用和验证"
  },
  {
    "concept": "综合能力培养",
    "explanation": "制作过程培养多种综合能力",
    "example": "空间想象、动手操作、团队合作"
  },
  {
    "concept": "创新思维发展",
    "explanation": "制作过程中的创新思维培养",
    "example": "设计改进、问题解决、创意表达"
  }
]',
'[
  "缺乏实践操作的具体方法",
  "不能有效验证理论知识",
  "制作过程缺乏系统规划",
  "忽视实践学习的反思总结"
]',
'[
  "项目化学习法：将制作任务项目化管理",
  "合作学习法：通过小组合作完成制作任务",
  "反思总结法：及时反思制作过程的收获",
  "展示交流法：通过展示交流深化学习效果"
]',
'{
  "emphasis": ["实践能力", "创新教育"],
  "application": ["STEAM教育", "项目学习"],
  "connection": ["素质教育", "创新培养"]
}',
'{
  "emphasis": ["理论验证", "实践应用"],
  "application": ["数学实验", "理论实践"],
  "connection": ["数学建模", "应用数学"]
}',
'1.0', true, 'zh-CN', 4.7, 'approved'),

-- MATH_G9S2_CH29_018: 根据三视图制作立体模型
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S2_CH29_018'),
'根据三视图制作立体模型是将二维图纸信息转化为三维实体的实践技能',
'根据三视图制作立体模型是投影与视图学习的重要实践技能，它要求学生能够准确理解三视图的信息，并将平面的投影图转化为立体的实物模型。制作过程包括：①图纸分析：仔细分析三视图的尺寸、比例和形状特征②材料选择：根据模型要求选择合适的制作材料③工艺规划：制定合理的制作工艺流程④精确制作：按照三视图的尺寸精确制作各部分⑤装配检验：组装模型并检验是否符合三视图要求。这一过程不仅验证了对投影理论的理解，还培养了动手能力、精确性和工程意识，是理论与实践相结合的重要环节。',
'[
  "将二维图纸信息转化为三维实体",
  "要求准确理解和应用三视图信息",
  "包括分析、制作、装配、检验等环节",
  "培养动手能力和工程意识",
  "是理论与实践结合的重要技能"
]',
'[
  {
    "name": "图纸转换",
    "formula": "三视图信息 → 空间理解 → 实体模型",
    "description": "从图纸到实体的转换过程"
  },
  {
    "name": "尺寸控制",
    "formula": "图纸尺寸 × 比例系数 = 实际制作尺寸",
    "description": "制作过程的尺寸控制方法"
  },
  {
    "name": "精度验证",
    "formula": "实测尺寸 ÷ 理论尺寸 = 制作精度",
    "description": "模型制作精度的验证方法"
  }
]',
'[
  {
    "title": "根据家具三视图制作模型",
    "problem": "木工学徒如何根据椅子的三视图制作实物模型？",
    "solution": "根据椅子三视图制作模型的过程：①图纸理解：分析主视图显示的椅子侧面轮廓，俯视图显示的座面形状，左视图显示的椅子宽度②材料准备：选择适当比例的木材或模型材料③部件制作：按照各视图尺寸制作椅背、座面、椅腿等部件④装配调试：按照三视图的位置关系组装各部件⑤精度检查：测量实际模型与三视图的符合程度⑥完善调整：根据检查结果进行必要的调整完善。这个过程体现了从图纸到实物的完整工艺流程。",
    "analysis": "通过具体实例理解图纸指导实际制作的工程价值"
  }
]',
'[
  {
    "concept": "图纸读取能力",
    "explanation": "准确理解和应用技术图纸信息的能力",
    "example": "工程图纸的读取和理解技能"
  },
  {
    "concept": "实践操作技能",
    "explanation": "将理论知识转化为实际操作的技能",
    "example": "动手制作和精确加工能力"
  },
  {
    "concept": "工程质量意识",
    "explanation": "制作过程中的精度和质量控制意识",
    "example": "工程标准和质量要求的体现"
  }
]',
'[
  "不能准确理解三视图信息",
  "制作过程缺乏精确性",
  "忽视尺寸和比例要求",
  "缺乏质量检验意识"
]',
'[
  "图纸精读法：仔细分析三视图的每个细节",
  "分步制作法：将复杂模型分解为简单部件",
  "精度控制法：制作过程中严格控制尺寸精度",
  "检验改进法：及时检验并改进制作质量"
]',
'{
  "emphasis": ["工艺技能", "质量控制"],
  "application": ["职业教育", "技能培训"],
  "connection": ["工匠精神", "工程素养"]
}',
'{
  "emphasis": ["理论应用", "实践验证"],
  "application": ["应用数学", "工程实践"],
  "connection": ["数学建模", "工程应用"]
}',
'1.0', true, 'zh-CN', 4.7, 'approved'),

-- MATH_G9S2_CH29_019: 立体模型的设计
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S2_CH29_019'),
'立体模型的设计是运用投影原理和空间思维进行创造性设计的综合能力',
'立体模型的设计是投影与视图学习的高层次应用，要求学生运用所学的投影理论和空间思维能力，自主设计具有特定功能或美学特征的立体模型。设计过程包括：①需求分析：明确设计目标和功能要求②创意构思：发挥想象力提出设计方案③方案论证：运用投影原理分析方案可行性④绘制图纸：绘制准确的三视图和设计图⑤模型制作：将设计方案转化为实物模型⑥效果评价：评价设计效果并优化改进。这一过程培养学生的创新思维、设计能力和工程素养，是从学习者向创造者转变的重要环节。',
'[
  "运用投影原理进行创造性设计",
  "要求综合运用空间思维和设计能力",
  "包括需求分析、创意构思、方案实现等环节",
  "培养创新思维和工程素养",
  "实现从学习者向创造者的转变"
]',
'[
  {
    "name": "设计流程",
    "formula": "需求分析 → 创意构思 → 方案论证 → 图纸绘制 → 模型制作",
    "description": "立体模型设计的完整流程"
  },
  {
    "name": "创新能力",
    "formula": "基础知识 + 空间思维 + 创意能力 = 设计创新",
    "description": "设计创新能力的构成要素"
  },
  {
    "name": "设计评价",
    "formula": "功能性 + 美观性 + 可行性 = 设计质量",
    "description": "立体模型设计的评价标准"
  }
]',
'[
  {
    "title": "设计环保垃圾桶的立体模型",
    "problem": "如何设计一个既美观又实用的分类垃圾桶立体模型？",
    "solution": "环保垃圾桶的设计过程：①需求分析：分析垃圾分类的功能需求，考虑美观性和实用性②创意构思：设计多舱室结构，采用不同颜色标识，添加便民功能③方案论证：运用投影原理分析结构合理性，验证各部分的空间关系④绘制图纸：绘制详细的三视图，标注尺寸和材料要求⑤模型制作：选择环保材料制作比例模型⑥效果评价：测试功能性、评价美观性、收集使用反馈。这个设计体现了环保理念与实用功能的完美结合。",
    "analysis": "通过环保主题设计理解立体模型设计的社会价值"
  }
]',
'[
  {
    "concept": "设计思维",
    "explanation": "系统性的创造性思维和问题解决方法",
    "example": "从问题识别到解决方案的思维过程"
  },
  {
    "concept": "创新能力",
    "explanation": "在掌握基础知识基础上的创造性应用",
    "example": "传统知识与创新思维的结合"
  },
  {
    "concept": "工程设计素养",
    "explanation": "工程设计过程中的科学态度和方法",
    "example": "严谨的设计流程和质量标准"
  }
]',
'[
  "缺乏系统的设计思维",
  "创意构思不够大胆创新",
  "设计方案缺乏可行性分析",
  "忽视设计的实用性和美观性"
]',
'[
  "设计思维法：培养系统性的设计思维方式",
  "头脑风暴法：通过头脑风暴激发创意灵感",
  "原型验证法：通过制作原型验证设计方案",
  "迭代优化法：在实践中不断改进设计方案"
]',
'{
  "emphasis": ["创新教育", "设计能力"],
  "application": ["创新教育", "设计思维培养"],
  "connection": ["创新能力", "设计教育"]
}',
'{
  "emphasis": ["数学应用", "创造性思维"],
  "application": ["数学建模", "创新思维"],
  "connection": ["应用数学", "创新数学"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G9S2_CH29_020: 数学活动：投影与视图的探索
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S2_CH29_020'),
'投影与视图的数学探索活动是深化理论理解和培养数学思维的综合实践',
'投影与视图的数学探索活动是本章学习的综合性实践活动，通过多样化的数学活动深化对投影理论的理解，培养数学思维和探索精神。活动内容包括：①投影现象观察：观察自然界和生活中的各种投影现象②投影规律探索：通过实验探索不同投影的性质和规律③数学建模活动：建立投影问题的数学模型④技术应用探索：探索投影理论在现代技术中的应用⑤创新实践项目：设计和实施投影相关的创新项目。这些活动有助于学生从数学的角度理解投影与视图，培养观察能力、探索精神、创新意识和数学应用能力。',
'[
  "深化投影理论理解的综合数学实践",
  "包括观察、探索、建模、应用等多种活动",
  "培养数学思维和探索精神",
  "强化投影理论的数学本质认识",
  "发展数学应用和创新能力"
]',
'[
  {
    "name": "探索活动循环",
    "formula": "观察现象 → 发现规律 → 建立模型 → 验证应用",
    "description": "数学探索活动的基本循环"
  },
  {
    "name": "数学思维发展",
    "formula": "观察能力 + 分析能力 + 抽象能力 + 应用能力",
    "description": "探索活动培养的数学思维能力"
  },
  {
    "name": "创新实践价值",
    "formula": "理论理解 + 实践体验 + 创新应用 = 综合素养",
    "description": "数学活动的综合教育价值"
  }
]',
'[
  {
    "title": "探索影子长度的数学规律",
    "problem": "如何通过数学活动探索不同时间太阳影子长度的变化规律？",
    "solution": "影子长度数学探索活动：①观察准备：选择固定物体，准备测量工具，记录时间和天气②数据收集：每隔一小时测量影子长度，记录太阳高度角③数据分析：绘制时间-影子长度关系图，寻找变化规律④数学建模：建立影子长度与太阳高度角的数学关系⑤规律验证：用数学模型预测其他时间的影子长度⑥应用拓展：探索这一规律在建筑设计、太阳能应用中的价值。这个活动体现了数学在解释自然现象中的重要作用。",
    "analysis": "通过自然现象探索体现数学活动的科学价值"
  }
]',
'[
  {
    "concept": "数学探索精神",
    "explanation": "主动探索数学规律和应用的精神品质",
    "example": "科学探索和数学发现的思维方式"
  },
  {
    "concept": "数学建模能力",
    "explanation": "用数学方法描述和解决实际问题的能力",
    "example": "将实际问题转化为数学模型"
  },
  {
    "concept": "数学应用意识",
    "explanation": "运用数学知识解决实际问题的意识",
    "example": "数学与现实生活的紧密联系"
  }
]',
'[
  "缺乏主动探索的数学精神",
  "不能有效进行数学建模",
  "忽视数学活动的系统性",
  "缺乏数学应用的创新意识"
]',
'[
  "问题驱动法：以问题为导向开展探索活动",
  "实验探索法：通过数学实验发现规律",
  "合作探究法：通过小组合作深化探索",
  "反思总结法：及时反思探索过程和收获"
]',
'{
  "emphasis": ["数学探索", "创新实践"],
  "application": ["数学活动", "探索性学习"],
  "connection": ["数学文化", "科学探索"]
}',
'{
  "emphasis": ["数学思维", "理论深化"],
  "application": ["数学研究", "理论探索"],
  "connection": ["数学本质", "思维发展"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'),

-- MATH_G9S2_CH29_021: 数学术语的中英对照
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S2_CH29_021'),
'投影与视图相关数学术语的中英对照，促进国际交流和专业发展',
'数学术语的中英对照是数学学习的重要组成部分，特别是在投影与视图这一具有强烈国际性和工程应用性的领域。主要术语包括：投影(Projection)、正投影(Orthogonal Projection)、中心投影(Central Projection)、平行投影(Parallel Projection)、三视图(Three Views)、主视图(Front View)、俯视图(Top View)、左视图(Left View)、投影面(Projection Plane)、投影中心(Center of Projection)、投影线(Projection Line)、视图(View)、立体图形(Solid Figure)、工程制图(Engineering Drawing)等。掌握这些术语的中英对照有助于学生在国际化背景下的学习和交流，为未来的专业发展奠定基础。',
'[
  "投影与视图领域的重要数学术语",
  "促进国际化数学学习和交流",
  "为专业发展奠定语言基础",
  "体现数学的国际通用性",
  "连接中西方数学教育体系"
]',
'[
  {
    "name": "基础投影术语",
    "formula": "投影=Projection, 正投影=Orthogonal Projection",
    "description": "投影相关的基础术语对照"
  },
  {
    "name": "视图系统术语",
    "formula": "三视图=Three Views, 主视图=Front View",
    "description": "视图系统的专业术语对照"
  },
  {
    "name": "工程应用术语",
    "formula": "工程制图=Engineering Drawing, 技术图纸=Technical Drawing",
    "description": "工程应用领域的术语对照"
  }
]',
'[
  {
    "title": "国际工程项目中的术语应用",
    "problem": "中国工程师与外国同事讨论建筑图纸时，如何准确使用英文术语？",
    "solution": "国际工程交流中的术语应用：①基础术语：用Orthogonal Projection描述正投影，用Three Views说明三视图系统②专业表达：用Front View、Top View、Left View准确指代主视图、俯视图、左视图③技术讨论：用Engineering Drawing表示工程制图，用Technical Specification说明技术规格④图纸说明：用Projection Plane解释投影面，用Scale Factor说明比例系数⑤质量控制：用Dimensional Accuracy表示尺寸精度。准确使用术语有助于提高国际合作效率和专业形象。",
    "analysis": "通过国际合作实例理解术语标准化的重要价值"
  }
]',
'[
  {
    "concept": "数学语言国际化",
    "explanation": "数学作为国际通用语言的特征",
    "example": "数学术语的国际标准化表达"
  },
  {
    "concept": "专业术语体系",
    "explanation": "投影与视图领域的完整术语体系",
    "example": "从基础概念到应用领域的术语覆盖"
  },
  {
    "concept": "跨文化数学交流",
    "explanation": "通过术语对照促进跨文化数学交流",
    "example": "中西方数学教育的对接和融合"
  }
]',
'[
  "不重视数学术语的国际化学习",
  "术语使用不够准确和规范",
  "缺乏国际交流的语言准备",
  "忽视术语学习的专业价值"
]',
'[
  "对照学习法：系统学习中英术语对照",
  "情境应用法：在具体情境中应用英文术语",
  "专业阅读法：通过英文资料强化术语理解",
  "交流实践法：在国际交流中练习术语应用"
]',
'{
  "emphasis": ["国际化教育", "专业素养"],
  "application": ["国际交流", "专业发展"],
  "connection": ["国际化人才", "专业能力"]
}',
'{
  "emphasis": ["数学语言", "术语体系"],
  "application": ["数学交流", "学术研究"],
  "connection": ["数学文化", "国际数学"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'); 