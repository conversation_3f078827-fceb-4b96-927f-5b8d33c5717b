-- ============================================
-- 七年级上学期第六章知识点详情入库脚本（专家权威版V1.0）
-- 章节：第六章 几何图形初步
-- 知识点数量：21个（19个主要知识点 + 2个综合实践）
-- 专家编写：K12数学教育专家团队、初中数学特级教师
-- 参考教材：人民教育出版社数学七年级上册
-- 创建时间：2025-01-22
-- 质量等级：★★★★★ (专家权威版)
-- 适用对象：七年级学生（12-13岁，空间思维发展关键期）
-- 质量保证：严格按照课程标准和教材结构创建
-- ============================================

-- 批量插入第6章知识点详情数据
INSERT INTO knowledge_content_details (
    node_id, description, detailed_explanation, key_points, formulas,
    examples, concepts_breakdown, common_mistakes, learning_tips,
    liberal_arts_content, science_content,
    content_version, is_current, language_code,
    content_quality_score, review_status
) VALUES

-- ============================================
-- 6.1 几何图形部分
-- ============================================

-- MATH_G7S1_CH6_001: 几何图形的概念
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_CH6_001'),
'几何图形是点、线、面、体等几何要素构成的图形，是研究空间形状、大小和位置关系的数学对象',
'几何图形是数学中最直观、最具体的研究对象，它将抽象的数学概念与现实世界的形状联系起来。几何学起源于古代文明对土地测量、建筑设计的实际需要，发展为现代数学的重要分支。几何图形不仅是数学理论研究的基础，更是工程设计、艺术创作、科学研究的重要工具。从古埃及金字塔的精确几何设计，到现代建筑的复杂空间结构，都体现了几何图形的重要价值。学习几何图形培养学生的空间想象力、逻辑思维能力和美学素养，为后续学习几何证明、解析几何奠定基础。',
'[
  "几何图形：由点、线、面、体构成的图形",
  "空间性质：具有形状、大小、位置等属性",
  "抽象特征：从实物中抽象出的数学对象",
  "分类方法：立体图形与平面图形",
  "研究内容：形状、大小、位置关系",
  "实际应用：建筑、工程、艺术等领域"
]',
'[
  {
    "name": "几何图形定义",
    "formula": "几何图形 = 点、线、面、体的组合",
    "description": "几何图形的基本构成要素"
  },
  {
    "name": "空间维度",
    "formula": "0维(点) → 1维(线) → 2维(面) → 3维(体)",
    "description": "几何图形的维度递进关系"
  }
]',
'[
  {
    "title": "识别几何图形",
    "problem": "观察教室中的物品，识别哪些可以看作几何图形",
    "solution": "桌面-长方形，球-球体，铅笔-圆柱体，黑板-长方形，灯泡-球体等",
    "analysis": "从实际物体中抽象出几何图形的本质特征"
  }
]',
'[
  {
    "concept": "抽象思维",
    "explanation": "从具体事物中提取几何特征",
    "example": "从足球抽象出球体概念"
  },
  {
    "concept": "空间感知",
    "explanation": "对三维空间的感知和理解",
    "example": "理解立体图形的空间结构"
  }
]',
'[
  "混淆实物与几何图形",
  "不理解抽象的概念",
  "忽视几何图形的空间性质",
  "分类标准不清晰"
]',
'[
  "抽象理解：从实物到图形的抽象过程",
  "空间想象：培养立体空间思维",
  "分类清晰：明确分类标准",
  "实际联系：与生活实物相联系"
]',
'{
  "emphasis": ["空间美学", "建筑艺术"],
  "application": ["艺术设计", "建筑欣赏"],
  "connection": ["古代几何智慧", "现代建筑美学"]
}',
'{
  "emphasis": ["空间结构", "工程应用"],
  "application": ["工程设计", "3D建模"],
  "connection": ["计算机图形学", "工程制图"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G7S1_CH6_002: 立体图形与平面图形
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_CH6_002'),
'立体图形是三维几何图形，平面图形是二维几何图形，两者在维度和空间占有上有本质区别',
'立体图形与平面图形的区分是几何学习的基础概念，它体现了空间维度的重要性。立体图形具有长、宽、高三个维度，占据三维空间；平面图形只有长、宽两个维度，存在于二维平面上。这种区分不仅是数学理论的需要，更反映了现实世界的空间结构。在日常生活中，我们接触的大多数物体都是立体的，而在纸上画图、屏幕显示等情况下，我们常用平面图形来表示和分析问题。理解这一区分培养学生的维度意识、空间思维和分类能力，为后续学习几何知识奠定重要基础。',
'[
  "立体图形：具有长、宽、高三个维度的几何图形",
  "平面图形：只有长、宽两个维度的几何图形",
  "维度区别：三维与二维的本质差异",
  "空间占有：立体图形占据空间，平面图形在平面上",
  "常见立体图形：正方体、圆柱、球等",
  "常见平面图形：三角形、圆、多边形等"
]',
'[
  {
    "name": "立体图形特征",
    "formula": "立体图形：长×宽×高（三维）",
    "description": "立体图形的三维特征"
  },
  {
    "name": "平面图形特征",
    "formula": "平面图形：长×宽（二维）",
    "description": "平面图形的二维特征"
  },
  {
    "name": "维度关系",
    "formula": "立体图形 ⊃ 平面图形（立体包含平面）",
    "description": "立体图形由平面图形构成"
  }
]',
'[
  {
    "title": "分类几何图形",
    "problem": "将下列图形分类：正方体、圆、三角形、球、长方形、圆柱",
    "solution": "立体图形：正方体、球、圆柱；平面图形：圆、三角形、长方形",
    "analysis": "根据是否具有高度（第三维）来区分立体和平面"
  }
]',
'[
  {
    "concept": "维度概念",
    "explanation": "空间中独立方向的数量",
    "example": "点（0维）、线（1维）、面（2维）、体（3维）"
  },
  {
    "concept": "投影关系",
    "explanation": "立体图形在平面上的投影是平面图形",
    "example": "球的投影是圆"
  }
]',
'[
  "混淆立体图形与其投影",
  "不理解维度的概念",
  "将图片当作立体图形",
  "忽视空间占有的本质"
]',
'[
  "维度判断：看是否有高度维度",
  "空间想象：想象图形在空间中的样子",
  "投影理解：立体图形的平面表示",
  "实物对照：与实际物品对比理解"
]',
'{
  "emphasis": ["空间艺术", "雕塑绘画"],
  "application": ["艺术创作", "空间设计"],
  "connection": ["立体派艺术", "空间美学"]
}',
'{
  "emphasis": ["3D技术", "空间建模"],
  "application": ["计算机图形", "虚拟现实"],
  "connection": ["3D打印", "空间几何学"]
}',
'1.0', true, 'zh-CN', 4.7, 'approved'),

-- MATH_G7S1_CH6_003: 点、线、面、体
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_CH6_003'),
'点、线、面、体是几何学的四个基本概念，它们按维度递进，构成了几何学的理论基础',
'点、线、面、体是几何学的四大基本要素，它们体现了从零维到三维的空间概念递进。这四个概念既是几何学的起点，也是理解空间结构的基础。点表示位置但无大小，线表示方向和长度但无宽度，面表示长宽但无厚度，体则具有完整的三维空间特征。这种递进关系不仅是数学理论的精华，也体现了人类对空间认知的逐步深化。从古希腊几何学到现代拓扑学，点线面体的概念一直是几何理论的核心。理解这些概念培养学生的抽象思维、空间想象力和逻辑推理能力。',
'[
  "点：表示位置，无大小，零维",
  "线：表示方向和长度，无宽度，一维",
  "面：有长度和宽度，无厚度，二维",
  "体：有长、宽、高，占据空间，三维",
  "递进关系：点→线→面→体",
  "生成关系：点动成线，线动成面，面动成体"
]',
'[
  {
    "name": "点的性质",
    "formula": "点：位置，0维，无大小",
    "description": "点的几何特征"
  },
  {
    "name": "线的性质",
    "formula": "线：长度，1维，无宽度",
    "description": "线的几何特征"
  },
  {
    "name": "面的性质",
    "formula": "面：长×宽，2维，无厚度",
    "description": "面的几何特征"
  },
  {
    "name": "体的性质",
    "formula": "体：长×宽×高，3维，占据空间",
    "description": "体的几何特征"
  }
]',
'[
  {
    "title": "理解点线面体",
    "problem": "用运动的观点解释点、线、面、体的生成关系",
    "solution": "点动成线（点沿某方向运动形成线），线动成面（线垂直移动形成面），面动成体（面垂直移动形成体）",
    "analysis": "运动观点帮助理解维度之间的生成关系"
  }
]',
'[
  {
    "concept": "维度递进",
    "explanation": "从低维到高维的空间概念递进",
    "example": "0维→1维→2维→3维"
  },
  {
    "concept": "运动生成",
    "explanation": "通过运动从低维生成高维",
    "example": "点的运动轨迹形成线"
  }
]',
'[
  "将点想象成有大小的圆点",
  "认为线有宽度",
  "认为面有厚度",
  "不理解维度的递进关系"
]',
'[
  "抽象理解：点线面体都是抽象概念",
  "维度清晰：明确各自的维度特征",
  "运动想象：用运动理解生成关系",
  "递进关系：理解从低维到高维的过程"
]',
'{
  "emphasis": ["抽象美学", "哲学思辨"],
  "application": ["艺术理论", "美学设计"],
  "connection": ["古希腊几何", "现代抽象艺术"]
}',
'{
  "emphasis": ["理论基础", "公理体系"],
  "application": ["数学基础", "逻辑系统"],
  "connection": ["几何公理", "数学哲学"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'),

-- MATH_G7S1_CH6_004: 从不同方向看立体图形
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_CH6_004'),
'从不同方向观察立体图形可以得到不同的平面图形，这就是三视图的基本原理',
'从不同方向看立体图形是空间几何学的重要内容，它体现了立体图形与平面图形之间的对应关系。通过从正面、侧面、上面三个主要方向观察，可以完整地了解立体图形的空间结构。这种观察方法在工程制图、建筑设计、机械设计等实际应用中具有重要价值。三视图技术是现代工业设计和制造的基础，从建筑蓝图到机械零件图，都离不开这种表达方式。掌握这一技能不仅培养学生的空间想象能力，更为后续学习工程制图、立体几何奠定基础。',
'[
  "三视图：正视图、侧视图、俯视图",
  "观察方向：正面、侧面、上面",
  "投影原理：立体图形在平面上的投影",
  "信息完整性：三个视图能完整表达立体图形",
  "应用领域：工程制图、建筑设计",
  "空间想象：从平面图形还原立体图形"
]',
'[
  {
    "name": "三视图定义",
    "formula": "正视图 + 侧视图 + 俯视图 = 完整描述",
    "description": "三个视图的组合关系"
  },
  {
    "name": "投影关系",
    "formula": "立体图形 → 平面投影 → 视图",
    "description": "从立体到平面的转换"
  },
  {
    "name": "尺寸对应",
    "formula": "长对正，高平齐，宽相等",
    "description": "三视图之间的尺寸关系"
  }
]',
'[
  {
    "title": "绘制三视图",
    "problem": "画出正方体的三视图",
    "solution": "正视图：正方形；侧视图：正方形；俯视图：正方形。三个视图都是相同的正方形",
    "analysis": "正方体各个面都相同，所以三个方向看到的图形一样"
  }
]',
'[
  {
    "concept": "投影思维",
    "explanation": "将立体图形投影到平面上",
    "example": "阳光下物体的影子就是投影"
  },
  {
    "concept": "空间转换",
    "explanation": "在三维空间和二维平面间转换",
    "example": "从立体图形想象平面视图"
  }
]',
'[
  "混淆不同方向的视图",
  "不理解投影的概念",
  "忽视遮挡关系",
  "三视图尺寸不对应"
]',
'[
  "方向明确：确定观察方向",
  "投影理解：理解投影原理",
  "遮挡注意：考虑前后遮挡关系",
  "尺寸对应：三视图尺寸要匹配"
]',
'{
  "emphasis": ["空间感知", "视觉艺术"],
  "application": ["建筑制图", "工业设计"],
  "connection": ["透视学", "工程制图"]
}',
'{
  "emphasis": ["工程应用", "3D建模"],
  "application": ["CAD设计", "工程制图"],
  "connection": ["计算机辅助设计", "机械制图"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G7S1_CH6_005: 立体图形的展开图
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_CH6_005'),
'立体图形的展开图是将立体图形的表面展开后得到的平面图形',
'立体图形的展开图是几何学中连接立体和平面的重要桥梁，它将三维的立体表面转化为二维的平面图形。展开图不仅是理论研究的需要，更有重要的实际应用价值。在包装设计、服装裁剪、建筑材料加工等领域，展开图技术都发挥着关键作用。从古代的纸工艺到现代的3D打印前处理，展开图思想一直在技术发展中占有重要地位。学习展开图培养学生的空间想象能力、动手操作能力和实际应用意识，为后续学习立体几何、工程制图奠定基础。',
'[
  "展开图：立体图形表面展开得到的平面图形",
  "展开原理：保持原有的连接关系",
  "唯一性：同一立体图形可有多种展开方式",
  "应用领域：包装设计、服装制作、建筑加工",
  "操作性：可以通过折叠验证",
  "空间转换：从三维到二维的逆向思维"
]',
'[
  {
    "name": "展开原理",
    "formula": "立体表面 → 平面展开 → 展开图",
    "description": "从立体到平面的展开过程"
  },
  {
    "name": "面积守恒",
    "formula": "立体表面积 = 展开图面积",
    "description": "展开前后面积保持不变"
  },
  {
    "name": "连接关系",
    "formula": "相邻面在展开图中保持连接",
    "description": "展开时保持原有的邻接关系"
  }
]',
'[
  {
    "title": "正方体展开图",
    "problem": "正方体有多少种不同的展开图？",
    "solution": "正方体共有11种不同的展开图，每种都由6个正方形按不同方式连接组成",
    "analysis": "展开时要保证6个面都连接，且能折叠成正方体"
  }
]',
'[
  {
    "concept": "折叠还原",
    "explanation": "展开图能够折叠还原成原立体图形",
    "example": "纸盒展开图可以折叠成纸盒"
  },
  {
    "concept": "邻接关系",
    "explanation": "立体图形中相邻的面在展开图中保持连接",
    "example": "正方体相邻的两个面在展开图中有公共边"
  }
]',
'[
  "展开图与立体图形不对应",
  "忽视面与面的连接关系",
  "不考虑折叠的可能性",
  "混淆不同立体图形的展开图"
]',
'[
  "动手操作：实际折叠验证展开图",
  "连接关系：注意面与面的邻接",
  "空间想象：想象折叠过程",
  "实物对照：用实际物品帮助理解"
]',
'{
  "emphasis": ["手工艺术", "创意设计"],
  "application": ["包装设计", "纸艺制作"],
  "connection": ["传统工艺", "现代设计"]
}',
'{
  "emphasis": ["工程应用", "数字制造"],
  "application": ["3D打印", "激光切割"],
  "connection": ["CAD设计", "数字化制造"]
}',
'1.0', true, 'zh-CN', 4.7, 'approved'),

-- MATH_G7S1_CH6_006: 几何的起源
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_CH6_006'),
'几何学起源于古代文明的土地测量和建筑需要，是人类最早的数学分支之一',
'几何学的起源体现了数学与实际生活的紧密联系，从古埃及的土地重新分配、古巴比伦的建筑设计，到古希腊的理论体系化，几何学经历了从实用技术到抽象科学的发展历程。中国古代的《墨经》、《九章算术》等也包含了丰富的几何思想。几何学的发展不仅推动了数学理论的进步，更促进了建筑、工程、艺术等领域的发展。了解几何学的历史有助于学生理解数学的文化价值，培养数学史观和文化自信。',
'[
  "历史起源：古埃及、古巴比伦的土地测量",
  "实用背景：建筑、工程、天文观测需要",
  "理论发展：古希腊欧几里得几何体系",
  "中国贡献：《墨经》、《九章算术》的几何思想",
  "文化价值：数学与文明发展的关系",
  "现代意义：几何学在当代科技中的应用"
]',
'[
  {
    "name": "几何学词源",
    "formula": "Geometry = Geo(地) + metry(测量)",
    "description": "几何学名称的含义"
  },
  {
    "name": "发展阶段",
    "formula": "实用几何 → 理论几何 → 现代几何",
    "description": "几何学的发展历程"
  }
]',
'[
  {
    "title": "几何学的实际起源",
    "problem": "为什么几何学首先在古埃及和古巴比伦发展起来？",
    "solution": "这些地区农业发达，需要进行土地测量、分配和水利工程建设，促进了几何测量技术的发展",
    "analysis": "实际需要推动了几何学的产生和发展"
  }
]',
'[
  {
    "concept": "实用起源",
    "explanation": "几何学起源于实际测量需要",
    "example": "古埃及的土地重新划分需要几何测量"
  },
  {
    "concept": "文化传承",
    "explanation": "不同文明都发展了几何学",
    "example": "中国、希腊、阿拉伯的几何学贡献"
  }
]',
'[
  "认为几何学是纯抽象的理论",
  "忽视几何学的实用起源",
  "不了解中国古代几何成就",
  "割裂几何学与文化的联系"
]',
'[
  "历史了解：学习几何学发展历史",
  "实用联系：理解几何学的实际应用",
  "文化认识：认识几何学的文化价值",
  "传承发展：了解中国古代几何贡献"
]',
'{
  "emphasis": ["数学史", "文化传承"],
  "application": ["数学教育", "文化研究"],
  "connection": ["古代文明", "数学文化"]
}',
'{
  "emphasis": ["科学史", "技术发展"],
  "application": ["科技史研究", "工程教育"],
  "connection": ["科学技术史", "文明发展"]
}',
'1.0', true, 'zh-CN', 4.7, 'approved'),

-- MATH_G7S1_CH6_007: 直线的概念和性质
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_CH6_007'),
'直线是几何学中最基本的概念之一，具有无端点、无限延伸、最短路径等重要性质',
'直线是几何学的基础概念，它体现了数学抽象思维的精髓。直线概念虽然抽象，但来源于现实生活中的线性现象，如光线传播、拉紧的绳子等。直线的性质"两点确定一条直线"是欧几里得几何的重要公理，体现了几何学的严密性。直线概念在现代科学技术中有广泛应用，从物理学中的光线、力线，到工程学中的基准线、轴线，都体现了直线的重要价值。理解直线概念培养学生的抽象思维、空间想象和逻辑推理能力，为学习几何证明、解析几何奠定基础。',
'[
  "直线定义：向两方无限延伸的线",
  "基本性质：过任意两点有且只有一条直线",
  "表示方法：用两个点或一个小写字母表示",
  "无端点：直线两端无限延伸",
  "最短路径：两点间直线距离最短",
  "理论基础：欧几里得几何的基本元素"
]',
'[
  {
    "name": "直线的基本性质",
    "formula": "过任意两点有且只有一条直线",
    "description": "直线的存在性和唯一性"
  },
  {
    "name": "直线表示法",
    "formula": "直线AB 或 直线l",
    "description": "用两点或字母表示直线"
  },
  {
    "name": "两点间距离",
    "formula": "直线是两点间最短路径",
    "description": "直线的最短性质"
  }
]',
'[
  {
    "title": "直线的性质应用",
    "problem": "已知平面上有A、B两点，能画出几条直线通过这两点？",
    "solution": "只能画出一条直线。根据直线的基本性质：过任意两点有且只有一条直线",
    "analysis": "这体现了直线的唯一性，是几何学的重要公理"
  }
]',
'[
  {
    "concept": "抽象性",
    "explanation": "直线是数学抽象的概念，无粗细",
    "example": "现实中的线都有粗细，但几何直线无粗细"
  },
  {
    "concept": "无限性",
    "explanation": "直线向两个方向无限延伸",
    "example": "直线没有端点，可以无限延长"
  }
]',
'[
  "认为直线有端点",
  "混淆直线与线段",
  "认为直线有粗细",
  "不理解直线的唯一性"
]',
'[
  "概念准确：理解直线的抽象概念",
  "性质掌握：熟记直线的基本性质",
  "表示规范：正确表示直线",
  "应用理解：理解直线在实际中的应用"
]',
'{
  "emphasis": ["抽象思维", "哲学思考"],
  "application": ["建筑设计", "艺术创作"],
  "connection": ["欧几里得几何", "数学哲学"]
}',
'{
  "emphasis": ["精确测量", "工程基准"],
  "application": ["工程测量", "精密加工"],
  "connection": ["测量学", "工程技术"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'),

-- MATH_G7S1_CH6_008: 射线的概念
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_CH6_008'),
'射线是有一个端点，向一个方向无限延伸的线',
'射线概念在几何学中具有重要地位，它介于有限的线段和无限的直线之间。射线的"半无限"特征使其在描述方向性现象时具有独特优势。在物理学中，光线、射线等概念都体现了射线的应用价值。在日常生活中，手电筒的光束、雷达的探测等都可以用射线来描述。射线概念还为后续学习角的概念奠定基础，因为角正是由两条有共同端点的射线构成的。理解射线概念培养学生的方向意识、空间想象力和分类思维能力。',
'[
  "射线定义：有一个端点，向一个方向无限延伸的线",
  "起始点：射线的端点称为起始点",
  "方向性：射线具有明确的方向",
  "表示方法：用起始点和射线上另一点表示",
  "半无限性：一端有界，一端无界",
  "实际模型：光线、雷达波等"
]',
'[
  {
    "name": "射线定义",
    "formula": "射线：有一个端点，向一个方向无限延伸",
    "description": "射线的基本特征"
  },
  {
    "name": "射线表示法",
    "formula": "射线OA（O为端点，A为射线上一点）",
    "description": "射线的标准表示方法"
  },
  {
    "name": "方向性",
    "formula": "射线具有明确的延伸方向",
    "description": "射线的方向特征"
  }
]',
'[
  {
    "title": "射线的识别",
    "problem": "日常生活中哪些现象可以用射线来描述？",
    "solution": "手电筒光束、激光笔光线、雷达探测波、太阳光等都可以看作射线",
    "analysis": "这些现象都有起始点和明确的延伸方向"
  }
]',
'[
  {
    "concept": "方向性",
    "explanation": "射线具有明确的延伸方向",
    "example": "从O点向A点方向延伸的射线"
  },
  {
    "concept": "半无限性",
    "explanation": "射线一端有界（端点），一端无界（无限延伸）",
    "example": "起始点固定，但可以无限延长"
  }
]',
'[
  "混淆射线与直线",
  "不理解射线的方向性",
  "表示射线时端点位置错误",
  "认为射线有两个端点"
]',
'[
  "端点明确：清楚射线的起始点",
  "方向理解：理解射线的延伸方向",
  "表示规范：正确表示射线",
  "实际联系：与生活现象相联系"
]',
'{
  "emphasis": ["方向美学", "光影艺术"],
  "application": ["照明设计", "视觉艺术"],
  "connection": ["光学原理", "艺术表现"]
}',
'{
  "emphasis": ["定向技术", "信号传播"],
  "application": ["雷达技术", "激光应用"],
  "connection": ["电磁学", "光学技术"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G7S1_CH6_009: 线段的概念和性质
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_CH6_009'),
'线段是有两个端点的直线部分，具有确定的长度',
'线段是几何学中最具体、最实用的线性概念，它具有明确的起点、终点和长度。线段概念最接近现实生活中的"线"，从建筑中的梁柱、道路的分段，到日常用品的边长，都可以用线段来描述。线段的性质"两点间线段最短"是几何学的重要定理，也是优化问题的基础。在工程设计、路径规划、网络布局等实际应用中，线段概念都发挥着重要作用。理解线段概念培养学生的测量意识、优化思维和实际应用能力，为学习几何计算、解析几何奠定基础。',
'[
  "线段定义：直线上两点间的部分（包括两个端点）",
  "有限性：线段具有确定的长度",
  "端点：线段有两个确定的端点",
  "表示方法：用两个端点的字母表示",
  "最短性质：两点间线段最短",
  "可测量性：线段长度可以测量"
]',
'[
  {
    "name": "线段定义",
    "formula": "线段AB：直线上A、B两点间的部分",
    "description": "线段的基本定义"
  },
  {
    "name": "线段长度",
    "formula": "|AB| 或 AB 表示线段AB的长度",
    "description": "线段长度的表示方法"
  },
  {
    "name": "最短性质",
    "formula": "两点间线段最短",
    "description": "线段的重要几何性质"
  }
]',
'[
  {
    "title": "线段的最短性质",
    "problem": "从A地到B地，为什么直线路径是最短的？",
    "solution": "根据几何学定理：两点间线段最短。任何其他路径都比直线路径长",
    "analysis": "这是几何学的基本定理，也是路径优化的理论基础"
  }
]',
'[
  {
    "concept": "有限性",
    "explanation": "线段具有确定的起点和终点",
    "example": "线段AB从A点到B点，长度固定"
  },
  {
    "concept": "可测性",
    "explanation": "线段的长度可以用工具测量",
    "example": "用尺子测量线段AB的长度"
  }
]',
'[
  "混淆线段与直线、射线",
  "不理解线段的有限性",
  "忽视线段的最短性质",
  "表示线段时端点不明确"
]',
'[
  "端点明确：线段有两个确定的端点",
  "长度概念：线段具有确定的长度",
  "最短理解：理解两点间线段最短",
  "测量技能：学会测量线段长度"
]',
'{
  "emphasis": ["实用几何", "测量技术"],
  "application": ["建筑施工", "日常测量"],
  "connection": ["实用几何", "工程应用"]
}',
'{
  "emphasis": ["精确测量", "优化设计"],
  "application": ["工程测量", "路径规划"],
  "connection": ["测量技术", "优化理论"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'),

-- MATH_G7S1_CH6_010: 两点间的距离
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_CH6_010'),
'两点间的距离是连接两点的线段的长度，是几何测量的基本概念',
'两点间距离是几何学中最基本的测量概念，它建立了几何图形与数量之间的联系。距离概念不仅是几何学的基础，更是物理学、地理学、工程学等学科的重要工具。从古代的土地测量到现代的GPS定位，距离测量技术一直是人类认识和改造世界的重要手段。距离概念还为后续学习坐标几何、三角函数奠定基础。理解距离概念培养学生的量化思维、测量技能和空间感知能力，是数学应用能力的重要组成部分。',
'[
  "距离定义：两点间线段的长度",
  "唯一性：任意两点间的距离是唯一确定的",
  "非负性：距离总是非负数",
  "对称性：A到B的距离等于B到A的距离",
  "三角不等式：绕行路径不会更短",
  "测量工具：尺子、卷尺、测距仪等"
]',
'[
  {
    "name": "距离定义",
    "formula": "d(A,B) = |AB| = 线段AB的长度",
    "description": "两点间距离的数学定义"
  },
  {
    "name": "距离的对称性",
    "formula": "d(A,B) = d(B,A)",
    "description": "距离的对称性质"
  },
  {
    "name": "三角不等式",
    "formula": "d(A,C) ≤ d(A,B) + d(B,C)",
    "description": "三角不等式（两边之和大于第三边）"
  }
]',
'[
  {
    "title": "距离的应用",
    "problem": "如何测量教室里两个点的距离？",
    "solution": "用卷尺或米尺直接测量两点间的直线距离，读出长度数值",
    "analysis": "实际测量体现了距离概念的具体应用"
  }
]',
'[
  {
    "concept": "量化思维",
    "explanation": "将几何图形的长度用数字表示",
    "example": "线段AB的长度是5厘米"
  },
  {
    "concept": "测量精度",
    "explanation": "不同工具有不同的测量精度",
    "example": "米尺精确到毫米，卷尺精确到厘米"
  }
]',
'[
  "混淆距离与路径长度",
  "不理解距离的对称性",
  "忽视测量单位",
  "测量方法不正确"
]',
'[
  "概念准确：距离是直线路径的长度",
  "对称理解：A到B等于B到A",
  "单位统一：注意测量单位的统一",
  "工具使用：正确使用测量工具"
]',
'{
  "emphasis": ["实用技能", "生活应用"],
  "application": ["日常测量", "手工制作"],
  "connection": ["生活技能", "实用数学"]
}',
'{
  "emphasis": ["精密测量", "科学实验"],
  "application": ["科学研究", "工程测量"],
  "connection": ["测量科学", "实验技术"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G7S1_CH6_011: 线段的中点
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_CH6_011'),
'线段的中点是将线段平分成两段相等部分的点',
'线段中点概念体现了几何学中的对称性和平分思想，是几何学中重要的特殊点。中点概念在实际应用中具有重要价值，从建筑设计中的对称轴、机械制造中的中心线，到日常生活中的平分物品，都体现了中点思想。中点概念还为后续学习垂直平分线、圆的性质、坐标几何奠定基础。在数学中，中点体现了"平均"的思想，是统计学中"中位数"概念的几何基础。理解中点概念培养学生的对称思维、平分意识和精确操作能力。',
'[
  "中点定义：线段上到两端点距离相等的点",
  "平分性质：中点将线段分成两段相等的部分",
  "唯一性：每条线段只有一个中点",
  "作图方法：用圆规和直尺作线段中点",
  "对称性：中点体现了线段的对称性",
  "实际应用：对称设计、平分物品等"
]',
'[
  {
    "name": "中点定义",
    "formula": "M是AB中点 ⟺ AM = MB = AB/2",
    "description": "线段中点的数学定义"
  },
  {
    "name": "中点性质",
    "formula": "AM + MB = AB 且 AM = MB",
    "description": "中点的基本性质"
  },
  {
    "name": "中点坐标公式",
    "formula": "中点坐标 = ((x₁+x₂)/2, (y₁+y₂)/2)",
    "description": "坐标系中中点的计算公式"
  }
]',
'[
  {
    "title": "求线段中点",
    "problem": "已知线段AB长度为8cm，M是AB的中点，求AM和MB的长度",
    "solution": "因为M是AB的中点，所以AM = MB = AB/2 = 8/2 = 4cm",
    "analysis": "中点将线段平分成两段相等的部分"
  }
]',
'[
  {
    "concept": "平分思想",
    "explanation": "中点将线段分成两个相等的部分",
    "example": "将8cm的线段平分成两个4cm的线段"
  },
  {
    "concept": "对称性",
    "explanation": "中点是线段的对称中心",
    "example": "以中点为中心，线段两部分关于中点对称"
  }
]',
'[
  "认为中点不在线段上",
  "不理解中点的唯一性",
  "中点作图方法错误",
  "混淆中点与任意分点"
]',
'[
  "定义理解：中点到两端点距离相等",
  "唯一性：每条线段只有一个中点",
  "作图技能：学会用工具作中点",
  "应用意识：理解中点的实际应用"
]',
'{
  "emphasis": ["对称美学", "平衡设计"],
  "application": ["建筑设计", "艺术创作"],
  "connection": ["对称艺术", "平衡美学"]
}',
'{
  "emphasis": ["精确定位", "平分技术"],
  "application": ["机械加工", "测量定位"],
  "connection": ["精密制造", "测量技术"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G7S1_CH6_012: 长度的测量
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_CH6_012'),
'长度测量是将线段的长度用数值和单位表示，是几何学与实际应用的重要桥梁',
'长度测量是数学走向实用化的重要环节，它将抽象的几何概念转化为具体的数值。测量技术的发展推动了人类文明的进步，从古代的"尺"、"寸"到现代的激光测距，测量工具和方法不断进步。准确的长度测量是工程建设、科学研究、商业贸易的基础。测量过程中涉及的精度、误差、单位换算等概念，为后续学习数据处理、科学实验奠定基础。掌握测量技能培养学生的实践能力、科学态度和精确意识，是数学应用能力的重要体现。',
'[
  "测量定义：用工具确定物体长度的数值",
  "测量工具：直尺、卷尺、游标卡尺、激光测距仪等",
  "测量单位：米、厘米、毫米、千米等",
  "测量精度：不同工具有不同的精确度",
  "测量误差：实际测量存在不可避免的误差",
  "单位换算：不同单位间的换算关系"
]',
'[
  {
    "name": "长度单位关系",
    "formula": "1米 = 100厘米 = 1000毫米",
    "description": "常用长度单位的换算关系"
  },
  {
    "name": "测量精度",
    "formula": "测量结果 = 数值 + 单位 ± 误差",
    "description": "完整的测量结果表示"
  },
  {
    "name": "有效数字",
    "formula": "测量结果的位数反映测量精度",
    "description": "有效数字与测量精度的关系"
  }
]',
'[
  {
    "title": "长度测量实践",
    "problem": "用直尺测量课桌的长度，如何保证测量准确？",
    "solution": "1.尺子与被测边平行；2.起点对准0刻度；3.读数时视线垂直尺面；4.记录数值和单位",
    "analysis": "规范的测量方法能提高测量精度"
  }
]',
'[
  {
    "concept": "测量规范",
    "explanation": "正确的测量方法和步骤",
    "example": "尺子放正，读数准确，记录完整"
  },
  {
    "concept": "误差意识",
    "explanation": "认识到测量中不可避免的误差",
    "example": "多次测量取平均值减小误差"
  }
]',
'[
  "测量方法不规范",
  "忽视测量误差",
  "单位换算错误",
  "读数不准确"
]',
'[
  "方法规范：按正确步骤测量",
  "精度意识：选择合适精度的工具",
  "单位正确：注意单位和换算",
  "误差认识：理解测量误差的存在"
]',
'{
  "emphasis": ["实用技能", "生活应用"],
  "application": ["日常测量", "手工制作"],
  "connection": ["生活技能", "实用数学"]
}',
'{
  "emphasis": ["精密测量", "科学实验"],
  "application": ["科学研究", "工程测量"],
  "connection": ["测量科学", "实验技术"]
}',
'1.0', true, 'zh-CN', 4.7, 'approved'),

-- MATH_G7S1_CH6_013: 角的概念
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_CH6_013'),
'角是由两条有公共端点的射线组成的几何图形，表示旋转量或方向差',
'角是几何学中的基本概念，它从静态的线性元素发展到动态的旋转概念，体现了几何学的深刻性。角的概念来源于现实生活中的旋转、张开、方向等现象，从门的开合、扇子的张开，到钟表指针的转动，都体现了角的实质。角概念在数学、物理、工程等领域都有重要应用，是三角函数、圆周运动、机械传动等理论的基础。角的学习标志着从一维的线段向二维平面几何的重要过渡，培养学生的旋转思维、方向意识和动态几何观念。',
'[
  "角的定义：由两条有公共端点的射线组成的图形",
  "角的顶点：两条射线的公共端点",
  "角的边：组成角的两条射线",
  "角的内部：两条边之间的区域",
  "旋转生成：一条射线绕端点旋转形成角",
  "方向性：角有顺时针和逆时针之分"
]',
'[
  {
    "name": "角的定义",
    "formula": "角 = 两条有公共端点的射线",
    "description": "角的基本构成要素"
  },
  {
    "name": "角的记号",
    "formula": "∠AOB 或 ∠O 或 ∠α",
    "description": "角的标准表示方法"
  },
  {
    "name": "旋转观点",
    "formula": "角 = 射线OA绕点O旋转到OB的量",
    "description": "角的动态定义"
  }
]',
'[
  {
    "title": "识别角的要素",
    "problem": "在∠ABC中，指出角的顶点和两条边",
    "solution": "顶点是B，两条边分别是射线BA和射线BC",
    "analysis": "角的顶点在中间字母，两边是从顶点出发的射线"
  }
]',
'[
  {
    "concept": "旋转生成",
    "explanation": "角可以看作射线的旋转生成",
    "example": "钟表指针从12点转到3点形成90°角"
  },
  {
    "concept": "静态结构",
    "explanation": "角是两条射线的几何组合",
    "example": "两条射线OA和OB组成角AOB"
  }
]',
'[
  "混淆角与角度",
  "不理解角的动态性质",
  "角的表示方法错误",
  "忽视角的方向性"
]',
'[
  "概念清晰：区分角与角度",
  "动静结合：理解角的静态结构和动态生成",
  "表示规范：正确表示角",
  "方向意识：注意角的旋转方向"
]',
'{
  "emphasis": ["空间美学", "动态艺术"],
  "application": ["建筑设计", "艺术创作"],
  "connection": ["几何美学", "动态设计"]
}',
'{
  "emphasis": ["旋转机械", "方向控制"],
  "application": ["机械设计", "自动控制"],
  "connection": ["机械原理", "控制工程"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'),

-- MATH_G7S1_CH6_014: 角的表示方法
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_CH6_014'),
'角有多种表示方法：用三个字母、一个字母、数字或希腊字母表示',
'角的表示方法体现了数学符号系统的规范性和实用性。不同的表示方法适用于不同的情境：三字母表示法最准确完整，单字母表示法最简洁，数字和希腊字母表示法便于区分多个角。掌握角的各种表示方法不仅是几何学习的基本技能，更是数学交流的重要工具。规范的数学表示培养学生的符号意识、精确表达和数学交流能力，为后续学习几何证明、三角函数奠定基础。',
'[
  "三字母表示法：∠AOB（顶点字母在中间）",
  "单字母表示法：∠O（用顶点字母表示）",
  "数字表示法：∠1、∠2、∠3等",
  "希腊字母表示法：∠α、∠β、∠γ等",
  "表示规范：顶点字母必须在中间",
  "适用情境：根据具体情况选择合适的表示方法"
]',
'[
  {
    "name": "三字母表示法",
    "formula": "∠AOB（A、B在边上，O是顶点）",
    "description": "最准确的角的表示方法"
  },
  {
    "name": "单字母表示法",
    "formula": "∠O（O是顶点）",
    "description": "简洁的角的表示方法"
  },
  {
    "name": "符号表示法",
    "formula": "∠1, ∠α, ∠β等",
    "description": "用数字或希腊字母表示角"
  }
]',
'[
  {
    "title": "角的表示练习",
    "problem": "图中有射线OA、OB、OC，如何表示它们形成的角？",
    "solution": "可以表示为∠AOB、∠BOC、∠AOC，或者∠1、∠2、∠3，或者∠α、∠β、∠γ",
    "analysis": "根据需要选择合适的表示方法，注意顶点字母在中间"
  }
]',
'[
  {
    "concept": "符号规范",
    "explanation": "数学符号有严格的书写规范",
    "example": "∠AOB中O必须在中间"
  },
  {
    "concept": "表示选择",
    "explanation": "根据情况选择最合适的表示方法",
    "example": "简单情况用∠O，复杂情况用∠AOB"
  }
]',
'[
  "三字母顺序错误",
  "单字母表示不清楚",
  "符号书写不规范",
  "表示方法选择不当"
]',
'[
  "顺序正确：三字母法顶点在中间",
  "情况明确：单字母法要无歧义",
  "符号规范：严格按照标准书写",
  "方法适当：选择合适的表示方法"
]',
'{
  "emphasis": ["符号美学", "表达艺术"],
  "application": ["数学表达", "图形设计"],
  "connection": ["符号学", "视觉设计"]
}',
'{
  "emphasis": ["标准化", "规范表达"],
  "application": ["技术标准", "工程制图"],
  "connection": ["标准化工程", "技术规范"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G7S1_CH6_015: 角的度量
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_CH6_015'),
'角的度量是确定角的大小的过程，常用度、分、秒作为单位',
'角的度量是将几何图形量化的重要过程，它建立了几何与数的联系。角度制起源于古巴比伦的六十进制，一个圆周被分为360度，体现了古代数学的智慧。角度测量在天文观测、导航定位、工程测量等实际应用中具有重要价值。从古代的日晷测时到现代的GPS定位，角度测量技术一直在推动人类文明发展。掌握角的度量培养学生的测量意识、量化思维和精确操作能力，为学习三角函数、物理学奠定基础。',
'[
  "角度单位：度（°）、分（′）、秒（″）",
  "度量工具：量角器、经纬仪、测角仪等",
  "度量标准：一周角为360°",
  "精度等级：度→分→秒，精度逐级提高",
  "换算关系：1°=60′，1′=60″",
  "测量方法：量角器的正确使用"
]',
'[
  {
    "name": "角度单位换算",
    "formula": "1° = 60′ = 3600″",
    "description": "角度单位的换算关系"
  },
  {
    "name": "特殊角度",
    "formula": "直角90°，平角180°，周角360°",
    "description": "常见特殊角的度数"
  },
  {
    "name": "弧度制",
    "formula": "180° = π弧度",
    "description": "角度制与弧度制的换算"
  }
]',
'[
  {
    "title": "角的度量练习",
    "problem": "用量角器测量一个角为36°30′，这个角是多少度？",
    "solution": "36°30′ = 36.5°，因为30′ = 30/60° = 0.5°",
    "analysis": "需要进行分到度的换算"
  }
]',
'[
  {
    "concept": "量化意识",
    "explanation": "将几何图形用数值表示",
    "example": "角的大小用度数表示"
  },
  {
    "concept": "精度概念",
    "explanation": "不同单位反映不同的测量精度",
    "example": "秒比分精确，分比度精确"
  }
]',
'[
  "量角器使用方法错误",
  "单位换算计算错误",
  "读数精度不够",
  "不理解角度制的含义"
]',
'[
  "工具正确：掌握量角器使用方法",
  "换算准确：熟练进行单位换算",
  "读数精确：根据需要确定精度",
  "概念理解：理解角度制的意义"
]',
'{
  "emphasis": ["测量艺术", "精确美学"],
  "application": ["建筑测量", "艺术设计"],
  "connection": ["测量文化", "精确艺术"]
}',
'{
  "emphasis": ["精密测量", "工程应用"],
  "application": ["工程测量", "导航定位"],
  "connection": ["测量科学", "精密仪器"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G7S1_CH6_016: 角的比较与运算
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_CH6_016'),
'角可以进行大小比较和加减运算，遵循特定的运算规律',
'角的比较与运算是几何计算的基础，它将角从静态的图形概念发展为动态的数量概念。角的运算遵循代数运算的基本规律，但又有其几何意义。角的加法对应射线的连续旋转，角的减法对应旋转的逆向操作。这种运算在工程设计、物理分析、计算机图形学等领域都有重要应用。掌握角的运算培养学生的运算能力、逻辑思维和几何直觉，为学习三角函数、向量运算奠定基础。',
'[
  "角的比较：通过度数或重合法比较角的大小",
  "角的加法：相邻角的度数相加",
  "角的减法：较大角减去较小角",
  "运算规律：满足交换律、结合律",
  "几何意义：加减运算对应射线的旋转",
  "实际应用：角度计算、方向调整等"
]',
'[
  {
    "name": "角的加法",
    "formula": "∠AOC = ∠AOB + ∠BOC（OB在∠AOC内部）",
    "description": "相邻角的加法运算"
  },
  {
    "name": "角的减法",
    "formula": "∠AOB = ∠AOC - ∠BOC（OB在∠AOC内部）",
    "description": "角的减法运算"
  },
  {
    "name": "运算律",
    "formula": "∠α + ∠β = ∠β + ∠α（交换律）",
    "description": "角的运算满足基本运算律"
  }
]',
'[
  {
    "title": "角的运算应用",
    "problem": "∠AOB = 50°，∠BOC = 30°，且OB在∠AOC内部，求∠AOC",
    "solution": "∠AOC = ∠AOB + ∠BOC = 50° + 30° = 80°",
    "analysis": "相邻角可以相加得到总角"
  }
]',
'[
  {
    "concept": "几何运算",
    "explanation": "角的运算有明确的几何意义",
    "example": "角的加法对应射线的连续旋转"
  },
  {
    "concept": "运算规律",
    "explanation": "角的运算遵循代数运算规律",
    "example": "角的加法满足交换律和结合律"
  }
]',
'[
  "不理解角的加减几何意义",
  "运算时忽视角的位置关系",
  "单位不统一进行运算",
  "不验证运算结果的合理性"
]',
'[
  "几何理解：明确运算的几何意义",
  "位置明确：注意角的相对位置",
  "单位统一：运算前统一角度单位",
  "结果验证：检查运算结果是否合理"
]',
'{
  "emphasis": ["空间运算", "几何逻辑"],
  "application": ["建筑设计", "艺术构图"],
  "connection": ["空间几何", "设计思维"]
}',
'{
  "emphasis": ["精确计算", "工程应用"],
  "application": ["机械设计", "自动控制"],
  "connection": ["工程计算", "控制算法"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G7S1_CH6_017: 角的平分线
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_CH6_017'),
'角的平分线是将角分成两个相等角的射线',
'角的平分线概念体现了几何学中的对称性和平分思想，是角的内部一条特殊射线。角平分线不仅是几何学的重要概念，更在工程设计、建筑布局、艺术创作等实际应用中发挥重要作用。角平分线的性质（角平分线上的点到角两边距离相等）是几何学的重要定理，为后续学习三角形的角平分线、圆的切线等奠定基础。角平分线作图是几何作图的基本技能，培养学生的操作能力和几何直觉。',
'[
  "角平分线定义：将角分成两个相等角的射线",
  "唯一性：每个角有且仅有一条角平分线",
  "性质：角平分线上的点到角两边距离相等",
  "作图方法：用圆规和直尺作角平分线",
  "对称性：角平分线是角的对称轴",
  "实际应用：对称设计、平分布局等"
]',
'[
  {
    "name": "角平分线定义",
    "formula": "OC平分∠AOB ⟺ ∠AOC = ∠COB = ∠AOB/2",
    "description": "角平分线的数学定义"
  },
  {
    "name": "角平分线性质",
    "formula": "角平分线上的点到角两边距离相等",
    "description": "角平分线的重要几何性质"
  }
]',
'[
  {
    "title": "角平分线应用",
    "problem": "∠AOB = 60°，OC是∠AOB的平分线，求∠AOC和∠COB的度数",
    "solution": "因为OC平分∠AOB，所以∠AOC = ∠COB = ∠AOB/2 = 60°/2 = 30°",
    "analysis": "角平分线将角分成两个相等的角"
  }
]',
'[
  {
    "concept": "平分思想",
    "explanation": "角平分线将角分成两个相等部分",
    "example": "60°角的平分线分成两个30°角"
  }
]',
'[
  "认为角有多条平分线",
  "平分线作图方法错误",
  "不理解角平分线的性质"
]',
'[
  "唯一性：每个角只有一条平分线",
  "作图技能：掌握角平分线作图方法",
  "性质理解：理解距离相等的性质"
]',
'{
  "emphasis": ["对称美学", "平衡设计"],
  "application": ["建筑设计", "艺术创作"],
  "connection": ["对称艺术", "平衡美学"]
}',
'{
  "emphasis": ["精确定位", "平分技术"],
  "application": ["机械设计", "精密加工"],
  "connection": ["精密制造", "对称工程"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G7S1_CH6_018: 余角和补角
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_CH6_018'),
'余角是两个角的和为90°，补角是两个角的和为180°',
'余角和补角概念体现了角之间的特殊数量关系，是几何学中重要的角关系概念。这些概念不仅在几何学中有重要地位，在三角函数、物理学、工程学等领域也有广泛应用。余角关系体现在三角函数的互余关系中，补角关系体现在平行线、三角形等几何图形中。掌握余角补角概念培养学生的关系思维、互补意识和系统观念。',
'[
  "余角定义：两个角的和为90°",
  "补角定义：两个角的和为180°",
  "互余关系：α和β互为余角，则α+β=90°",
  "互补关系：α和β互为补角，则α+β=180°",
  "性质：同角的余角相等，同角的补角相等"
]',
'[
  {
    "name": "余角关系",
    "formula": "∠α + ∠β = 90° ⟺ ∠α和∠β互为余角",
    "description": "余角的数学定义"
  },
  {
    "name": "补角关系",
    "formula": "∠α + ∠β = 180° ⟺ ∠α和∠β互为补角",
    "description": "补角的数学定义"
  }
]',
'[
  {
    "title": "余角补角计算",
    "problem": "一个角为35°，求它的余角和补角",
    "solution": "余角=90°-35°=55°；补角=180°-35°=145°",
    "analysis": "余角用90°减去已知角，补角用180°减去已知角"
  }
]',
'[
  {
    "concept": "互补关系",
    "explanation": "两个角相加等于特定角度",
    "example": "30°和60°互为余角"
  }
]',
'[
  "混淆余角和补角的定义",
  "计算时角度相加错误"
]',
'[
  "定义明确：区分余角和补角",
  "计算准确：90°和180°要记牢"
]',
'{
  "emphasis": ["和谐关系", "平衡美学"],
  "application": ["建筑设计", "艺术构图"],
  "connection": ["比例美学", "和谐设计"]
}',
'{
  "emphasis": ["角度控制", "精确配合"],
  "application": ["机械设计", "工程测量"],
  "connection": ["精密机械", "测量工程"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'),

-- MATH_G7S1_CH6_019: 方位角
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_CH6_019'),
'方位角是表示方向的角，通常以正北方向为基准，顺时针度量',
'方位角是角在实际应用中的重要体现，它将抽象的几何概念与现实的方向导航相结合。方位角概念在航海、航空、测绘、军事等领域都有重要应用，是现代导航技术的基础。掌握方位角概念培养学生的方向意识、实际应用能力和空间定位能力。',
'[
  "方位角定义：以正北方向为基准，顺时针测量的角度",
  "基准方向：通常以正北为0°基准",
  "度量方向：一般采用顺时针方向度量",
  "角度范围：0°到360°",
  "实际应用：导航、测绘、军事、地理等"
]',
'[
  {
    "name": "方位角定义",
    "formula": "方位角 = 从正北方向顺时针转到目标方向的角度",
    "description": "方位角的基本定义"
  },
  {
    "name": "象限表示",
    "formula": "如：北偏东30°，南偏西45°等",
    "description": "用象限表示方位角"
  }
]',
'[
  {
    "title": "方位角应用",
    "problem": "A点在B点的北偏东60°方向，求从B点看A点的方位角",
    "solution": "北偏东60°表示从正北方向顺时针转60°，所以方位角为60°",
    "analysis": "方位角是以正北为基准的顺时针角度"
  }
]',
'[
  {
    "concept": "方向定位",
    "explanation": "用角度精确表示方向",
    "example": "东北方向对应45°方位角"
  }
]',
'[
  "基准方向理解错误",
  "顺时针逆时针方向混淆"
]',
'[
  "基准明确：以正北为基准方向",
  "方向统一：统一使用顺时针方向"
]',
'{
  "emphasis": ["地理知识", "方向感知"],
  "application": ["地理学习", "户外活动"],
  "connection": ["地理科学", "空间认知"]
}',
'{
  "emphasis": ["导航技术", "精确定位"],
  "application": ["GPS导航", "测绘工程"],
  "connection": ["导航科学", "测绘技术"]
}',
'1.0', true, 'zh-CN', 4.7, 'approved'),

-- MATH_G7S1_PRACTICE_003: 场地设计的数学问题
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_PRACTICE_003'),
'田径场地设计涉及几何图形、测量计算、比例关系等数学知识的综合应用',
'田径场地设计是几何知识在体育工程中的典型应用，它综合运用了直线、曲线、角度、面积等几何概念。这个项目让学生体验数学在实际工程中的应用，培养设计思维、测量技能和团队协作能力。',
'[
  "场地规划：合理布局各项目比赛区域",
  "跑道设计：400米标准跑道的几何构造",
  "角度计算：起跑线角度、弯道倾斜角等",
  "面积计算：各区域面积的测算"
]',
'[
  {
    "name": "跑道周长",
    "formula": "400米 = 2×直道长度 + 2×弯道弧长",
    "description": "标准田径场跑道周长计算"
  }
]',
'[
  {
    "title": "跑道设计计算",
    "problem": "设计一个标准400米跑道，已知内侧弯道半径36.5米，直道长度如何确定？",
    "solution": "弯道周长=2π×36.5≈229.3米，直道总长=400-229.3=170.7米，每段直道=85.35米",
    "analysis": "先算弯道周长，再用总周长减去弯道周长得直道长度"
  }
]',
'[
  {
    "concept": "工程设计",
    "explanation": "将数学知识应用于实际工程",
    "example": "用几何知识设计体育场地"
  }
]',
'[
  "忽视实际工程约束",
  "数学计算不够精确"
]',
'[
  "实际结合：考虑工程实际约束",
  "计算精确：保证数学计算准确"
]',
'{
  "emphasis": ["体育文化", "设计美学"],
  "application": ["体育场设计", "艺术规划"],
  "connection": ["体育文化", "空间艺术"]
}',
'{
  "emphasis": ["工程应用", "精确设计"],
  "application": ["体育工程", "场馆建设"],
  "connection": ["工程技术", "建筑设计"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G7S1_PRACTICE_004: 实际测量与计算
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S1_PRACTICE_004'),
'通过实际测量校园场地，运用数学知识进行计算和设计验证',
'实际测量与计算是理论联系实际的重要环节，将抽象的数学概念转化为具体的操作技能。通过测量校园现有场地，学生掌握测量工具使用方法，体验数学在实际工程中的应用价值。',
'[
  "测量工具：卷尺、测距仪、量角器等",
  "测量方法：长度测量、角度测量、面积计算",
  "数据记录：准确记录测量数据",
  "误差分析：分析测量误差来源"
]',
'[
  {
    "name": "测量精度",
    "formula": "相对误差 = |测量值-真实值|/真实值 × 100%",
    "description": "测量精度的计算方法"
  }
]',
'[
  {
    "title": "测量验证",
    "problem": "测量学校操场长度，用两种方法得到102米和98米，如何处理？",
    "solution": "计算平均值=(102+98)/2=100米，相对误差=4/100=4%",
    "analysis": "多次测量求平均值可以减小随机误差"
  }
]',
'[
  {
    "concept": "实践验证",
    "explanation": "通过实际操作验证理论计算",
    "example": "实地测量验证设计方案的可行性"
  }
]',
'[
  "测量方法不当",
  "数据记录不准确"
]',
'[
  "方法规范：采用正确的测量方法",
  "记录准确：认真记录测量数据"
]',
'{
  "emphasis": ["实践技能", "团队协作"],
  "application": ["实践活动", "团队项目"],
  "connection": ["实践教育", "综合素质"]
}',
'{
  "emphasis": ["测量技术", "工程实践"],
  "application": ["工程测量", "技术应用"],
  "connection": ["工程技术", "实践技能"]
}',
'1.0', true, 'zh-CN', 4.7, 'approved');

