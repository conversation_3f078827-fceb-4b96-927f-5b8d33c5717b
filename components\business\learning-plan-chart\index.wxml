<view class="learning-plan-chart {{useCard ? 'card-style' : ''}}">
  <!-- 标题区域 -->
  <view class="chart-header" wx:if="{{title || showDate}}">
    <view class="chart-title" wx:if="{{title}}">{{title}}</view>
    <view class="chart-date" wx:if="{{showDate && dateFormatted}}">{{dateFormatted}}</view>
  </view>
  
  <!-- 主体内容区域 -->
  <view class="chart-content">
    <!-- 进度环 -->
    <view class="progress-circle-container">
      <circular-progress 
        progress="{{completion}}" 
        size="{{progressSize}}" 
        color="{{primaryColor}}" 
        useGradient="{{useGradient}}"
        startColor="{{startColor}}"
        endColor="{{endColor}}"
        label="完成率">
      </circular-progress>
    </view>
    
    <!-- 数据统计 -->
    <view class="stats-container">
      <!-- 任务完成情况 -->
      <view class="stats-item">
        <view class="stats-label">任务完成</view>
        <view class="stats-value" style="color: {{primaryColor}}">{{completedTasks}}/{{totalTasks}}</view>
      </view>
      
      <!-- 学习天数 -->
      <view class="stats-item">
        <view class="stats-label">学习天数</view>
        <view class="stats-value">{{studyDays}}</view>
      </view>
      
      <!-- 每日学习时间 -->
      <view class="stats-item">
        <view class="stats-label">计划用时</view>
        <view class="stats-value">{{dailyTime}}分钟/天</view>
      </view>
      
      <!-- 今日学习时间 -->
      <view class="stats-item" wx:if="{{todayTime > 0}}">
        <view class="stats-label">今日学习</view>
        <view class="stats-value">{{todayTime}}分钟</view>
      </view>
    </view>
  </view>
</view> 