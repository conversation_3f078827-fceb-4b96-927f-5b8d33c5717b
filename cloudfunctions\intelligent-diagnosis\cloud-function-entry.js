// 微信小程序云函数专用入口文件
// 简化版本，避免代码检查问题

const cloud = require('wx-server-sdk');

// 初始化云开发
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

/**
 * 云函数主入口
 * @param {Object} event 云函数入参
 * @param {Object} context 云函数上下文
 */
exports.main = async (event, context) => {
  console.log('🚀 智能诊断云函数被调用，参数:', event);
  
  try {
    const { action = 'comprehensive_diagnosis', data = {} } = event;
    
    // 导入主要的诊断模块
    const mainFunction = require('./index.js');
    
    // 调用主诊断函数
    const result = await mainFunction.main(event, context);
    
    console.log('✅ 云函数执行成功');
    return result;
    
  } catch (error) {
    console.error('❌ 云函数执行失败:', error);
    return {
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    };
  }
};

/**
 * 本地测试函数（仅供开发使用）
 */
async function localTest() {
  console.log('🧪 开始本地测试...');
  
  const testEvent = {
    action: 'comprehensive_diagnosis',
    data: {
      studentId: 'test-student-001',
      problemAnswers: [
        { problemId: 'math_001', answer: 'correct', timeSpent: 30 },
        { problemId: 'math_002', answer: 'incorrect', timeSpent: 45 }
      ],
      difficultyLevel: 'medium'
    }
  };
  
  try {
    const result = await exports.main(testEvent, {});
    console.log('✅ 测试结果:', result);
    return { success: true, result };
  } catch (error) {
    console.error('❌ 测试失败:', error);
    return { success: false, error: error.message };
  }
}

// 导出本地测试函数
module.exports = { 
  main: exports.main,
  localTest 
}; 