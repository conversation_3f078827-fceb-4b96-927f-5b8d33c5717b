Component({
  properties: {
    formula: {
      type: String,
      value: ''
    },
    size: {
      type: String,
      value: 'normal' // small, normal, large
    },
    color: {
      type: String,
      value: '#333333'
    },
    inline: {
      type: Boolean,
      value: false
    }
  },

  data: {
    renderFormula: '',
    processedFormula: ''
  },

  observers: {
    'formula': function(formula) {
      if (formula) {
        this.processFormula(formula);
      }
    }
  },

  methods: {
    processFormula(formula) {
      // 处理公式，确保格式正确
      let processedFormula = formula.trim();
      
      // 如果不是以$包围的，自动添加
      if (!this.data.inline && !processedFormula.startsWith('$$')) {
        processedFormula = '$$' + processedFormula;
      }
      
      if (!this.data.inline && !processedFormula.endsWith('$$')) {
        processedFormula = processedFormula + '$$';
      }
      
      if (this.data.inline && !processedFormula.startsWith('$')) {
        processedFormula = '$' + processedFormula;
      }
      
      if (this.data.inline && !processedFormula.endsWith('$')) {
        processedFormula = processedFormula + '$';
      }
      
      this.setData({
        renderFormula: formula,
        processedFormula: processedFormula
      });
    },

    handleTap() {
      this.triggerEvent('tap', {
        formula: this.data.formula
      });
      
      // 点击公式时放大显示
      wx.showModal({
        title: '数学公式',
        content: this.data.renderFormula,
        showCancel: false
      });
    }
  }
}); 