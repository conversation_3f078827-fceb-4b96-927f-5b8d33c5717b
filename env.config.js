// 统一环境配置管理
const ENV_CONFIG = {
  // 云环境配置
  CLOUD_ENV_ID: 'cloud1-0g3pg16f43f63333',
  CLOUD_REGION: 'ap-guangzhou',
  
  // 数据库配置 (建议使用云函数环境变量)
  DATABASE: {
    POSTGRESQL: {
      // 以下配置仅作为默认值，实际生产环境请使用云函数环境变量:
      // host: process.env.host
      // port: process.env.port 
      // database: process.env.db
      // user: process.env.name
      // password: process.env.password
      host: '**************',
      port: 5434,
      database: 'k12',
      user: 'postgres',
      password: 'difyai123456'
    }
  },
  
  // 云函数配置
  CLOUD_FUNCTIONS: {
    timeout: 60,
    memorySize: 512,
    runtime: 'Nodejs16.13',
    installDependency: true
  }
};

// 导出配置
module.exports = ENV_CONFIG;

// 兼容微信小程序环境
if (typeof exports !== 'undefined') {
  exports.ENV_CONFIG = ENV_CONFIG;
} 