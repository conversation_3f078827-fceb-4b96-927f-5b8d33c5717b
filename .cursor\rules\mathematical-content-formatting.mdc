---
description: 
globs: 
alwaysApply: false
---
# 数学内容格式化与数据库字段处理规范

本规范适用于K12数学学习智能体微信小程序项目中所有包含数学表达式的数据库脚本和教育内容，特别是[grade_12_2025_second_semester.sql](mdc:database_scripts/grade_scripts/grade_12_2025_second_semester.sql)等数据库文件。

## 📋 目录
1. [关键问题解决方案](mdc:#关键问题解决方案)
2. [内容保护原则](mdc:#内容保护原则)
3. [E前缀应用规则](mdc:#e前缀应用规则)
4. [PostgreSQL转义规范](mdc:#postgresql转义规范)
5. [KaTeX语法规范](mdc:#katex语法规范)
6. [数学表达式检测方法](mdc:#数学表达式检测方法)
7. [度数符号强制KaTeX规范](mdc:#度数符号强制katex规范)
8. [K12教育内容规范](mdc:#k12教育内容规范)
9. [质量控制流程](mdc:#质量控制流程)
10. [工具化检查方法](mdc:#工具化检查方法)
11. [常见错误与解决方案](mdc:#常见错误与解决方案)
12. [最佳实践指南](mdc:#最佳实践指南)

## 🚨 关键问题解决方案

### 问题一：包含转义符的字段缺少E包裹
**现象**: SQL脚本中包含转义符但未使用E前缀，导致转义错误
```sql
-- ❌ 错误写法
VALUES ('{"text": "$$\\Delta=4a^2-4$$"}');

-- ✅ 正确写法  
VALUES (E'{"text": "$$\\\\Delta=4a^2-4$$"}');
```

### 问题二：PostgreSQL E字符串转义不足
**现象**: f'(x)显示为f(x)，\Delta显示异常
```sql
-- ❌ 错误写法（转义不足）
E'f\'(x)>0 \\Rightarrow f(x)\\uparrow'
E'\\Delta=4a^2-4'

-- ✅ 正确写法（双重转义）
E'f\\'(x)>0 \\\\Rightarrow f(x)\\\\uparrow'  
E'\\\\Delta=4a^2-4'
```

**根本原因**: PostgreSQL E字符串将第一个`\`视为转义符，需要双重转义才能正确显示数学符号。

### 问题三：数学表达式未使用KaTeX语法
**现象**: 直接使用数学文本而非KaTeX语法
```sql
-- ❌ 错误写法（未使用KaTeX语法）
E'"content": "函数f(x+y)+f(x-y)=2f(x)f(y)的性质"'

-- ✅ 正确写法（使用KaTeX语法）
E'"content": "函数$f(x+y)+f(x-y)=2f(x)f(y)$的性质"'
```

**强制要求**: 所有数学表达式、变量、函数必须使用KaTeX语法包裹。

### 问题四：E前缀判断标准混淆  
**现象**: 错误地以$符号而非转义符作为E前缀判断标准
```sql
-- ❌ 错误理解：认为包含$符号就需要E前缀
E'{"variable": "变量$x的值"}'          -- 无转义符，错误使用E前缀
E'{"price": "商品价格$100"}'          -- 无转义符，错误使用E前缀  

-- ✅ 正确理解：仅以转义符\为判断标准
'{"variable": "变量$x的值"}'          -- 无转义符，正确不用E前缀
'{"price": "商品价格$100"}'          -- 无转义符，正确不用E前缀
E'{"formula": "$x=\\\\frac{1}{2}$"}'  -- 含转义符\，正确使用E前缀
```

**🚨 核心判断原则**: E前缀专门用于PostgreSQL处理反斜杠转义符'\'，与$符号完全无关。
**绝对标准**: 有转义符\ → 用E前缀，无转义符\ → 禁用E前缀

## 🚨 核心规则三大原则（严格执行）

### 原则一：E前缀使用规则
```
包含转义符需要处理的 → 必须用E包裹
其他情况 → 不需要E包裹

判断标准：字段内容是否包含转义符'\'
- 有转义符'\' → 必须使用E前缀  
- 无转义符'\' → 严禁使用E前缀
```

### 原则二：强制KaTeX语法
```
所有数学公式 → 必须用KaTeX语法处理
所有数学表达式、变量、函数、符号 → 无例外使用KaTeX包裹

包括但不限于：
- 函数：f(x) → $f(x)$
- 变量：x, a → $x$, $a$ 
- 符号：Δ, π → $\\Delta$, $\\pi$
- 等式：x=1 → $x=1$
- 不等式：a>b, x≥3, k≠1 → $a>b$, $x≥3$, $k≠1$
- 计算式：4-1=3, 5×10000 → $4-1=3$, $5×10000$
- 数学关系：AB=5, x-3≥0 → $AB=5$, $x-3≥0$
- 取值范围：x≥3, -1<x≤2 → $x≥3$, $-1<x≤2$
```

**🚨 绝对禁止的错误做法**：
```
❌ 直接使用数学表达式：a>b, x=2, k≠1
❌ 上标字符：x²而非$x^2$
❌ 未包裹的运算：5×3而非$5×3$
❌ 裸露的变量：变量x而非变量$x$
❌ 未包裹的不等式：x-3≥0, -1<x≤2
❌ 未包裹的计算：4-1=3, 5×10000=50000
❌ 未包裹的关系：AB=5, AC=6
❌ 未包裹的参数：当k≠1时, a>0时
```

**🔧 强制检测规则**：
以下模式在文本中出现时必须立即修正为KaTeX格式：
- 变量关系：`a>b` → `$a>b$`
- 等式：`x=2` → `$x=2$`  
- 不等式：`k≠1` → `$k≠1$`
- 取值范围：`x≥3` → `$x≥3$`
- 计算表达式：`4-1=3` → `$4-1=3$`
- 乘法运算：`5×10000` → `$5×10000$`
- 几何关系：`AB=5` → `$AB=5$`
- 条件表达式：`x-3≥0` → `$x-3≥0$`

### 原则三：$...$和$$...$$严格使用规则
```
按优先级从高到低执行：

$$...$$（显示数学模式）用于：
1. 正式数学陈述（定理/定律/定义/引理）
2. 复杂数学结构（矩阵/积分/求和/极限）  
3. 长公式（含分数且字符≥25 或 运算符≥3个）
4. 参数方程组（2+个参数式，字符≤35，无复杂符号）

$...$（行内数学模式）用于：
1. 简单数学对象（单变量/数字/坐标点）
2. 行内短公式（字符≤20且无分式/积分/多行结构）
3. 希腊字母表示

默认：保障可读性使用$...$
```

## 1. 内容保护原则

### 🚫 禁止操作
- **禁止修改字段结构与格式**：保持SQL INSERT语句的原有结构
- **禁止`&`符号**：mp-html组件不识别，使用空格符号分隔
- **禁止在普通字符串前添加E前缀**：只在必要时使用
- **禁止删除或修改字段顺序**：保持数据库表结构一致性
- **禁止修改JSON字段结构**：保持前端解析兼容性

### ✅ 保护要求
- 保持原有的SQL字段结构不变
- 维护中文教育内容的完整性和准确性
- 确保数据库语法的正确性
- 保持K12教育内容的专业性和适用性
- 维护小程序前端渲染的兼容性

## 2. E前缀应用规则

### 🚨 **绝对严格规则（2024年12月最新修正）**
```
🚨 唯一判断原则：包含反斜杠转义符'\'的 → 必须使用E前缀
              不含反斜杠转义符'\'的 → 严禁使用E前缀
              
执行标准：100%严格执行，仅以转义符\为唯一判断标准
```

**最新判断标准（2024年12月修正）**：
1. **含有反斜杠转义符'\'的字段** → **必须**使用E前缀
2. **不含反斜杠转义符'\'的字段** → **严禁**使用E前缀  
3. **唯一检查方法**：在字段内容中搜索反斜杠`\`字符
4. **移除$符号判断**：不再以$符号作为E前缀判断依据
5. **执行要求**：每个字段都必须严格遵守此规则
6. **PostgreSQL原理**：E前缀是PostgreSQL脚本入库时处理转义符的专用标记
7. **重要修正**：包含$符号的数学公式字段，仅当同时包含转义符\时才使用E前缀

### 🔍 **快速判断方法**
```javascript
/**
 * 严格判断字段内容是否需要E前缀 - 唯一标准
 * @param {string} fieldContent - 字段内容
 * @returns {boolean} - 是否需要E前缀
 */
function needsEPrefix(fieldContent) {
    // 🚨 唯一判断标准：是否包含转义符'\'
    return fieldContent.includes('\\');
}

/**
 * E前缀使用验证函数
 * @param {string} sqlLine - SQL行内容
 * @returns {object} - 验证结果
 */
function validateEPrefixUsage(sqlLine) {
    const hasEPrefix = sqlLine.trim().includes("E'");
    const fieldContent = sqlLine.match(/(E?)'([^']*)'/) || ['', '', ''];
    const content = fieldContent[2];
    
    const shouldHaveE = content.includes('\\');
    
    if (shouldHaveE && !hasEPrefix) {
        return {
            isValid: false,
            error: '❌ 包含转义符但缺少E前缀',
            fix: '添加E前缀',
            line: sqlLine
        };
    }
    
    if (!shouldHaveE && hasEPrefix) {
        return {
            isValid: false,
            error: '❌ 不含转义符却使用了E前缀',
            fix: '移除E前缀',
            line: sqlLine
        };
    }
    
    return {
        isValid: true,
        message: '✅ E前缀使用正确'
    };
}
```

### 应用原则与决策树（简化版）
```mermaid
graph TD
    A[检查字段内容] --> B{包含转义符'\'?}
    B -->|是| C[✅ 必须使用E前缀]
    B -->|否| D[❌ 严禁使用E前缀]
    
    C --> E[检查是否使用E前缀]
    E -->|已使用| F[✅ 正确]
    E -->|未使用| G[❌ 错误：需要添加E前缀]
    
    D --> H[检查是否使用E前缀]
    H -->|未使用| I[✅ 正确]
    H -->|已使用| J[❌ 错误：需要移除E前缀]
```

### ✅ 必须使用E前缀的情况（含转义符）
```sql
-- 包含转义符的字段（需要E前缀）
E'函数$f(x)=\\\\frac{1}{x}$的导数'
E'正弦定理：$$\\\\frac{a}{\\\\sin A}=2R$$'
E'当$\\\\Delta>0$时方程有两个实根'
E'求导得$f\\'(x)=3x^2-6x$'
E'极限：$\\\\lim_{x \\\\to 0}$'
E'不等式：$x \\\\geq 0$'
E'三角函数：$\\\\sin x$, $\\\\cos x$'
E'对数函数：$\\\\log_a x$'
E'{"basicQuestions": [{"id": "q1", "content": "反比例函数的一般形式是什么？", "answer": "$y=\\\\frac{k}{x}$（$k\\\\neq0$）"}]}'
```

### ❌ 严禁使用E前缀的情况（无转义符）
```sql
-- 纯中文文本和常规符号（不含转义符）
'{"basicQuestions": [{"id": "q1", "content": "什么是反比例函数？", "answer": "形如y=k/x的函数，k为非零常数"}]}'
'{"sections": [{"title": "建模原理", "text": "实际问题→数学语言→函数模型"}]}'
'["综合应用困难", "建模能力不足", "问题分析不清"]'
'["强化建模思维", "构建知识网络", "培养系统思维"]'
'["基础知识", "解题方法", "典型例题", "练习巩固"]'
'["函数导数综合", "数学建模", "问题解决", "综合应用"]'
'{"content": "通过练习提高解题能力"}'
'["投影的概念和分类"]'
'["理解投影本质", "认识投影类型", "培养空间想象能力"]'
-- 包含$符号但不含转义符的情况
'{"content": "价格为$100的商品"}'
'{"note": "收入$income，支出$expense"}'
```

### 🔧 检查工具命令
```bash
# 检查错误使用E前缀的情况（不含转义符却用了E前缀）
grep -n "E'[^']*'" file.sql | grep -v '\\'

# 检查遗漏E前缀的情况（含转义符但没用E前缀）  
grep -n "'[^']*\\[^']*'" file.sql | grep -v "^.*E'"

# 🚨 新增：检查begin环境换行符是否使用12个反斜杠
grep -n "\\\\begin{[^}]*}" file.sql | grep -v "\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\'"

# 检查8个反斜杠的换行符（需要修复为12个）
grep -n "\\\\\\\\\\\\\\\\[^\\\\]" file.sql

# 完整验证脚本
awk '/E'\''/{if(!/\\/) print "❌ 错误E前缀: " NR ": " $0} /'\''.*\\.*'\''/{if(!/^.*E'\''/) print "❌ 缺少E前缀: " NR ": " $0}' file.sql
```

## 3. PostgreSQL转义规范

### 🚨 **核心转义规则（2024年12月最新修订）**

#### **规则1：KaTeX命令使用4个反斜杠**
```
❌ 错误：使用2个或8个反斜杠
\\frac{a}{b}         → 错误：2个反斜杠
\\\\\\\\frac{a}{b}   → 错误：8个反斜杠

✅ 正确：必须使用4个反斜杠
\\\\frac{a}{b}       → 正确：4个反斜杠

KaTeX原始：\frac, \sqrt, \text, \sin, \alpha, \begin, \end
PostgreSQL：\\\\frac, \\\\sqrt, \\\\text, \\\\sin, \\\\alpha, \\\\begin, \\\\end
```

#### **规则2：SQL单引号转义使用1个反斜杠（重要修正）**
```
❌ 错误：使用双重转义
导数：f\\'\\'(x), f\\'\\'\\'\\'(x)

✅ 正确：仅使用1个反斜杠转义
导数：f\\'(x), f\\'\\'(x)

🚨 关键原则：
- KaTeX命令需要4个反斜杠：\\\\frac, \\\\sin, \\\\alpha
- SQL单引号只需要1个反斜杠：\\'
- 不要混淆这两种不同的转义需求
```

**具体对比表：**
| 内容类型 | 原始形式 | PostgreSQL E字符串 | 说明 |
|---|---|---|---|
| **KaTeX命令** | `\frac{a}{b}` | `\\\\frac{a}{b}` | 需要4个反斜杠 |
| **单引号** | `f'(x)` | `f\\'(x)` | 仅需1个反斜杠 |
| **组合使用** | `\frac{d}{dx}f'(x)` | `\\\\frac{d}{dx}f\\'(x)` | 分别处理 |

#### **规则3：数学符号连写禁止规则**
```
❌ 严重错误：连写会被误识别为$$显示公式
$>$$0$$>$    → 会被mp-html误解析为 $>$ $$0$$ $>$

✅ 正确：必须分开书写
$>$ $0$ $>$  → 每个数学对象独立包裹
```

#### **规则4：begin环境换行符使用12个反斜杠**
```
KaTeX原始：\\
PostgreSQL：\\\\\\\\\\\\\\\\\\\\\\\\
```

### 🚨 **关键转义区别总结（2024年12月重要更新）**

**三种不同的转义需求，切勿混淆：**

1. **KaTeX数学命令 → 4个反斜杠**
   ```
   \\frac → \\\\frac
   \\sin → \\\\sin  
   \\alpha → \\\\alpha
   ```

2. **SQL单引号 → 1个反斜杠**
   ```
   f'(x) → f\\'(x)
   f''(x) → f\\'\\'(x)
   ```

3. **begin环境换行符 → 12个反斜杠**
   ```
   \\\\ → \\\\\\\\\\\\\\\\\\\\\\\\
   ```

**❌ 常见错误：** 将单引号也用4个反斜杠转义
**✅ 正确做法：** 根据内容类型选择正确的转义数量

### 🔥 **完整转义对照表（最新修订版）**

| 数学内容 | KaTeX原始 | PostgreSQL E字符串 | 说明 | 备注 |
|---|-----|----|---|----|
| **普通KaTeX语法（4个反斜杠）** |
| 分数 | `\frac{a}{b}` | `\\\\frac{a}{b}` | 分式表达 | ⚠️ 不是8个反斜杠 |
| 根号 | `\sqrt{x}` | `\\\\sqrt{x}` | 根式表达 | ⚠️ 不是8个反斜杠 |
| 文本 | `\text{ABC}` | `\\\\text{ABC}` | 数学文本 | ⚠️ 不是8个反斜杠 |
| 三角函数 | `\sin, \cos` | `\\\\sin, \\\\cos` | 数学函数 | ⚠️ 不是8个反斜杠 |
| 希腊字母 | `\alpha, \pi` | `\\\\alpha, \\\\pi` | 希腊符号 | ⚠️ 不是8个反斜杠 |
| 运算符 | `\geq, \leq` | `\\\\geq, \\\\leq` | 关系符号 | ⚠️ 不是8个反斜杠 |
| 环境开始 | `\begin{cases}` | `\\\\begin{cases}` | 环境命令 | ⚠️ 不是8个反斜杠 |
| 环境结束 | `\end{cases}` | `\\\\end{cases}` | 环境命令 | ⚠️ 不是8个反斜杠 |
| **度数符号（4个反斜杠 - 重要修正）** |
| 度数符号 | `^{\circ}` | `^{\\\\circ}` | 角度单位 | 🚨 **统一4个反斜杠标准** |
| **单引号转义（1个反斜杠）** |
| 一阶导数 | `f'(x)` | `f\\'(x)` | 导数符号 | **仅1个反斜杠转义** |
| 二阶导数 | `f''(x)` | `f\\'\\'(x)` | 高阶导数 | **每个单引号1个反斜杠** |
| **数学符号连写禁止（重要）** |
| 比较符号 | `正数>0>负数` | `正数$>$ $0$ $>$负数` | 大小关系 | 🚨 禁止连写$>$$0$$>$ |
| 不等式链 | `a<b<c` | `$a<b<c$` 或 `$a$ $<$ $b$ $<$ $c$` | 连续不等式 | 🚨 避免误解析为$$ |
| **begin换行符（12个反斜杠）** |
| 换行符 | `\\\\` | `\\\\\\\\\\\\\\\\\\\\\\\\` | 仅在begin环境内 | 特殊环境专用 |

### 📝 标准格式示例

#### **普通数学表达式（4个反斜杠）**
```sql
-- 分数
E'$\\\\frac{x^2+1}{x-1}$'

-- 根号
E'$\\\\sqrt{x^2+y^2}$'

-- 函数
E'$\\\\sin x + \\\\cos x$'

-- 希腊字母
E'$\\\\alpha + \\\\beta = \\\\pi$'
```

#### **导数表达式（单引号1个反斜杠转义）**
```sql
-- 一阶导数
E'$f\\'(x) = 2x + 1$'

-- 二阶导数
E'$f\\'\\'(x) = 2$'

-- 混合表达式
E'$\\\\frac{dy}{dx} = f\\'(x)$'
```

#### **begin环境（换行符12个反斜杠）**
```sql
-- 方程组
E'$$\\\\begin{cases} x+y=1 \\\\\\\\\\\\\\\\\\\\\\\\ x-y=3 \\\\end{cases}$$'

-- 矩阵
E'$$\\\\begin{pmatrix} 1 & 2 \\\\\\\\\\\\\\\\\\\\\\\\ 3 & 4 \\\\end{pmatrix}$$'

-- 分段函数
E'$$f(x) = \\\\begin{cases} x, & x \\\\geq 0 \\\\\\\\\\\\\\\\\\\\\\\\ -x, & x < 0 \\\\end{cases}$$'
```

### ❌ 常见错误对照

| 错误类型 | 错误写法 | 正确写法 |
|---------|----------|----------|
| **KaTeX语法错误** |
| 2个反斜杠 | `\\frac{a}{b}` | `\\\\frac{a}{b}` |
| 6个反斜杠 | `\\\\\\frac{a}{b}` | `\\\\frac{a}{b}` |
| 8个反斜杠 | `\\\\\\\\frac{a}{b}` | `\\\\frac{a}{b}` |
| **单引号转义错误** |
| 未转义 | `f'(x)` | `f\\'(x)` |
| 双重转义 | `f\\\\'(x)` | `f\\'(x)` |
| **换行符错误** |
| 4个反斜杠 | `\\\\\\\\ ` | `\\\\\\\\\\\\\\\\\\\\\\\\` |
| 8个反斜杠 | `\\\\\\\\\\\\\\\\ ` | `\\\\\\\\\\\\\\\\\\\\\\\\` |
| 16个反斜杠 | `\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\` | `\\\\\\\\\\\\\\\\\\\\\\\\` |

### 🔧 快速检测方法

#### **检测错误的反斜杠数量**
```bash
# 检测KaTeX语法错误（应该是4个反斜杠）
grep -n "\\\\text{" file.sql    # 错误：2个反斜杠
grep -n "\\\\\\\\\\\\text{" file.sql  # 错误：6个反斜杠  
grep -n "\\\\\\\\\\\\\\\\text{" file.sql  # 错误：8个反斜杠

# 检测导数转义错误
grep -n "f'(" file.sql          # 错误：未转义
grep -n "f\\\\\\\\'(" file.sql       # 错误：过度转义

# 检测换行符错误
grep -n "\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\ " file.sql  # 错误：过度转义
```

#### **自动修复模式**
```javascript
// 修复KaTeX语法（统一为4个反斜杠）
content = content.replace(/([^\\])\\([a-zA-Z]+\{)/g, '$1\\\\\\\\$2');

// 修复导数转义（统一为1个反斜杠）
content = content.replace(/f''/g, "f\\\\'\\\\''");
content = content.replace(/f'/g, "f\\\\'");

// 修复begin换行符（统一为12个反斜杠）
content = content.replace(/(\\\\begin\{[^}]+\}[^\\]*)\\\\\\\\([^\\])/g, '$1\\\\\\\\\\\\\\\\\\\\\\\\$2');
```

## 4. KaTeX语法规范

### 🚨 数学表达式强制KaTeX化规范

**强制要求**: 所有数学表达式、变量、函数、符号必须使用KaTeX语法包裹，无例外。

#### 必须KaTeX化的内容类型
1. **所有数学函数表达式**
```sql
-- ❌ 错误：直接使用数学文本
E'"content": "函数f(x)的性质"'
E'"content": "g(x)=x^2+1"'

-- ✅ 正确：必须用KaTeX语法包裹
E'"content": "函数$f(x)$的性质"'
E'"content": "$g(x)=x^2+1$"'
```

2. **所有数学变量和参数**
```sql
-- ❌ 错误
E'"content": "参数a的取值范围"'
E'"content": "变量x的定义域"'

-- ✅ 正确
E'"content": "参数$a$的取值范围"'
E'"content": "变量$x$的定义域"'
```

3. **所有数学符号和运算**
```sql
-- ❌ 错误
E'"content": "当Δ>0时"'
E'"content": "求f\'(x)"'

-- ✅ 正确
E'"content": "当$\\\\Delta>0$时"'
E'"content": "求$f\\'(x)$"'
```

### 5. 数学表达式格式规则（优先级从高到低）

#### **必须使用 `$$...$$` 的情况**

##### 1. **正式数学陈述**  
- **定义/定理/定律/引理的完整表述**  
```sql
-- 牛顿第二定律
E'$$F=ma\\\\quad\\\\text{(牛顿第二定律)}$$'
-- 欧拉公式  
E'$$e^{i\\\\pi}+1=0$$'
-- 勾股定理
E'$$a^2+b^2=c^2\\\\quad\\\\text{(勾股定理)}$$'
```

- **定义、命题的规范表达式**  
```sql
-- 函数连续性定义
E'$$\\\\lim_{x \\\\to x_0} f(x) = f(x_0)\\\\quad\\\\text{(连续性定义)}$$'
-- 导数定义
E'$$f\\'(x) = \\\\lim_{h \\\\to 0} \\\\frac{f(x+h)-f(x)}{h}\\\\quad\\\\text{(导数定义)}$$'
```

##### 2. **复杂数学结构**  
- **多行公式/矩阵/行列式**  
```sql
-- 矩阵表示
E'$$\\\\begin{bmatrix} a & b \\\\\\\\\\\\\\\\ c & d \\\\end{bmatrix}$$'
-- 行列式
E'$$\\\\begin{vmatrix} a & b \\\\\\\\\\\\\\\\ c & d \\\\end{vmatrix} = ad - bc$$'
```

- **积分/求和/极限运算**  
```sql
-- 定积分
E'$$\\\\int_a^b f(x)dx$$'
-- 求和公式
E'$$\\\\sum_{i=1}^n i = \\\\frac{n(n+1)}{2}$$'
-- 极限表达式
E'$$\\\\lim_{x \\\\to \\\\infty} \\\\frac{1}{x} = 0$$'
```

##### 3. **长公式标准**（满足任一条件）  
- **含分数运算且字符数≥25**  
```sql
-- 椭圆方程（总字符数≥25且含分数）
E'$$\\\\frac{x^2}{a^2}+\\\\frac{y^2}{b^2}=1$$'
-- 双曲线方程
E'$$\\\\frac{x^2}{a^2}-\\\\frac{y^2}{b^2}=1$$'
```

- **含多个运算符(≥3个)**  
```sql
-- 二项式定理（含4个运算符：+, ^, +, +）
E'$$(x+y)^3 = x^3 + 3x^2y + 3xy^2 + y^3$$'
-- 三角恒等式（含多个运算符）
E'$$\\\\sin^2\\\\theta + \\\\cos^2\\\\theta = 1$$'
```

##### 4. **参数方程组**（需同时满足）  
- **由2+个参数式组成**（如$x=f(t),y=g(t)$）  
- **每个表达式为"变量=函数(参数)"形式**  
- **总字符数≤35且无复杂符号**  
```sql
-- 椭圆参数方程（符合所有条件）
E'$$x=a\\\\cos t,\\\\ y=b\\\\sin t$$'
-- 抛物线参数方程（简单形式）
E'$$x=2pt^2,\\\\ y=2pt$$'
-- 直线参数方程（标准形式）
E'$$x=x_0+at,\\\\ y=y_0+bt$$'
```

#### **必须使用 `$...$` 的情况**

##### 1. **简单数学对象**  
- **单变量/数字**：$x$, $\\pi$  
```sql
-- 单变量
E'变量$x$的取值范围'
E'常数$\\\\pi$的近似值'
E'参数$a$的讨论'
```

- **坐标点**：$P(1,-2)$, $F(\\frac{p}{2},0)$
```sql
-- 坐标表示
E'点$P(1,-2)$的坐标'
E'焦点$F(\\\\frac{p}{2},0)$的位置'
E'顶点$A(0,0)$, $B(1,1)$'
```

##### 2. **行内短公式**（同时满足）  
- **字符数≤20且无分式/积分**  
- **不包含多行结构**  
  例："斜率$k=\\tan\\theta$"
```sql
-- 简单等式（字符数≤20）
E'斜率$k=\\\\tan\\\\theta$'
E'面积$S=\\\\frac{1}{2}ab$'
E'周长$C=2\\\\pi r$'

-- 简单不等式
E'当$x>0$时函数递增'
E'若$a\\\\neq 0$则有解'
```

##### 3. **希腊字母表示**  
$\\alpha$, $\\beta$, $\\gamma$
```sql
-- 希腊字母参数
E'角度$\\\\alpha$, $\\\\beta$, $\\\\gamma$'
E'参数$\\\\lambda$的取值'
E'变量$\\\\theta$的范围'
```

#### **严格执行的默认处理流程**
```python
def format_math(expr):
    if needs_display_math(expr):  # 满足$$条件
        return f"$${expr}$$" 
    elif needs_inline_math(expr): # 满足$条件
        return f"${expr}$"
    else:                         # 保障可读性默认
        return f"${expr}$"
```

#### **数学表达式格式判断函数实现**
```javascript
/**
 * 数学表达式格式化函数 - 严格按照优先级规则执行
 * @param {string} expr - 数学表达式内容
 * @returns {string} - 格式化后的表达式
 */
function formatMath(expr) {
    // 检查是否需要显示数学模式 ($$...$$)
    if (needsDisplayMath(expr)) {
        return `$$${expr}$$`;
    }
    // 检查是否需要行内数学模式 ($...$)
    else if (needsInlineMath(expr)) {
        return `$${expr}$`;
    }
    // 默认使用行内模式确保可读性
    else {
        return `$${expr}$`;
    }
}

/**
 * 判断是否需要显示数学模式 - 严格按照优先级规则
 */
function needsDisplayMath(expr) {
    // 1. 正式数学陈述检查（最高优先级）
    if (expr.includes('\\\\text{') && (
        expr.includes('定理') || expr.includes('定律') || 
        expr.includes('定义') || expr.includes('引理')
    )) {
        return true;
    }
    
    // 2. 复杂数学结构检查
    if (expr.includes('\\\\begin{') || expr.includes('\\\\int') || 
        expr.includes('\\\\sum') || expr.includes('\\\\lim') ||
        expr.includes('\\\\prod') || expr.includes('matrix')) {
        return true;
    }
    
    // 3. 长公式检查 - 含分数且字符数≥25
    if (expr.includes('\\\\frac') && expr.length >= 25) {
        return true;
    }
    
    // 4. 多运算符检查 - ≥3个主要运算符
    const operatorMatches = expr.match(/[+\-*/=<>]/g);
    if (operatorMatches && operatorMatches.length >= 3) {
        return true;
    }
    
    // 5. 参数方程组检查 - 需要同时满足3个条件
    if (expr.includes(',') && expr.length <= 35) {
        const equationParts = expr.split(/[,，]/);
        if (equationParts.length >= 2 && 
            equationParts.every(part => part.includes('='))) {
            // 检查是否为"变量=函数(参数)"形式且无复杂符号
            const hasComplexSymbols = expr.includes('\\\\frac') || 
                                    expr.includes('\\\\int') || 
                                    expr.includes('\\\\sum');
            if (!hasComplexSymbols) {
                return true;
            }
        }
    }
    
    return false;
}

/**
 * 判断是否需要行内数学模式
 */
function needsInlineMath(expr) {
    // 1. 简单数学对象：单变量、数字、坐标点
    if (expr.match(/^[a-zA-Z]$/) || expr.match(/^\\\\[a-zA-Z]+$/) || 
        expr.match(/^[A-Z]\([^)]*\)$/)) {
        return true;
    }
    
    // 2. 行内短公式：字符数≤20且无分式/积分且无多行结构
    if (expr.length <= 20 && 
        !expr.includes('\\\\frac') && 
        !expr.includes('\\\\int') && 
        !expr.includes('\\\\begin') &&
        !expr.includes('\\\\sum') &&
        !expr.includes('\\\\lim')) {
        return true;
    }
    
    // 3. 希腊字母表示
    if (expr.match(/^\\\\(alpha|beta|gamma|delta|theta|phi|pi|sigma|omega)$/)) {
        return true;
    }
    
    return true; // 默认使用行内模式
}
```

#### **格式判断函数实现**
```javascript
/**
 * 数学表达式格式化函数
 * @param {string} expr - 数学表达式内容
 * @returns {string} - 格式化后的表达式
 */
function formatMath(expr) {
    // 检查是否需要显示数学模式 ($$...$$)
    if (needsDisplayMath(expr)) {
        return `$$${expr}$$`;
    }
    // 检查是否需要行内数学模式 ($...$)
    else if (needsInlineMath(expr)) {
        return `$${expr}$`;
    }
    // 默认使用行内模式确保可读性
    else {
        return `$${expr}$`;
    }
}

/**
 * 判断是否需要显示数学模式
 */
function needsDisplayMath(expr) {
    // 1. 正式数学陈述检查
    if (expr.includes('\\\\text{') && (expr.includes('定理') || expr.includes('定律') || expr.includes('定义') || expr.includes('引理'))) {
        return true;
    }
    
    // 2. 复杂数学结构检查
    if (expr.includes('\\begin{') || expr.includes('\\int') || expr.includes('\\sum') || expr.includes('\\lim') || expr.includes('matrix')) {
        return true;
    }
    
    // 3. 长公式检查（含分数且字符数≥25）
    if (expr.includes('\\frac') && expr.length >= 25) {
        return true;
    }
    
    // 4. 多运算符检查（≥3个主要运算符）
    const operatorCount = (expr.match(/[+\-*/=]/g) || []).length;
    if (operatorCount >= 3) {
        return true;
    }
    
    // 5. 参数方程组检查
    if (expr.includes(',') && expr.match(/[xyz]=.*[,，]/)) {
        const parts = expr.split(/[,，]/);
        if (parts.length >= 2 && expr.length <= 35) {
            return true;
        }
    }
    
    return false;
}

/**
 * 判断是否需要行内数学模式
 */
function needsInlineMath(expr) {
    // 简单数学对象：单变量、数字、坐标点
    if (expr.match(/^[a-zA-Z]$/) || expr.match(/^\\[a-zA-Z]+$/) || expr.match(/^[A-Z]\([^)]*\)$/)) {
        return true;
    }
    
    // 行内短公式：字符数≤20且无复杂结构
    if (expr.length <= 20 && !expr.includes('\\frac') && !expr.includes('\\int') && !expr.includes('\\begin')) {
        return true;
    }
    
    return true; // 默认使用行内模式
}
```

### 🚨 PostgreSQL转义结合格式规则

#### 环境命令双重转义原理
在PostgreSQL E字符串中，所有以反斜杠开头的LaTeX/KaTeX命令都需要双重转义：

```sql
-- ❌ 错误：单重转义
E'$\\begin{cases} ... \\end{cases}$'

-- ✅ 正确：双重转义 + 双美元符号
E'$$\\\\begin{cases} ... \\\\end{cases}$$'
```

#### 完整格式选择规则表
| 数学内容类型 | 判断标准 | 包裹符号 | PostgreSQL示例 |
|-------------|----------|----------|----------------|
| 正式陈述 | 含定理/定律/定义标注 | `$$...$$` | `E'$$F=ma\\\\quad\\\\text{(牛顿第二定律)}$$'` |
| 复杂结构 | 含环境/积分/求和/极限 | `$$...$$` | `E'$$\\\\int_a^b f(x)dx$$'` |
| 长公式 | 含分数且字符≥25或运算符≥3 | `$$...$$` | `E'$$\\\\frac{x^2}{a^2}+\\\\frac{y^2}{b^2}=1$$'` |
| 参数方程组 | 2+参数式，字符≤35 | `$$...$$` | `E'$$x=a\\\\cos t,\\\\ y=b\\\\sin t$$'` |
| 简单对象 | 单变量/坐标点 | `$...$` | `E'点$P(1,-2)$的坐标'` |
| 短公式 | 字符≤20，无复杂结构 | `$...$` | `E'斜率$k=\\\\tan\\\\theta$'` |
| 希腊字母 | 单个希腊字母参数 | `$...$` | `E'角度$\\\\alpha$的值'` |

#### 🚨 完整的begin环境转义示例（关键修正）
```sql
-- begin环境多行公式转义标准（mp-html组件实际显示验证）
-- KaTeX原始语法: f(x) = \begin{cases} x, x \geq 0 \\ -x, x < 0 \end{cases}
-- mp-html组件显示: f(x) = \\begin{cases} x, x \\geq 0 \\\\\\ -x, x < 0 \\end{cases}
-- PostgreSQL E字符串存储:
E'$$f(x) = \\\\begin{cases} x, x \\\\geq 0 \\\\\\\\\\\\\\\\ -x, x < 0 \\\\end{cases}$$'

-- 🔍 转义层级详细分析：
-- KaTeX原始: \begin → mp-html显示: \\begin → PostgreSQL存储: \\\\begin (4个反斜杠)
-- KaTeX原始: \geq → mp-html显示: \\geq → PostgreSQL存储: \\\\geq (4个反斜杠)  
-- KaTeX原始: \\ → mp-html显示: \\\\\\ (6个反斜杠) → PostgreSQL存储: \\\\\\\\\\\\\\\\ (12个反斜杠！)
-- KaTeX原始: \end → mp-html显示: \\end → PostgreSQL存储: \\\\end (4个反斜杠)

-- ⚠️ 关键规律总结：
-- 1. 普通KaTeX命令：原始1个→mp-html 2个→PostgreSQL 4个反斜杠
-- 2. begin环境换行符特例：原始1个→mp-html 6个→PostgreSQL 12个反斜杠
-- 3. 原因：mp-html组件对begin环境换行符有特殊处理要求
```

### 🔍 错误检测正则表达式

| 问题类型 | 描述 | 正则表达式 | 替换建议 |
|---------|------|------------|----------|
| 单个$ | 检测单个$包围的复杂环境 | \$.*\\\\begin.*\$ | 改为双$包围 |
| $...$ | 检测需要双$的数学环境 | \$[^$]*\\\\begin\{cases\}.*\$ | 改为$$...$$包围 |
| begin | 检测缺少转义的环境命令 | \\\\begin\{[^}]+\} | 检查是否为\\\\begin |
| cos,sin | 检测缺少转义的三角函数 | \\\\(cos\|sin\|tan\|theta) | 检查是否为\\\\命令 |
| 8反斜杠 | 检测8个反斜杠的换行符（不足） | \\\\\\\\\\\\\\\\ | 替换为12个反斜杠换行符 |
| 12反斜杠 | 检测正确的12个反斜杠换行符 | \\\\\\\\\\\\\\\\\\\\\\\\ | 保持不变（正确格式） |
| begin换行 | 检测begin环境换行符是否正确 | \\\\begin\{.*\}\.\*\\\\\\\\\\\\\\\\ | 确保使用12个反斜杠 |
| &符号 | 检测array环境中的&对齐符号 | \\\\begin\{array\}.*& | 替换为\\\\quad空格 |
| array转义 | 检测array环境转义是否正确 | \\\\begin\{array\} | 确保双重转义 |

### 🔧 集成格式规则的检查修复函数

```javascript
// 整合新格式规则的数学公式检查和修复
function formatMathFormulasWithRules(sqlContent) {
  // 第一步：基础转义修复
  sqlContent = fixBasicEscaping(sqlContent);
  
  // 第二步：根据新格式规则调整包裹符号
  sqlContent = applyFormatRules(sqlContent);
  
  return sqlContent;
}

// 基础转义修复（保持原有功能）
function fixBasicEscaping(sqlContent) {
  // 修复1：将8个反斜杠的换行符替换为12个反斜杠
  sqlContent = sqlContent.replace(/\\\\\\\\\\\\\\\\/g, '\\\\\\\\\\\\\\\\\\\\\\\\');
  
  // 修复2：确保begin环境使用双美元符号包围
  sqlContent = sqlContent.replace(/\$([^$]*\\\\begin\{[^}]+\}[^$]*)\$/g, '$$$$1$$');
  
  // 修复3：检查环境命令是否正确双重转义
  sqlContent = sqlContent.replace(/([^\\])\\begin\{/g, '$1\\\\begin{');
  sqlContent = sqlContent.replace(/([^\\])\\end\{/g, '$1\\\\end{');
  
  // 修复4：替换array环境中的&符号为\\quad空格
  sqlContent = sqlContent.replace(/(\\\\\\\\\\\text\{[^}]*\}) & /g, '$1 \\\\\\\\quad ');
  sqlContent = sqlContent.replace(/ & (\\\\\\\\\\\text\{)/g, ' \\\\\\\\quad $1');
  sqlContent = sqlContent.replace(/ & /g, ' \\\\\\\\quad ');
  
  return sqlContent;
}

// 应用新格式规则（新增）
function applyFormatRules(sqlContent) {
  // 正则匹配数学表达式内容（在E'...'字符串内的数学部分）
  const mathExpressionRegex = /E'([^']*\$[^']*\$[^']*)'/g;
  
  return sqlContent.replace(mathExpressionRegex, (match, content) => {
    // 提取数学表达式部分
    const mathParts = content.match(/\$\$?[^$]+\$\$?/g);
    if (!mathParts) return match;
    
    let newContent = content;
    
    mathParts.forEach(mathExpr => {
      // 去除包裹符号，获取纯数学内容
      const innerMath = mathExpr.replace(/^\$+|\$+$/g, '');
      
      // 根据新规则判断应使用的包裹符号
      const shouldUseDisplay = needsDisplayMath(innerMath);
      const correctFormat = shouldUseDisplay ? `$$${innerMath}$$` : `$${innerMath}$`;
      
      // 替换原有格式
      newContent = newContent.replace(mathExpr, correctFormat);
    });
    
    return `E'${newContent}'`;
  });
}

// 基于新规则的显示数学模式判断函数
function needsDisplayMath(expr) {
  // 1. 正式数学陈述检查（含定理/定律/定义标注）
  if (expr.includes('\\\\text{') && (
      expr.includes('定理') || expr.includes('定律') || 
      expr.includes('定义') || expr.includes('引理')
  )) {
    return true;
  }
  
  // 2. 复杂数学结构检查
  if (expr.includes('\\\\begin{') || expr.includes('\\\\int') || 
      expr.includes('\\\\sum') || expr.includes('\\\\lim') ||
      expr.includes('\\\\prod') || expr.includes('matrix')) {
    return true;
  }
  
  // 3. 长公式检查（含分数且字符数≥25）
  if (expr.includes('\\\\frac') && expr.length >= 25) {
    return true;
  }
  
  // 4. 多运算符检查（≥3个主要运算符）
  const operatorMatches = expr.match(/[+\-*/=<>]/g);
  if (operatorMatches && operatorMatches.length >= 3) {
    return true;
  }
  
  // 5. 参数方程组检查（包含逗号分隔的多个等式，字符数≤35）
  if (expr.includes(',') && expr.length <= 35) {
    const equationParts = expr.split(/[,，]/);
    if (equationParts.length >= 2 && 
        equationParts.every(part => part.includes('='))) {
      return true;
    }
  }
  
  return false;
}

// 增强的问题检测函数
function detectMathIssuesWithNewRules(sqlContent) {
  const issues = [];
  
  // 原有检测保持不变
  if (/\$[^$]*\\\\begin\{[^}]+\}[^$]*\$/.test(sqlContent)) {
    issues.push('发现单$包围begin环境，应改为$$...$$');
  }
  
  if (/\\\\\\\\\\\\\\\\ /.test(sqlContent)) {
    issues.push('发现8个反斜杠换行符，应使用12个反斜杠');
  }
  
  if (/\\\\begin\{array\}.*&/.test(sqlContent)) {
    issues.push('发现array环境使用&符号，应改为\\\\quad空格');
  }
  
  // 新增：PostgreSQL转义不足检测
  
  // 检测向量符号转义不足
  if (/(?<!\\\\)\\vec\{/.test(sqlContent)) {
    issues.push('发现向量符号\\vec转义不足，应写成\\\\\\\\vec');
  }
  
  if (/(?<!\\\\)\\cdot(?![\\\\])/.test(sqlContent)) {
    issues.push('发现点积符号\\cdot转义不足，应写成\\\\\\\\cdot');
  }
  
  // 检测三角函数转义不足
  if (/(?<!\\\\)\\(sin|cos|tan|sec|csc|cot)(?![\\\\])/.test(sqlContent)) {
    issues.push('发现三角函数转义不足，如\\sin应写成\\\\\\\\sin');
  }
  
  // 检测根号转义不足
  if (/(?<!\\\\)\\sqrt\{/.test(sqlContent)) {
    issues.push('发现根号符号\\sqrt转义不足，应写成\\\\\\\\sqrt');
  }
  
  // 检测希腊字母转义不足
  if (/(?<!\\\\)\\(alpha|beta|gamma|delta|theta|phi|pi|sigma|omega)(?![\\\\])/.test(sqlContent)) {
    issues.push('发现希腊字母转义不足，如\\theta应写成\\\\\\\\theta');
  }
  
  // 🚨 新增：格式规则检测
  
  // 检测可能需要$$...$$但使用了$...$的情况
  const singleDollarRegex = /\$([^$]{25,})\$/g;
  let match;
  while ((match = singleDollarRegex.exec(sqlContent)) !== null) {
    const content = match[1];
    if (needsDisplayMath(content)) {
      issues.push(`发现长公式使用单$包围，建议改为$$...$$：${content.substring(0, 25)}...`);
    }
  }
  
  // 检测含定理/定律但未使用$$...$$的情况
  if (/\$[^$]*\\\\\\\\text\{[^}]*(定理|定律|定义)[^}]*\}[^$]*\$/.test(sqlContent)) {
    issues.push('发现正式数学陈述使用单$包围，应改为$$...$$');
  }
  
  // 检测复杂结构使用单$的情况
  if (/\$[^$]*\\\\(int|sum|lim|prod|begin)[^$]*\$/.test(sqlContent)) {
    issues.push('发现复杂数学结构使用单$包围，应改为$$...$$');
  }
  
  return issues;
}

// 完整的格式化和验证流程
function validateAndFormatMathContent(sqlContent) {
  console.log('开始数学内容格式化和验证...');
  
  // 第一步：检测问题
  const issues = detectMathIssuesWithNewRules(sqlContent);
  if (issues.length > 0) {
    console.log('发现以下问题：');
    issues.forEach((issue, index) => {
      console.log(`${index + 1}. ${issue}`);
    });
  }
  
  // 第二步：自动修复
  const fixedContent = formatMathFormulasWithRules(sqlContent);
  
  // 第三步：再次验证
  const remainingIssues = detectMathIssuesWithNewRules(fixedContent);
  if (remainingIssues.length === 0) {
    console.log('✅ 所有数学格式问题已修复');
  } else {
    console.log('⚠️ 仍有以下问题需要手动处理：');
    remainingIssues.forEach((issue, index) => {
      console.log(`${index + 1}. ${issue}`);
    });
  }
  
  return {
    content: fixedContent,
    issues: issues,
    remainingIssues: remainingIssues,
    isFullyFixed: remainingIssues.length === 0
  };
}
```

## 6. 数学表达式检测方法

### 🔍 系统化数学表达式检测规范

#### 核心检测原则
所有包含数学内容的文本都必须用KaTeX语法包裹，检测方法按优先级从高到低执行：

#### 1. 强制检测的数学表达式模式

##### **变量和参数表达式**
```javascript
// 正则表达式检测模式
const MATH_VARIABLE_PATTERNS = {
    // 单变量表达式：x, y, a, b, t 等
    SINGLE_VARIABLES: /[^$]\s([a-zA-Z])\s[^$]/g,
    
    // 变量等式：x=2, a=5, k≠1 等
    VARIABLE_EQUATIONS: /[^$](mdc:[a-zA-Z][=≠<>≤≥][0-9/-a-zA-Z]+)[^$]/g,
    
    // 变量条件：当x=0时, 若a>0则 等
    VARIABLE_CONDITIONS: /(当|若|设)[^$]*([a-zA-Z][=≠<>≤≥][^$，。]*)[^$]/g,
    
    // 参数范围：x≥3, -1<x≤2, k≠1 等
    PARAMETER_RANGES: /[^$](mdc:[/-]?[0-9]*[<≤]?[a-zA-Z][≤<>=≠][0-9/-][^$]*)[^$]/g
};

// 使用示例
function detectVariableExpressions(text) {
    const results = [];
    
    // 检测单变量
    let match;
    while ((match = MATH_VARIABLE_PATTERNS.SINGLE_VARIABLES.exec(text)) !== null) {
        results.push({
            type: 'single_variable',
            match: match[0],
            variable: match[1],
            suggestion: `变量$${match[1]}$`
        });
    }
    
    // 检测变量等式
    while ((match = MATH_VARIABLE_PATTERNS.VARIABLE_EQUATIONS.exec(text)) !== null) {
        results.push({
            type: 'variable_equation',
            match: match[0],
            equation: match[1],
            suggestion: `$${match[1]}$`
        });
    }
    
    return results;
}
```

##### **函数表达式检测**
```javascript
const FUNCTION_PATTERNS = {
    // 函数表达式：f(x), g(t), h(a,b) 等
    FUNCTION_CALLS: /[^$](mdc:[a-zA-Z]/([a-zA-Z0-9,/s/+/-/*/]*))[^$]/g,
    
    // 函数定义：y=ax+b, f(x)=x²+1 等
    FUNCTION_DEFINITIONS: /[^$](mdc:[a-zA-Z]/([^)]*\)\s*[=]\s*[^$，。]*)[^$]/g,
    
    // 复合函数：f(g(x)), h(f(x)+1) 等
    COMPOSITE_FUNCTIONS: /[^$](mdc:[a-zA-Z]/([a-zA-Z]/([^)]*\)[^)]*\))[^$]/g
};

function detectFunctionExpressions(text) {
    const results = [];
    
    Object.entries(FUNCTION_PATTERNS).forEach(([type, pattern]) => {
        let match;
        while ((match = pattern.exec(text)) !== null) {
            results.push({
                type: type.toLowerCase(),
                match: match[0],
                expression: match[1],
                suggestion: `$${match[1]}$`
            });
        }
    });
    
    return results;
}
```

##### **计算表达式检测**
```javascript
const CALCULATION_PATTERNS = {
    // 基本运算：4-1=3, 5×10000 等
    BASIC_CALCULATIONS: /[^$](mdc:[0-9]+[/+/-/×/÷/*/][0-9]+(?:=[0-9]+)?)[^$]/g,
    
    // 几何计算：周长=2×3.14×r, 面积=πr² 等
    GEOMETRY_CALCULATIONS: /[^$](mdc:(?:周长|面积|体积|长度|宽度|高度)[=][^$，。]*)[^$]/g,
    
    // 比例计算：比例尺为1:10000 等
    RATIO_CALCULATIONS: /[^$](mdc:(?:比例尺?|比值|比率)(?:为|是|等于)[^$，。]*[0-9]+:[0-9]+[^$]*)[^$]/g,
    
    // 单位计算：5m, 160cm, 25kg 等
    UNIT_CALCULATIONS: /[^$](mdc:[0-9]+(?:/.[0-9]+)?(?:m|cm|mm|km|kg|g|℃|°)[^$]*)[^$]/g
};

function detectCalculationExpressions(text) {
    const results = [];
    
    Object.entries(CALCULATION_PATTERNS).forEach(([type, pattern]) => {
        let match;
        while ((match = pattern.exec(text)) !== null) {
            let suggestion = match[1];
            
            // 特殊处理单位表达式
            if (type === 'UNIT_CALCULATIONS') {
                // 提取数字和单位
                const unitMatch = match[1].match(/([0-9]+(?:\.[0-9]+)?)([a-zA-Z°℃]+)/);
                if (unitMatch) {
                    suggestion = `$${unitMatch[1]}\\\\text{${unitMatch[2]}}$`;
                }
            } else {
                suggestion = `$${match[1]}$`;
            }
            
            results.push({
                type: type.toLowerCase(),
                match: match[0],
                expression: match[1],
                suggestion: suggestion
            });
        }
    });
    
    return results;
}
```

##### **几何关系检测**
```javascript
const GEOMETRY_PATTERNS = {
    // 几何关系：AB=5, BC=6, AC=7 等
    SEGMENT_RELATIONS: /[^$](mdc:[A-Z]{2,3}[=][0-9]+[^$]*)[^$]/g,
    
    // 角度关系：∠A=60°, ∠ABC=90° 等
    ANGLE_RELATIONS: /[^$](mdc:∠[A-Z]{1,3}[=][0-9]+°?[^$]*)[^$]/g,
    
    // 坐标点：P(1,2), A(0,0), B(-1,3) 等
    COORDINATE_POINTS: /[^$](mdc:[A-Z]/([/-]?[0-9]+[,，][/-]?[0-9]+))[^$]/g,
    
    // 直角标记：Rt∠C, 直角Rt 等
    RIGHT_ANGLE_MARKS: /[^$](mdc:(?:直角)?Rt(?:∠[A-Z]?)?)[^$]/g
};

function detectGeometryExpressions(text) {
    const results = [];
    
    Object.entries(GEOMETRY_PATTERNS).forEach(([type, pattern]) => {
        let match;
        while ((match = pattern.exec(text)) !== null) {
            let suggestion = match[1];
            
            // 特殊处理直角标记
            if (type === 'RIGHT_ANGLE_MARKS') {
                suggestion = match[1].replace(/Rt/g, '$\\\\text{Rt}$');
            } else {
                suggestion = `$${match[1]}$`;
            }
            
            results.push({
                type: type.toLowerCase(),
                match: match[0],
                expression: match[1],
                suggestion: suggestion
            });
        }
    });
    
    return results;
}
```

#### 2. 综合检测函数

```javascript
/**
 * 综合数学表达式检测函数
 * @param {string} text - 待检测的文本内容
 * @returns {Array} - 检测结果数组
 */
function comprehensiveMathDetection(text) {
    const allResults = [];
    
    // 按优先级顺序执行检测
    const detectionFunctions = [
        detectVariableExpressions,
        detectFunctionExpressions,
        detectCalculationExpressions,
        detectGeometryExpressions
    ];
    
    detectionFunctions.forEach(detectFunc => {
        const results = detectFunc(text);
        allResults.push(...results);
    });
    
    // 去重和排序
    const uniqueResults = allResults.filter((item, index, arr) => 
        arr.findIndex(t => t.match === item.match) === index
    );
    
    return uniqueResults.sort((a, b) => text.indexOf(a.match) - text.indexOf(b.match));
}

/**
 * 自动修复数学表达式
 * @param {string} text - 原始文本
 * @returns {string} - 修复后的文本
 */
function autoFixMathExpressions(text) {
    const detectionResults = comprehensiveMathDetection(text);
    
    let fixedText = text;
    
    // 从后往前替换，避免位置偏移
    detectionResults.reverse().forEach(result => {
        const startIndex = fixedText.lastIndexOf(result.match);
        if (startIndex !== -1) {
            const beforeMatch = fixedText.substring(0, startIndex);
            const afterMatch = fixedText.substring(startIndex + result.match.length);
            
            // 构造替换内容
            const replacement = result.match.replace(result.expression, result.suggestion);
            
            fixedText = beforeMatch + replacement + afterMatch;
        }
    });
    
    return fixedText;
}
```

#### 3. 批量检测脚本

```bash
#!/bin/bash
# 数学表达式批量检测脚本

echo "🔍 开始批量检测数学表达式..."

# 检测未包裹的变量表达式
echo "📊 检测未包裹的变量表达式..."
grep -n "[^$][a-zA-Z]=[0-9][^$]" database_scripts/grade_scripts/*.sql > missing_variable_wrap.log

# 检测未包裹的函数表达式
echo "📊 检测未包裹的函数表达式..."
grep -n "[^$][a-zA-Z](mdc:[a-zA-Z0-9,]*)[^$]" database_scripts/grade_scripts/*.sql > missing_function_wrap.log

# 检测未包裹的计算表达式
echo "📊 检测未包裹的计算表达式..."
grep -n "[^$][0-9]+[×÷+\-][0-9]+[^$]" database_scripts/grade_scripts/*.sql > missing_calculation_wrap.log

# 检测未包裹的几何关系
echo "📊 检测未包裹的几何关系..."
grep -n "[^$][A-Z]{2}=[0-9]+[^$]" database_scripts/grade_scripts/*.sql > missing_geometry_wrap.log

# 检测未包裹的坐标点
echo "📊 检测未包裹的坐标点..."
grep -n "[^$][A-Z](mdc:[0-9/-,]+)[^$]" database_scripts/grade_scripts/*.sql > missing_coordinate_wrap.log

echo "✅ 检测完成，请查看生成的日志文件"
```

## 7. 度数符号强制KaTeX规范

### 🚨 度数符号强制处理规范

#### 核心规则：度数符号必须使用KaTeX语法

**绝对要求**：所有度数符号都必须使用标准KaTeX语法处理，禁止直接使用文本符号。

#### 1. 度数符号标准格式

##### **标准KaTeX语法**
```sql
-- ✅ 正确：使用KaTeX度数符号
E'角度为$90^{\\\\circ}$'
E'旋转了$45^{\\\\circ}$'
E'∠A=$60^{\\\\circ}$，∠B=$30^{\\\\circ}$'

-- ❌ 错误：直接使用度数文本符号
E'角度为$90°$'
E'旋转了45°'
E'∠A=60°，∠B=30°'
```

##### **PostgreSQL转义规范**
```sql
-- KaTeX原始语法: 90^{\circ}
-- PostgreSQL E字符串: 90^{\\\\circ}

-- 🔍 转义层级分析：
-- 原始: \circ → PostgreSQL: \\\\circ (4个反斜杠)
-- 必须使用E前缀因为包含转义符号
```

#### 2. 度数符号检测模式

##### **检测正则表达式**
```javascript
const DEGREE_PATTERNS = {
    // 检测未用KaTeX处理的度数符号
    UNESCAPED_DEGREE: /[^$](mdc:[0-9]+)°[^$]/g,
    
    // 检测在$...$内但未用\circ的度数
    DOLLAR_DEGREE: /\$([^$]*[0-9]+)°([^$]*)\$/g,
    
    // 检测角度表达式中的度数
    ANGLE_DEGREE: /∠[A-Z]{1,3}=([0-9]+)°/g,
    
    // 检测温度相关的度数（需要用\text{}处理）
    TEMPERATURE_DEGREE: /([0-9]+)℃/g
};

/**
 * 检测度数符号使用问题
 * @param {string} text - 待检测文本
 * @returns {Array} - 问题列表
 */
function detectDegreeIssues(text) {
    const issues = [];
    
    // 检测未用KaTeX处理的度数符号
    let match;
    while ((match = DEGREE_PATTERNS.UNESCAPED_DEGREE.exec(text)) !== null) {
        issues.push({
            type: 'unescaped_degree',
            match: match[0],
            number: match[1],
            suggestion: `$${match[1]}^{\\\\\\\\circ}$`,
            description: '度数符号必须用KaTeX语法：^{\\\\circ}'
        });
    }
    
    // 检测$...$内错误的度数符号
    while ((match = DEGREE_PATTERNS.DOLLAR_DEGREE.exec(text)) !== null) {
        issues.push({
            type: 'wrong_degree_in_dollar',
            match: match[0],
            suggestion: `$${match[1]}^{\\\\\\\\circ}${match[2]}$`,
            description: '在KaTeX中度数必须用^{\\\\circ}而不是°'
        });
    }
    
    // 检测角度表达式中的度数
    while ((match = DEGREE_PATTERNS.ANGLE_DEGREE.exec(text)) !== null) {
        issues.push({
            type: 'angle_degree',
            match: match[0],
            number: match[1],
            suggestion: match[0].replace('°', '^{\\\\\\\\circ}'),
            description: '角度表达式中的度数必须用^{\\\\circ}'
        });
    }
    
    // 检测温度度数（特殊处理）
    while ((match = DEGREE_PATTERNS.TEMPERATURE_DEGREE.exec(text)) !== null) {
        issues.push({
            type: 'temperature_degree',
            match: match[0],
            number: match[1],
            suggestion: `$${match[1]}\\\\text{℃}$`,
            description: '温度符号使用\\\\text{℃}而不是直接的℃'
        });
    }
    
    return issues;
}
```

#### 3. 自动修复函数

```javascript
/**
 * 自动修复度数符号
 * @param {string} sqlContent - SQL文件内容
 * @returns {string} - 修复后的内容
 */
function autoFixDegreeSymbols(sqlContent) {
    return sqlContent
        // 修复未包裹的度数符号
        .replace(/([^$])([0-9]+)°([^$])/g, '$1$$$2^{\\\\\\\\circ}$$$3')
        
        // 修复$...$内错误的度数符号
        .replace(/\$([^$]*[0-9]+)°([^$]*)\$/g, '$$$1^{\\\\\\\\circ}$2$$')
        
        // 修复角度表达式中的度数
        .replace(/(∠[A-Z]{1,3}=)([0-9]+)°/g, '$$$1$2^{\\\\\\\\circ}$$')
        
        // 修复温度符号
        .replace(/([^$])([0-9]+)℃([^$])/g, '$1$$$2\\\\\\\\text{℃}$$$3')
        
        // 确保含转义符的字段使用E前缀
        .replace(/^(\s*)'([^']*\\\\[^']*)'([^E])/gm, '$1E\'$2\'$3');
}
```

#### 4. 度数符号对照表

| 类型 | ❌ 错误写法 | ✅ 正确写法（PostgreSQL） | 说明 |
|------|------------|------------------------|------|
| **普通角度** | `90°` | `$90^{\\\\circ}$` | 必须用KaTeX ^{\\circ} |
| **角度表达式** | `∠A=60°` | `$∠A=60^{\\\\circ}$` | 整个角度表达式包裹 |
| **多个角度** | `60°、30°、90°` | `$60^{\\\\circ}$、$30^{\\\\circ}$、$90^{\\\\circ}$` | 每个角度独立包裹 |
| **角度范围** | `0°≤α≤90°` | `$0^{\\\\circ}≤α≤90^{\\\\circ}$` | 整个不等式包裹 |
| **温度符号** | `25℃` | `$25\\\\text{℃}$` | 温度用\\text{}包裹 |
| **在$内错误** | `$90°$` | `$90^{\\\\circ}$` | 修正为标准circ语法 |

#### 5. 强制检查脚本

```bash
#!/bin/bash
# 度数符号强制检查脚本

echo "🌡️ 开始检查度数符号格式..."

# 检查未包裹的度数符号
echo "📊 检查未包裹的度数符号..."
grep -n "[^$][0-9]°[^$]" database_scripts/grade_scripts/*.sql > unescaped_degrees.log

# 检查$...$内错误的度数符号
echo "📊 检查$...$内错误的度数符号..."
grep -n "\$[^$]*[0-9]°[^$]*\$" database_scripts/grade_scripts/*.sql > wrong_dollar_degrees.log

# 检查角度表达式
echo "📊 检查角度表达式..."
grep -n "∠[A-Z]=[0-9]°" database_scripts/grade_scripts/*.sql > angle_degrees.log

# 检查温度符号
echo "📊 检查温度符号..."
grep -n "[0-9]℃" database_scripts/grade_scripts/*.sql > temperature_degrees.log

# 统计发现的问题
echo "📈 统计结果："
echo "未包裹度数符号: $(wc -l < unescaped_degrees.log) 处"
echo "错误$内度数符号: $(wc -l < wrong_dollar_degrees.log) 处"
echo "角度表达式问题: $(wc -l < angle_degrees.log) 处"
echo "温度符号问题: $(wc -l < temperature_degrees.log) 处"

echo "✅ 度数符号检查完成"
```

#### 6. VS Code 配置支持

```json
{
    "degree-symbol-formatter": {
        "rules": [
            {
                "find": "([0-9]+)°",
                "replace": "$$$1^{\\\\\\\\circ}$$",
                "description": "Convert degree symbols to KaTeX format"
            },
            {
                "find": "∠([A-Z]{1,3})=([0-9]+)°",
                "replace": "$∠$1=$2^{\\\\\\\\circ}$",
                "description": "Convert angle expressions with degrees"
            },
            {
                "find": "([0-9]+)℃",
                "replace": "$$$1\\\\\\\\text{℃}$$",
                "description": "Convert temperature symbols"
            }
        ]
    }
}
```

### 数学表达式强制KaTeX化规范

#### 识别数学内容标准
所有包含以下内容的文本都必须使用KaTeX语法：

1. **函数表达式**
```sql
-- ❌ 错误
E'"content": "函数f(x)的性质"'
E'"content": "g(x)=x^2+1"'

-- ✅ 正确  
E'"content": "函数$f(x)$的性质"'
E'"content": "$g(x)=x^2+1$"'
```

2. **数学文本符号（重要新增规则）**
**🚨 关键规则**：数学中的文本符号（缩写、单位、标记等）必须用`\text{}`包裹

```sql
-- ❌ 错误：直接使用文本符号
E'"content": "直角三角形的$Rt$角"'
E'"content": "点$A$、$B$、$C$和$Rt$"'
E'"content": "单位$cm$的长度"'

-- ✅ 正确：使用\text{}包裹
E'"content": "直角三角形的$\\\\text{Rt}$角"'
E'"content": "点$A$、$B$、$C$和$\\\\text{Rt}$"'
E'"content": "单位$\\\\text{cm}$的长度"'
```

**必须使用`\text{}`的情况：**
- **几何标记**：`Rt`（直角）、`∠`（角）、`△`（三角形）等
- **单位符号**：`cm`、`m`、`kg`、`℃`等
- **数学缩写**：`sin`、`cos`、`log`等（注意：KaTeX函数应用`\\sin`、`\\cos`、`\\log`）
- **变量说明**：`max`、`min`、`当且仅当`等
- **几何图形**：`ABCD`（四边形）、`O`（圆心）等标记

**完整的文本符号转义对照表：**
| 数学文本 | ❌ 错误写法 | ✅ 正确写法（PostgreSQL转义） |
|---|---|---|
| 直角标记Rt | `$Rt$` | `$\\\\text{Rt}$` |
| 单位cm | `$cm$` | `$\\\\text{cm}$` |
| 最大值max | `$max$` | `$\\\\text{max}$` |
| 当且仅当 | `$当且仅当$` | `$\\\\text{当且仅当}$` |
| 几何图形ABCD | `$ABCD$` | `$\\\\text{ABCD}$` |
| 角度单位° | `$°$` | `$^{\\\\circ}$` |
| 温度单位℃ | `$℃$` | `$\\\\text{℃}$` |

**检测和修复函数：**
```javascript
/**
 * 检测需要用\text{}包裹的数学文本符号
 * @param {string} mathExpr - 数学表达式内容
 * @returns {Array} - 需要修复的文本符号列表
 */
function detectTextSymbols(mathExpr) {
    const textSymbolPatterns = [
        // 几何标记
        /\$([^\\$]*[^\\])Rt([^\\$]*)\$/g,  // 直角标记
        /\$([^\\$]*[^\\])(max|min|当且仅当)([^\\$]*)\$/g,  // 数学术语
        
        // 单位符号（排除数学函数）
        /\$([^\\$]*[^\\])(cm|mm|m|km|kg|g|℃|°)([^\\$]*)\$/g,
        
        // 几何图形标记（连续大写字母）
        /\$([^\\$]*[^\\])([A-Z]{2,})([^\\$]*)\$/g,
        
        // 中文数学术语
        /\$([^\\$]*[^\\])(其中|使得|满足|当|则|因为|所以)([^\\$]*)\$/g
    ];
    
    const issues = [];
    textSymbolPatterns.forEach((pattern, index) => {
        let match;
        while ((match = pattern.exec(mathExpr)) !== null) {
            issues.push({
                type: 'unescaped_text_symbol',
                match: match[0],
                symbol: match[2],
                suggestion: `$${match[1]}\\\\\\\\text{${match[2]}}${match[3]}$`
            });
        }
    });
    
    return issues;
}

/**
 * 自动修复数学文本符号
 * @param {string} sqlContent - SQL文件内容
 * @returns {string} - 修复后的内容
 */
function autoFixTextSymbols(sqlContent) {
    return sqlContent
        // 修复直角标记
        .replace(/\$([^$]*[^\\])Rt([^$]*)\$/g, '$$$1\\\\\\\\text{Rt}$2$$')
        
        // 修复常见单位
        .replace(/\$([^$]*[^\\])(cm|mm|m|km|kg|g)([^$]*)\$/g, '$$$1\\\\\\\\text{$2}$3$$')
        
        // 修复温度和角度单位
        .replace(/\$([^$]*[^\\])℃([^$]*)\$/g, '$$$1\\\\\\\\text{℃}$2$$')
        .replace(/\$([^$]*[^\\])°([^$]*)\$/g, '$$$1^{\\\\\\\\circ}$2$$')
        
        // 修复数学术语
        .replace(/\$([^$]*[^\\])(max|min)([^$]*)\$/g, '$$$1\\\\\\\\text{$2}$3$$')
        
        // 修复几何图形标记（2-4个连续大写字母）
        .replace(/\$([^$]*[^\\])([A-Z]{2,4})([^$]*)\$/g, '$$$1\\\\\\\\text{$2}$3$$')
        
        // 修复中文数学术语
        .replace(/\$([^$]*[^\\])(当且仅当|其中|使得|满足)([^$]*)\$/g, '$$$1\\\\\\\\text{$2}$3$$');
}
```

2. **数学公式和等式**
```sql
-- ❌ 错误
E'"content": "满足f(x+y)+f(x-y)=2f(x)f(y)"'
E'"content": "当f(0)≠0时"'

-- ✅ 正确
E'"content": "满足$f(x+y)+f(x-y)=2f(x)f(y)$"'
E'"content": "当$f(0)\\\\neq0$时"'
```

3. **变量和参数**
```sql
-- ❌ 错误
E'"content": "参数a的取值范围"'
E'"content": "变量x的定义域"'

-- ✅ 正确
E'"content": "参数$a$的取值范围"'
E'"content": "变量$x$的定义域"'
```

4. **坐标和点**
```sql
-- ❌ 错误
E'"content": "点P(1,2)的坐标"'
E'"content": "在x=0处"'

-- ✅ 正确
E'"content": "点$P(1,2)$的坐标"'
E'"content": "在$x=0$处"'
```

5. **数学符号和运算**
```sql
-- ❌ 错误
E'"content": "当Δ>0时"'
E'"content": "求f\'(x)"'

-- ✅ 正确
E'"content": "当$\\\\Delta>0$时"'
E'"content": "求$f\\'(x)$"'
```

### 强制转换规则表

| 数学内容类型 | 识别模式 | KaTeX转换 | PostgreSQL转义 |
|-------------|---------|----------|---------------|
| 函数名 | f(x), g(t), h(a) | $f(x)$, $g(t)$, $h(a)$ | `$f(x)$`, `$g(t)$`, `$h(a)$` |
| 变量 | x, y, a, b, t | $x$, $y$, $a$, $b$, $t$ | `$x$`, `$y$`, `$a$`, `$b$`, `$t$` |
| 等式 | f(x)=y+1 | $f(x)=y+1$ | `$f(x)=y+1$` |
| 不等式 | x>0, y≤1 | $x>0$, $y\\leq1$ | `$x>0$`, `$y\\\\leq1$` |
| 导数 | f'(x), f''(x) | $f'(x)$, $f''(x)$ | `$f\\'(x)$`, `$f\\'\\'(x)$` |
| 极限 | x→0, n→∞ | $x\\to0$, $n\\to\\infty$ | `$x\\\\to0$`, `$n\\\\to\\\\infty$` |
| 分数 | 1/2, a/b | $\\frac{1}{2}$, $\\frac{a}{b}$ | `$\\\\frac{1}{2}$`, `$\\\\frac{a}{b}$` |
| 指数 | x^2, e^x | $x^2$, $e^x$ | `$x^2$`, `$e^x$` |
| 对数 | log x, ln x | $\\log x$, $\\ln x$ | `$\\\\log x$`, `$\\\\ln x$` |
| 三角函数 | sin x, cos θ | $\\sin x$, $\\cos \\theta$ | `$\\\\sin x$`, `$\\\\cos \\\\theta$` |
| 根号 | √x, ∛a | $\\sqrt{x}$, $\\sqrt[3]{a}$ | `$\\\\sqrt{x}$`, `$\\\\sqrt[3]{a}$` |
| **数学文本符号（新增）** | **Rt, cm, max, ABCD** | **$\\\\text{Rt}$, $\\\\text{cm}$** | **`$\\\\text{Rt}$`, `$\\\\text{cm}$`** |
| 几何标记 | Rt（直角）, ∠（角） | $\\\\text{Rt}$, $\\angle$ | `$\\\\text{Rt}$`, `$\\\\angle$` |
| 单位符号 | cm, m, kg, ℃ | $\\\\text{cm}$, $\\\\text{℃}$ | `$\\\\text{cm}$`, `$\\\\text{℃}$` |
| 数学术语 | max, min, 当且仅当 | $\\\\text{max}$, $\\\\text{当且仅当}$ | `$\\\\text{max}$`, `$\\\\text{当且仅当}$` |
| 几何图形标记 | ABCD, △ABC | $\\\\text{ABCD}$, $\\triangle ABC$ | `$\\\\text{ABCD}$`, `$\\\\triangle ABC$` |

## 5. K12教育内容规范

### 年级适应性规范
```sql
-- 小学阶段（1-6年级）：简化表达
E'加法：$2+3=5$'
E'乘法表：$7 \\\\times 8 = 56$'

-- 初中阶段（7-9年级）：基础代数
E'一元二次方程：$ax^2+bx+c=0$（$a \\\\neq 0$）'
E'勾股定理：$a^2+b^2=c^2$'

-- 高中阶段（10-12年级）：高等数学
E'导数定义：$f\\'(x) = \\\\lim_{h \\\\to 0} \\\\frac{f(x+h)-f(x)}{h}$'
E'积分公式：$\\\\int_a^b f(x)dx$'
```

### 中文数学表达规范
```sql
-- 标准中文数学表达
E'正弦函数$y = \\\\sin x$的周期为$2\\\\pi$'
E'当$x > 0$时，函数$f(x) = \\\\ln x$单调递增'

-- 避免英文数学术语混用
-- ❌ 错误：混用英文术语
E'function f(x)的domain是实数集'

-- ✅ 正确：使用中文术语
E'函数$f(x)$的定义域是实数集'
```

### 教育阶段特殊处理
```sql
-- 分段函数（避免使用&符号）
E'绝对值函数：$$f(x) = \\\\begin{cases} x, & x \\\\geq 0 \\\\\\\\ -x, & x < 0 \\\\end{cases}$$'

-- 集合表示
E'定义域：$\\\\{x | x \\\\in \\\\mathbb{R}, x \\\\neq 0\\\\}$'

-- 区间表示
E'单调区间：$(0, +\\\\infty)$'
```

## 6. 质量控制流程

### 六步检查流程
1. **E前缀判断检查**
   - 使用判断函数验证每个字段
   - 确认包含数学表达式的字段使用E前缀
   - 确认纯文本字段不使用E前缀

2. **KaTeX语法完整性检查**
   - 所有数学表达式用$...$或$$...$$包裹
   - 函数、变量、符号正确使用KaTeX语法
   - 复杂公式使用$$...$$，简单表达式使用$...$

3. **PostgreSQL转义验证**
   - 所有反斜杠正确双重转义
   - 单引号在导数表达式中正确转义
   - 特殊数学符号转义符合规范

4. **教育内容适应性检查**
   - 符合K12教育阶段要求
   - 中文表达规范准确
   - 数学术语使用恰当

5. **结构完整性验证**
   - SQL语法正确无误
   - JSON格式符合规范
   - 字段顺序与表结构一致

6. **渲染效果确认**
   - 数学公式在小程序中正确显示
   - 无乱码或显示异常
   - 教学内容清晰易懂

### 自动化检查清单
```javascript
// E前缀使用检查
const checkEPrefix = (content) => {
    const needsE = needsEPrefix(content);
    const hasE = content.startsWith('E\'');
    return needsE === hasE;
};

// 转义完整性检查
const checkEscaping = (content) => {
    // 检查未转义的反斜杠
    const unescapedBackslash = /(?<!\\)\\(?![\\'])/g;
    return !unescapedBackslash.test(content);
};

// KaTeX语法检查
const checkKaTeXSyntax = (content) => {
    // 检查未包裹的数学表达式
    const mathPattern = /[a-zA-Z]\([a-zA-Z0-9,+-]+\)|[a-zA-Z]=|[a-zA-Z]>/;
    const wrapped = /\$.*\$/;
    return !mathPattern.test(content) || wrapped.test(content);
};
```

## 7. 工具化检查方法

### 正则表达式检查工具（严格三大原则版）
```javascript
// 🚨 基于三大核心原则的严格检查规则

// 原则一：E前缀使用规则检查
// 1. 检查缺少E前缀的数学内容（含$符号但无E前缀）
const MISSING_E_PREFIX = /^\s*'[^']*\$[^']*'/g;

// 2. 检查错误使用E前缀（无$符号但有E前缀）
const WRONG_E_PREFIX = /^\s*E'[^'$]*'/g;

// 原则二：强制KaTeX语法检查
// 3. 检查未用KaTeX包裹的数学表达式
const UNWRAPPED_MATH_EXPR = /[a-zA-Z]\([a-zA-Z0-9,+-]*\)(?!\$)/g;
const UNWRAPPED_VARIABLES = /[^$][a-zA-Z]=[^$]/g;
const UNWRAPPED_FUNCTIONS = /函数[^$]*[a-zA-Z]\([^)]*\)[^$]/g;

// 3. 检查未转义的反斜杠
const UNESCAPED_BACKSLASH = /(?<!\\)\\(?![\\'])/g;

// 4. 检查未包裹的数学表达式
const UNWRAPPED_MATH = /[a-zA-Z]\([a-zA-Z0-9,+-]*\)(?!\$)/g;

// 🚨 5. 检查数学文本符号（新增重要规则）
const UNESCAPED_TEXT_SYMBOLS = {
    // 几何标记
    RT_SYMBOL: /\$([^$]*[^\\])Rt([^$]*)\$/g,
    ANGLE_SYMBOL: /\$([^$]*[^\\])∠([^$]*)\$/g,
    
    // 单位符号
    UNIT_SYMBOLS: /\$([^$]*[^\\])(cm|mm|m|km|kg|g|℃)([^$]*)\$/g,
    
    // 数学术语
    MATH_TERMS: /\$([^$]*[^\\])(max|min|当且仅当|其中|使得|满足)([^$]*)\$/g,
    
    // 几何图形标记（2-4个连续大写字母）
    GEOMETRY_LABELS: /\$([^$]*[^\\])([A-Z]{2,4})([^$]*)\$/g,
    
    // 角度符号
    DEGREE_SYMBOL: /\$([^$]*[^\\])°([^$]*)\$/g
};

// 🚨 6. 检查未包裹的数学表达式（新增重要规则）
const UNWRAPPED_MATH_EXPRESSIONS = {
    // 变量关系和不等式
    INEQUALITIES: /[^$][a-zA-Z][><=≥≤≠±][^$]/g,
    EQUATIONS: /[^$][a-zA-Z0-9]=[\d\-][^$]/g,
    VARIABLE_RANGES: /[^$]\-?\d+<[a-zA-Z]≤?\d+[^$]/g,
    
    // 计算表达式  
    CALCULATIONS: /[^$]\d+[\-+×÷]\d+=[^$]/g,
    MULTIPLICATIONS: /[^$]\d+×\d+[^$]/g,
    
    // 几何关系
    GEOMETRY_RELATIONS: /[^$][A-Z]{2}=\d+[^$]/g,
    SEGMENT_LENGTHS: /[^$]底边[A-Z]{2}=\d+[^$]/g,
    
    // 条件表达式
    CONDITIONS: /[^$][a-zA-Z][\-+]\d+[≥≤><=≠]\d+[^$]/g,
    PARAMETERS: /当[^$]*[a-zA-Z][≠=<>≥≤][^$]*时/g
};

// 5. 检查begin环境转义问题（新增）
const WRONG_BEGIN_ENVIRONMENT = /\$\\\\begin\{[^}]+\}/g;  // 应该用$$...$$包裹
const INSUFFICIENT_ENV_ESCAPE = /\\begin\{[^}]+\}/g;      // 环境命令需要双重转义
const INSUFFICIENT_FUNC_ESCAPE = /\\(sin|cos|tan|sec|csc|cot|log|ln|exp)(?!\\)/g;  // 函数需要双重转义
const INSUFFICIENT_GREEK_ESCAPE = /\\(alpha|beta|gamma|delta|theta|phi|pi|sigma|omega)(?!\\)/g;  // 希腊字母需要双重转义

// 6. 检查复杂环境的包裹符号
const COMPLEX_ENV_SINGLE_DOLLAR = /\$[^$]*\\\\begin\{(cases|matrix|pmatrix|bmatrix|array|align)\}/g;

// 使用示例（三大原则严格检查）
function validateMathContent(sqlContent) {
    const errors = [];
    
    // 原则一：E前缀使用规则检查
    if (MISSING_E_PREFIX.test(sqlContent)) {
        errors.push('❌ 违反原则一：发现包含$符号但缺少E前缀的字段');
    }
    
    if (WRONG_E_PREFIX.test(sqlContent)) {
        errors.push('❌ 违反原则一：发现不含$符号却使用了E前缀的字段');
    }
    
    // 原则二：强制KaTeX语法检查
    if (UNWRAPPED_MATH_EXPR.test(sqlContent)) {
        errors.push('❌ 违反原则二：发现未用KaTeX包裹的数学表达式');
    }
    
    if (UNWRAPPED_VARIABLES.test(sqlContent)) {
        errors.push('❌ 违反原则二：发现未用KaTeX包裹的数学变量');
    }
    
    if (UNWRAPPED_FUNCTIONS.test(sqlContent)) {
        errors.push('❌ 违反原则二：发现未用KaTeX包裹的函数表达式');
    }
    
    if (UNESCAPED_BACKSLASH.test(sqlContent)) {
        errors.push('❌ 发现未正确转义的反斜杠');
    }
    
    if (UNWRAPPED_MATH.test(sqlContent)) {
        errors.push('❌ 发现未用KaTeX语法包裹的数学表达式');
    }
    
    // 🚨 新增：数学文本符号检查
    if (UNESCAPED_TEXT_SYMBOLS.RT_SYMBOL.test(sqlContent)) {
        errors.push('❌ 发现未用\\text{}包裹的Rt符号，应写成$\\\\\\\\text{Rt}$');
    }
    
    if (UNESCAPED_TEXT_SYMBOLS.UNIT_SYMBOLS.test(sqlContent)) {
        errors.push('❌ 发现未用\\text{}包裹的单位符号，应写成$\\\\\\\\text{cm}$');
    }
    
    if (UNESCAPED_TEXT_SYMBOLS.MATH_TERMS.test(sqlContent)) {
        errors.push('❌ 发现未用\\text{}包裹的数学术语，应写成$\\\\\\\\text{max}$');
    }
    
    if (UNESCAPED_TEXT_SYMBOLS.GEOMETRY_LABELS.test(sqlContent)) {
        errors.push('❌ 发现未用\\text{}包裹的几何图形标记，应写成$\\\\\\\\text{ABCD}$');
    }
    
    if (UNESCAPED_TEXT_SYMBOLS.DEGREE_SYMBOL.test(sqlContent)) {
        errors.push('❌ 发现角度符号°应写成$^{\\\\\\\\circ}$或$\\\\\\\\text{°}$');
    }
    
    // 🚨 新增：未包裹数学表达式检查
    if (UNWRAPPED_MATH_EXPRESSIONS.INEQUALITIES.test(sqlContent)) {
        errors.push('❌ 发现未包裹的不等式表达式，如a>b应写成$a>b$');
    }
    
    if (UNWRAPPED_MATH_EXPRESSIONS.EQUATIONS.test(sqlContent)) {
        errors.push('❌ 发现未包裹的等式表达式，如x=2应写成$x=2$');
    }
    
    if (UNWRAPPED_MATH_EXPRESSIONS.VARIABLE_RANGES.test(sqlContent)) {
        errors.push('❌ 发现未包裹的变量范围，如-1<x≤2应写成$-1<x≤2$');
    }
    
    if (UNWRAPPED_MATH_EXPRESSIONS.CALCULATIONS.test(sqlContent)) {
        errors.push('❌ 发现未包裹的计算表达式，如4-1=3应写成$4-1=3$');
    }
    
    if (UNWRAPPED_MATH_EXPRESSIONS.MULTIPLICATIONS.test(sqlContent)) {
        errors.push('❌ 发现未包裹的乘法运算，如5×10000应写成$5×10000$');
    }
    
    if (UNWRAPPED_MATH_EXPRESSIONS.GEOMETRY_RELATIONS.test(sqlContent)) {
        errors.push('❌ 发现未包裹的几何关系，如AB=5应写成$AB=5$');
    }
    
    if (UNWRAPPED_MATH_EXPRESSIONS.CONDITIONS.test(sqlContent)) {
        errors.push('❌ 发现未包裹的条件表达式，如x-3≥0应写成$x-3≥0$');
    }
    
    if (UNWRAPPED_MATH_EXPRESSIONS.PARAMETERS.test(sqlContent)) {
        errors.push('❌ 发现未包裹的参数条件，如"当k≠1时"应写成"当$k≠1$时"');
    }
    
    if (WRONG_BEGIN_ENVIRONMENT.test(sqlContent)) {
        errors.push('发现begin环境使用单美元符号，应使用双美元符号$$...$$');
    }
    
    if (INSUFFICIENT_ENV_ESCAPE.test(sqlContent)) {
        errors.push('发现begin环境命令转义不足，\\begin应写成\\\\\\\\begin');
    }
    
    if (INSUFFICIENT_FUNC_ESCAPE.test(sqlContent)) {
        errors.push('发现数学函数转义不足，\\sin应写成\\\\\\\\sin');
    }
    
    if (INSUFFICIENT_GREEK_ESCAPE.test(sqlContent)) {
        errors.push('发现希腊字母转义不足，\\theta应写成\\\\\\\\theta');
    }
    
    if (COMPLEX_ENV_SINGLE_DOLLAR.test(sqlContent)) {
        errors.push('发现复杂数学环境使用单美元符号，应使用$$...$$');
    }
    
    return errors;
}

// 自动修复函数（增强版）
function autoFixMathEscaping(sqlContent) {
    return sqlContent
        // 修复环境命令转义
        .replace(/\\begin\{/g, '\\\\\\\\begin{')
        .replace(/\\end\{/g, '\\\\\\\\end{')
        // 修复函数转义
        .replace(/\\(sin|cos|tan|sec|csc|cot|log|ln|exp)(?!\\)/g, '\\\\\\\\$1')
        // 修复希腊字母转义
        .replace(/\\(alpha|beta|gamma|delta|theta|phi|pi|sigma|omega)(?!\\)/g, '\\\\\\\\$1')
        // 修复复杂环境的包裹符号
        .replace(/\$([^$]*\\\\begin\{(?:cases|matrix|pmatrix|bmatrix|array|align)\}[^$]*)\$/g, '$$$$1$$')
        
        // 🚨 新增：修复未包裹的数学表达式
        // 修复不等式表达式
        .replace(/([^$])([a-zA-Z])([><=≥≤≠±])([a-zA-Z0-9])([^$])/g, '$1$$$2$3$4$$$5')
        // 修复等式表达式  
        .replace(/([^$])([a-zA-Z0-9])=([0-9\-])([^$])/g, '$1$$$2=$3$$$4')
        // 修复变量范围
        .replace(/([^$])(\-?\d+<[a-zA-Z]≤?\d+)([^$])/g, '$1$$$2$$$3')
        // 修复计算表达式
        .replace(/([^$])(\d+[\-+×÷]\d+=[\d\-+×÷]+)([^$])/g, '$1$$$2$$$3')
        // 修复乘法运算
        .replace(/([^$])(\d+×\d+)([^$])/g, '$1$$$2$$$3')
        // 修复几何关系
        .replace(/([^$])([A-Z]{2}=\d+)([^$])/g, '$1$$$2$$$3')
        // 修复条件表达式
        .replace(/([^$])([a-zA-Z][\-+]\d+[≥≤><=≠]\d+)([^$])/g, '$1$$$2$$$3')
        // 修复参数条件
        .replace(/(当[^$]*)([a-zA-Z][≠=<>≥≤][^$]*)(时)/g, '$1$$$2$$$3');
}
```

### 批量检查脚本
```bash
#!/bin/bash
# 数学内容格式检查脚本

echo "开始检查数学内容格式..."

# 检查缺少E前缀的情况
grep -n "'\{.*\\\\" database_scripts/grade_scripts/*.sql | \
grep -v "^.*E'" > missing_e_prefix.log

# 检查未转义的分数符号
grep -n "\\\\frac[^\\\\]" database_scripts/grade_scripts/*.sql > \
unescaped_frac.log

# 检查未包裹的函数表达式
grep -n "[a-zA-Z](mdc:[a-zA-Z0-9,+-]*)[^$]" database_scripts/grade_scripts/*.sql | \
grep -v "\$" > unwrapped_functions.log

echo "检查完成，请查看生成的日志文件"
```

### VS Code 扩展配置
```json
{
    "editor.quickSuggestions": {
        "other": true,
        "comments": false,
        "strings": true
    },
    "editor.wordBasedSuggestions": true,
    "emmet.includeLanguages": {
        "sql": "html"
    },
    "files.associations": {
        "*.sql": "sql"
    },
    "sql.format.enable": true,
    "math-content-formatter.enable": true,
    "math-content-formatter.rules": {
        "enforceEPrefix": true,
        "enforceDoubleEscape": true,
        "enforceKaTeXWrapping": true
    }
}
```

## 8. **常见错误与解决方案（2024年12月重要更新）**

### 🚨 **关键修正（基于实际问题反馈）**

#### **修正一：KaTeX命令反斜杠数量标准**
```sql
❌ 错误：使用2个反斜杠
E'$\\frac{a}{b}$'

❌ 错误：使用8个反斜杠  
E'$\\\\\\\\\\\\\\\\frac{a}{b}$'

✅ 正确：必须使用4个反斜杠
E'$\\\\frac{a}{b}$'
```

#### **修正二：数学符号连写问题**
```sql
❌ 严重错误：$>$$0$$>$ 会被误解析为 $>$ $$0$$ $>$
E'[\"正数$>$$0$$>$负数\"]'

✅ 正确：必须分开书写每个数学对象
E'[\"正数$>$ $0$ $>$负数\"]'
```

#### **修正三：数学术语和符号包裹**
```sql
❌ 错误：数学术语未使用\\text{}包裹
E'\"直角$Rt$角在$C$处\"'

✅ 正确：数学术语必须用\\text{}包裹
E'\"直角$\\\\text{Rt}$角在$C$处\"'
```

## 8. 常见错误与解决方案

### 错误类型分类

#### 类型一：E前缀使用错误
```sql
-- 问题：包含数学符号但未使用E前缀
❌ '{"text": "函数$f(x)=\\frac{1}{x}$的性质"}'

-- 解决：添加E前缀并正确转义
✅ E'{"text": "函数$f(x)=\\\\frac{1}{x}$的性质"}'

-- 问题：不需要E前缀却使用了
❌ E'["基础知识", "解题方法", "典型例题"]'

-- 解决：移除不必要的E前缀
✅ '["基础知识", "解题方法", "典型例题"]'
```

#### 类型二：转义不完整错误
```sql
-- 问题：反斜杠转义不足
❌ E'$\\frac{x^2}{y^2}$'

-- 解决：使用双重转义
✅ E'$\\\\frac{x^2}{y^2}$'

-- 问题：导数符号转义错误
❌ E'$f\'(x) = 2x$'

-- 解决：正确转义单引号
✅ E'$f\\'(x) = 2x$'
```

#### 类型三：KaTeX语法缺失错误
```sql
-- 问题：数学表达式未包裹
❌ E'函数f(x)在x=0处连续'

-- 解决：使用KaTeX语法包裹
✅ E'函数$f(x)$在$x=0$处连续'

-- 问题：复杂公式使用错误语法
❌ E'$\frac{x^2+y^2}{x+y}$'

-- 解决：复杂分数使用$$...$$
✅ E'$$\\\\frac{x^2+y^2}{x+y}$$'
```

#### 类型四：数学文本符号错误（新增重要类型）
```sql
-- 问题：直角标记Rt未用\text{}包裹
❌ E'直角三角形$ABC$中，$Rt$角在$C$处'

-- 解决：用\text{}包裹数学文本符号
✅ E'直角三角形$ABC$中，$\\\\text{Rt}$角在$C$处'

-- 问题：单位符号未用\text{}包裹
❌ E'边长为$5cm$的正方形'

-- 解决：单位符号必须用\text{}包裹
✅ E'边长为$5\\\\text{cm}$的正方形'

-- 问题：数学术语未用\text{}包裹
❌ E'函数$f(x)$的$max$值'

-- 解决：数学术语用\text{}包裹
✅ E'函数$f(x)$的$\\\\text{max}$值'

-- 问题：几何图形标记未用\text{}包裹
❌ E'四边形$ABCD$的对角线'

-- 解决：几何图形标记用\text{}包裹
✅ E'四边形$\\\\text{ABCD}$的对角线'

-- 问题：角度符号使用错误
❌ E'角度为$90°$'

-- 解决：角度符号使用标准KaTeX语法
✅ E'角度为$90^{\\\\circ}$'
```

#### 类型五：K12内容适应性错误
```sql
-- 问题：术语使用不当
❌ E'function $f(x)$的derivative'

-- 解决：使用中文数学术语
✅ E'函数$f(x)$的导数'

-- 问题：难度超出年级要求
❌ E'微分方程：$\\\\frac{dy}{dx} + P(x)y = Q(x)$' -- 高中阶段

-- 解决：调整为适当难度
✅ E'导数公式：$f\\'(x) = \\\\lim_{h \\\\to 0} \\\\frac{f(x+h)-f(x)}{h}$'
```

### 快速修复指南

#### 1. E前缀快速判断口诀
```
有美元符号 → 必须E前缀
有双反斜杠 → 必须E前缀
有导数符号 → 必须E前缀
纯中文教学 → 不需E前缀
```

#### 2. 转义快速检查口诀
```
一个反斜杠 → 改为四个反斜杠（\\\\）
一个单引号 → 改为反斜杠单引号（\\'）
分数符号 → frac前加双反斜杠（\\\\frac）
希腊字母 → 字母前加双反斜杠（\\\\alpha）
```

#### 3. KaTeX包裹快速规则
```
单个变量 → 用$...$包裹：$x$, $y$, $a$
简单等式 → 用$...$包裹：$f(x)=2x+1$
复杂分数 → 用$$...$$包裹：$$\\frac{a+b}{c+d}$$
多行公式 → 用$$...$$包裹：$$\\begin{cases}...$$
```

#### 4. 数学文本符号快速规则（新增）
```
几何标记 → 用\text{}包裹：$\\\\text{Rt}$ (直角)
单位符号 → 用\text{}包裹：$\\\\text{cm}$, $\\\\text{kg}$
数学术语 → 用\text{}包裹：$\\\\text{max}$, $\\\\text{min}$
图形标记 → 用\text{}包裹：$\\\\text{ABCD}$ (四边形)
角度符号 → 用标准语法：$90^{\\\\circ}$ (度数)
中文术语 → 用\text{}包裹：$\\\\text{当且仅当}$
```

## 9. 最佳实践指南

### 开发工作流程（三大原则严格执行版）
1. **编写前准备**
   - 确认目标年级和知识点难度
   - 熟练掌握三大核心原则
   - 准备好KaTeX语法参考和$...$、$$...$$使用规则

2. **编写过程规范（按三大原则执行）**
   - **原则一执行**：严格按照$符号判断是否使用E前缀（有$用E，无$禁E）
   - **原则二执行**：所有数学内容必须用KaTeX语法包裹，无例外
   - **原则三执行**：按优先级规则选择$...$或$$...$$包裹格式

3. **检查验证流程（三大原则验证）**
   - **原则一验证**：使用E前缀检查工具，确保$符号与E前缀完全匹配
   - **原则二验证**：确保所有数学表达式都用KaTeX语法包裹
   - **原则三验证**：确保$$...$$和$...$使用符合优先级规则
   - 在小程序环境中测试渲染效果

4. **版本控制最佳实践**
   - 数学内容修改单独提交
   - 详细描述修改的公式内容
   - 测试通过后再合并到主分支

### 团队协作规范（三大原则协作版）
1. **代码审查清单（三大原则严格版）**
   - [ ] **原则一检查**：E前缀使用100%正确（有$用E，无$禁E）
   - [ ] **原则二检查**：所有数学内容都用KaTeX语法包裹
   - [ ] **原则三检查**：$$...$$和$...$使用符合优先级规则
   - [ ] **转义规范**：符合PostgreSQL规范
   - [ ] **教育内容**：适合K12年级要求
   - [ ] **渲染效果**：小程序中正常显示

2. **快速检查命令**
   ```bash
   # 一行命令检查E前缀使用
   grep -n "E'[^']*'" file.sql | grep -v '\$' && echo "❌ 发现错误E前缀"
   grep -n "'[^']*\$[^']*'" file.sql | grep -v "E'" && echo "❌ 发现遗漏E前缀"
   ```

2. **知识分享机制**
   - 定期分享常见错误案例
   - 建立数学内容格式化FAQ
   - 维护最新的转义符号对照表

3. **工具统一使用**
   - 统一使用检查脚本
   - 共享VS Code配置
   - 建立自动化CI检查

### 性能优化建议
1. **数据库查询优化**
   - 避免在查询中使用复杂的转义处理
   - 使用预编译语句提高性能
   - 合理使用索引

2. **前端渲染优化**
   - 缓存常用的KaTeX渲染结果
   - 异步加载复杂公式
   - 优化大型公式的显示效果

3. **维护性考虑**
   - 建立数学内容的版本管理
   - 记录公式变更历史
   - 设置内容更新的通知机制

## 10. 最佳实践示例对照表

### 常见数学表达式修正示例

| 错误写法 | 正确写法 | 说明 |
|---------|---------|------|
| `已知a>b，比较-2a与-2b的大小` | `已知$a>b$，比较$-2a$与$-2b$的大小` | 所有变量和不等式必须用$包裹 |
| `当x=0时，函数值为` | `当$x=0$时，函数值为` | 等式条件必须用$包裹 |
| `若k≠1，则有解` | `若$k≠1$，则有解` | 不等式参数必须用$包裹 |
| `x-3≥0的解集` | `$x-3≥0$的解集` | 不等式表达式必须完整包裹 |
| `4-1=3，所以` | `$4-1=3$，所以` | 计算表达式必须用$包裹 |
| `5×10000=50000` | `$5×10000=50000$` | 乘法运算必须用$包裹 |
| `线段AB=5，BC=6` | `线段$AB=5$，$BC=6$` | 几何关系必须用$包裹 |
| `当-1<x≤2时` | `当$-1<x≤2$时` | 取值范围必须完整包裹 |
| `函数f(x)在区间` | `函数$f(x)$在区间` | 函数表达式必须用$包裹 |
| `因为7²=49，8²=64` | `因为$7^2=49$，$8^2=64$` | 指数运算必须用KaTeX语法 |

### 完整E前缀+KaTeX包裹示例

```sql
-- ❌ 错误示例
'已知函数f(x)=x²+1，当x>0时，求f(x)的值域'

-- ✅ 正确示例  
E'已知函数$f(x)=x^2+1$，当$x>0$时，求$f(x)$的值域'

-- ❌ 错误示例（不等式性质）
'已知a>b，比较-2a与-2b的大小'

-- ✅ 正确示例
'已知$a>b$，比较$-2a$与$-2b$的大小'

-- ❌ 错误示例（计算过程）
'因为7²=49，8²=64，所以7<√50<8'

-- ✅ 正确示例
E'因为$7^2=49$，$8^2=64$，所以$7<\\\\sqrt{50}<8$'
```

## 11. 相关文件与资源

### 主要应用文件
- [grade_7_2025_second_semester_complete.sql](mdc:database_scripts/grade_scripts/grade_7_2025_second_semester_complete.sql) - 7年级下学期完整脚本（需要大量修正）
- [grade_12_2025_second_semester.sql](mdc:database_scripts/grade_scripts/grade_12_2025_second_semester.sql) - 高三下学期数据脚本
- [grade_12_2025_first_semester.sql](mdc:database_scripts/grade_scripts/grade_12_2025_first_semester.sql) - 高三上学期数据脚本
- [grade_9_2025_second_semester_complete.sql](mdc:database_scripts/grade_scripts/grade_9_2025_second_semester_complete.sql) - 初三下学期完整脚本
- [grade_8_2025_second_semester_complete.sql](mdc:database_scripts/grade_scripts/grade_8_2025_second_semester_complete.sql) - 初二下学期完整脚本

### 配套文件
- [grade_12_2025_curriculum_update_plan.md](mdc:database_scripts/grade_12_2025_curriculum_update_plan.md) - 课程更新计划
- [knowledge_relationships](mdc:database_scripts/knowledge_relationships) - 知识点关系定义

### 参考资源
- [KaTeX官方文档](mdc:https:/katex.org/docs/supported.html) - 支持的数学符号
- [PostgreSQL E字符串文档](mdc:https:/www.postgresql.org/docs/current/sql-syntax-lexical.html#SQL-SYNTAX-STRINGS-ESCAPE) - 转义字符规范
- [mp-html组件文档](mdc:https:/github.com/jin-yufeng/mp-html) - 小程序HTML渲染组件

---

遵循此规范可确保K12数学教育内容的专业性、准确性、可读性和技术兼容性。通过工具化检查和规范化流程，提高数学内容的质量和维护效率。













































