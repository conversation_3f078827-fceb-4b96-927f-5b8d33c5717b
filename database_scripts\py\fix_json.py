import json
import re

def fix_json_escapes(json_str):
    """修复JSON字符串中的转义字符问题"""
    # 常见的需要修复的转义字符
    fixes = [
        # 修复LaTeX数学符号的转义
        (r'\\circ', r'\\\\circ'),
        (r'\\\'', r'\\\\\''),
        (r'\\"', r'\\\\"'),
        # 修复其他常见的转义问题
        (r'(?<!\\)\\(?![\\"/bfnrt])', r'\\\\'),  # 修复单独的反斜杠
    ]
    
    fixed_str = json_str
    for pattern, replacement in fixes:
        fixed_str = re.sub(pattern, replacement, fixed_str)
    
    return fixed_str

def extract_and_fix_json():
    """从insert.json中提取并修复JSON代码块"""
    print("正在提取和修复JSON代码块...")
    
    try:
        # 读取原文件
        with open('insert.json', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 解析外层JSON
        data = json.loads(content)
        text_content = data['text']
        
        # 提取JSON代码块
        pattern = r'```json(.*?)```'
        matches = re.findall(pattern, text_content, re.DOTALL)
        
        if not matches:
            print("没有找到JSON代码块")
            return
        
        json_content = matches[0].strip()
        print(f"原始JSON代码块长度: {len(json_content)}")
        
        # 尝试解析原始JSON
        try:
            parsed = json.loads(json_content)
            print("原始JSON解析成功，无需修复")
            return json_content
        except json.JSONDecodeError as e:
            print(f"原始JSON解析失败: {e}")
            print(f"错误位置: {e.pos}")
            
            # 显示错误周围的内容
            start = max(0, e.pos - 50)
            end = min(len(json_content), e.pos + 50)
            print(f"错误周围内容: {json_content[start:end]}")
        
        # 修复转义字符
        fixed_content = fix_json_escapes(json_content)
        print(f"修复后JSON长度: {len(fixed_content)}")
        
        # 尝试解析修复后的JSON
        try:
            parsed = json.loads(fixed_content)
            print("修复后JSON解析成功!")
            print(f"题目数量: {len(parsed) if isinstance(parsed, list) else 'N/A'}")
            
            # 创建修复后的文件
            fixed_data = data.copy()
            fixed_data['text'] = text_content.replace(json_content, fixed_content)
            
            with open('insert_fixed.json', 'w', encoding='utf-8') as f:
                json.dump(fixed_data, f, ensure_ascii=False, indent=2)
            
            print("已创建修复后的文件: insert_fixed.json")
            return fixed_content
            
        except json.JSONDecodeError as e:
            print(f"修复后仍然解析失败: {e}")
            print(f"错误位置: {e.pos}")
            
            # 尝试更激进的修复
            print("尝试更激进的修复方法...")
            
            # 移除有问题的字符和行
            lines = fixed_content.split('\n')
            problem_line = e.lineno - 1 if e.lineno > 0 else 0
            
            if problem_line < len(lines):
                print(f"问题行 {e.lineno}: {lines[problem_line]}")
                
                # 尝试修复这一行
                if '\\circ' in lines[problem_line]:
                    lines[problem_line] = lines[problem_line].replace('\\circ', '\\\\circ')
                if '\\"' in lines[problem_line] and not '\\\\"' in lines[problem_line]:
                    lines[problem_line] = lines[problem_line].replace('\\"', '\\\\"')
                
                ultra_fixed = '\n'.join(lines)
                
                try:
                    parsed = json.loads(ultra_fixed)
                    print("激进修复成功!")
                    
                    fixed_data = data.copy()
                    fixed_data['text'] = text_content.replace(json_content, ultra_fixed)
                    
                    with open('insert_ultra_fixed.json', 'w', encoding='utf-8') as f:
                        json.dump(fixed_data, f, ensure_ascii=False, indent=2)
                    
                    print("已创建激进修复后的文件: insert_ultra_fixed.json")
                    return ultra_fixed
                    
                except json.JSONDecodeError as e2:
                    print(f"激进修复也失败: {e2}")
            
            return None
            
    except Exception as e:
        print(f"处理过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    result = extract_and_fix_json()
    if result:
        print("JSON修复完成")
    else:
        print("JSON修复失败") 