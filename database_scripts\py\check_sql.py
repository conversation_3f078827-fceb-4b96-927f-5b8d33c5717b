import insert
import json

def check_sql():
    try:
        # 读取insert.json文件
        with open('insert.json', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 生成SQL
        result = insert.main(content)
        sql = result['insert_sql']
        
        print("检查SQL中的LaTeX公式转义...")
        
        # 检查是否还有E字符串
        if "E'" in sql:
            print("⚠️  SQL中仍然包含E字符串")
            e_string_count = sql.count("E'")
            print(f"E字符串数量: {e_string_count}")
        else:
            print("✓ SQL中没有E字符串，使用标准字符串字面值")
        
        # 检查LaTeX公式示例
        latex_patterns = ["\\\\frac", "\\\\sqrt", "\\\\(", "\\\\)"]
        for pattern in latex_patterns:
            if pattern in sql:
                print(f"✓ 找到LaTeX模式: {pattern}")
                # 找到第一个包含该模式的位置
                start = sql.find(pattern) - 20
                end = sql.find(pattern) + 50
                if start < 0:
                    start = 0
                sample = sql[start:end]
                print(f"  示例: ...{sample}...")
        
        # 检查双反斜杠的数量
        double_backslash = sql.count("\\\\")
        print(f"SQL中双反斜杠数量: {double_backslash}")
        
        # 将SQL保存到文件供进一步检查
        with open('generated_sql.sql', 'w', encoding='utf-8') as f:
            f.write(sql)
        print("✓ SQL已保存到 generated_sql.sql")
        
        return True
        
    except Exception as e:
        print(f"✗ 检查失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    check_sql() 