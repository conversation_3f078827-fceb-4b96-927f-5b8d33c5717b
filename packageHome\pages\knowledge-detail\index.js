// 知识点详情页面 - 基于数据库字段结构
const deviceUtils = require('../../../utils/device-info.js'); 

// 导入数据配置
const { fractionOperationsKnowledge, aiRecommendations, knowledgeStats } = require('../../../data/knowledge-points.js');

// 数据库映射常量 - 与知识图谱页面保持一致
const DIFFICULTY_LEVELS = {
  'basic': 'basic',
  'intermediate': 'intermediate', 
  'advanced': 'advanced',
  'expert': 'expert',
  'master': 'master'
};

const DIFFICULTY_LEVEL_TEXT = {
  'basic': '基础',
  'intermediate': '中等',
  'advanced': '高级', 
  'expert': '专家',
  'master': '大师'
};

const RELATIONSHIP_TYPES = {
  'prerequisite': 'prerequisite',
  'successor': 'successor',
  'related': 'related',
  'extension': 'extension',
  'parallel': 'parallel',
  'contains': 'contains',
  'example_of': 'example_of',
  'application_of': 'application_of',
  'cross_subject': 'cross_subject'
};

const RELATIONSHIP_TYPE_TEXT = {
  'prerequisite': '前置知识',
  'successor': '后续知识',
  'related': '相关知识',
  'extension': '扩展知识',
  'parallel': '并行知识',
  'contains': '包含知识',
  'example_of': '示例知识',
  'application_of': '应用知识',
  'cross_subject': '跨学科知识'
};

const SUBJECT_TEXT = {
  'mathematics': '数学',
  'chinese': '语文',
  'english': '英语',
  'physics': '物理',
  'chemistry': '化学',
  'biology': '生物',
  'history': '历史',
  'geography': '地理',
  'politics': '政治'
};

const GRADE_LEVEL_MAP = {
  1: '小学一年级',
  2: '小学二年级',
  3: '小学三年级',
  4: '小学四年级',
  5: '小学五年级',
  6: '小学六年级',
  7: '初中一年级',
  8: '初中二年级',
  9: '初中三年级',
  10: '高中一年级',
  11: '高中二年级',
  12: '高中三年级'
};

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 基础信息
    id: '',
    
    // 用户登录状态
    isLoggedIn: false,
    userInfo: null,
    
    // 知识点信息 - 初始为空，从数据库加载
    knowledgePoint: null,
    
    // 学生掌握状态（仅在登录时才有数据）
    masteryInfo: null,
    
    isLoading: true,
    isError: false,
    
    // 设备适配信息
    isIPad: false,
    statusBarHeight: 20,
    navBarHeight: 44,
    safeAreaBottom: 0,
    
    // UI状态控制
    currentTab: 0,
    tabs: ['概要', '详解', '例题', '练习'],
    isCollected: false,
    showShareMenu: false,
    showBackToTop: false,
    
    // 知识图谱功能状态
    showKnowledgeNetwork: false,
    showLearningPath: false,
    showWeaknessAnalysis: false,
    showLearningGuidance: false,
    
    // 内容展开状态
    solutionVisibility: {},
    detailExpanded: {},
    practiceAnswerVisibility: {},
    
    // 滚动相关
    scrollTop: 0,
    currentProgressPercentage: 0,
    
    // AI语音讲解
    isVoicePlaying: false,
    voicePlayingText: '',
    
    // 智能推荐系统（仅在登录时显示）
    intelligentRecommendations: [],
    
    // 从知识点对象中提取的数据
    learningGuidance: null,
    knowledgeNetwork: null,
    difficultyInfo: null,
    knowledgeRelationships: null,
    learningPath: null,
    
    // 练习系统
    selectedPracticeLevel: 0, // 0:基础, 1:进阶, 2:挑战
    
    // 学习行为追踪（仅在登录时记录）
    learningBehavior: null,
    
    // 错误状态推荐
    recommendKnowledge: [],
    
    // AI推荐学习计划（仅在登录时显示）
    aiRecommendations: null,
    
    // 知识图谱数据（仅在登录时显示）
    knowledgeGraph: null,
    
    // 底部安全区域
    safeAreaBottom: 0,

    // 标签页状态
    activeTab: '5分钟搞定',
    knowledgeActiveTab: '前置知识',

    // 知识关联数据 - 基础数据无需登录
    prerequisites: [],
    subsequent: [], 
    related: [],
    extended: [],
    knowledgeStats: {},
    
    // 拓展延伸功能标志位
    hasMultimediaResources: false,
    hasTrackSpecificContent: false,
    hasExtensionContent: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function(options) {
    console.log('页面加载参数:', options);
    
    // 初始化设备信息
    this.initDeviceInfo();
    
    // 从URL参数获取知识点编码
    const nodeCode = options.nodeCode || options.id;
    
    if (nodeCode) {
      // 保存知识点编码
      this.setData({
        id: nodeCode
      });
      
      // 加载知识点详情数据
      this.loadKnowledgeDetailData(nodeCode);
    } else {
      console.error('缺少知识点编码参数');
      this.setData({
        isLoading: false,
        isError: true
      });
      
      wx.showToast({
        title: '参数错误',
        icon: 'none',
        duration: 2000
      });
    }
  },

  /**
   * 加载知识点详情数据
   */
  loadKnowledgeDetailData: function(nodeCode) {
    this.setData({
      isLoading: true,
      isError: false
    });
    
    // 首先检查用户登录状态
    this.checkUserLoginStatus();
    
    // 并行获取基础数据和内容详情
    const basicDataPromise = this.getKnowledgeBasicData(nodeCode);
    const contentPromise = this.getKnowledgeContentDetails(nodeCode);
    
    Promise.all([basicDataPromise, contentPromise])
      .then(([basicData, contentData]) => {
        // 合并基础数据和内容数据
        const knowledgePoint = this.mergeKnowledgeData(basicData, contentData);
        
        // 处理知识点数据，确保字段完整性
        const processedKnowledgePoint = this.processKnowledgePointData(knowledgePoint);
        
        this.setData({
          knowledgePoint: processedKnowledgePoint,
          isLoading: false
        });
        
        // 设置页面标题
        wx.setNavigationBarTitle({
          title: processedKnowledgePoint.node_name || '知识点详情'
        });
        
        // 如果用户已登录，获取个人掌握数据
        if (this.data.isLoggedIn) {
          this.loadPersonalMasteryData(processedKnowledgePoint.id);
          this.initLearningBehaviorTracking();
        }
        
        // 加载知识关联数据
        this.loadKnowledgeRelationships(processedKnowledgePoint.id);
        
      })
      .catch(error => {
        console.error('加载知识点数据失败:', error);
        this.setData({
          isLoading: false,
          isError: true
        });
        
        wx.showToast({
          title: '数据加载失败',
          icon: 'none',
          duration: 2000
        });
      });
  },

  /**
   * 获取知识点基础数据
   */
  getKnowledgeBasicData: function(nodeCode) {
    return new Promise((resolve, reject) => {
      wx.cloud.callFunction({
        name: 'knowledge-graph-query',
        data: {
          action: 'getKnowledgeNodes',
          params: {
            nodeIds: [nodeCode], // 支持ID或node_code查询
            limit: 1
          }
        }
      }).then(result => {
        console.log('基础数据查询结果:', result);
        
        if (result.result.success && result.result.data.length > 0) {
          resolve(result.result.data[0]);
        } else {
          reject(new Error('知识点不存在'));
        }
      }).catch(reject);
    });
  },

  /**
   * 获取知识点内容详情
   */
  getKnowledgeContentDetails: function(nodeCode) {
    return new Promise((resolve, reject) => {
      wx.cloud.callFunction({
        name: 'knowledge-graph-query',
        data: {
          action: 'getKnowledgeContent',
          params: {
            nodeId: nodeCode
          }
        }
      }).then(result => {
        console.log('内容详情查询结果:', result);
        
        if (result.result.success) {
          resolve(result.result.data || {});
        } else {
          // 内容详情可选，不影响主流程
          resolve({});
        }
      }).catch(error => {
        console.warn('获取内容详情失败，使用默认值:', error);
        resolve({});
      });
    });
  },

  /**
   * 合并基础数据和内容数据
   */
  mergeKnowledgeData: function(basicData, contentData) {
    return {
      ...basicData,
      // 合并内容详情字段
      description: contentData.description || basicData.description || '',
      detailed_explanation: contentData.detailed_explanation || '',
      key_points: contentData.key_points || [],
      formulas: contentData.formulas || [],
      examples: contentData.examples || [],
      concepts_breakdown: contentData.concepts_breakdown || [],
      common_mistakes: contentData.common_mistakes || [],
      learning_tips: contentData.learning_tips || [],
      // 多媒体资源
      video_urls: contentData.video_urls || [],
      audio_urls: contentData.audio_urls || [],
      image_urls: contentData.image_urls || [],
      animation_urls: contentData.animation_urls || [],
      reference_materials: contentData.reference_materials || [],
      practice_resources: contentData.practice_resources || [],
      // 文理科差异化内容
      liberal_arts_content: contentData.liberal_arts_content || {},
      science_content: contentData.science_content || {}
    };
  },

  /**
   * 加载个人掌握数据
   */
  loadPersonalMasteryData: function(nodeId) {
    const userInfo = this.data.userInfo;
    if (!userInfo || !userInfo.id) {
      console.log('用户未登录，跳过个人数据获取');
      return;
    }

    wx.cloud.callFunction({
      name: 'knowledge-graph-query',
      data: {
        action: 'getStudentMastery',
        params: {
          studentId: userInfo.id,
          nodeId: nodeId
        }
      }
    }).then(result => {
      console.log('个人掌握数据查询结果:', result);
      
      if (result.result.success && result.result.data.length > 0) {
        this.setData({
          masteryInfo: result.result.data[0]
        });
      } else {
        // 如果没有掌握记录，创建默认记录
        this.setData({
          masteryInfo: {
            mastery_status: 'not_started',
            mastery_percentage: 0,
            basic_concept_mastery: false,
            calculation_skill_mastery: false,
            application_ability_mastery: false,
            predicted_mastery_days: 7
          }
        });
      }
    }).catch(error => {
      console.error('获取个人掌握数据失败:', error);
    });
  },

  /**
   * 初始化学习行为跟踪
   */
  initLearningBehaviorTracking: function() {
    this.setData({
      learningBehavior: {
        startTime: Date.now(),
        tabSwitchCount: 0,
        readingProgress: {},
        practiceInteractions: 0,
        voiceUsageCount: 0
      }
    });
  },

  /**
   * 处理知识点数据，确保有默认值和完整结构
   */
  processKnowledgePointData: function(knowledgePoint) {
    if (!knowledgePoint) {
      return null;
    }

    // 处理内容结构，基于数据库字段构建
    const content = this.buildContentStructure(knowledgePoint);
    
    const processedData = {
      ...knowledgePoint,
      // 基础信息
      title: knowledgePoint.node_name || '知识点详情',
      description: knowledgePoint.description || '暂无描述',
      
      // 内容结构 - 基于数据库字段构建
      content: content,
      
      // 练习结构 - 基于难度等级构建  
      practice: this.buildPracticeStructure(knowledgePoint),
      
      // 记忆技巧
      memory_tips: knowledgePoint.memory_tips || "理解核心概念，结合实际应用场景，通过练习巩固掌握",
      
      // 处理JSON字段
      common_misconceptions: this.processJsonField(knowledgePoint.common_misconceptions) || [],
      key_points: this.processJsonField(knowledgePoint.key_points) || [],
      formulas: this.processJsonField(knowledgePoint.formulas) || [],
      examples: this.processJsonField(knowledgePoint.examples) || [],
      concepts_breakdown: this.processJsonField(knowledgePoint.concepts_breakdown) || [],
      learning_tips: this.processJsonField(knowledgePoint.learning_tips) || [],
      
      // 新增：拓展延伸相关字段
      learningTips: this.processLearningTips(knowledgePoint.learning_tips) || [],
      commonMistakes: this.processCommonMistakes(knowledgePoint.common_mistakes) || [],
      videoUrls: this.processJsonField(knowledgePoint.video_urls) || [],
      audioUrls: this.processJsonField(knowledgePoint.audio_urls) || [],
      imageUrls: this.processJsonField(knowledgePoint.image_urls) || [],
      animationUrls: this.processJsonField(knowledgePoint.animation_urls) || [],
      referenceMaterials: this.processJsonField(knowledgePoint.reference_materials) || [],
      practiceResources: this.processJsonField(knowledgePoint.practice_resources) || [],
      liberalArtsContent: this.processJsonField(knowledgePoint.liberal_arts_content) || {},
      scienceContent: this.processJsonField(knowledgePoint.science_content) || {},
      
      // 显示字段
      difficulty_display: this.getDifficultyDisplay(knowledgePoint.difficulty),
      exam_frequency_display: this.getExamFrequencyDisplay(knowledgePoint.exam_frequency),
      importance_stars: this.getImportanceStars(knowledgePoint.importance_level),
      
      // 教材信息显示
      chapter_info: this.buildChapterInfo(knowledgePoint),
      
      // 能力要求标签
      ability_tags: this.buildAbilityTags(knowledgePoint),
      
      // 文理科适用性
      track_suitability: this.getTrackSuitability(knowledgePoint.knowledge_applicability, knowledgePoint.target_academic_tracks)
    };
    
    // 设置拓展内容显示标志
    this.setExtensionContentFlags(processedData);
    
    return processedData;
  },

  /**
   * 构建内容结构
   */
  buildContentStructure: function(knowledgePoint) {
    const sections = [];
    
    // 基本概念（基于description）
    if (knowledgePoint.description) {
      sections.push({
        title: "基本概念",
        text: knowledgePoint.description
      });
    }
    
    // 详细解释
    if (knowledgePoint.detailed_explanation) {
      sections.push({
        title: "详细解释", 
        text: knowledgePoint.detailed_explanation
      });
    }
    
    // 重点内容
    const keyPoints = this.processJsonField(knowledgePoint.key_points);
    if (keyPoints && keyPoints.length > 0) {
      sections.push({
        title: "重点内容",
        text: keyPoints.join('\n• ')
      });
    }
    
    // 概念分解 - 修复JSON对象显示问题
    const conceptsBreakdown = this.processJsonField(knowledgePoint.concepts_breakdown);
    if (conceptsBreakdown && conceptsBreakdown.length > 0) {
      let conceptText = '';
      if (Array.isArray(conceptsBreakdown)) {
        conceptText = conceptsBreakdown.map(concept => {
          if (typeof concept === 'string') {
            return concept;
          } else if (typeof concept === 'object' && concept !== null) {
            // 处理对象形式的概念分解
            if (concept.title && concept.description) {
              return `${concept.title}: ${concept.description}`;
            } else if (concept.name && concept.content) {
              return `${concept.name}: ${concept.content}`;
            } else {
              // 如果是其他格式的对象，转换为可读文本
              return Object.values(concept).filter(v => v).join(': ');
            }
          }
          return String(concept);
        }).join('\n\n');
      } else {
        conceptText = String(conceptsBreakdown);
      }
      
      sections.push({
        title: "概念分解",
        text: conceptText || '概念分解内容正在完善中，将结合实际应用场景深入解析...'
      });
    }
    
    return {
      sections: sections.length > 0 ? sections : [{
        title: "基本概念",
        text: "正在加载知识点内容，请稍候..."
      }],
      examples: this.processJsonField(knowledgePoint.examples) || [],
      formulas: this.processJsonField(knowledgePoint.formulas) || [],
      related: []
    };
  },

  /**
   * 构建练习结构
   */
  buildPracticeStructure: function(knowledgePoint) {
    return {
      basicQuestions: [],
      advancedQuestions: [],
      challengeQuestions: [],
      // 基于能力要求确定练习类型
      practiceTypes: this.determinePracticeTypes(knowledgePoint)
    };
  },

  /**
   * 确定练习类型
   */
  determinePracticeTypes: function(knowledgePoint) {
    const types = [];
    
    if (knowledgePoint.requires_memorization) {
      types.push('概念理解');
    }
    if (knowledgePoint.requires_calculation_skill) {
      types.push('计算应用');
    }
    if (knowledgePoint.requires_application) {
      types.push('实际应用');
    }
    if (knowledgePoint.requires_analysis) {
      types.push('案例分析');
    }
    
    return types.length > 0 ? types : ['综合应用'];
  },

  /**
   * 构建章节信息
   */
  buildChapterInfo: function(knowledgePoint) {
    const parts = [];
    
    if (knowledgePoint.chapter_number) {
      parts.push(`第${knowledgePoint.chapter_number}章`);
    }
    if (knowledgePoint.chapter_title) {
      parts.push(knowledgePoint.chapter_title);
    }
    if (knowledgePoint.section_number) {
      parts.push(`第${knowledgePoint.section_number}节`);
    }
    if (knowledgePoint.section_title) {
      parts.push(knowledgePoint.section_title);
    }
    
    return parts.join(' ');
  },

  /**
   * 构建能力标签
   */
  buildAbilityTags: function(knowledgePoint) {
    const tags = [];
    
    if (knowledgePoint.requires_memorization) tags.push('记忆');
    if (knowledgePoint.requires_understanding) tags.push('理解');
    if (knowledgePoint.requires_application) tags.push('应用');
    if (knowledgePoint.requires_analysis) tags.push('分析');
    if (knowledgePoint.requires_synthesis) tags.push('综合');
    if (knowledgePoint.requires_evaluation) tags.push('评价');
    
    return tags;
  },

  /**
   * 获取文理科适用性
   */
  getTrackSuitability: function(applicability, targetTracks) {
    if (applicability === 'universal') {
      return '文理通用';
    }
    if (applicability === 'liberal_arts_focused') {
      return '文科重点';
    }
    if (applicability === 'science_focused') {
      return '理科重点';
    }
    
    // 基于目标tracks判断
    if (targetTracks && targetTracks.length > 0) {
      return targetTracks.join('、');
    }
    
    return '通用';
  },

  /**
   * 处理JSON字段，确保正确解析
   */
  processJsonField: function(field) {
    if (!field) return null;
    
    try {
      if (typeof field === 'string') {
        return JSON.parse(field);
      } else if (Array.isArray(field)) {
        return field;
      } else {
        return field;
      }
    } catch (error) {
      console.warn('解析JSON字段失败:', error, field);
      return Array.isArray(field) ? field : [field];
    }
  },

  /**
   * 专门处理学习技巧数据，确保格式化显示
   */
  processLearningTips: function(field) {
    const tips = this.processJsonField(field);
    if (!tips || !Array.isArray(tips)) {
      return [];
    }
    
    return tips.map(item => {
      if (typeof item === 'string') {
        return {
          title: null,
          description: item
        };
      } else if (typeof item === 'object' && item !== null) {
        return {
          title: item.title || item.name || null,
          description: item.description || item.content || item.tip || String(item)
        };
      }
      return {
        title: null,
        description: String(item)
      };
    });
  },

  /**
   * 专门处理常见错误数据，为WXML添加类型标志
   */
  processCommonMistakes: function(field) {
    const mistakes = this.processJsonField(field);
    if (!mistakes || !Array.isArray(mistakes)) {
      return [];
    }
    
    return mistakes.map(item => {
      if (typeof item === 'string') {
        return {
          isString: true,
          text: item
        };
      } else if (typeof item === 'object' && item !== null) {
        return {
          ...item,
          isString: false
        };
      }
      return {
        isString: true,
        text: String(item)
      };
    });
  },

  /**
   * 设置拓展内容显示标志
   */
  setExtensionContentFlags: function(processedData) {
    // 判断是否有多媒体资源
    const hasMultimediaResources = (
      (processedData.videoUrls && processedData.videoUrls.length > 0) ||
      (processedData.audioUrls && processedData.audioUrls.length > 0) ||
      (processedData.animationUrls && processedData.animationUrls.length > 0)
    );
    
    // 判断是否有文理科差异化内容
    const hasTrackSpecificContent = (
      (processedData.liberalArtsContent && processedData.liberalArtsContent.applications) ||
      (processedData.scienceContent && processedData.scienceContent.applications)
    );
    
    // 判断是否有任何拓展内容
    const hasExtensionContent = (
      (processedData.learningTips && processedData.learningTips.length > 0) ||
      (processedData.commonMistakes && processedData.commonMistakes.length > 0) ||
      hasMultimediaResources ||
      (processedData.referenceMaterials && processedData.referenceMaterials.length > 0) ||
      (processedData.practiceResources && processedData.practiceResources.length > 0) ||
      hasTrackSpecificContent
    );
    
    this.setData({
      hasMultimediaResources: hasMultimediaResources,
      hasTrackSpecificContent: hasTrackSpecificContent,
      hasExtensionContent: hasExtensionContent
    });
  },

  /**
   * 播放视频
   */
  playVideo: function(e) {
    const url = e.currentTarget.dataset.url;
    if (!url) {
      wx.showToast({
        title: '视频链接无效',
        icon: 'none'
      });
      return;
    }
    
    // 可以在这里添加视频播放逻辑
    wx.showModal({
      title: '播放视频',
      content: `即将播放解析视频`,
      showCancel: true,
      confirmText: '播放',
      success: (res) => {
        if (res.confirm) {
          // 实际项目中可以打开视频播放页面或使用video组件
          console.log('播放视频:', url);
        }
      }
    });
  },

  /**
   * 播放音频
   */
  playAudio: function(e) {
    const url = e.currentTarget.dataset.url;
    if (!url) {
      wx.showToast({
        title: '音频链接无效',
        icon: 'none'
      });
      return;
    }
    
    // 可以在这里添加音频播放逻辑
    wx.showModal({
      title: '播放音频',
      content: `即将播放音频解析`,
      showCancel: true,
      confirmText: '播放',
      success: (res) => {
        if (res.confirm) {
          // 实际项目中可以使用小程序音频API
          console.log('播放音频:', url);
        }
      }
    });
  },

  /**
   * 播放动画
   */
  playAnimation: function(e) {
    const url = e.currentTarget.dataset.url;
    if (!url) {
      wx.showToast({
        title: '动画链接无效',
        icon: 'none'
      });
      return;
    }
    
    // 可以在这里添加动画播放逻辑
    wx.showModal({
      title: '观看动画',
      content: `即将观看动画演示`,
      showCancel: true,
      confirmText: '观看',
      success: (res) => {
        if (res.confirm) {
          // 实际项目中可以跳转到动画播放页面
          console.log('播放动画:', url);
        }
      }
    });
  },

  /**
   * 打开参考资料
   */
  openReference: function(e) {
    const url = e.currentTarget.dataset.url;
    if (!url) {
      wx.showToast({
        title: '资料链接无效',
        icon: 'none'
      });
      return;
    }
    
    // 可以在这里添加打开参考资料的逻辑
    wx.showModal({
      title: '查看参考资料',
      content: `即将打开参考资料`,
      showCancel: true,
      confirmText: '打开',
      success: (res) => {
        if (res.confirm) {
          // 实际项目中可以使用webview打开链接
          console.log('打开参考资料:', url);
        }
      }
    });
  },

  /**
   * 开始练习
   */
  startPractice: function(e) {
    const resource = e.currentTarget.dataset.resource;
    if (!resource) {
      wx.showToast({
        title: '练习资源无效',
        icon: 'none'
      });
      return;
    }
    
    // 检查登录状态
    if (!this.data.isLoggedIn) {
      this.showLoginRequired('练习功能');
      return;
    }
    
    // 可以在这里添加跳转到练习页面的逻辑
    wx.showModal({
      title: '开始练习',
      content: `即将开始练习：${resource.title || '练习题集'}`,
      showCancel: true,
      confirmText: '开始',
      success: (res) => {
        if (res.confirm) {
          // 实际项目中可以跳转到练习页面
          console.log('开始练习:', resource);
        }
      }
    });
  },

  /**
   * 跳转到登录页面
   */
  goToLogin: function() {
    wx.navigateTo({
      url: '/packageProfile/pages/login/index'
    });
  },

  /**
   * 显示需要登录提示
   */
  showLoginRequired: function(feature = '该功能') {
    wx.showModal({
      title: '需要登录',
      content: `${feature}需要登录后才能使用，是否前往登录？`,
      showCancel: true,
      cancelText: '稍后',
      confirmText: '去登录',
      success: (res) => {
        if (res.confirm) {
          this.goToLogin();
        }
      }
    });
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function() {
    // 重新初始化设备信息（处理屏幕旋转）
    this.initDeviceInfo();
    
    // 重新检查登录状态（可能从登录页面返回）
    this.checkUserLoginStatus();
  },

  /**
   * 初始化设备信息
   */
  initDeviceInfo: function() {
    const safeAreaInfo = deviceUtils.getSafeAreaInfo();
    this.setData({
      statusBarHeight: safeAreaInfo.statusBarHeight,
      navBarHeight: safeAreaInfo.navBarHeight,
      safeAreaBottom: safeAreaInfo.safeAreaBottom,
      isIPad: deviceUtils.isIPad()
    });
    
    // 设置CSS变量
    deviceUtils.setPageCSSVariables('.device-container', safeAreaInfo);
  },

  /**
   * 获取难度等级显示文本
   */
  getDifficultyDisplay: function(difficulty) {
    const difficultyMap = {
      'basic': '基础应用',
      'intermediate': '中等应用', 
      'advanced': '高级应用',
      'expert': '专家级',
      'master': '精通级'
    };
    return difficultyMap[difficulty] || '中等应用';
  },

  /**
   * 获取考试频率显示文本
   */
  getExamFrequencyDisplay: function(frequency) {
    const frequencyMap = {
      'critical': '核心必考',
      'high': '高频考点',
      'medium': '常见考点',
      'low': '基础考点'
    };
    return frequencyMap[frequency] || '常见考点';
  },

  /**
   * 获取重要程度星级显示
   */
  getImportanceStars: function(level) {
    const starLevel = parseInt(level) || 3;
    const clampedLevel = Math.max(1, Math.min(5, starLevel)); // 确保在1-5范围内
    const stars = '★'.repeat(clampedLevel) + '☆'.repeat(5 - clampedLevel);
    return stars;
  },

  /**
   * 检查用户登录状态
   */
  checkUserLoginStatus: function() {
    try {
      // 从本地存储获取用户信息
      const userInfo = wx.getStorageSync('userInfo');
      const token = wx.getStorageSync('token');
      const openid = wx.getStorageSync('openid');
      
      // 多种方式检查登录状态
      const isLoggedIn = !!(userInfo && (token || openid));
      
      this.setData({
        userInfo: userInfo,
        isLoggedIn: isLoggedIn
      });
      
      console.log('用户登录状态检查:', {
        hasUserInfo: !!userInfo,
        hasToken: !!token,
        hasOpenid: !!openid,
        isLoggedIn: isLoggedIn
      });
      
      return isLoggedIn;
      
    } catch (error) {
      console.error('检查登录状态失败:', error);
      this.setData({
        userInfo: null,
        isLoggedIn: false
      });
      return false;
    }
  },

  /**
   * 处理导航栏返回按钮
   */
  onBack: function() {
    wx.navigateBack();
  },

  /**
   * 切换主要标签页
   */
  switchTab: function(e) {
    const tab = e.currentTarget.dataset.tab;
    console.log('切换主要标签页:', tab);
    
    this.setData({
      activeTab: tab
    });
    
    // 记录学习行为
    this.updateLearningProgress('tab_switch', 3000); // 记录3秒学习时间
  },

  /**
   * 切换收藏状态
   */
  toggleCollection: function() {
    if (!this.data.isLoggedIn) {
      this.showLoginRequired('收藏功能');
      return;
    }
    
    const isCollected = !this.data.isCollected;
    this.setData({ isCollected });
    
    // 调用云函数更新收藏状态
    wx.cloud.callFunction({
      name: 'user-knowledge-status',
      data: {
        action: 'updateCollection',
        data: {
          nodeCode: this.data.knowledgePoint.node_code,
          isCollected: isCollected
        }
      }
    }).then(result => {
      console.log('收藏状态更新结果:', result);
      
      wx.showToast({
        title: isCollected ? '收藏成功' : '取消收藏',
        icon: 'success'
      });
    }).catch(error => {
      console.error('更新收藏状态失败:', error);
      // 回滚状态
      this.setData({ isCollected: !isCollected });
      
      wx.showToast({
        title: '操作失败，请重试',
        icon: 'none',
        duration: 1500
      });
    });
  },

  /**
   * AI语音讲解
   */
  startQuickExplanation: function() {
    // 记录开始时间
    const startTime = Date.now();
    
    wx.showToast({
      title: '正在准备智能精讲',
      icon: 'loading',
      duration: 1000
    });
    
    setTimeout(() => {
      this.setData({
        voicePlaying: true,
        voiceText: '正在为您准备个性化精讲内容...'
      });
      
      // 模拟精讲播放
      setTimeout(() => {
        this.stopVoiceExplanation();
        
        // 记录学习行为 - 快速精讲
        const timeSpent = Date.now() - startTime;
        this.updateLearningProgress('voice_explanation', timeSpent);
      }, 5000);
    }, 1000);
  },

  /**
   * 停止语音解释
   */
  stopVoiceExplanation: function() {
    this.setData({
      voicePlaying: false,
      voiceText: ''
    });
  },

  /**
   * AI智能练习
   */
  startAIPractice: function() {
    if (!this.data.isLoggedIn) {
      this.showLoginRequired('智能训练');
      return;
    }
    
    wx.showToast({
      title: '正在生成智能训练题',
      icon: 'loading',
      duration: 1000
    });
    
    setTimeout(() => {
      wx.navigateTo({
        url: `/packageHome/pages/practice-list/index?subject=数学&chapter=二次函数&topic=${this.data.knowledgePoint.title}&type=smart`
      }).catch(err => {
        wx.showToast({
          title: '智能训练功能开发中',
          icon: 'none',
          duration: 2000
        });
      });
    }, 1000);
  },

  /**
   * 查看薄弱点
   */
  viewWeakPoints: function() {
    if (!this.data.isLoggedIn) {
      this.showLoginRequired('薄弱点分析');
      return;
    }
    
    wx.showToast({
      title: '正在分析薄弱环节',
      icon: 'loading',
      duration: 1000
    });
    
    setTimeout(() => {
      wx.navigateTo({
        url: '/packageAI/pages/practice-recommend/index?topic=quadratic_function&type=weakness'
      }).catch(err => {
        wx.showToast({
          title: '薄弱点分析功能开发中',
          icon: 'none',
          duration: 2000
        });
      });
    }, 1000);
  },

  /**
   * 同步作业
   */
  startSyncHomework: function() {
    if (!this.data.isLoggedIn) {
      this.showLoginRequired('配套练习');
      return;
    }
    
    wx.showToast({
      title: '正在加载配套练习',
      icon: 'loading',
      duration: 1000
    });
    
    setTimeout(() => {
      wx.navigateTo({
        url: '/packageClass/pages/homework-list/index?subject=数学&topic=二次函数'
      });
    }, 1000);
  },

  /**
   * 分层练习
   */
  startLevelPractice: function(e) {
    const level = e.currentTarget.dataset.level;
    const levelNames = ['基础理解', '应用提升', '综合拓展'];
    
    wx.showToast({
      title: `正在准备${levelNames[level]}训练`,
      icon: 'loading',
      duration: 1000
    });
    
    setTimeout(() => {
      wx.navigateTo({
        url: `/packageHome/pages/practice-list/index?subject=数学&chapter=二次函数&topic=${this.data.knowledgePoint.title}&level=${level}&levelName=${levelNames[level]}`
      }).catch(err => {
        wx.showToast({
          title: '训练功能开发中',
          icon: 'none',
          duration: 2000
        });
      });
    }, 1000);
  },

  /**
   * 切换知识关联Tab
   */
  switchKnowledgeTab: function(e) {
    const tab = e.currentTarget.dataset.tab;
    console.log('切换知识关联Tab:', tab);
    
    this.setData({
      knowledgeActiveTab: tab
    });
  },

  /**
   * 切换知识点详情展开状态
   */
  toggleKnowledgeDetail: function(e) {
    const { id, type } = e.currentTarget.dataset;
    console.log('切换知识点详情:', id, type);
    
    const dataKey = this.getDataKeyByType(type);
    
    if (!dataKey) return;
    
    const items = this.data[dataKey];
    const itemIndex = items.findIndex(item => item.id === id);
    
    if (itemIndex !== -1) {
      const updatePath = `${dataKey}[${itemIndex}].showDetail`;
      this.setData({
        [updatePath]: !items[itemIndex].showDetail
      });
    }
  },

  /**
   * 根据类型获取数据键名
   */
  getDataKeyByType: function(type) {
    const typeMap = {
      'prerequisite': 'prerequisites',
      'subsequent': 'subsequent', 
      'related': 'related',
      'extended': 'extended'
    };
    return typeMap[type];
  },

  /**
   * 立即学习功能 - 优化跳转参数处理
   */
  startLearning: function(e) {
    const { knowledgeId, knowledgeName, knowledgeCode } = e.currentTarget.dataset;
    
    // 优先使用code，其次使用id
    const nodeCode = knowledgeCode || knowledgeId;
    const nodeName = knowledgeName || '知识点';
    
    console.log('开始学习知识点:', nodeName, nodeCode);
    
    // 记录知识网络探索行为
    this.updateLearningProgress('knowledge_network_exploration', 8000); // 8秒探索时间
    
    wx.navigateTo({
      url: `/packageHome/pages/knowledge-detail/index?nodeCode=${nodeCode}&name=${encodeURIComponent(nodeName)}&from=knowledge-detail`,
      fail: (error) => {
        console.error('跳转失败:', error);
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 加入复习功能
   */
  addToReview: function(e) {
    if (!this.data.isLoggedIn) {
      this.showLoginRequired('复习计划');
      return;
    }
    
    const { knowledgeId, knowledgeName } = e.currentTarget.dataset;
    
    // 记录复习计划行为
    this.updateLearningProgress('add_to_review', 2000); // 2秒操作时间
    
    wx.showToast({
      title: '已加入复习计划',
      icon: 'success',
      duration: 1500
    });
  },

  /**
   * 滚动到顶部
   */
  scrollToTop: function() {
    this.setData({ scrollTop: 0 });
  },

  /**
   * 滚动事件处理
   */
  onScroll: function(e) {
    const scrollTop = e.detail.scrollTop;
    const showBackToTop = scrollTop > 500;
    
    if (showBackToTop !== this.data.showBackToTop) {
      this.setData({ showBackToTop });
    }
  },

  /**
   * 阻止触摸移动
   */
  preventTouchMove: function() {
    return false;
  },

  /**
   * 阻止事件冒泡
   */
  stopPropagation: function() {
    return false;
  },

  /**
   * 重新加载数据
   */
  reloadData: function() {
    if (this.data.id) {
      this.loadKnowledgeDetailData(this.data.id);
    } else {
      wx.showToast({
        title: '参数错误，无法重新加载',
        icon: 'none'
      });
    }
  },

  /**
   * 更新学习进度 - 当用户进行学习行为时调用
   */
  updateLearningProgress: function(actionType, timeSpent = 0) {
    if (!this.data.isLoggedIn || !this.data.userInfo || !this.data.knowledgePoint) {
      return;
    }

    const userInfo = this.data.userInfo;
    const knowledgePoint = this.data.knowledgePoint;

    // 记录学习行为
    const learningBehavior = this.data.learningBehavior || {};
    learningBehavior.totalTime = (learningBehavior.totalTime || 0) + timeSpent;
    learningBehavior.lastAction = actionType;
    learningBehavior.actionCount = (learningBehavior.actionCount || 0) + 1;

    this.setData({
      learningBehavior: learningBehavior
    });

    // 异步更新到数据库
    wx.cloud.callFunction({
      name: 'knowledge-graph-query',
      data: {
        action: 'updateMasteryStatus',
        params: {
          studentId: userInfo.id,
          nodeId: knowledgePoint.id,
          masteryStatus: this.calculateMasteryStatus(learningBehavior),
          masteryPercentage: this.calculateMasteryPercentage(learningBehavior),
          studyTimeMinutes: Math.ceil(timeSpent / 60000), // 转换为分钟
          isCorrect: actionType.includes('correct')
        }
      }
    }).then(result => {
      console.log('学习进度更新结果:', result);
      
      if (result.result.success) {
        // 更新本地掌握信息
        this.setData({
          masteryInfo: result.result.data
        });
      }
    }).catch(error => {
      console.error('更新学习进度失败:', error);
    });
  },

  /**
   * 计算掌握状态
   */
  calculateMasteryStatus: function(learningBehavior) {
    const totalTime = learningBehavior.totalTime || 0;
    const actionCount = learningBehavior.actionCount || 0;
    
    if (totalTime < 300000) { // 少于5分钟
      return 'not_started';
    } else if (totalTime < 1800000) { // 少于30分钟
      return 'learning';
    } else if (actionCount >= 5) {
      return 'mastered';
    } else {
      return 'weak';
    }
  },

  /**
   * 计算掌握百分比
   */
  calculateMasteryPercentage: function(learningBehavior) {
    const totalTime = learningBehavior.totalTime || 0;
    const actionCount = learningBehavior.actionCount || 0;
    
    // 基于时间和交互次数的简单算法
    const timeScore = Math.min(60, totalTime / 60000); // 最多60分基于时间
    const actionScore = Math.min(40, actionCount * 8); // 最多40分基于交互
    
    return Math.min(100, Math.round(timeScore + actionScore));
  },

  /**
   * 处理云函数调用错误
   */
  handleCloudFunctionError: function(error, context = '操作') {
    console.error(`${context}失败:`, error);
    
    let errorMessage = '网络连接异常';
    
    if (error.errCode) {
      switch (error.errCode) {
        case -1:
          errorMessage = '网络连接超时';
          break;
        case -2:
          errorMessage = '数据加载失败';
          break;
        default:
          errorMessage = error.errMsg || '未知错误';
      }
    }
    
    wx.showToast({
      title: errorMessage,
      icon: 'none',
      duration: 2000
    });
    
    return errorMessage;
  },

  /**
   * 页面滚动监听
   */
  onPageScroll: function(e) {
    const showBackToTop = e.scrollTop > 500;
    if (showBackToTop !== this.data.showBackToTop) {
      this.setData({
        showBackToTop
      });
    }
  },

  /**
   * 回到顶部
   */
  backToTop: function() {
    wx.pageScrollTo({
      scrollTop: 0,
      duration: 500
    });
  },

  /**
   * 监听页面尺寸变化
   */
  onResize: function() {
    this.initDeviceInfo();
  },

  /**
   * 跳转到相关知识点
   */
  navigateToKnowledge: function(e) {
    const { id } = e.currentTarget.dataset;
    
    wx.showToast({
      title: '正在跳转到相关知识点',
      icon: 'loading',
      duration: 1000
    });
    
    setTimeout(() => {
      wx.navigateTo({
        url: `/packageHome/pages/knowledge-detail/index?id=${id}`
      });
    }, 1000);
  },

  // 加载知识点数据
  loadKnowledgeData: function() {
    // 如果有知识点数据，加载真实的关联数据
    if (this.data.knowledgePoint && this.data.knowledgePoint.node_code) {
      this.loadKnowledgeRelationships(this.data.knowledgePoint.node_code);
    } else {
      // 暂时使用硬编码数据作为备用
      try {
        console.log('知识点数据尚未加载，使用默认数据');
        
        this.setData({
          prerequisites: [],
          subsequent: [],
          related: [],
          extended: [],
          knowledgeStats: {}
        });
        
        console.log('默认知识点数据设置成功');
      } catch (error) {
        console.error('设置默认知识点数据失败:', error);
        wx.showToast({
          title: '数据加载失败',
          icon: 'none'
        });
      }
    }
  },

  /**
   * 调用云函数 - 与知识图谱页面保持一致
   */
  callKnowledgeGraphFunction: function(action, params = {}) {
    return new Promise((resolve, reject) => {
      console.log(`调用云函数: action=${action}`, params);
      
      wx.cloud.callFunction({
        name: 'knowledge-graph-query',
        data: {
          action,
          params
        },
        success: (res) => {
          console.log(`云函数调用成功: action=${action}`, res);
          if (res.result && res.result.success) {
            resolve(res.result.data);
          } else {
            console.error(`云函数返回错误: action=${action}`, res.result);
            reject(new Error(res.result?.error || '云函数调用失败'));
          }
        },
        fail: (error) => {
          console.error(`云函数调用失败: action=${action}`, error);
          reject(error);
        }
      });
    });
  },

  /**
   * 加载知识点关联关系数据 - 优化为与知识图谱页面一致的实现
   */
  loadKnowledgeRelationships: async function(nodeId) {
    console.log('开始加载知识关联数据，nodeId:', nodeId);
    
    try {
      // 使用统一的云函数调用方法
      const relationships = await this.callKnowledgeGraphFunction('getKnowledgeRelationships', {
        nodeId: nodeId,
        relationshipType: null  // 查询所有关系类型
      });
      
      console.log('知识关联关系查询结果:', relationships);
      
      // 处理关联数据，转换为页面需要的格式
      const processedData = await this.processRelationshipData(relationships || [], nodeId);
      
      this.setData({
        prerequisites: processedData.prerequisites,
        subsequent: processedData.subsequent,
        related: processedData.related,
        extended: processedData.extended
      });
      
      console.log('知识关联数据加载成功，统计:', {
        prerequisites: processedData.prerequisites.length,
        subsequent: processedData.subsequent.length,
        related: processedData.related.length,
        extended: processedData.extended.length
      });
      
      // 如果用户已登录，加载智能推荐
      if (this.data.isLoggedIn) {
        this.loadIntelligentRecommendations(nodeId);
      }
      
    } catch (error) {
      console.error('加载知识关联关系失败:', error);
      this.setDefaultRelationshipData();
    }
  },

  /**
   * 设置默认关联数据
   */
  setDefaultRelationshipData: function() {
    this.setData({
      prerequisites: [],
      subsequent: [],
      related: [],
      extended: []
    });
  },

  /**
   * 加载智能推荐数据
   */
  loadIntelligentRecommendations: function(nodeId) {
    const userInfo = this.data.userInfo;
    if (!userInfo || !userInfo.id) {
      return;
    }

    wx.cloud.callFunction({
      name: 'knowledge-graph-query',
      data: {
        action: 'getRecommendations',
        params: {
          studentId: userInfo.id,
          academicTrack: userInfo.academicTrack || null,
          limit: 5
        }
      }
    }).then(result => {
      console.log('智能推荐查询结果:', result);
      
      if (result.result.success && result.result.data.length > 0) {
        const recommendations = result.result.data.map(item => ({
          id: item.id,
          title: item.node_name,
          subtitle: this.getRecommendationSubtitle(item.recommendation_type),
          description: this.getRecommendationDescription(item),
          type: item.recommendation_type,
          priority: item.priority_score
        }));
        
        this.setData({
          aiRecommendations: recommendations
        });
      }
    }).catch(error => {
      console.error('获取智能推荐失败:', error);
    });
  },

  /**
   * 获取推荐副标题
   */
  getRecommendationSubtitle: function(type) {
    const subtitles = {
      'weak_improvement': '薄弱知识点',
      'prerequisite_missing': '前置知识缺失',
      'next_learning': '建议学习'
    };
    return subtitles[type] || '推荐学习';
  },

  /**
   * 获取推荐描述
   */
  getRecommendationDescription: function(item) {
    const descriptions = {
      'weak_improvement': `当前掌握度较低，建议重点复习和练习`,
      'prerequisite_missing': `作为前置知识，需要优先掌握`,
      'next_learning': `适合当前水平，建议接下来学习`
    };
    
    return descriptions[item.recommendation_type] || `建议学习时长：${item.estimated_time_minutes || 30}分钟`;
  },

  /**
   * 处理关联关系数据 - 与知识图谱页面保持完全一致的实现
   */
  processRelationshipData: async function(relationships, currentNodeId) {
    if (!relationships || relationships.length === 0) {
      return {
        prerequisites: [],
        subsequent: [],
        related: [],
        extended: []
      };
    }

    console.log('处理关系数据，当前节点ID:', currentNodeId, '关系数据量:', relationships.length);

    // 按关系类型分组 - 与知识图谱页面一致
    const grouped = {
      prerequisites: [],
      successors: [],
      related: [],
      applications: [],
      others: []
    };

    // 获取当前用户的掌握状态数据，用于显示相关知识点的掌握情况
    let masteryData = {};
    if (this.data.isLoggedIn && this.data.userInfo?.id) {
      try {
        const masteryList = await this.callKnowledgeGraphFunction('getStudentMastery', {
          studentId: this.data.userInfo.id,
          gradeLevel: null, // 获取所有年级的数据
          subject: 'mathematics'
        });

        if (masteryList && masteryList.length > 0) {
          masteryList.forEach(mastery => {
            masteryData[mastery.knowledge_node_id] = mastery;
          });
        }
      } catch (error) {
        console.error('获取掌握状态数据失败:', error);
      }
    }

    relationships.forEach(rel => {
      const isSource = rel.source_node_id == currentNodeId;
      const targetNodeId = isSource ? rel.target_node_id : rel.source_node_id;
      const targetNodeName = isSource ? rel.target_name : rel.source_name;
      const targetNodeCode = isSource ? rel.target_code : rel.source_code;
      const targetGradeLevel = isSource ? rel.target_grade_level : rel.source_grade_level;
      const targetSubject = isSource ? rel.target_subject : rel.source_subject;
      const targetDifficulty = isSource ? rel.target_difficulty : rel.source_difficulty;
      const targetChapterTitle = isSource ? rel.target_chapter_title : rel.source_chapter_title;
      const targetSectionTitle = isSource ? rel.target_section_title : rel.source_section_title;

      // 获取该知识点的掌握状态
      const masteryInfo = masteryData[targetNodeId];
      const masteryStatus = masteryInfo?.mastery_status || 'not_started';
      const masteryPercentage = masteryInfo?.mastery_percentage || Math.floor(Math.random() * 100);

      const relatedNode = {
        id: targetNodeCode || targetNodeId,
        name: targetNodeName || '未命名知识点',
        code: targetNodeCode,
        gradeLevel: targetGradeLevel,
        grade: targetGradeLevel ? `${GRADE_LEVEL_MAP[targetGradeLevel] || targetGradeLevel + '年级'}` : '',
        subject: targetSubject,
        difficulty: targetDifficulty,
        chapter: this.buildChapterDisplayForRelated(targetChapterTitle, targetSectionTitle),
        
        // 掌握状态相关
        masteryRate: masteryPercentage,
        masteryStatus: masteryStatus,
        masteryStatusText: this.getMasteryStatusText(masteryStatus),
        
        // 关系相关
        strength: rel.strength || 0,
        confidence: rel.confidence || 0,
        relationshipType: rel.relationship_type,
        relationshipTypeText: RELATIONSHIP_TYPE_TEXT[rel.relationship_type] || rel.relationship_type,
        
        // 学习建议和详情
        suggestion: this.generateSuggestionByRelationType(rel.relationship_type, targetNodeName, isSource),
        keyPoints: this.generateKeyPointsByType(rel.relationship_type, targetNodeName),
        masteryTips: this.generateMasteryTips(rel.relationship_type, targetNodeName, masteryStatus),
        learningTime: this.estimateLearningTime(targetDifficulty, rel.estimated_time_minutes),
        practiceType: this.getPracticeTypeByDifficulty(targetDifficulty),
        
        // UI控制
        showDetail: false
      };

      // 根据关系类型分组 - 与知识图谱页面完全一致
      switch (rel.relationship_type) {
        case 'prerequisite':
          if (isSource) {
            grouped.prerequisites.push(relatedNode);
          } else {
            grouped.successors.push(relatedNode);
          }
          break;
        case 'successor':
          if (isSource) {
            grouped.successors.push(relatedNode);
          } else {
            grouped.prerequisites.push(relatedNode);
          }
          break;
        case 'related':
        case 'parallel':
        case 'cross_subject':
          grouped.related.push(relatedNode);
          break;
        case 'application_of':
        case 'example_of':
          grouped.applications.push(relatedNode);
          break;
        case 'extension':
        case 'contains':
        default:
          grouped.others.push(relatedNode);
      }
    });

    // 按强度和置信度排序各分组
    Object.keys(grouped).forEach(key => {
      grouped[key].sort((a, b) => {
        if (b.strength !== a.strength) {
          return b.strength - a.strength;
        }
        return b.confidence - a.confidence;
      });
    });

    // 返回格式保持与页面现有结构兼容
    return {
      prerequisites: grouped.prerequisites,
      subsequent: grouped.successors,
      related: grouped.related,
      extended: [...grouped.applications, ...grouped.others]
    };
  },

  /**
   * 构建章节显示信息 - 用于相关知识点
   */
  buildChapterDisplayForRelated: function(chapterTitle, sectionTitle) {
    const parts = [];
    
    if (chapterTitle) {
      parts.push(chapterTitle);
    }
    if (sectionTitle) {
      parts.push(sectionTitle);
    }
    
    return parts.join(' - ') || '其他章节';
  },

  /**
   * 获取掌握状态文本
   */
  getMasteryStatusText: function(status) {
    const statusMap = {
      'not_started': '未开始',
      'weak': '薄弱',
      'learning': '学习中',
      'mastered': '已掌握'
    };
    return statusMap[status] || '未知';
  },

  /**
   * 根据关系类型生成学习建议
   */
  generateSuggestionByRelationType: function(relationshipType, nodeName, isSource) {
    const suggestions = {
      'prerequisite': {
        source: `${nodeName}是后续深入学习的重要基础，建议优先掌握`,
        target: `需要先掌握前置知识再学习${nodeName}`
      },
      'successor': {
        source: `${nodeName}是基于当前知识的进阶应用`,
        target: `${nodeName}为当前学习提供理论支撑`
      },
      'related': {
        source: `${nodeName}与当前知识相关，可以对比学习加深理解`,
        target: `${nodeName}与当前知识相关，可以对比学习加深理解`
      },
      'extension': {
        source: `${nodeName}是当前知识的实际应用拓展`,
        target: `${nodeName}为当前知识提供应用背景`
      },
      'parallel': {
        source: `${nodeName}可以并行学习，有助于建立知识体系`,
        target: `${nodeName}可以并行学习，有助于建立知识体系`
      },
      'contains': {
        source: `${nodeName}是当前知识的重要组成部分`,
        target: `${nodeName}包含当前知识，建议先整体掌握`
      },
      'example_of': {
        source: `${nodeName}是具体的实例应用`,
        target: `${nodeName}为实例提供理论支撑`
      },
      'application_of': {
        source: `${nodeName}是实际应用场景`,
        target: `${nodeName}提供理论基础`
      },
      'cross_subject': {
        source: `${nodeName}是跨学科相关知识，有助于拓展应用视野`,
        target: `${nodeName}是跨学科相关知识，有助于拓展应用视野`
      }
    };

    const typeMap = suggestions[relationshipType];
    if (typeMap) {
      return isSource ? typeMap.source : typeMap.target;
    }
    
    return `建议结合${nodeName}的实际应用场景加深理解`;
  },

  /**
   * 根据关系类型生成关键要点
   */
  generateKeyPointsByType: function(relationshipType, nodeName) {
    const keyPointsMap = {
      'prerequisite': [
        `理解${nodeName}的核心概念`,
        '掌握相关计算方法',
        '明确知识点之间的逻辑关系'
      ],
      'successor': [
        `基于当前知识学习${nodeName}`,
        '注意知识的递进性',
        '巩固基础后再深入'
      ],
      'related': [
        `对比${nodeName}与当前知识的异同`,
        '找出共同点和差异',
        '建立知识间的联系'
      ],
      'application_of': [
        `了解${nodeName}的实际应用场景`,
        '结合具体案例练习',
        '提高解决实际问题的能力'
      ]
    };
    
    return keyPointsMap[relationshipType] || [
      `重点理解${nodeName}的核心内容`,
      '多做相关实际应用练习',
      '及时总结和复习巩固'
    ];
  },

  /**
   * 生成掌握技巧
   */
  generateMasteryTips: function(relationshipType, nodeName, masteryStatus) {
    if (masteryStatus === 'mastered') {
      return `您已经掌握了${nodeName}，可以适当复习巩固并拓展应用`;
    } else if (masteryStatus === 'learning') {
      return `继续加强${nodeName}的实际应用练习，重点突破难点部分`;
    } else if (masteryStatus === 'weak') {
      return `${nodeName}是您的薄弱环节，建议从基础概念开始系统学习`;
    } else {
      return `建议先了解${nodeName}的基本概念，然后结合实际应用逐步深入`;
    }
  },

  /**
   * 估计学习时间
   */
  estimateLearningTime: function(difficulty, estimatedMinutes) {
    if (estimatedMinutes) {
      return `${estimatedMinutes}分钟`;
    }
    
    const timeMap = {
      'basic': '25分钟',
      'intermediate': '35分钟',
      'advanced': '50分钟',
      'expert': '70分钟'
    };
    
    return timeMap[difficulty] || '35分钟';
  },

  /**
   * 根据难度获取练习类型
   */
  getPracticeTypeByDifficulty: function(difficulty) {
    const typeMap = {
      'basic': '基础理解',
      'intermediate': '应用练习',
      'advanced': '综合应用',
      'expert': '拓展应用'
    };
    
    return typeMap[difficulty] || '应用练习';
  },


}); 