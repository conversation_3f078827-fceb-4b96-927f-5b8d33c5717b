Component({
  properties: {
    selectedFeature: {
      type: String,
      value: ''
    },
    inputValue: {
      type: String,
      value: ''
    },
    isRecording: {
      type: <PERSON><PERSON>an,
      value: false
    },
    sendEnabled: {
      type: <PERSON><PERSON>an,
      value: false
    },
    focus: {
      type: <PERSON><PERSON>an,
      value: false
    }
  },
  
  externalClasses: ['custom-class'],
  
  lifetimes: {
    attached: function() {
      // 确保图标样式已加载
      console.log('图标面板已加载');
      
      // 检查图标样式是否正确应用
      const that = this;
      wx.nextTick(function() {
        const query = that.createSelectorQuery();
        query.select('.icon-homework').boundingClientRect(function(rect) {
          if (!rect) {
            console.warn('功能面板图标可能未正确加载');
          } else {
            console.log('功能面板图标已正确加载');
          }
        }).exec();
      });
    }
  },
  
  methods: {
    onFeatureTap(e) {
      const type = e.currentTarget.dataset.type;
      this.triggerEvent('featureTap', { type });
    },
    
    onTakePhoto() {
      this.triggerEvent('takePhoto');
    },
    
    onInput(e) {
      // 添加日志以排查问题
      console.log('输入框内容变化', e.detail.value);
      
      // 确保value是一个字符串
      const value = e.detail.value || '';
      this.triggerEvent('input', { value });
    },
    
    onInputFocus(e) {
      this.triggerEvent('inputFocus', e.detail);
    },
    
    onInputBlur(e) {
      this.triggerEvent('inputBlur', e.detail);
    },
    
    onSend() {
      this.triggerEvent('send');
    },
    
    onStartVoiceRecord(e) {
      this.triggerEvent('startVoiceRecord', e);
    },
    
    onEndVoiceRecord(e) {
      this.triggerEvent('endVoiceRecord', e);
    },
    
    onVoiceTouchMove(e) {
      this.triggerEvent('voiceTouchMove', e);
    }
  }
}) 