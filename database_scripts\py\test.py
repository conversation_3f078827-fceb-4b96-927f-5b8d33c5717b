import insert
import json

def test_insert():
    try:
        # 读取insert.json文件
        with open('insert.json', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 调用insert.main函数
        result = insert.main(content)
        
        print("✓ JSON解析成功")
        print("返回结果的键:", list(result.keys()))
        
        if 'insert_sql' in result:
            print("✓ SQL生成成功")
            print(f"生成的SQL长度: {len(result['insert_sql'])} 字符")
            
            # 检查SQL的开头部分
            sql_preview = result['insert_sql'][:200] + "..."
            print(f"SQL预览:\n{sql_preview}")
        else:
            print("✗ 未找到insert_sql键")
            print("实际返回:", result)
        
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_insert() 