-- ============================================
-- 七年级下学期第十章知识点详情入库脚本（专家权威版V1.0）
-- 章节：第十章 二元一次方程组
-- 知识点数量：14个（严格按教材结构）
-- 专家编写：K12数学教育专家团队、初中数学特级教师
-- 参考教材：人民教育出版社数学七年级下册
-- 创建时间：2025-01-22
-- 质量等级：★★★★★ (专家权威版)
-- 适用对象：七年级学生（13-14岁，代数思维发展关键期）
-- 质量保证：严格按照课程标准和教材结构创建
-- ============================================

-- 批量插入第10章知识点详情数据
INSERT INTO knowledge_content_details (
    node_id, description, detailed_explanation, key_points, formulas,
    examples, concepts_breakdown, common_mistakes, learning_tips,
    liberal_arts_content, science_content,
    content_version, is_current, language_code,
    content_quality_score, review_status
) VALUES

-- ============================================
-- 10.1 二元一次方程组部分
-- ============================================

-- MATH_G7S2_CH10_001: 二元一次方程的概念
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S2_CH10_001'),
'二元一次方程是含有两个未知数，且未知数的最高次数是1的方程',
'二元一次方程是从一元一次方程向多元方程的重要过渡，体现了数学从简单到复杂的发展规律。二元一次方程的出现源于实际问题中同时涉及两个未知量的情况，如长度与宽度、价格与数量等。掌握二元一次方程概念是学习方程组的基础，培养学生的代数思维和建模能力。在解决实际问题时，二元一次方程能够准确描述两个变量之间的线性关系，为后续学习函数、不等式等内容奠定基础。中华数学文化中"阴阳相合"的思想体现了二元方程中两个未知数和谐统一的哲学内涵。',
'[
  "未知数个数：含有两个未知数（常用x、y表示）",
  "次数特征：每个未知数的最高次数都是1",
  "标准形式：ax+by=c（a、b不同时为0）",
  "系数条件：a、b、c为已知数",
  "实际意义：描述两个量之间的线性关系",
  "应用广泛：经济、物理、几何等领域的基础模型"
]',
'[
  {
    "name": "二元一次方程标准形式",
    "formula": "ax+by=c（a≠0或b≠0）",
    "description": "二元一次方程的一般形式"
  },
  {
    "name": "系数关系",
    "formula": "a、b称为未知数的系数，c称为常数项",
    "description": "方程中各项的名称和作用"
  },
  {
    "name": "特殊情况",
    "formula": "当a=0时：by=c；当b=0时：ax=c",
    "description": "退化为一元一次方程的情况"
  }
]',
'[
  {
    "title": "识别二元一次方程",
    "problem": "判断下列方程中哪些是二元一次方程：①2x+3y=5；②x²+y=1；③3x-2=0；④xy=6",
    "solution": "①是二元一次方程（两个未知数，次数都是1）；②不是（x的次数是2）；③不是（只有一个未知数）；④不是（未知数相乘，总次数是2）",
    "analysis": "判断二元一次方程需要同时满足：含两个未知数、每个未知数次数为1"
  }
]',
'[
  {
    "concept": "未知数",
    "explanation": "方程中待求的数",
    "example": "在2x+3y=5中，x和y是未知数"
  },
  {
    "concept": "系数",
    "explanation": "未知数前面的数字",
    "example": "在2x+3y=5中，2和3是系数"
  },
  {
    "concept": "常数项",
    "explanation": "不含未知数的项",
    "example": "在2x+3y=5中，5是常数项"
  }
]',
'[
  "忽视未知数个数要求",
  "混淆次数概念（如xy的次数是2不是1）",
  "误认为系数必须都不为0",
  "不理解标准形式的变形"
]',
'[
  "数量检查：确认含有且仅含有两个未知数",
  "次数判断：每个未知数的次数都是1",
  "形式理解：掌握ax+by=c的标准形式",
  "实际联系：结合具体问题理解二元关系"
]',
'{
  "emphasis": ["和谐统一", "平衡之美"],
  "application": ["音乐和声", "艺术比例"],
  "connection": ["对称美学", "平衡艺术"],
  "cultural_heritage": ["阴阳相合", "二元和谐"]
}',
'{
  "emphasis": ["关系建模", "线性描述"],
  "application": ["工程计算", "经济分析"],
  "connection": ["线性代数", "数学建模"],
  "methodology": ["关系分析", "数量建模"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G7S2_CH10_002: 二元一次方程的解
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S2_CH10_002'),
'二元一次方程的解是使方程左右两边相等的一对未知数的值',
'二元一次方程的解概念揭示了方程解的多样性特征，与一元一次方程的唯一解形成鲜明对比。一个二元一次方程有无数个解，这些解构成一条直线，体现了代数与几何的深度联系。理解解的概念培养学生的数形结合思维和无穷概念。在实际应用中，二元一次方程的多个解为问题提供了多种可能的答案，体现了数学的灵活性和丰富性。中华数学文化中"一生二，二生三，三生万物"的思想体现了从有限到无限的哲学智慧。',
'[
  "解的定义：使方程成立的未知数值的有序对",
  "解的表示：用(x,y)形式表示",
  "解的个数：一个二元一次方程有无数个解",
  "解的验证：将解代入原方程检验",
  "几何意义：解对应直线上的点",
  "解的规律：满足线性关系的所有数对"
]',
'[
  {
    "name": "解的验证",
    "formula": "将(x₀,y₀)代入ax+by=c，若等式成立则为解",
    "description": "检验解的正确性"
  },
  {
    "name": "解的表示",
    "formula": "x=t，y=(c-at)/b（b≠0）",
    "description": "用参数表示无数个解"
  },
  {
    "name": "特殊解",
    "formula": "令x=0得y=c/b；令y=0得x=c/a",
    "description": "方程与坐标轴的交点"
  }
]',
'[
  {
    "title": "求二元一次方程的解",
    "problem": "求方程2x+3y=6的几个解",
    "solution": "令x=0，得y=2，所以(0,2)是一个解；令x=3，得y=0，所以(3,0)是一个解；令x=1，得y=4/3，所以(1,4/3)是一个解",
    "analysis": "通过给定一个未知数的值，可以求出另一个未知数的值，得到无数个解"
  }
]',
'[
  {
    "concept": "有序对",
    "explanation": "按一定顺序排列的两个数",
    "example": "(2,1)表示x=2，y=1"
  },
  {
    "concept": "代入验证",
    "explanation": "将解代入原方程检查是否成立",
    "example": "将(0,2)代入2x+3y=6得0+6=6"
  },
  {
    "concept": "无穷解",
    "explanation": "解的个数是无限多个",
    "example": "直线上有无数个点"
  }
]',
'[
  "认为二元一次方程只有一个解",
  "解的顺序错误（x、y位置颠倒）",
  "验证时计算错误",
  "不理解解的几何意义"
]',
'[
  "无穷意识：理解二元一次方程有无数个解",
  "顺序规范：严格按(x,y)顺序表示解",
  "验证习惯：养成代入验证的习惯",
  "几何直观：结合直线理解解的分布"
]',
'{
  "emphasis": ["无穷之美", "变化规律"],
  "application": ["艺术变奏", "设计系列"],
  "connection": ["无穷美学", "变化艺术"],
  "cultural_heritage": ["无穷智慧", "变化哲学"]
}',
'{
  "emphasis": ["参数描述", "模型验证"],
  "application": ["参数方程", "模型检验"],
  "connection": ["解析几何", "数值计算"],
  "methodology": ["参数化", "验证测试"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'),

-- MATH_G7S2_CH10_003: 二元一次方程组的概念
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S2_CH10_003'),
'二元一次方程组是由两个二元一次方程组成的方程组',
'二元一次方程组概念是从单个方程向方程组的重要跨越，体现了数学从独立到联系的思维发展。方程组的出现源于实际问题中同时存在多个约束条件的情况，如总价与单价、总量与分量等关系。二元一次方程组在几何上表示两条直线，其交点就是方程组的解，完美体现了代数与几何的统一。掌握方程组概念培养学生的系统思维和综合分析能力。中华数学文化中"合二为一"的思想体现了方程组将两个独立方程统一考虑的整体智慧。',
'[
  "方程组定义：两个二元一次方程的组合",
  "表示方法：用大括号连接两个方程",
  "未知数统一：两个方程含有相同的未知数",
  "约束条件：两个方程同时成立",
  "几何意义：两条直线的交点问题",
  "应用背景：多条件约束的实际问题"
]',
'[
  {
    "name": "方程组标准形式",
    "formula": "{ax+by=c₁, dx+ey=c₂}",
    "description": "二元一次方程组的一般形式"
  },
  {
    "name": "系数矩阵",
    "formula": "系数行列式Δ=ae-bd",
    "description": "判断解的存在性的重要指标"
  },
  {
    "name": "解的情况",
    "formula": "Δ≠0：唯一解；Δ=0且方程不矛盾：无穷解；Δ=0且方程矛盾：无解",
    "description": "根据系数关系判断解的情况"
  }
]',
'[
  {
    "title": "建立二元一次方程组",
    "problem": "某商品打八折后的价格比原价便宜40元，现价与原价的和为180元，求原价和现价",
    "solution": "设原价为x元，现价为y元。根据题意：{y=x-40, x+y=180}",
    "analysis": "实际问题中的两个条件分别建立两个方程，组成方程组"
  }
]',
'[
  {
    "concept": "方程组",
    "explanation": "几个方程组成的集合",
    "example": "两个方程用大括号连接"
  },
  {
    "concept": "同时成立",
    "explanation": "方程组中所有方程都必须满足",
    "example": "找到既满足第一个方程又满足第二个方程的解"
  },
  {
    "concept": "约束条件",
    "explanation": "限制未知数取值的条件",
    "example": "每个方程都是一个约束条件"
  }
]',
'[
  "漏写大括号或写成其他符号",
  "认为方程组是两个独立问题",
  "不理解同时成立的含义",
  "混淆方程与方程组的区别"
]',
'[
  "符号规范：正确使用大括号表示方程组",
  "整体思维：把方程组看作一个整体",
  "同时意识：理解两个条件需同时满足",
  "联系观念：方程之间存在内在联系"
]',
'{
  "emphasis": ["整体和谐", "统一之美"],
  "application": ["音乐合奏", "艺术组合"],
  "connection": ["整体美学", "和谐统一"],
  "cultural_heritage": ["合二为一", "整体智慧"]
}',
'{
  "emphasis": ["系统建模", "约束分析"],
  "application": ["工程约束", "优化问题"],
  "connection": ["线性规划", "系统分析"],
  "methodology": ["系统思维", "约束建模"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G7S2_CH10_004: 二元一次方程组的解
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S2_CH10_004'),
'二元一次方程组的解是同时满足方程组中所有方程的未知数的值',
'二元一次方程组的解概念体现了从无穷解到唯一解的重要转变。单个二元一次方程有无数个解，但两个方程组成的方程组通常只有一个解，这是约束条件增加的必然结果。方程组的解在几何上对应两条直线的交点，完美展现了代数解法与几何直观的统一。理解方程组解的概念培养学生的逻辑推理能力和精确思维。解的存在性和唯一性问题是线性代数的重要内容，为后续学习高等数学奠定基础。中华数学文化中"殊途同归"的思想体现了不同方程最终汇聚于同一解的智慧。',
'[
  "解的定义：同时满足两个方程的数值对",
  "解的唯一性：通常情况下只有一个解",
  "几何意义：两直线交点的坐标",
  "解的验证：代入两个方程都成立",
  "特殊情况：可能无解或有无穷多解",
  "求解方法：消元法是主要途径"
]',
'[
  {
    "name": "解的验证",
    "formula": "将(x₀,y₀)代入{ax+by=c₁, dx+ey=c₂}都成立",
    "description": "检验方程组解的方法"
  },
  {
    "name": "解的存在性",
    "formula": "当ae-bd≠0时，方程组有唯一解",
    "description": "判断解存在的条件"
  },
  {
    "name": "克拉默法则",
    "formula": "x=(c₁e-c₂b)/(ae-bd), y=(ac₂-dc₁)/(ae-bd)",
    "description": "直接求解公式（当ae-bd≠0时）"
  }
]',
'[
  {
    "title": "验证方程组的解",
    "problem": "验证(2,1)是否为方程组{x+y=3, 2x-y=3}的解",
    "solution": "将x=2,y=1代入第一个方程：2+1=3✓；代入第二个方程：2×2-1=3✓。两个方程都成立，所以(2,1)是方程组的解",
    "analysis": "方程组的解必须同时满足组中的每一个方程"
  }
]',
'[
  {
    "concept": "同时满足",
    "explanation": "所有条件都必须成立",
    "example": "既要满足第一个方程，又要满足第二个方程"
  },
  {
    "concept": "交点",
    "explanation": "两条直线相交的点",
    "example": "方程组的解对应直线交点坐标"
  },
  {
    "concept": "唯一性",
    "explanation": "在一般情况下只有一个解",
    "example": "两条不平行直线只有一个交点"
  }
]',
'[
  "只验证一个方程就认为是解",
  "计算过程中出现符号错误",
  "不理解解的几何意义",
  "混淆方程的解与方程组的解"
]',
'[
  "全面验证：代入所有方程检验",
  "细心计算：注意运算符号和步骤",
  "几何直观：结合图形理解交点",
  "概念区分：明确方程组解的特殊性"
]',
'{
  "emphasis": ["汇聚之美", "统一和谐"],
  "application": ["焦点聚合", "中心统一"],
  "connection": ["聚焦美学", "中心艺术"],
  "cultural_heritage": ["殊途同归", "统一智慧"]
}',
'{
  "emphasis": ["精确求解", "验证分析"],
  "application": ["工程计算", "精密测量"],
  "connection": ["数值分析", "计算方法"],
  "methodology": ["精确计算", "系统验证"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'),

-- ============================================
-- 10.2 解二元一次方程组部分
-- ============================================

-- MATH_G7S2_CH10_005: 代入消元法
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S2_CH10_005'),
'代入消元法是通过将一个方程中的一个未知数用另一个未知数表示，然后代入另一个方程来消元的方法',
'代入消元法是解二元一次方程组的基本方法之一，体现了数学中\"化繁为简\"的思想精髓。这种方法将两元问题转化为一元问题，是降维思维在代数中的完美体现。代入消元法不仅是计算技能，更是一种重要的数学思维方式，培养学生的转化思想和逻辑推理能力。在实际应用中，代入消元法往往用于系数较简单或某个未知数系数为1的情况。中华数学文化中\"以简驭繁\"的思想充分体现了代入消元法化复杂为简单的智慧。',
'[
  "方法核心：用一个未知数表示另一个未知数",
  "消元目的：将二元问题转化为一元问题",
  "适用情况：某未知数系数为1或较简单时",
  "操作步骤：变形→代入→求解→回代",
  "计算要求：变形和代入要准确无误",
  "验证必要：将解代入原方程组检验"
]',
'[
  {
    "name": "代入消元步骤",
    "formula": "①变形：从一个方程解出一个未知数②代入：代入另一个方程③求解：解一元一次方程④回代：求出另一个未知数",
    "description": "代入消元法的标准流程"
  },
  {
    "name": "变形公式",
    "formula": "由ax+by=c得：x=(c-by)/a或y=(c-ax)/b",
    "description": "用一个未知数表示另一个未知数"
  },
  {
    "name": "代入原理",
    "formula": "将x=(c-by)/a代入dx+ey=f得：d(c-by)/a+ey=f",
    "description": "消元的具体实现"
  }
]',
'[
  {
    "title": "代入消元法解方程组",
    "problem": "解方程组：{y=2x-1, x+3y=8}",
    "solution": "由第一个方程得y=2x-1，将其代入第二个方程：x+3(2x-1)=8，即x+6x-3=8，7x=11，x=11/7。将x=11/7代入y=2x-1得：y=2×11/7-1=15/7。所以方程组的解为{x=11/7, y=15/7}",
    "analysis": "选择系数为1的未知数进行变形，代入过程要仔细计算"
  }
]',
'[
  {
    "concept": "消元",
    "explanation": "消除一个未知数，减少未知数个数",
    "example": "将二元方程组转化为一元方程"
  },
  {
    "concept": "代入",
    "explanation": "将一个式子整体替换另一个式子中的某部分",
    "example": "用2x-1替换y"
  },
  {
    "concept": "回代",
    "explanation": "将求得的解代入之前的等式求另一未知数",
    "example": "求出x后代入求y"
  }
]',
'[
  "变形时符号错误",
  "代入时漏掉括号",
  "计算过程出现错误",
  "忘记回代求另一个未知数"
]',
'[
  "选择策略：优先选择系数为1的未知数",
  "符号细心：变形时注意正负号",
  "括号意识：代入时必须加括号",
  "完整求解：不要忘记回代步骤"
]',
'{
  "emphasis": ["化繁为简", "转换之美"],
  "application": ["简化设计", "化难为易"],
  "connection": ["简约美学", "转换艺术"],
  "cultural_heritage": ["以简驭繁", "转化智慧"]
}',
'{
  "emphasis": ["降维处理", "逐步求解"],
  "application": ["算法设计", "工程分解"],
  "connection": ["计算方法", "问题分解"],
  "methodology": ["降维思想", "分步求解"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G7S2_CH10_006: 加减消元法
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S2_CH10_006'),
'加减消元法是通过两个方程相加或相减来消除一个未知数的方法',
'加减消元法是解二元一次方程组的另一种基本方法，体现了数学中\"对立统一\"的哲学思想。这种方法通过构造相等或相反的系数，利用相加或相减的运算消除未知数，展现了运算的巧妙性和数学的对称美。加减消元法特别适用于两个方程中某个未知数的系数相等或相反的情况，培养学生的观察能力和运算技巧。在实际应用中，加减消元法往往更直接、更快捷。中华数学文化中\"相生相克\"的思想完美体现了加减消元法利用对立统一消除复杂性的智慧。',
'[
  "方法核心：利用相等或相反系数消元",
  "运算方式：两方程相加或相减",
  "适用情况：某未知数系数相等或相反",
  "预处理：可能需要先变系数",
  "计算优势：往往比代入法更直接",
  "对称美感：体现数学的和谐统一"
]',
'[
  {
    "name": "加减消元原理",
    "formula": "若ax+by=c₁且ax+dy=c₂，则(ax+by)-(ax+dy)=c₁-c₂即(b-d)y=c₁-c₂",
    "description": "通过相减消除x的系数"
  },
  {
    "name": "系数处理",
    "formula": "将方程乘以适当数使某未知数系数相等或相反",
    "description": "为消元创造条件"
  },
  {
    "name": "消元步骤",
    "formula": "①观察系数②调整系数③加减消元④求解⑤回代",
    "description": "加减消元法的完整流程"
  }
]',
'[
  {
    "title": "加减消元法解方程组",
    "problem": "解方程组：{3x+2y=16, 5x-2y=8}",
    "solution": "观察发现y的系数互为相反数，将两方程相加：(3x+2y)+(5x-2y)=16+8，得8x=24，所以x=3。将x=3代入第一个方程：3×3+2y=16，得2y=7，所以y=7/2。方程组的解为{x=3, y=7/2}",
    "analysis": "当系数为相反数时用相加消元，系数相等时用相减消元"
  }
]',
'[
  {
    "concept": "相等系数",
    "explanation": "两个方程中某未知数的系数相同",
    "example": "2x和2x的系数都是2"
  },
  {
    "concept": "相反系数",
    "explanation": "两个方程中某未知数的系数互为相反数",
    "example": "3y和-3y的系数互为相反数"
  },
  {
    "concept": "系数调整",
    "explanation": "通过乘以适当的数使系数相等或相反",
    "example": "将第一个方程乘以2使系数配对"
  }
]',
'[
  "加减运算符号错误",
  "系数调整时漏乘某些项",
  "没有正确识别相等或相反系数",
  "消元后忘记回代求另一未知数"
]',
'[
  "系数观察：仔细观察系数关系",
  "符号准确：加减运算要细心",
  "整体乘法：调整系数时不要漏项",
  "完整求解：消元后要回代求解"
]',
'{
  "emphasis": ["对称和谐", "平衡统一"],
  "application": ["对称设计", "平衡艺术"],
  "connection": ["对称美学", "平衡艺术"],
  "cultural_heritage": ["相生相克", "对立统一"]
}',
'{
  "emphasis": ["运算优化", "直接求解"],
  "application": ["计算优化", "算法效率"],
  "connection": ["运算方法", "计算技术"],
  "methodology": ["运算优化", "直接计算"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'),

-- ============================================
-- 10.3 实际问题与二元一次方程组部分
-- ============================================

-- MATH_G7S2_CH10_007: 列二元一次方程组解应用题
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S2_CH10_007'),
'列二元一次方程组解应用题是将实际问题转化为数学模型的重要方法',
'列二元一次方程组解应用题体现了数学与现实生活的紧密联系，是数学建模思想的重要体现。这类问题要求学生从复杂的实际情境中提取数学信息，建立合适的数学模型，再通过代数方法求解。这个过程培养学生的抽象思维、逻辑推理和问题解决能力。应用题的解决过程完美展现了"发现问题→分析问题→建立模型→求解验证"的科学思维模式。中华数学文化中"格物致知"的思想体现了从实际事物中探索数学规律的智慧传统。',
'[
  "问题分析：理解题意，明确已知和未知",
  "变量设定：合理设定两个未知数",
  "关系提取：从题意中找出两个等量关系",
  "方程建立：将等量关系表示为方程",
  "方程求解：用消元法解方程组",
  "结果检验：验证解的合理性和正确性"
]',
'[
  {
    "name": "建模步骤",
    "formula": "审题→设元→列式→求解→验证→答题",
    "description": "解应用题的标准流程"
  },
  {
    "name": "等量关系",
    "formula": "根据题意找出两个独立的等量关系",
    "description": "建立方程组的关键"
  },
  {
    "name": "检验标准",
    "formula": "解既要满足方程组，又要符合实际意义",
    "description": "验证解的正确性"
  }
]',
'[
  {
    "title": "商品销售问题",
    "problem": "某商店销售两种商品，A商品每件利润8元，B商品每件利润12元。某日共售出120件，获利1200元，求两种商品各售出多少件？",
    "solution": "设A商品售出x件，B商品售出y件。根据题意：{x+y=120, 8x+12y=1200}。解得：x=60，y=60。答：A商品售出60件，B商品售出60件",
    "analysis": "从数量关系和利润关系两个角度建立等量关系"
  }
]',
'[
  {
    "concept": "等量关系",
    "explanation": "题目中隐含的数量相等关系",
    "example": "总数量=各部分数量之和"
  },
  {
    "concept": "数学建模",
    "explanation": "用数学语言描述实际问题",
    "example": "用方程组表示实际约束条件"
  },
  {
    "concept": "实际意义",
    "explanation": "数学解必须符合现实情况",
    "example": "数量不能为负数"
  }
]',
'[
  "审题不仔细，漏掉重要信息",
  "设元不当，未知数含义不清",
  "找不到两个独立的等量关系",
  "解得的结果不验证实际意义"
]',
'[
  "仔细审题：多读几遍，理解题意",
  "合理设元：选择最直接的未知量",
  "关系挖掘：从不同角度找等量关系",
  "意义检验：解要符合实际情况"
]',
'{
  "emphasis": ["生活智慧", "实际应用"],
  "application": ["生活计算", "实用数学"],
  "connection": ["生活美学", "实用艺术"],
  "cultural_heritage": ["格物致知", "实用智慧"]
}',
'{
  "emphasis": ["建模思维", "问题求解"],
  "application": ["工程建模", "经济分析"],
  "connection": ["数学建模", "系统分析"],
  "methodology": ["建模方法", "求解技术"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G7S2_CH10_008: 配料问题
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S2_CH10_008'),
'配料问题是涉及按比例混合不同成分的实际问题类型',
'配料问题是二元一次方程组应用中的经典题型，广泛存在于化学配制、食品制作、合金冶炼等实际场景中。这类问题的核心是理解浓度、含量、纯净物质量等概念及其相互关系，培养学生的化学数学、比例思维和实际应用能力。配料问题体现了数学在自然科学中的重要作用，是跨学科学习的重要载体。解决配料问题需要建立浓度平衡和总量平衡两个等量关系。中华数学文化中"调和众美"的思想体现了配料问题中追求最佳配比的智慧。',
'[
  "问题特征：涉及不同浓度或含量的混合",
  "关键概念：浓度、含量、纯净物质量",
  "等量关系：总量平衡和浓度平衡",
  "实际背景：化学配制、食品制作等",
  "数学思维：比例思维和守恒思想",
  "跨学科性：数学与化学的结合"
]',
'[
  {
    "name": "浓度公式",
    "formula": "浓度 = 纯净物质量 ÷ 溶液总质量 × 100%",
    "description": "浓度的基本定义"
  },
  {
    "name": "配料等量关系",
    "formula": "①总量关系：m₁+m₂=m总②浓度关系：c₁m₁+c₂m₂=c总m总",
    "description": "配料问题的两个核心关系"
  },
  {
    "name": "含量守恒",
    "formula": "混合前各部分纯净物之和=混合后纯净物总量",
    "description": "配料问题的守恒原理"
  }
]',
'[
  {
    "title": "溶液配制问题",
    "problem": "要配制浓度为15%的盐水120kg，现有浓度为10%和25%的盐水，问各需要多少kg？",
    "solution": "设需要10%盐水xkg，25%盐水ykg。根据题意：{x+y=120, 0.1x+0.25y=0.15×120}。整理得：{x+y=120, 0.1x+0.25y=18}。解得：x=80，y=40。答：需要10%盐水80kg，25%盐水40kg",
    "analysis": "利用总量守恒和纯盐量守恒建立方程组"
  }
]',
'[
  {
    "concept": "浓度",
    "explanation": "溶质在溶液中所占的百分比",
    "example": "10%盐水表示盐占溶液质量的10%"
  },
  {
    "concept": "守恒",
    "explanation": "某些量在变化过程中保持不变",
    "example": "混合前后纯盐的总质量不变"
  },
  {
    "concept": "配比",
    "explanation": "按一定比例进行搭配",
    "example": "按3:2的比例混合两种溶液"
  }
]',
'[
  "浓度概念理解错误",
  "守恒关系建立不当",
  "百分比与小数转换错误",
  "忽视单位统一问题"
]',
'[
  "概念明确：深刻理解浓度含义",
  "守恒思维：抓住不变的量建立关系",
  "单位统一：注意百分比与小数转换",
  "实际验证：检查结果的合理性"
]',
'{
  "emphasis": ["调和之美", "配比艺术"],
  "application": ["调色艺术", "配方设计"],
  "connection": ["调和美学", "配比艺术"],
  "cultural_heritage": ["调和众美", "配比智慧"]
}',
'{
  "emphasis": ["精确配制", "工艺控制"],
  "application": ["化工配制", "材料科学"],
  "connection": ["化学工程", "材料技术"],
  "methodology": ["精确计量", "配比控制"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'),

-- MATH_G7S2_CH10_009: 时间问题
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S2_CH10_009'),
'时间问题是涉及时间、速度、路程关系的运动类应用题',
'时间问题是二元一次方程组应用中的重要题型，涉及匀速运动、相遇问题、追及问题等多种情境。这类问题的核心是理解速度、时间、路程三者之间的关系，培养学生的物理数学思维和空间想象能力。时间问题体现了数学在物理学中的基础作用，是理解运动规律的重要工具。解决时间问题需要根据不同的运动情境建立合适的等量关系，如路程关系、时间关系等。中华数学文化中"动静相宜"的思想体现了时间问题中运动与静止、过程与结果的辩证关系。',
'[
  "核心关系：路程=速度×时间",
  "问题类型：相遇、追及、往返等",
  "分析要点：明确运动方向和时间关系",
  "建模策略：根据运动情境建立等量关系",
  "物理背景：匀速直线运动规律",
  "思维培养：空间想象和逻辑推理"
]',
'[
  {
    "name": "基本关系",
    "formula": "路程 = 速度 × 时间，即s = vt",
    "description": "匀速运动的基本关系式"
  },
  {
    "name": "相遇问题",
    "formula": "相向运动：s总 = (v₁ + v₂) × t",
    "description": "两物体相向运动时的路程关系"
  },
  {
    "name": "追及问题",
    "formula": "同向运动：s差 = (v₁ - v₂) × t",
    "description": "两物体同向运动时的路程关系"
  }
]',
'[
  {
    "title": "相遇问题",
    "problem": "甲、乙两人同时从A、B两地相向而行，甲的速度是5km/h，乙的速度是4km/h，1.5小时后相遇。若甲比乙提前0.5小时出发，则甲出发后多长时间两人相遇？A、B两地间的距离是多少？",
    "solution": "设A、B两地距离为s km，甲出发后t小时两人相遇。由第一次相遇：s=5×1.5+4×1.5=13.5km。由第二次相遇：5t=4(t-0.5)+13.5，解得t=1.5小时。答：A、B两地距离13.5km，甲出发后1.5小时相遇",
    "analysis": "利用两次不同的运动情境建立两个等量关系"
  }
]',
'[
  {
    "concept": "相遇",
    "explanation": "两个运动物体在某时刻到达同一位置",
    "example": "甲从A地、乙从B地相向而行最终在某点相遇"
  },
  {
    "concept": "追及",
    "explanation": "后面的物体追上前面的物体",
    "example": "快车追上慢车"
  },
  {
    "concept": "匀速运动",
    "explanation": "速度保持不变的运动",
    "example": "汽车以60km/h的速度匀速行驶"
  }
]',
'[
  "混淆相遇与追及的运动方向",
  "时间关系理解错误",
  "忽视运动的先后时间差",
  "路程关系建立不当"
]',
'[
  "图示分析：画出运动示意图",
  "方向明确：区分相向和同向运动",
  "时间细心：注意出发时间的先后",
  "关系准确：正确建立路程等量关系"
]',
'{
  "emphasis": ["动静之美", "时空和谐"],
  "application": ["动画设计", "节奏艺术"],
  "connection": ["动态美学", "时空艺术"],
  "cultural_heritage": ["动静相宜", "时空智慧"]
}',
'{
  "emphasis": ["运动分析", "精确计算"],
  "application": ["交通规划", "物流调度"],
  "connection": ["运动学", "控制论"],
  "methodology": ["运动建模", "时空分析"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G7S2_CH10_010: 图表信息问题
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S2_CH10_010'),
'图表信息问题是从统计图表中提取信息建立方程组解决问题的应用题型',
'图表信息问题体现了数学与统计学、数据科学的紧密联系，是信息时代重要的数学应用技能。这类问题要求学生能够从各种统计图表（条形图、折线图、扇形图、表格等）中准确提取数据信息，理解数据间的内在关系，建立合适的数学模型。图表信息问题培养学生的数据分析能力、信息处理能力和图表阅读能力，是现代社会必备的数学素养。中华数学文化中"观象制器"的思想体现了从图表信息中洞察规律、解决问题的智慧传统。',
'[
  "信息提取：从图表中准确读取数据",
  "关系理解：理解数据间的逻辑关系",
  "模型建立：根据信息建立方程组",
  "数据验证：检验结果与图表的一致性",
  "应用广泛：统计、经济、社会等领域",
  "能力培养：数据分析和信息处理能力"
]',
'[
  {
    "name": "图表读取",
    "formula": "横轴读取×值，纵轴读取y值，交点确定坐标",
    "description": "从坐标图中读取数据的方法"
  },
  {
    "name": "比例关系",
    "formula": "扇形图中：部分/整体 = 扇形角度/360°",
    "description": "从扇形图中计算比例"
  },
  {
    "name": "趋势分析",
    "formula": "增长率 = (后期数值-前期数值)/前期数值×100%",
    "description": "分析数据变化趋势"
  }
]',
'[
  {
    "title": "销售统计图表问题",
    "problem": "某商店销售A、B两种商品，从统计图可知：第一季度A商品销量是B商品的1.5倍，第二季度A商品销量比第一季度增加20%，B商品销量比第一季度增加50%，两种商品第二季度总销量为350件。求第一季度A、B商品各销售多少件？",
    "solution": "设第一季度A商品销量为x件，B商品销量为y件。根据题意：{x=1.5y, 1.2x+1.5y=350}。解得：x=150，y=100。答：第一季度A商品销售150件，B商品销售100件",
    "analysis": "从图表信息中提取比例关系和增长关系建立方程组"
  }
]',
'[
  {
    "concept": "数据提取",
    "explanation": "从图表中准确获取数值信息",
    "example": "从条形图中读取各项的数值"
  },
  {
    "concept": "比例关系",
    "explanation": "不同数据之间的比例对应关系",
    "example": "A是B的1.5倍"
  },
  {
    "concept": "增长率",
    "explanation": "数据变化的百分比",
    "example": "销量增加20%"
  }
]',
'[
  "图表读取不准确",
  "忽视数据间的隐含关系",
  "增长率计算错误",
  "单位不统一导致错误"
]',
'[
  "仔细观察：认真阅读图表信息",
  "关系挖掘：找出数据间的内在联系",
  "计算准确：正确处理百分比问题",
  "单位统一：注意各项数据的单位"
]',
'{
  "emphasis": ["信息之美", "数据艺术"],
  "application": ["图表设计", "信息可视化"],
  "connection": ["信息美学", "数据艺术"],
  "cultural_heritage": ["观象制器", "信息智慧"]
}',
'{
  "emphasis": ["数据分析", "信息处理"],
  "application": ["数据挖掘", "商业分析"],
  "connection": ["统计学", "数据科学"],
  "methodology": ["数据建模", "信息分析"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'),

-- ============================================
-- 10.4 三元一次方程组的解法举例部分
-- ============================================

-- MATH_G7S2_CH10_011: 三元一次方程组的概念
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S2_CH10_011'),
'三元一次方程组是由三个含有三个未知数的一次方程组成的方程组',
'三元一次方程组概念是从二元向多元的重要扩展，体现了数学从简单到复杂的螺旋式发展规律。三元一次方程组的出现源于实际问题中同时涉及三个未知量的复杂情况，如立体几何中的空间坐标、物理学中的三维运动等。掌握三元一次方程组概念培养学生的高阶思维和空间想象能力，为学习线性代数、解析几何等高等数学内容奠定基础。在几何上，三个方程分别表示三个平面，其交点就是方程组的解。中华数学文化中"三才合一"的思想体现了三元一次方程组将三个独立方程统一考虑的整体智慧。',
'[
  "方程组定义：三个一次方程的组合",
  "未知数特征：含有三个未知数（通常用x、y、z）",
  "次数要求：每个未知数的最高次数都是1",
  "几何意义：三个平面的交点问题",
  "解的情况：可能有唯一解、无穷解或无解",
  "应用拓展：空间几何、物理学等领域"
]',
'[
  {
    "name": "标准形式",
    "formula": "{a₁x+b₁y+c₁z=d₁, a₂x+b₂y+c₂z=d₂, a₃x+b₃y+c₃z=d₃}",
    "description": "三元一次方程组的一般形式"
  },
  {
    "name": "系数矩阵",
    "formula": "系数行列式D = |a₁ b₁ c₁; a₂ b₂ c₂; a₃ b₃ c₃|",
    "description": "判断解的存在性的重要工具"
  },
  {
    "name": "解的判断",
    "formula": "D≠0：唯一解；D=0：可能无解或无穷解",
    "description": "根据系数行列式判断解的情况"
  }
]',
'[
  {
    "title": "建立三元一次方程组",
    "problem": "某商店销售三种商品，已知A商品2件、B商品3件、C商品1件共计55元；A商品1件、B商品2件、C商品3件共计50元；A商品3件、B商品1件、C商品2件共计60元。求各商品的单价",
    "solution": "设A、B、C商品单价分别为x、y、z元。根据题意：{2x+3y+z=55, x+2y+3z=50, 3x+y+2z=60}",
    "analysis": "三个不同的购买组合提供三个独立的等量关系"
  }
]',
'[
  {
    "concept": "三元",
    "explanation": "含有三个未知数",
    "example": "x、y、z三个未知数"
  },
  {
    "concept": "平面方程",
    "explanation": "三元一次方程在空间中表示一个平面",
    "example": "2x+3y+z=6表示空间中的一个平面"
  },
  {
    "concept": "交点",
    "explanation": "三个平面的公共交点",
    "example": "三个平面可能交于一点、一条直线或不相交"
  }
]',
'[
  "混淆二元与三元方程组",
  "不理解三个方程同时成立的要求",
  "忽视几何意义的重要性",
  "认为求解方法完全相同"
]',
'[
  "维度意识：理解从二维到三维的扩展",
  "整体思维：三个方程必须同时满足",
  "几何直观：结合空间几何理解",
  "方法拓展：在二元方法基础上发展"
]',
'{
  "emphasis": ["三才合一", "立体和谐"],
  "application": ["立体艺术", "空间设计"],
  "connection": ["立体美学", "空间艺术"],
  "cultural_heritage": ["三才合一", "立体智慧"]
}',
'{
  "emphasis": ["空间建模", "多维分析"],
  "application": ["三维建模", "空间分析"],
  "connection": ["空间几何", "线性代数"],
  "methodology": ["多元建模", "空间思维"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G7S2_CH10_012: 解三元一次方程组
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S2_CH10_012'),
'解三元一次方程组主要采用消元法，通过逐步消元将三元问题转化为二元问题',
'解三元一次方程组是代数方法在高维空间的重要应用，体现了数学中\"降维\"思想的精髓。通过消元法，将复杂的三元问题逐步简化为二元问题，再进一步简化为一元问题，完美诠释了\"化繁为简\"的数学智慧。这种方法不仅是计算技能，更是重要的数学思维方式，培养学生的系统性思维和逻辑推理能力。解三元一次方程组的过程体现了数学的层次性和递进性特征。中华数学文化中\"抽丝剥茧\"的思想完美体现了逐步消元、层层深入的解题策略。',
'[
  "方法原理：逐步消元降维求解",
  "消元顺序：先消一个变量，再消第二个",
  "转化思想：三元→二元→一元",
  "回代求解：逐层回代求出所有未知数",
  "验证检查：将解代入原方程组验证",
  "系统性强：体现数学的层次性特征"
]',
'[
  {
    "name": "消元步骤",
    "formula": "①选择消元变量②消元得二元方程组③解二元方程组④回代求第三个变量",
    "description": "解三元一次方程组的标准流程"
  },
  {
    "name": "消元策略",
    "formula": "优先消除系数简单的变量",
    "description": "选择消元变量的原则"
  },
  {
    "name": "验证公式",
    "formula": "将(x₀,y₀,z₀)代入三个原方程都成立",
    "description": "检验解的正确性"
  }
]',
'[
  {
    "title": "解三元一次方程组",
    "problem": "解方程组：{x+y+z=6, 2x-y+z=3, x+2y-z=1}",
    "solution": "①消z：方程1+方程3得2x+3y=7（方程4）；方程2+方程3得3x+y=4（方程5）②解二元方程组{2x+3y=7, 3x+y=4}：由方程5得y=4-3x，代入方程4得2x+3(4-3x)=7，解得x=1，y=1③回代：z=6-x-y=6-1-1=4④验证：(1,1,4)满足三个原方程。所以方程组的解为{x=1, y=1, z=4}",
    "analysis": "通过消元将三元问题转化为二元问题，再逐步求解"
  }
]',
'[
  {
    "concept": "降维",
    "explanation": "减少未知数的个数",
    "example": "从三个未知数减少到两个未知数"
  },
  {
    "concept": "逐步消元",
    "explanation": "一次消除一个变量",
    "example": "先消z得到关于x、y的方程组"
  },
  {
    "concept": "回代",
    "explanation": "将已求得的解代入前面的方程",
    "example": "求出x、y后代入原方程求z"
  }
]',
'[
  "消元过程中计算错误",
  "回代时代入错误的方程",
  "忘记验证所有原方程",
  "消元顺序选择不当增加计算量"
]',
'[
  "策略选择：优先消除系数简单的变量",
  "计算细心：消元过程要步步准确",
  "回代规范：按顺序逐层回代",
  "全面验证：检验所有原方程"
]',
'{
  "emphasis": ["层次之美", "渐进和谐"],
  "application": ["层次设计", "渐进艺术"],
  "connection": ["层次美学", "渐进艺术"],
  "cultural_heritage": ["抽丝剥茧", "层次智慧"]
}',
'{
  "emphasis": ["算法优化", "系统求解"],
  "application": ["算法设计", "系统分析"],
  "connection": ["数值方法", "计算技术"],
  "methodology": ["系统求解", "算法优化"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'),

-- MATH_G7S2_CH10_013: 中国古代数学中的线性方程组
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S2_CH10_013'),
'中国古代数学在线性方程组求解方面有着辉煌的成就，形成了独特的方法体系',
'中国古代数学在线性方程组领域创造了举世瞩目的成就，特别是《九章算术》中的\"方程\"章，系统地论述了线性方程组的解法，比欧洲早了一千多年。古代数学家创造的\"消元法\"与现代高斯消元法本质相同，体现了中华民族卓越的数学智慧。这些成就不仅展现了古代中国数学的先进性，更体现了中华文化注重实际应用、追求精确计算的特色。学习古代数学成就有助于培养学生的民族自豪感和文化认同感，激发对数学学习的兴趣和热情。',
'[
  "历史地位：世界上最早的线性方程组理论",
  "经典著作：《九章算术》方程章",
  "方法创新：消元法的古代版本",
  "实际应用：解决田亩、赋税、工程等问题",
  "计算工具：算筹的巧妙运用",
  "文化价值：体现中华数学文化的博大精深"
]',
'[
  {
    "name": "古代消元法",
    "formula": "通过算筹排列和运算消除未知数",
    "description": "《九章算术》中的消元方法"
  },
  {
    "name": "方程术",
    "formula": "古代求解线性方程组的专门方法",
    "description": "中国古代数学的重要成就"
  },
  {
    "name": "算筹计算",
    "formula": "用算筹表示数字和进行运算",
    "description": "古代数学计算的主要工具"
  }
]',
'[
  {
    "title": "《九章算术》方程问题",
    "problem": "今有上禾三秉，中禾二秉，下禾一秉，实三十九斗；上禾二秉，中禾三秉，下禾一秉，实三十四斗；上禾一秉，中禾二秉，下禾三秉，实二十六斗。问上、中、下禾实各几何？",
    "solution": "这是典型的三元一次方程组问题。设上禾一秉为x斗，中禾一秉为y斗，下禾一秉为z斗。则{3x+2y+z=39, 2x+3y+z=34, x+2y+3z=26}。用古代消元法可得：上禾一秉9.25斗，中禾一秉4.25斗，下禾一秉2.75斗",
    "analysis": "这个问题展现了中国古代数学解决实际问题的卓越能力"
  }
]',
'[
  {
    "concept": "方程术",
    "explanation": "古代中国求解线性方程组的方法",
    "example": "《九章算术》中的系统理论"
  },
  {
    "concept": "算筹",
    "explanation": "古代中国的计算工具",
    "example": "用竹签或木条进行数学运算"
  },
  {
    "concept": "实用数学",
    "explanation": "注重解决实际问题的数学传统",
    "example": "田亩计算、赋税分配等"
  }
]',
'[
  "不了解古代数学的先进性",
  "忽视中华数学文化的价值",
  "认为古代方法已经过时",
  "不理解古代方法与现代方法的联系"
]',
'[
  "历史自豪：认识中华数学文化的辉煌",
  "方法对比：古代方法与现代方法的异同",
  "文化传承：继承和发扬优秀数学传统",
  "创新精神：学习古代数学家的创新思维"
]',
'{
  "emphasis": ["文化传承", "历史荣耀"],
  "application": ["文化教育", "历史传承"],
  "connection": ["文化美学", "历史艺术"],
  "cultural_heritage": ["九章算术", "方程智慧"]
}',
'{
  "emphasis": ["方法创新", "计算技术"],
  "application": ["算法研究", "数值计算"],
  "connection": ["计算数学", "算法理论"],
  "methodology": ["古代算法", "计算创新"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G7S2_CH10_014: 中国古代著名的一次不定方程组问题
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S2_CH10_014'),
'中国古代数学在不定方程组求解方面创造了独特的理论和方法，以"韩信点兵"等问题最为著名',
'中国古代数学在不定方程组领域创造了世界领先的成就，特别是\"中国剩余定理\"的发现，比西方早了一千多年。\"韩信点兵\"、\"物不知数\"等经典问题不仅展现了古代数学家的卓越智慧，更体现了中华文化善于从生活实践中提炼数学问题的特色。这些问题的求解方法体现了中国古代数学注重算法、追求通法的特点，对现代密码学、计算机科学等领域都有重要影响。学习这些古代数学成就有助于培养学生的数学文化素养和民族自信心。',
'[
  "经典问题：韩信点兵、物不知数等",
  "理论成就：中国剩余定理的发现",
  "方法特色：大衍求一术等独特算法",
  "实际背景：军事、天文、历法等应用",
  "世界影响：对现代数学和计算机科学的贡献",
  "文化意义：体现中华数学文化的独特魅力"
]',
'[
  {
    "name": "韩信点兵问题",
    "formula": "x≡a₁(mod m₁), x≡a₂(mod m₂), ..., x≡aₙ(mod mₙ)",
    "description": "一次同余方程组的标准形式"
  },
  {
    "name": "中国剩余定理",
    "formula": "当m₁,m₂,...,mₙ两两互质时，同余方程组有唯一解",
    "description": "求解一次同余方程组的理论基础"
  },
  {
    "name": "大衍求一术",
    "formula": "古代求解同余方程组的具体算法",
    "description": "中国古代数学的重要算法"
  }
]',
'[
  {
    "title": "韩信点兵问题",
    "problem": "有物不知其数，三三数之剩二，五五数之剩三，七七数之剩二，问物几何？",
    "solution": "这是著名的\"物不知数\"问题。设物品总数为x，则：x≡2(mod 3), x≡3(mod 5), x≡2(mod 7)。用大衍求一术可得：x=23（最小正整数解）。一般解为x=23+105k（k为非负整数）",
    "analysis": "这个问题展现了中国古代数学在数论方面的卓越成就"
  }
]',
'[
  {
    "concept": "同余",
    "explanation": "两个数除以同一个数有相同的余数",
    "example": "23≡2(mod 3)表示23除以3余2"
  },
  {
    "concept": "不定方程",
    "explanation": "解的个数不唯一的方程",
    "example": "一次同余方程组通常有无穷多解"
  },
  {
    "concept": "大衍求一术",
    "explanation": "中国古代求解同余方程组的算法",
    "example": "《孙子算经》中记载的求解方法"
  }
]',
'[
  "不理解同余的概念",
  "混淆不定方程与一般方程",
  "不了解中国剩余定理的意义",
  "忽视古代算法的现代价值"
]',
'[
  "概念理解：深刻理解同余关系",
  "历史认知：了解中国数学的世界贡献",
  "算法学习：掌握大衍求一术的思想",
  "现代应用：认识古代算法的现代意义"
]',
'{
  "emphasis": ["文化瑰宝", "智慧传承"],
  "application": ["文化研究", "历史教育"],
  "connection": ["文化艺术", "智慧传承"],
  "cultural_heritage": ["韩信点兵", "大衍求一"]
}',
'{
  "emphasis": ["算法研究", "理论创新"],
  "application": ["密码学", "计算机科学"],
  "connection": ["数论", "算法理论"],
  "methodology": ["古代算法", "现代应用"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'); 