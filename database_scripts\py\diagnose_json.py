import json
import re

def diagnose_json():
    """详细诊断JSON问题"""
    print("开始详细诊断JSON问题...")
    
    try:
        # 读取文件
        with open('insert.json', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 解析外层JSON
        data = json.loads(content)
        text_content = data['text']
        
        # 提取JSON代码块
        pattern = r'```json(.*?)```'
        matches = re.findall(pattern, text_content, re.DOTALL)
        
        if not matches:
            print("没有找到JSON代码块")
            return
        
        json_content = matches[0].strip()
        print(f"JSON代码块长度: {len(json_content)}")
        
        # 分析前几行
        lines = json_content.split('\n')
        print(f"总行数: {len(lines)}")
        
        print("\n前10行内容:")
        for i, line in enumerate(lines[:10], 1):
            print(f"{i:2}: {repr(line)}")
        
        # 尝试找到第6行的问题
        if len(lines) >= 6:
            problem_line = lines[5]  # 第6行，索引为5
            print(f"\n第6行详细内容: {repr(problem_line)}")
            print(f"第6行长度: {len(problem_line)}")
            
            # 检查第30个字符周围
            if len(problem_line) >= 30:
                start = max(0, 25)
                end = min(len(problem_line), 35)
                print(f"第30个字符周围: {repr(problem_line[start:end])}")
        
        # 尝试手动修复几个常见问题
        print("\n尝试手动修复...")
        
        # 首先修复转义问题
        fixed_content = json_content
        
        # 修复双引号转义问题
        fixed_content = re.sub(r'(?<!\\)"', r'\"', fixed_content)  # 转义未转义的双引号
        fixed_content = re.sub(r'\\"', r'"', fixed_content)        # 先移除所有转义
        fixed_content = re.sub(r'"([^"]*)":', r'"\1":', fixed_content)  # 重新正确转义键
        
        # 保存中间结果用于检查
        with open('debug_step1.json', 'w', encoding='utf-8') as f:
            f.write(fixed_content)
        
        print("已保存第一步修复结果到 debug_step1.json")
        
        # 尝试解析
        try:
            parsed = json.loads(fixed_content)
            print("第一步修复成功!")
            return
        except json.JSONDecodeError as e:
            print(f"第一步修复失败: {e}")
            print(f"错误位置: line {e.lineno}, column {e.colno}")
            
            # 显示错误行
            if e.lineno <= len(lines):
                error_line = lines[e.lineno - 1]
                print(f"错误行内容: {repr(error_line)}")
        
        # 尝试最简单的方法：直接从文本中提取纯净的JSON
        print("\n尝试提取纯净JSON...")
        
        # 找到第一个 [ 和最后一个 ]
        start_bracket = json_content.find('[')
        end_bracket = json_content.rfind(']')
        
        if start_bracket != -1 and end_bracket != -1:
            clean_json = json_content[start_bracket:end_bracket+1]
            print(f"提取的纯净JSON长度: {len(clean_json)}")
            
            # 保存纯净JSON
            with open('clean_extracted.json', 'w', encoding='utf-8') as f:
                f.write(clean_json)
            
            try:
                parsed = json.loads(clean_json)
                print("纯净JSON解析成功!")
                print(f"题目数量: {len(parsed)}")
                
                # 创建一个正确的insert.json文件
                correct_data = {
                    "text": f"```json\n{json.dumps(parsed, ensure_ascii=False, indent=2)}\n```",
                    "usage": data.get("usage", {}),
                    "finish_reason": data.get("finish_reason", "stop"),
                    "files": data.get("files", [])
                }
                
                with open('insert_corrected.json', 'w', encoding='utf-8') as f:
                    json.dump(correct_data, f, ensure_ascii=False, indent=2)
                
                print("已创建正确的文件: insert_corrected.json")
                return parsed
                
            except json.JSONDecodeError as e:
                print(f"纯净JSON解析也失败: {e}")
        
        return None
        
    except Exception as e:
        print(f"诊断过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    result = diagnose_json()
    if result:
        print("\n诊断完成，问题已修复")
    else:
        print("\n诊断完成，但问题未能修复") 