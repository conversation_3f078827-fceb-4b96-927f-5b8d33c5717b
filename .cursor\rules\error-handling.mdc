---
description: 
globs: 
alwaysApply: true
---
# 错误处理与日志规范

本项目采用统一的错误处理机制和日志记录标准，确保应用的稳定性和可调试性。

## 错误类型分类

- **网络错误**：API请求失败、网络连接中断等
- **数据错误**：数据格式错误、必要字段缺失等
- **逻辑错误**：业务逻辑处理异常
- **权限错误**：用户无权限执行某操作
- **系统错误**：小程序运行环境异常

## 错误处理策略

- 所有API请求必须包含错误处理逻辑
- 使用try-catch包裹可能出错的代码块
- 关键操作必须有回滚机制
- 用户操作产生的错误必须有友好提示
- 系统性错误需引导用户刷新或重启应用

## 错误提示规范

- 提示语言简明友好，避免技术术语
- 区分用户可解决的错误和系统错误
- 提供可行的解决建议
- 重要操作的错误必须提供重试选项
- 连续错误不重复提示，避免骚扰

## 日志记录标准

- 日志分级：DEBUG, INFO, WARNING, ERROR, FATAL
- 关键业务流程记录INFO级别日志
- 所有错误必须记录ERROR级别日志
- 日志内容包含：时间戳、级别、模块、具体信息、关联数据
- 敏感信息（如密码）不记录在日志中

## 实现参考

- 错误处理工具：[utils/error-handler.js](mdc:utils/error-handler.js)
- 日志记录工具：[utils/logger.js](mdc:utils/logger.js)
- 错误提示组件：[components/common/error-toast/index.js](mdc:components/common/error-toast/index.js)

## 实现方式

```javascript
// 错误处理与日志记录示例
const logger = require('../utils/logger');

function fetchUserData(userId) {
  try {
    // 业务逻辑
    logger.info('开始获取用户数据', { userId });
    
    // API请求
    return api.getUserData(userId)
      .then(data => {
        logger.info('获取用户数据成功', { userId });
        return data;
      })
      .catch(error => {
        logger.error('获取用户数据失败', { userId, error: error.message });
        
        // 根据错误类型进行友好提示
        if (error.type === 'network') {
          wx.showToast({ title: '网络连接不稳定，请稍后再试', icon: 'none' });
        } else if (error.type === 'auth') {
          wx.showToast({ title: '登录已过期，请重新登录', icon: 'none' });
        } else {
          wx.showToast({ title: '获取数据失败，请重试', icon: 'none' });
        }
        
        throw error; // 继续向上传递错误
      });
  } catch (e) {
    logger.error('用户数据处理异常', { userId, error: e.message });
    wx.showToast({ title: '系统繁忙，请稍后再试', icon: 'none' });
  }
}
```

## 日志上报

- 开发环境日志仅记录在本地控制台
- 生产环境ERROR及以上级别日志自动上报到服务器
- 条件允许时记录用户操作路径，便于复现问题
- 定期分析错误日志，优化产品体验
