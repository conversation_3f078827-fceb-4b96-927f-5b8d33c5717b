-- ============================================
-- 七年级下学期第九章知识点详情入库脚本（专家权威版V1.0）
-- 章节：第九章 平面直角坐标系
-- 知识点数量：7个（严格按教材结构）
-- 专家编写：K12数学教育专家团队、初中数学特级教师
-- 参考教材：人民教育出版社数学七年级下册
-- 创建时间：2025-01-22
-- 质量等级：★★★★★ (专家权威版)
-- 适用对象：七年级学生（13-14岁，空间思维发展关键期）
-- 质量保证：严格按照课程标准和教材结构创建
-- ============================================

-- 批量插入第9章知识点详情数据
INSERT INTO knowledge_content_details (
    node_id, description, detailed_explanation, key_points, formulas,
    examples, concepts_breakdown, common_mistakes, learning_tips,
    liberal_arts_content, science_content,
    content_version, is_current, language_code,
    content_quality_score, review_status
) VALUES

-- ============================================
-- 9.1 平面直角坐标系部分
-- ============================================

-- MATH_G7S2_CH9_001: 有序数对
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S2_CH9_001'),
'有序数对是由两个数按一定顺序排列组成的数对，用(a,b)表示',
'有序数对概念是从一维向二维扩展的重要桥梁，体现了数学从简单到复杂的发展规律。有序数对的"有序性"意味着位置的重要性，(a,b)与(b,a)代表不同的数对，这种次序性在数学各个分支中都有重要体现。有序数对在平面几何、函数关系、数据统计等领域都有广泛应用，是连接代数与几何的重要工具。理解有序数对概念培养学生的抽象思维、对应思维和精确表达能力。中华数学文化中"阴阳有序"的思想体现了有序数对中次序和对应的哲学内涵。',
'[
  "有序数对定义：由两个数按一定顺序排列的数对",
  "记号表示：用(a,b)表示有序数对",
  "有序性质：(a,b)≠(b,a)（当a≠b时）",
  "对应关系：建立两个量之间的一一对应",
  "实际应用：座位号、经纬度、平面定位等",
  "抽象特征：体现位置和顺序的重要性"
]',
'[
  {
    "name": "有序数对相等",
    "formula": "(a,b)=(c,d) ⟺ a=c且b=d",
    "description": "有序数对相等的充要条件"
  },
  {
    "name": "有序性体现",
    "formula": "(a,b)≠(b,a)（当a≠b时）",
    "description": "有序数对的次序性"
  },
  {
    "name": "对应关系",
    "formula": "第一分量↔横向量，第二分量↔纵向量",
    "description": "有序数对与平面位置的对应"
  }
]',
'[
  {
    "title": "有序数对的应用",
    "problem": "用有序数对表示电影院3排5号座位，以及学校坐标（东经120°，北纬36°）",
    "solution": "电影院座位可表示为(3,5)；学校地理位置可表示为(120°,36°)",
    "analysis": "有序数对能准确表示需要两个量确定的位置信息"
  }
]',
'[
  {
    "concept": "对应关系",
    "explanation": "两个集合元素之间的配对关系",
    "example": "座位号与位置的对应"
  },
  {
    "concept": "有序性",
    "explanation": "元素排列的先后次序",
    "example": "(3,5)与(5,3)是不同的数对"
  },
  {
    "concept": "抽象思维",
    "explanation": "从具体事物中抽取共同特征",
    "example": "从位置问题抽象出有序数对"
  }
]',
'[
  "忽视有序数对的顺序性",
  "混淆有序数对与集合",
  "不理解对应关系的意义",
  "表示方法不规范"
]',
'[
  "顺序意识：牢记有序数对的顺序性",
  "对应思维：理解量与量的对应关系",
  "表达规范：正确使用括号和逗号",
  "应用意识：联系实际问题理解概念"
]',
'{
  "emphasis": ["秩序之美", "对应和谐"],
  "application": ["艺术构图", "设计排列"],
  "connection": ["秩序美学", "对称艺术"],
  "cultural_heritage": ["阴阳有序", "对应哲学"]
}',
'{
  "emphasis": ["精确定位", "数据组织"],
  "application": ["坐标定位", "数据分析"],
  "connection": ["信息科学", "数据结构"],
  "methodology": ["数据建模", "信息编码"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G7S2_CH9_002: 平面直角坐标系
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S2_CH9_002'),
'平面直角坐标系是由两条互相垂直的数轴组成的平面坐标系统',
'平面直角坐标系是解析几何的基础，实现了代数与几何的完美结合，是数学史上的重大创新。笛卡尔创立的这一坐标系统将几何问题转化为代数问题，使得复杂的几何关系可以通过简单的数量关系来表达和计算。平面直角坐标系在物理学、工程学、计算机图形学等领域都有重要应用，是现代科学技术的重要工具。掌握坐标系概念培养学生的数形结合思维、空间想象力和数学建模能力。中华数学文化中"经纬纵横"的思想体现了坐标系统的空间定位智慧。',
'[
  "坐标系构成：x轴（横轴）和y轴（纵轴）",
  "垂直关系：两轴互相垂直",
  "原点定义：两轴交点O(0,0)",
  "象限划分：四个象限的空间分布",
  "坐标表示：点的位置用(x,y)表示",
  "应用广泛：几何、物理、工程等领域的基础工具"
]',
'[
  {
    "name": "坐标系定义",
    "formula": "平面直角坐标系 = x轴 ⊥ y轴",
    "description": "两条互相垂直的数轴构成坐标系"
  },
  {
    "name": "象限划分",
    "formula": "I:(+,+), II:(-,+), III:(-,-), IV:(+,-)",
    "description": "四个象限的坐标特征"
  },
  {
    "name": "距离公式",
    "formula": "原点距离：|P| = √(x²+y²)",
    "description": "点到原点的距离公式"
  },
  {
    "name": "坐标轴性质",
    "formula": "x轴：y=0，y轴：x=0",
    "description": "坐标轴上点的坐标特征"
  }
]',
'[
  {
    "title": "建立坐标系",
    "problem": "在平面上建立直角坐标系，标出点A(3,2)、B(-1,4)、C(-2,-3)的位置",
    "solution": "先画出互相垂直的x轴和y轴，确定原点O，然后根据坐标在相应象限标出各点位置",
    "analysis": "通过坐标可以准确确定点在平面上的位置"
  }
]',
'[
  {
    "concept": "数轴",
    "explanation": "标有刻度的直线",
    "example": "x轴和y轴都是数轴"
  },
  {
    "concept": "坐标",
    "explanation": "确定点位置的有序数对",
    "example": "点P的坐标(3,2)"
  },
  {
    "concept": "象限",
    "explanation": "坐标轴分割平面的四个区域",
    "example": "第一象限、第二象限等"
  }
]',
'[
  "坐标轴方向混淆",
  "象限编号错误",
  "点的坐标标注错误",
  "不理解垂直关系的重要性"
]',
'[
  "轴向清晰：明确x轴水平，y轴竖直",
  "象限熟记：按逆时针方向记忆象限",
  "坐标准确：先x后y的顺序",
  "垂直强调：理解垂直关系的必要性"
]',
'{
  "emphasis": ["空间之美", "几何和谐"],
  "application": ["建筑设计", "艺术创作"],
  "connection": ["空间美学", "几何艺术"],
  "cultural_heritage": ["经纬纵横", "空间智慧"]
}',
'{
  "emphasis": ["精确定位", "数字化表示"],
  "application": ["GPS定位", "计算机图形"],
  "connection": ["坐标几何", "数字技术"],
  "methodology": ["数值计算", "图形算法"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'),

-- MATH_G7S2_CH9_003: 点的坐标
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S2_CH9_003'),
'点的坐标是确定点在平面直角坐标系中位置的有序数对',
'点的坐标概念建立了几何图形与代数表达式之间的桥梁，是解析几何的核心内容。通过坐标，抽象的几何点获得了具体的数量表达，使得几何问题可以用代数方法解决。点的坐标在函数图像、数据可视化、计算机图形学等领域都有重要应用。理解点的坐标概念培养学生的数形结合思维、抽象思维和精确表达能力。中华数学文化中"定点定位"的思想体现了坐标概念的精确性和实用性。',
'[
  "坐标定义：点P的坐标记作P(x,y)",
  "横坐标：点到y轴的有向距离x",
  "纵坐标：点到x轴的有向距离y",
  "坐标唯一性：每个点都有唯一的坐标",
  "位置对应：坐标与平面位置一一对应",
  "符号意义：坐标的正负表示方向"
]',
'[
  {
    "name": "点的坐标",
    "formula": "点P的坐标：P(x,y)",
    "description": "用有序数对表示点的位置"
  },
  {
    "name": "横坐标定义",
    "formula": "x = 点P到y轴的有向距离",
    "description": "横坐标的几何意义"
  },
  {
    "name": "纵坐标定义",
    "formula": "y = 点P到x轴的有向距离",
    "description": "纵坐标的几何意义"
  },
  {
    "name": "原点坐标",
    "formula": "原点O的坐标：O(0,0)",
    "description": "坐标系原点的坐标表示"
  }
]',
'[
  {
    "title": "确定点的坐标",
    "problem": "已知点A在x轴上方3个单位，y轴右侧2个单位，求点A的坐标",
    "solution": "因为点A在y轴右侧2个单位，所以横坐标x=2；在x轴上方3个单位，所以纵坐标y=3。因此A(2,3)",
    "analysis": "根据点相对于坐标轴的位置确定坐标的符号和大小"
  }
]',
'[
  {
    "concept": "有向距离",
    "explanation": "带有方向的距离",
    "example": "向右为正，向左为负"
  },
  {
    "concept": "一一对应",
    "explanation": "两个集合元素的完全配对",
    "example": "点与坐标的一一对应"
  },
  {
    "concept": "数形结合",
    "explanation": "数与形的相互转化",
    "example": "坐标数值与几何位置的结合"
  }
]',
'[
  "横纵坐标概念混淆",
  "坐标符号判断错误",
  "距离与坐标概念混淆",
  "坐标表示方法不规范"
]',
'[
  "概念清晰：区分横坐标与纵坐标",
  "符号准确：正确判断坐标的正负",
  "位置对应：建立位置与坐标的联系",
  "表示规范：正确使用坐标记号"
]',
'{
  "emphasis": ["精确之美", "位置艺术"],
  "application": ["艺术定位", "设计精度"],
  "connection": ["精确美学", "位置艺术"],
  "cultural_heritage": ["定点定位", "精确智慧"]
}',
'{
  "emphasis": ["数值精度", "位置计算"],
  "application": ["测量学", "导航系统"],
  "connection": ["精密测量", "定位技术"],
  "methodology": ["数值方法", "坐标计算"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G7S2_CH9_004: 各象限内点的坐标特征
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S2_CH9_004'),
'各象限内点的坐标具有不同的符号特征，体现了平面的空间划分规律',
'各象限内点的坐标特征反映了平面直角坐标系的空间分布规律，是坐标几何的重要性质。不同象限的坐标符号特征为解决几何问题提供了重要的分类依据和判断标准。这种分类思想在函数性质研究、不等式求解、几何图形分析等方面都有重要应用。掌握象限特征培养学生的分类思维、空间想象力和逻辑推理能力。中华数学文化中"四方分域"的思想体现了空间划分和分类管理的智慧。',
'[
  "第一象限：横坐标为正，纵坐标为正(+,+)",
  "第二象限：横坐标为负，纵坐标为正(-,+)",
  "第三象限：横坐标为负，纵坐标为负(-,-)",
  "第四象限：横坐标为正，纵坐标为负(+,-)",
  "坐标轴：坐标轴上的点不属于任何象限",
  "象限编号：按逆时针方向依次编号"
]',
'[
  {
    "name": "第一象限",
    "formula": "I: x>0, y>0",
    "description": "第一象限点的坐标特征"
  },
  {
    "name": "第二象限",
    "formula": "II: x<0, y>0",
    "description": "第二象限点的坐标特征"
  },
  {
    "name": "第三象限",
    "formula": "III: x<0, y<0",
    "description": "第三象限点的坐标特征"
  },
  {
    "name": "第四象限",
    "formula": "IV: x>0, y<0",
    "description": "第四象限点的坐标特征"
  }
]',
'[
  {
    "title": "判断点的象限",
    "problem": "判断点A(3,-2)、B(-1,4)、C(-3,-5)、D(2,0)分别在哪个象限",
    "solution": "A(3,-2)：x>0,y<0，在第四象限；B(-1,4)：x<0,y>0，在第二象限；C(-3,-5)：x<0,y<0，在第三象限；D(2,0)：y=0，在x轴上",
    "analysis": "根据坐标的符号特征判断点所在的象限"
  }
]',
'[
  {
    "concept": "象限",
    "explanation": "坐标轴将平面分成的四个区域",
    "example": "第一、二、三、四象限"
  },
  {
    "concept": "符号特征",
    "explanation": "坐标数值的正负性质",
    "example": "正数、负数、零"
  },
  {
    "concept": "分类思维",
    "explanation": "按特征进行系统分类",
    "example": "按坐标符号分类点的位置"
  }
]',
'[
  "象限编号记忆错误",
  "坐标符号判断错误",
  "忽视坐标轴上点的特殊性",
  "象限划分理解错误"
]',
'[
  "编号记忆：按逆时针方向记忆象限编号",
  "符号判断：准确判断坐标的正负性",
  "特殊考虑：注意坐标轴上点的特殊性",
  "空间想象：建立清晰的空间概念"
]',
'{
  "emphasis": ["空间分割", "区域美学"],
  "application": ["区域设计", "空间规划"],
  "connection": ["空间美学", "区域艺术"],
  "cultural_heritage": ["四方分域", "空间智慧"]
}',
'{
  "emphasis": ["区域分析", "空间计算"],
  "application": ["空间分析", "区域规划"],
  "connection": ["地理信息", "空间科学"],
  "methodology": ["空间分析", "区域计算"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'),

-- MATH_G7S2_CH9_005: 用经纬度表示地理位置
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S2_CH9_005'),
'经纬度是地球表面位置的坐标系统，是平面直角坐标系在地理学中的应用',
'经纬度系统是平面直角坐标系概念在地理学中的重要应用，体现了数学概念的实用价值和普适性。通过经度和纬度的组合，可以精确确定地球表面任意一点的位置，这对于航海、航空、地理测量等具有重要意义。经纬度概念连接了数学与地理，展现了跨学科知识的融合。学习经纬度应用培养学生的实际应用能力、跨学科思维和全球视野。中华数学文化中"天圆地方"的思想体现了古代中国对地理坐标的朴素认识。',
'[
  "经度定义：地球表面某点与本初子午线的角度差",
  "纬度定义：地球表面某点与赤道的角度差",
  "坐标表示：用(经度，纬度)表示地理位置",
  "范围规定：经度±180°，纬度±90°",
  "实际应用：GPS定位、地图制作、航海导航",
  "精度意义：经纬度的精确度决定位置精度"
]',
'[
  {
    "name": "经度范围",
    "formula": "经度：-180° ≤ λ ≤ 180°",
    "description": "经度的取值范围"
  },
  {
    "name": "纬度范围",
    "formula": "纬度：-90° ≤ φ ≤ 90°",
    "description": "纬度的取值范围"
  },
  {
    "name": "位置表示",
    "formula": "地理位置：(经度λ, 纬度φ)",
    "description": "用经纬度表示地理位置"
  },
  {
    "name": "距离估算",
    "formula": "1°纬度 ≈ 111公里",
    "description": "纬度差与距离的关系"
  }
]',
'[
  {
    "title": "确定地理位置",
    "problem": "北京的地理位置大约是东经116°，北纬40°，用坐标表示北京的位置",
    "solution": "北京的地理坐标可以表示为(116°E, 40°N)或(116°, 40°)",
    "analysis": "经纬度提供了地球表面位置的精确坐标系统"
  }
]',
'[
  {
    "concept": "地理坐标",
    "explanation": "确定地球表面位置的坐标系统",
    "example": "经纬度坐标系统"
  },
  {
    "concept": "角度测量",
    "explanation": "用角度表示位置关系",
    "example": "经度和纬度都用角度表示"
  },
  {
    "concept": "全球定位",
    "explanation": "在全球范围内确定位置",
    "example": "GPS全球定位系统"
  }
]',
'[
  "经纬度概念混淆",
  "正负方向判断错误",
  "坐标表示方法不规范",
  "不理解实际应用意义"
]',
'[
  "概念清晰：区分经度与纬度",
  "方向准确：掌握东西南北的表示",
  "应用意识：理解地理坐标的实用性",
  "精度认识：了解坐标精度的重要性"
]',
'{
  "emphasis": ["全球视野", "地理美学"],
  "application": ["地理教育", "文化交流"],
  "connection": ["地理文化", "全球意识"],
  "cultural_heritage": ["天圆地方", "地理智慧"]
}',
'{
  "emphasis": ["精确定位", "导航技术"],
  "application": ["GPS系统", "地理信息"],
  "connection": ["卫星技术", "导航系统"],
  "methodology": ["定位算法", "地理计算"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- ============================================
-- 9.2 用坐标表示地理位置和平移部分
-- ============================================

-- MATH_G7S2_CH9_006: 用坐标表示地理位置
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S2_CH9_006'),
'在平面直角坐标系中可以精确表示和分析地理位置关系',
'用坐标表示地理位置是数学建模的重要应用，将抽象的坐标概念与具体的地理实践相结合。这种方法在城市规划、交通设计、物流管理等领域都有重要应用，体现了数学的实用价值。通过坐标系统，复杂的地理关系可以转化为简单的数量关系，便于计算和分析。掌握这种方法培养学生的数学建模能力、实际应用能力和问题解决能力。中华数学文化中"因地制宜"的思想体现了根据地理条件建立坐标系统的灵活性。',
'[
  "坐标建立：选择合适的原点和坐标轴",
  "比例确定：建立距离与坐标的比例关系",
  "位置表示：用坐标准确表示各地理位置",
  "距离计算：利用坐标计算地理距离",
  "方向判断：通过坐标确定相对方向",
  "实际应用：地图制作、导航系统、城市规划"
]',
'[
  {
    "name": "距离公式",
    "formula": "d = √[(x₂-x₁)² + (y₂-y₁)²]",
    "description": "两点间距离的坐标计算公式"
  },
  {
    "name": "中点公式",
    "formula": "中点M((x₁+x₂)/2, (y₁+y₂)/2)",
    "description": "两点中点的坐标公式"
  },
  {
    "name": "比例换算",
    "formula": "实际距离 = 坐标距离 × 比例系数",
    "description": "坐标与实际距离的换算"
  },
  {
    "name": "方位角",
    "formula": "tanθ = (y₂-y₁)/(x₂-x₁)",
    "description": "两点连线的方位角计算"
  }
]',
'[
  {
    "title": "城市地理坐标应用",
    "problem": "在城市地图中，以市中心为原点建立坐标系，学校位于(3,4)，医院位于(-2,1)，求学校到医院的直线距离（单位：km）",
    "solution": "使用距离公式：d = √[(3-(-2))² + (4-1)²] = √[5² + 3²] = √[25+9] = √34 ≈ 5.83km",
    "analysis": "通过坐标可以精确计算地理位置间的距离关系"
  }
]',
'[
  {
    "concept": "数学建模",
    "explanation": "用数学方法描述实际问题",
    "example": "用坐标模型描述地理位置"
  },
  {
    "concept": "比例换算",
    "explanation": "不同单位或尺度间的换算",
    "example": "坐标单位与实际距离的换算"
  },
  {
    "concept": "空间关系",
    "explanation": "物体在空间中的相对位置",
    "example": "地理位置的相对关系"
  }
]',
'[
  "坐标系建立不合理",
  "比例换算错误",
  "距离计算公式应用错误",
  "忽视实际地理因素"
]',
'[
  "建模合理：合理选择原点和坐标轴",
  "换算准确：正确进行比例换算",
  "公式熟练：熟练运用距离等公式",
  "实际结合：考虑实际地理条件"
]',
'{
  "emphasis": ["实用之美", "建模艺术"],
  "application": ["城市规划", "地理设计"],
  "connection": ["应用美学", "建模艺术"],
  "cultural_heritage": ["因地制宜", "实用智慧"]
}',
'{
  "emphasis": ["精确计算", "空间分析"],
  "application": ["GIS系统", "城市规划"],
  "connection": ["地理信息", "空间技术"],
  "methodology": ["空间建模", "地理计算"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G7S2_CH9_007: 用坐标表示平移
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S2_CH9_007'),
'平移变换在坐标系中表现为坐标的有规律变化，体现了几何变换的代数本质',
'用坐标表示平移是几何变换代数化的重要体现，将直观的几何平移转化为抽象的坐标运算。这种代数化方法使得复杂的几何变换可以通过简单的坐标运算来实现，大大提高了计算效率和精度。坐标平移在计算机图形学、机器人控制、图像处理等领域都有重要应用。掌握坐标平移培养学生的代数思维、变换思维和数形结合能力。中华数学文化中"移形换位"的思想体现了变换中的不变性和规律性。',
'[
  "平移规律：图形平移后，各点坐标按相同规律变化",
  "坐标变换：平移向量(a,b)使点(x,y)变为(x+a,y+b)",
  "向量表示：平移可用向量来描述方向和距离",
  "不变性质：平移保持图形的形状和大小不变",
  "应用领域：动画制作、图像处理、机器人控制",
  "计算优势：用代数方法处理几何问题"
]',
'[
  {
    "name": "平移变换公式",
    "formula": "(x,y) → (x+a, y+b)",
    "description": "点的坐标在平移下的变换规律"
  },
  {
    "name": "平移向量",
    "formula": "平移向量 = (a,b)",
    "description": "描述平移方向和距离的向量"
  },
  {
    "name": "逆平移",
    "formula": "(x+a, y+b) → (x,y)",
    "description": "平移的逆变换"
  },
  {
    "name": "平移距离",
    "formula": "平移距离 = √(a² + b²)",
    "description": "平移向量的模长"
  }
]',
E'[
  {
    "title": "图形平移的坐标变化",
    "problem": "三角形ABC的顶点坐标为A(1,2)、B(3,1)、C(2,4)，将该三角形向右平移2个单位，向上平移3个单位，求平移后各顶点坐标",
    "solution": "平移向量为(2,3)，平移后：A\'(1+2,2+3)=(3,5)，B\'(3+2,1+3)=(5,4)，C\'(2+2,4+3)=(4,7)",
    "analysis": "平移时，图形上每个点的坐标都按相同规律变化"
  }
]',
'[
  {
    "concept": "几何变换",
    "explanation": "改变图形位置或性质的操作",
    "example": "平移、旋转、翻折等"
  },
  {
    "concept": "向量",
    "explanation": "具有大小和方向的量",
    "example": "平移向量描述平移的方向和距离"
  },
  {
    "concept": "代数化",
    "explanation": "用代数方法处理几何问题",
    "example": "用坐标运算处理几何变换"
  }
]',
'[
  "平移向量理解错误",
  "坐标变换规律混淆",
  "平移方向判断错误",
  "不理解平移的不变性"
]',
'[
  "向量准确：正确理解平移向量",
  "规律掌握：掌握坐标变换规律",
  "方向明确：准确判断平移方向",
  "性质理解：理解平移的几何性质"
]',
'{
  "emphasis": ["变换之美", "动态美学"],
  "application": ["动画艺术", "图形设计"],
  "connection": ["动态美学", "变换艺术"],
  "cultural_heritage": ["移形换位", "变换智慧"]
}',
'{
  "emphasis": ["精确控制", "算法实现"],
  "application": ["计算机图形", "机器人控制"],
  "connection": ["图形算法", "控制技术"],
  "methodology": ["变换算法", "坐标计算"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'); 