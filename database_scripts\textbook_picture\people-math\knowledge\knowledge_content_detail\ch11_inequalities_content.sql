-- ============================================
-- 七年级下学期第十一章知识点详情入库脚本（专家权威版V1.0）
-- 章节：第十一章 不等式与不等式组
-- 知识点数量：10个（严格按教材结构）
-- 专家编写：K12数学教育专家团队、初中数学特级教师
-- 参考教材：人民教育出版社数学七年级下册
-- 创建时间：2025-01-22
-- 质量等级：★★★★★ (专家权威版)
-- 适用对象：七年级学生（13-14岁，不等关系认知发展关键期）
-- 质量保证：严格按照课程标准和教材结构创建
-- ============================================

-- 批量插入第11章知识点详情数据
INSERT INTO knowledge_content_details (
    node_id, description, detailed_explanation, key_points, formulas,
    examples, concepts_breakdown, common_mistakes, learning_tips,
    liberal_arts_content, science_content,
    content_version, is_current, language_code,
    content_quality_score, review_status
) VALUES

-- ============================================
-- 11.1 不等式部分
-- ============================================

-- MATH_G7S2_CH11_001: 不等式的概念
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S2_CH11_001'),
'不等式是表示两个量大小关系的数学表达式',
'不等式概念是数学中表达不等关系的重要工具，是从等式向更广泛关系的重要扩展。不等式反映了客观世界中普遍存在的不等关系，如长短、轻重、快慢等，体现了数学对现实世界的深刻抽象。掌握不等式概念不仅是学习不等式理论的基础，更培养学生的比较思维、关系思维和辩证思维。不等式在优化理论、经济学、物理学等领域都有重要应用，是现代数学的基础内容。中华数学文化中"大小有序"的思想体现了不等关系的重要性和普遍性。',
'[
  "关系表达：用符号表示两个量的大小关系",
  "符号体系：>、<、≥、≤四种基本符号",
  "实际背景：来源于现实世界的比较关系",
  "抽象特征：从具体比较到抽象关系的升华",
  "应用广泛：优化、决策、控制等领域的基础",
  "思维培养：发展比较思维和关系思维"
]',
'[
  {
    "name": "不等式定义",
    "formula": "a>b, a<b, a≥b, a≤b",
    "description": "四种基本不等关系的符号表示"
  },
  {
    "name": "不等号含义",
    "formula": ">：大于，<：小于，≥：大于或等于，≤：小于或等于",
    "description": "不等号的精确含义"
  },
  {
    "name": "实数比较",
    "formula": "在数轴上，右边的数总比左边的数大",
    "description": "实数大小比较的几何直观"
  }
]',
'[
  {
    "title": "建立不等式",
    "problem": "用不等式表示：①小明的身高不超过160cm；②某商品的价格至少50元；③今天的气温高于昨天",
    "solution": "①设小明身高为h cm，则h≤160；②设商品价格为p元，则p≥50；③设今天气温为T₁，昨天气温为T₂，则T₁>T₂",
    "analysis": "不等式是用数学语言表达现实中不等关系的重要工具"
  }
]',
'[
  {
    "concept": "不等关系",
    "explanation": "两个量之间大小、多少的比较关系",
    "example": "身高、重量、速度等的比较"
  },
  {
    "concept": "不等号",
    "explanation": "表示不等关系的数学符号",
    "example": ">表示大于，<表示小于"
  },
  {
    "concept": "数轴比较",
    "explanation": "利用数轴上点的位置比较数的大小",
    "example": "数轴上右边的点对应的数较大"
  }
]',
'[
  "混淆不等号的方向",
  "忽视等号的含义（≥、≤）",
  "不理解不等式的实际意义",
  "在数轴上比较时方向错误"
]',
'[
  "符号记忆：开口总指向较大的数",
  "实际联系：结合具体情境理解不等关系",
  "数轴直观：利用数轴位置理解大小关系",
  "含义准确：区分严格不等与非严格不等"
]',
'{
  "emphasis": ["秩序之美", "关系和谐"],
  "application": ["比较艺术", "层次设计"],
  "connection": ["秩序美学", "关系艺术"],
  "cultural_heritage": ["大小有序", "关系智慧"]
}',
'{
  "emphasis": ["关系建模", "比较分析"],
  "application": ["优化设计", "决策分析"],
  "connection": ["关系数学", "优化理论"],
  "methodology": ["比较方法", "关系分析"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G7S2_CH11_002: 不等式的性质
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S2_CH11_002'),
'不等式的性质是关于不等式运算和变形的基本规律',
'不等式的性质是不等式理论的核心内容，揭示了不等关系在运算过程中的变化规律。这些性质不仅是解不等式的理论基础，更体现了数学中的对称性和不变性原理。掌握不等式性质培养学生的逻辑推理能力和规律认识能力，特别是性质3中"不等号方向改变"的重要规律，体现了数学的严谨性和精确性。不等式性质在证明、求解、优化等方面都有重要应用，是后续学习函数、线性规划等内容的基础。中华数学文化中"变中有常"的思想完美体现了不等式性质中不变与变化的辩证关系。',
'[
  "性质体系：三个基本性质的完整体系",
  "运算规律：加减乘除运算对不等关系的影响",
  "方向变化：负数乘除导致不等号方向改变",
  "传递性质：不等关系的传递规律",
  "应用基础：解不等式和证明的理论依据",
  "逻辑严谨：体现数学推理的严密性"
]',
'[
  {
    "name": "不等式性质1",
    "formula": "如果a>b，那么a+c>b+c，a-c>b-c",
    "description": "不等式两边同时加减同一个数，不等号方向不变"
  },
  {
    "name": "不等式性质2",
    "formula": "如果a>b，c>0，那么ac>bc，a/c>b/c",
    "description": "不等式两边同时乘除正数，不等号方向不变"
  },
  {
    "name": "不等式性质3",
    "formula": "如果a>b，c<0，那么ac<bc，a/c<b/c",
    "description": "不等式两边同时乘除负数，不等号方向改变"
  },
  {
    "name": "传递性",
    "formula": "如果a>b，b>c，那么a>c",
    "description": "不等关系的传递性质"
  }
]',
'[
  {
    "title": "不等式性质应用",
    "problem": "已知-2x+3>7，求x的取值范围",
    "solution": "由-2x+3>7，根据性质1，两边减3得：-2x>4。根据性质3，两边除以-2（注意不等号方向改变）得：x<-2",
    "analysis": "解不等式的关键是正确应用不等式性质，特别注意负数乘除时方向的改变"
  }
]',
'[
  {
    "concept": "性质1",
    "explanation": "加减不改变不等号方向",
    "example": "5>3，则5+2>3+2"
  },
  {
    "concept": "性质2",
    "explanation": "乘除正数不改变不等号方向",
    "example": "5>3，则5×2>3×2"
  },
  {
    "concept": "性质3",
    "explanation": "乘除负数改变不等号方向",
    "example": "5>3，则5×(-2)<3×(-2)"
  }
]',
'[
  "忘记负数乘除时改变不等号方向",
  "混淆性质的适用条件",
  "不理解传递性的含义",
  "应用性质时计算错误"
]',
'[
  "符号意识：时刻注意乘除数的正负性",
  "方向记忆：负数乘除必须变方向",
  "条件明确：准确理解每个性质的条件",
  "步骤规范：变形时写明依据的性质"
]',
'{
  "emphasis": ["变中有常", "规律之美"],
  "application": ["规律探索", "逻辑推理"],
  "connection": ["变化美学", "规律艺术"],
  "cultural_heritage": ["变中有常", "规律智慧"]
}',
'{
  "emphasis": ["逻辑推理", "规律应用"],
  "application": ["逻辑系统", "推理机制"],
  "connection": ["数理逻辑", "推理科学"],
  "methodology": ["逻辑方法", "推理技术"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'),

-- MATH_G7S2_CH11_003: 用求差法比较大小
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S2_CH11_003'),
'求差法是通过计算两个数或式子的差来比较它们大小的方法',
'求差法是比较大小的重要方法，体现了数学中"化未知为已知"的转化思想。通过计算差值，将抽象的大小比较转化为具体的正负性判断，是一种巧妙的数学方法。求差法不仅适用于数的比较，更广泛应用于代数式、函数值等的比较，培养学生的转化思维和计算能力。这种方法在数学分析、不等式证明等高等数学中都有重要应用。中华数学文化中"以减求比"的思想体现了通过做差运算来认识大小关系的智慧方法。',
'[
  "方法原理：通过计算差值判断大小关系",
  "转化思想：将比较问题转化为正负性判断",
  "计算步骤：作差→化简→判断符号",
  "适用范围：数值比较、代数式比较等",
  "思维培养：发展转化思维和计算思维",
  "应用拓展：为后续不等式证明奠定基础"
]',
'[
  {
    "name": "求差法原理",
    "formula": "若a-b>0，则a>b；若a-b<0，则a<b；若a-b=0，则a=b",
    "description": "通过差的正负性判断大小关系"
  },
  {
    "name": "求差法步骤",
    "formula": "①作差：计算a-b；②化简：对差式进行化简；③判断：确定差的正负性",
    "description": "求差法的标准操作流程"
  },
  {
    "name": "常用技巧",
    "formula": "配方、因式分解、有理化等化简方法",
    "description": "求差法中常用的代数变形技巧"
  }
]',
'[
  {
    "title": "用求差法比较大小",
    "problem": "比较√7+√3与2√5的大小",
    "solution": "计算(√7+√3)-2√5。设a=√7+√3，b=2√5，则a²=(√7+√3)²=7+3+2√21=10+2√21，b²=(2√5)²=20。比较a²与b²：(10+2√21)-20=2√21-10。因为√21<5（即21<25），所以2√21<10，即2√21-10<0。因此a²<b²，又a、b都为正数，所以a<b，即√7+√3<2√5",
    "analysis": "求差法通过平方比较避免了直接计算根式的困难"
  }
]',
'[
  {
    "concept": "作差",
    "explanation": "计算两个数或式子的差",
    "example": "比较a与b，计算a-b"
  },
  {
    "concept": "化简",
    "explanation": "对差式进行代数变形",
    "example": "因式分解、配方等方法"
  },
  {
    "concept": "判断符号",
    "explanation": "确定差式的正负性",
    "example": "差为正则前者大，差为负则后者大"
  }
]',
'[
  "作差时计算错误",
  "化简过程不充分",
  "符号判断错误",
  "忽视特殊情况（如需要平方比较）"
]',
'[
  "计算细心：作差时注意符号和运算",
  "化简彻底：充分利用代数变形技巧",
  "符号明确：准确判断差的正负性",
  "方法灵活：根据题目特点选择合适技巧"
]',
'{
  "emphasis": ["转化之美", "方法智慧"],
  "application": ["思维转换", "策略运用"],
  "connection": ["转化美学", "方法艺术"],
  "cultural_heritage": ["以减求比", "转化智慧"]
}',
'{
  "emphasis": ["计算方法", "逻辑推理"],
  "application": ["数值计算", "比较分析"],
  "connection": ["计算数学", "比较科学"],
  "methodology": ["差值方法", "比较技术"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- ============================================
-- 11.2 一元一次不等式部分
-- ============================================

-- MATH_G7S2_CH11_004: 一元一次不等式的概念
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S2_CH11_004'),
'一元一次不等式是含有一个未知数且未知数的最高次数为1的不等式',
'一元一次不等式概念是不等式理论向具体应用的重要发展，体现了从一般到特殊的数学认识过程。一元一次不等式将不等关系与代数方程理论相结合，为解决实际问题提供了强有力的数学工具。掌握一元一次不等式概念是学习解不等式方法的基础，培养学生的代数思维和问题建模能力。在经济学的成本分析、物理学的阈值问题、工程学的优化设计等领域都有重要应用。中华数学文化中"以一知多"的思想体现了从简单形式认识复杂关系的智慧方法。',
'[
  "定义特征：一个未知数，最高次数为1",
  "标准形式：ax+b>0或ax+b<0等形式",
  "未知数系数：一次项系数不为零",
  "解的概念：使不等式成立的未知数的值",
  "解集概念：所有解组成的集合",
  "实际建模：现实问题的数学表达"
]',
'[
  {
    "name": "一元一次不等式定义",
    "formula": "形如ax+b>0 (a≠0)的不等式",
    "description": "含一个未知数且未知数最高次数为1的不等式"
  },
  {
    "name": "标准形式",
    "formula": "ax+b>0, ax+b<0, ax+b≥0, ax+b≤0 (a≠0)",
    "description": "一元一次不等式的四种基本形式"
  },
  {
    "name": "解的定义",
    "formula": "使不等式成立的未知数的值",
    "description": "满足不等式条件的x的取值"
  },
  {
    "name": "解集",
    "formula": "所有解组成的集合",
    "description": "用集合或区间表示的解的范围"
  }
]',
'[
  {
    "title": "判断一元一次不等式",
    "problem": "下列哪些是一元一次不等式？①2x+3>5；②x²-1<0；③2x+y≥3；④5>3",
    "solution": "①2x+3>5是一元一次不等式（一个未知数x，最高次数为1）；②x²-1<0不是（最高次数为2）；③2x+y≥3不是（含两个未知数）；④5>3不是（不含未知数）",
    "analysis": "判断一元一次不等式的关键是确认未知数个数和最高次数"
  }
]',
'[
  {
    "concept": "未知数",
    "explanation": "不等式中的变量，通常用x表示",
    "example": "在2x+3>5中，x是未知数"
  },
  {
    "concept": "次数",
    "explanation": "未知数的最高指数",
    "example": "2x+3中x的次数为1"
  },
  {
    "concept": "系数",
    "explanation": "未知数前的数字",
    "example": "2x中2是系数，且必须不为0"
  }
]',
'[
  "混淆一元二次不等式",
  "忽视系数不为零的条件",
  "误认为含多个未知数的不等式",
  "不理解解与解集的区别"
]',
'[
  "定义掌握：准确理解一元一次的含义",
  "形式识别：熟练识别标准形式",
  "条件明确：注意系数不为零的限制",
  "概念区分：区分解和解集的概念"
]',
'{
  "emphasis": ["简约之美", "形式和谐"],
  "application": ["模型构建", "问题表达"],
  "connection": ["形式美学", "简约艺术"],
  "cultural_heritage": ["以一知多", "形式智慧"]
}',
'{
  "emphasis": ["数学建模", "代数方法"],
  "application": ["问题建模", "代数分析"],
  "connection": ["代数学", "建模科学"],
  "methodology": ["建模方法", "代数技术"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G7S2_CH11_005: 解一元一次不等式
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S2_CH11_005'),
'解一元一次不等式是求出使不等式成立的未知数取值范围的过程',
'解一元一次不等式是不等式理论的核心技能，体现了数学中的算法思想和程序思维。解不等式的过程类似于解方程，但需要特别注意不等式性质的应用，特别是负数乘除时方向的改变。这一技能不仅是数学学习的重要内容，更是培养学生逻辑思维、程序思维和细致严谨品质的重要途径。解不等式在优化问题、决策分析、工程设计等领域都有重要应用。中华数学文化中"循法而行"的思想体现了按照规律和方法解决问题的智慧。',
'[
  "解题步骤：系统化的求解程序",
  "性质应用：正确使用不等式性质",
  "方向注意：负数乘除改变方向",
  "化简过程：逐步简化到标准形式",
  "检验习惯：验证解的正确性",
  "表示方法：用数轴或区间表示解集"
]',
'[
  {
    "name": "解不等式步骤",
    "formula": "①去分母；②去括号；③移项；④合并同类项；⑤系数化为1",
    "description": "解一元一次不等式的标准流程"
  },
  {
    "name": "关键注意",
    "formula": "不等式两边乘除负数时，不等号方向改变",
    "description": "解不等式过程中的重要原则"
  },
  {
    "name": "解集表示",
    "formula": "用不等式、数轴或区间表示",
    "description": "解集的多种表示方法"
  }
]',
'[
  {
    "title": "解一元一次不等式",
    "problem": "解不等式：3(x-2)≤2x+4",
    "solution": "去括号：3x-6≤2x+4；移项：3x-2x≤4+6；合并同类项：x≤10。解集为{x|x≤10}或(-∞,10]",
    "analysis": "解不等式的关键是正确应用不等式性质，保持解题步骤的规范性"
  },
  {
    "title": "含负系数的不等式",
    "problem": "解不等式：-2x+5>3x-10",
    "solution": "移项：-2x-3x>-10-5；合并：-5x>-15；两边除以-5（不等号变向）：x<3。解集为{x|x<3}或(-∞,3)",
    "analysis": "当系数为负数时，化系数为1需要改变不等号方向"
  }
]',
'[
  {
    "concept": "去分母",
    "explanation": "两边同乘各分母的最小公倍数",
    "example": "解x/2+1>x/3时，两边乘6"
  },
  {
    "concept": "移项",
    "explanation": "将含未知数的项移到一边，常数项移到另一边",
    "example": "3x>2x+5移项得3x-2x>5"
  },
  {
    "concept": "系数化1",
    "explanation": "将未知数的系数变为1",
    "example": "2x>6化为x>3"
  }
]',
'[
  "移项时符号错误",
  "忘记改变不等号方向",
  "去分母时遗漏某些项",
  "解集表示不正确"
]',
'[
  "步骤规范：按照标准步骤逐步求解",
  "符号仔细：特别注意负数乘除的方向变化",
  "移项准确：移项时正确改变符号",
  "检验验证：代入特殊值检验解的正确性"
]',
'{
  "emphasis": ["程序之美", "步骤和谐"],
  "application": ["方法艺术", "程序设计"],
  "connection": ["程序美学", "方法艺术"],
  "cultural_heritage": ["循法而行", "程序智慧"]
}',
'{
  "emphasis": ["算法思维", "程序方法"],
  "application": ["算法设计", "程序控制"],
  "connection": ["算法科学", "程序技术"],
  "methodology": ["算法方法", "程序思维"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'),

-- MATH_G7S2_CH11_006: 在数轴上表示不等式的解集
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S2_CH11_006'),
'在数轴上表示不等式的解集是将抽象的数学解集用几何图形直观展示的方法',
'在数轴上表示解集是数形结合思想的重要体现，将抽象的代数解集转化为直观的几何图形，增强了数学的可视化和理解性。这种表示方法不仅帮助学生更好地理解解集的含义，更培养学生的数形结合思维和几何直观能力。数轴表示法在概率统计、函数分析、优化理论等领域都有重要应用，是现代数学可视化的基础方法。中华数学文化中"数形相映"的思想完美体现了数与形的有机统一。',
'[
  "几何直观：用数轴上的图形表示数的范围",
  "符号约定：实心点、空心点的不同含义",
  "方向表示：用射线或线段表示解集范围",
  "端点处理：正确处理边界点的包含关系",
  "数形结合：抽象数学与直观图形的统一",
  "可视化思维：培养几何直观和空间想象"
]',
'[
  {
    "name": "端点表示",
    "formula": "实心点：包含该点；空心点：不包含该点",
    "description": "数轴上点的表示约定"
  },
  {
    "name": "解集表示",
    "formula": "x>a：a右侧射线，a为空心点；x≥a：a右侧射线，a为实心点",
    "description": "不同不等式解集的数轴表示"
  },
  {
    "name": "方向规律",
    "formula": ">或≥向右；<或≤向左",
    "description": "不等号方向与数轴方向的对应关系"
  }
]',
'[
  {
    "title": "数轴表示解集",
    "problem": "在数轴上表示下列不等式的解集：①x>-2；②x≤3；③-1<x≤2",
    "solution": "①x>-2：在-2处画空心点，向右画射线；②x≤3：在3处画实心点，向左画射线；③-1<x≤2：在-1处画空心点，在2处画实心点，连接线段",
    "analysis": "数轴表示解集要注意端点的实虚和射线的方向"
  }
]',
'[
  {
    "concept": "实心点",
    "explanation": "表示该点包含在解集中",
    "example": "x≥3中，3用实心点表示"
  },
  {
    "concept": "空心点",
    "explanation": "表示该点不包含在解集中",
    "example": "x>3中，3用空心点表示"
  },
  {
    "concept": "射线",
    "explanation": "表示向某个方向无限延伸",
    "example": "x>3用向右的射线表示"
  }
]',
'[
  "端点实虚标记错误",
  "射线方向画反",
  "忽略边界点的包含关系",
  "数轴标度不准确"
]',
'[
  "符号对应：>、<用空心，≥、≤用实心",
  "方向记忆：>、≥向右，<、≤向左",
  "标度准确：数轴上准确标出关键点位置",
  "检查验证：用特殊值验证数轴表示的正确性"
]',
'{
  "emphasis": ["数形之美", "直观表达"],
  "application": ["视觉艺术", "图形设计"],
  "connection": ["数形美学", "视觉艺术"],
  "cultural_heritage": ["数形相映", "直观智慧"]
}',
'{
  "emphasis": ["几何直观", "可视化方法"],
  "application": ["数据可视化", "图形分析"],
  "connection": ["几何学", "可视化科学"],
  "methodology": ["可视化方法", "几何技术"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G7S2_CH11_007: 一元一次不等式的应用
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S2_CH11_007'),
'一元一次不等式的应用是运用不等式解决实际问题的过程',
'一元一次不等式的应用体现了数学与现实生活的密切联系，是数学建模思想的重要体现。通过将实际问题转化为数学不等式，培养学生的抽象思维、建模能力和问题解决能力。这类应用涉及经济、工程、管理、生活等多个领域，如成本控制、资源配置、质量管理等，体现了数学的实用价值和社会功能。中华数学文化中"学以致用"的思想完美体现了数学理论与实践应用的有机结合。',
'[
  "建模过程：将实际问题转化为数学模型",
  "关键词识别：理解题目中的不等关系",
  "设未知数：选择合适的未知数",
  "列不等式：根据题意建立不等式",
  "求解验证：解不等式并检验实际意义",
  "应用广泛：经济、工程、生活等多领域应用"
]',
'[
  {
    "name": "应用题解题步骤",
    "formula": "①审题；②设未知数；③列不等式；④解不等式；⑤检验；⑥答题",
    "description": "一元一次不等式应用题的标准解题流程"
  },
  {
    "name": "关键词汇",
    "formula": "至少：≥；至多：≤；超过：>；不足：<；不超过：≤；不少于：≥",
    "description": "常见语言与数学符号的对应关系"
  },
  {
    "name": "检验原则",
    "formula": "解必须符合实际问题的条件限制",
    "description": "应用题解答的实际性检验"
  }
]',
'[
  {
    "title": "成本控制问题",
    "problem": "某工厂生产一种产品，固定成本为5000元，每件产品的变动成本为30元，售价为50元。至少生产多少件产品才能盈利？",
    "solution": "设生产x件产品。盈利条件：收入>成本，即50x>5000+30x。解得：20x>5000，x>250。答：至少生产251件产品才能盈利",
    "analysis": "盈利问题的关键是理解收入与成本的关系，建立正确的不等式"
  },
  {
    "title": "资源配置问题",
    "problem": "小明计划用不超过100元买笔记本和钢笔，笔记本每本8元，钢笔每支12元。如果买了5本笔记本，最多还能买几支钢笔？",
    "solution": "设买x支钢笔。根据题意：8×5+12x≤100，即40+12x≤100。解得：12x≤60，x≤5。答：最多还能买5支钢笔",
    "analysis": "资源限制问题要准确理解约束条件，正确建立不等式"
  }
]',
'[
  {
    "concept": "建模",
    "explanation": "将实际问题转化为数学问题",
    "example": "将盈利条件转化为收入大于成本"
  },
  {
    "concept": "关键词",
    "explanation": "题目中表示不等关系的词语",
    "example": "至少表示≥，至多表示≤"
  },
  {
    "concept": "实际检验",
    "explanation": "检查数学解是否符合实际情况",
    "example": "件数必须是正整数"
  }
]',
'[
  "关键词理解错误",
  "建立不等式时方向错误",
  "忽视实际问题的限制条件",
  "解集与实际答案混淆"
]',
'[
  "仔细审题：准确理解题目中的不等关系",
  "关键词敏感：熟练掌握常见关键词的含义",
  "建模准确：正确将实际关系转化为数学不等式",
  "实际检验：确保解符合实际问题的要求"
]',
'{
  "emphasis": ["实用智慧", "生活艺术"],
  "application": ["问题解决", "生活应用"],
  "connection": ["应用美学", "实用艺术"],
  "cultural_heritage": ["学以致用", "实践智慧"]
}',
'{
  "emphasis": ["数学建模", "问题求解"],
  "application": ["建模科学", "决策分析"],
  "connection": ["应用数学", "建模科学"],
  "methodology": ["建模方法", "应用技术"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'),

-- ============================================
-- 11.3 一元一次不等式组部分
-- ============================================

-- MATH_G7S2_CH11_008: 一元一次不等式组的概念
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S2_CH11_008'),
'一元一次不等式组是由几个含有同一个未知数的一元一次不等式组成的不等式集合',
'一元一次不等式组概念是不等式理论的重要扩展，体现了从简单到复杂、从单一到综合的数学发展过程。不等式组将多个约束条件统一在一个数学模型中，更贴近实际问题的复杂性，培养学生的系统思维和综合分析能力。不等式组在线性规划、资源配置、多目标优化等领域有重要应用，是现代运筹学和管理科学的基础工具。中华数学文化中"统筹兼顾"的思想完美体现了协调多个条件、寻求最优解的智慧。',
'[
  "组合概念：多个不等式的统一考虑",
  "同一未知数：所有不等式含有相同的未知数",
  "公共解集：满足所有不等式的解的集合",
  "条件约束：多重限制条件的数学表达",
  "系统思维：培养综合分析和统筹思维",
  "实际建模：复杂问题的数学建模基础"
]',
'[
  {
    "name": "不等式组定义",
    "formula": "由几个含有同一个未知数的一元一次不等式组成",
    "description": "一元一次不等式组的基本定义"
  },
  {
    "name": "表示方法",
    "formula": "用大括号表示：{不等式1, 不等式2, ...}",
    "description": "不等式组的标准书写形式"
  },
  {
    "name": "解集定义",
    "formula": "同时满足组中所有不等式的未知数取值范围",
    "description": "不等式组解集的概念"
  },
  {
    "name": "解集关系",
    "formula": "不等式组的解集是各个不等式解集的交集",
    "description": "解集之间的关系"
  }
]',
'[
  {
    "title": "不等式组的判断",
    "problem": "下列哪些是一元一次不等式组？①{x+1>0, 2x-3<5}；②{x²>0, x-1<0}；③{x>0, y<1}",
    "solution": "①{x+1>0, 2x-3<5}是一元一次不等式组（同一未知数x，都是一次）；②{x²>0, x-1<0}不是（含二次）；③{x>0, y<1}不是（含两个不同未知数）",
    "analysis": "判断不等式组需要确认是否含有同一个未知数且都是一次不等式"
  }
]',
'[
  {
    "concept": "不等式组",
    "explanation": "多个不等式的组合",
    "example": "{x>1, x<5}表示x既大于1又小于5"
  },
  {
    "concept": "公共解",
    "explanation": "同时满足所有不等式的解",
    "example": "对于{x>1, x<5}，公共解是1<x<5"
  },
  {
    "concept": "交集",
    "explanation": "各个解集的公共部分",
    "example": "解集的交集就是不等式组的解集"
  }
]',
'[
  "混淆不等式组与方程组",
  "不理解公共解的概念",
  "忽视同一未知数的要求",
  "不会用大括号表示不等式组"
]',
'[
  "概念清晰：准确理解不等式组的定义",
  "符号规范：正确使用大括号表示",
  "条件明确：确认同一未知数和一次的要求",
  "解集理解：准确理解公共解集的含义"
]',
'{
  "emphasis": ["统筹之美", "和谐统一"],
  "application": ["综合协调", "统筹安排"],
  "connection": ["统筹美学", "和谐艺术"],
  "cultural_heritage": ["统筹兼顾", "协调智慧"]
}',
'{
  "emphasis": ["系统思维", "综合分析"],
  "application": ["系统工程", "综合优化"],
  "connection": ["系统科学", "综合方法"],
  "methodology": ["系统方法", "综合技术"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G7S2_CH11_009: 解一元一次不等式组
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S2_CH11_009'),
'解一元一次不等式组是求出同时满足组中所有不等式的未知数取值范围的过程',
'解一元一次不等式组是不等式理论的重要技能，体现了数学中的分解与综合思想。通过分别求解各个不等式，再求交集得到最终解集，培养学生的分析综合能力和逻辑推理能力。这一过程体现了"分而治之"的解题策略，将复杂问题分解为简单子问题，再综合得出结果。解不等式组的技能在线性规划、约束优化、资源分配等领域有重要应用。中华数学文化中"分合有序"的思想完美体现了分析与综合的辩证统一。',
'[
  "分解思想：将不等式组分解为单个不等式",
  "逐个求解：分别解决每个不等式",
  "解集求交：求各解集的公共部分",
  "情况分析：根据解集关系确定最终结果",
  "数轴辅助：利用数轴直观显示解集关系",
  "口诀记忆：大小小大中间找，大大小小解不了"
]',
'[
  {
    "name": "解题步骤",
    "formula": "①分别解各个不等式；②求各解集的交集；③用数轴表示解集",
    "description": "解一元一次不等式组的标准流程"
  },
  {
    "name": "解集关系口诀",
    "formula": "同大取大，同小取小，大小小大中间找，大大小小解不了",
    "description": "判断解集关系的记忆口诀"
  },
  {
    "name": "交集求法",
    "formula": "不等式组的解集是各个不等式解集的交集",
    "description": "解集的数学原理"
  }
]',
'[
  {
    "title": "解不等式组（有解情况）",
    "problem": "解不等式组：{x+3>1, 2x-1<5}",
    "solution": "解第一个：x+3>1，得x>-2；解第二个：2x-1<5，得x<3。两个解集：x>-2和x<3。根据\"大小小大中间找\"，解集为-2<x<3",
    "analysis": "先分别求解，再根据口诀确定交集，这种情况有公共解"
  },
  {
    "title": "解不等式组（无解情况）",
    "problem": "解不等式组：{x+1>3, x-2<-1}",
    "solution": "解第一个：x+1>3，得x>2；解第二个：x-2<-1，得x<1。两个解集：x>2和x<1。根据\"大大小小解不了\"，不等式组无解",
    "analysis": "当第一个解集的下界大于第二个解集的上界时，不等式组无解"
  }
]',
'[
  {
    "concept": "分别求解",
    "explanation": "先解组中的每个不等式",
    "example": "分别解x>-2和x<3"
  },
  {
    "concept": "交集",
    "explanation": "各解集的公共部分",
    "example": "x>-2和x<3的交集是-2<x<3"
  },
  {
    "concept": "解集判断",
    "explanation": "根据解集关系确定最终结果",
    "example": "用口诀快速判断解集关系"
  }
]',
'[
  "忘记求交集，只给出单个解集",
  "口诀记忆错误或应用错误",
  "不会判断无解的情况",
  "数轴表示解集错误"
]',
'[
  "步骤清晰：先分别求解，再求交集",
  "口诀熟练：熟记并正确应用解集关系口诀",
  "数轴辅助：用数轴直观表示和判断解集",
  "情况全面：考虑有解、无解等各种情况"
]',
'{
  "emphasis": ["分合之美", "逻辑和谐"],
  "application": ["逻辑分析", "综合推理"],
  "connection": ["逻辑美学", "推理艺术"],
  "cultural_heritage": ["分合有序", "逻辑智慧"]
}',
'{
  "emphasis": ["逻辑推理", "算法思维"],
  "application": ["逻辑分析", "算法设计"],
  "connection": ["逻辑科学", "算法技术"],
  "methodology": ["逻辑方法", "算法思维"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'),

-- MATH_G7S2_CH11_010: 一元一次不等式组的应用
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G7S2_CH11_010'),
'一元一次不等式组的应用是运用不等式组解决具有多重约束条件的实际问题',
'一元一次不等式组的应用体现了数学建模思想的进一步发展，将复杂的多约束实际问题转化为数学模型，培养学生的综合分析能力和问题解决能力。这类应用更贴近现实生活中的复杂情况，如资源分配、生产计划、投资决策等，体现了数学的实用价值和社会意义。掌握不等式组应用为后续学习线性规划等高级数学内容奠定基础。中华数学文化中"多谋并举"的思想完美体现了统筹考虑多个条件、寻求最优方案的智慧方法。',
'[
  "多重约束：处理具有多个限制条件的问题",
  "建模思维：将复杂实际问题转化为数学模型",
  "综合分析：统筹考虑各种条件和要求",
  "决策支持：为实际决策提供数学依据",
  "优化思想：在约束条件下寻求最优解",
  "实用价值：解决生活中的实际问题"
]',
'[
  {
    "name": "应用题解题步骤",
    "formula": "①审题；②设未知数；③列不等式组；④解不等式组；⑤检验；⑥答题",
    "description": "不等式组应用题的标准解题流程"
  },
  {
    "name": "建模要点",
    "formula": "找出所有约束条件，逐一建立不等式",
    "description": "将实际问题转化为数学模型的关键"
  },
  {
    "name": "检验原则",
    "formula": "解必须同时满足所有实际条件",
    "description": "应用题解答的完整性检验"
  }
]',
'[
  {
    "title": "生产计划问题",
    "problem": "某工厂计划生产甲、乙两种产品，甲产品每件利润8元，乙产品每件利润12元。受原料限制，甲产品最多生产50件，乙产品最多生产30件。要使总利润不少于400元且甲产品数量不少于乙产品数量，求甲产品的生产数量范围。",
    "solution": "设甲产品生产x件，乙产品生产y件。约束条件：①0≤x≤50；②0≤y≤30；③8x+12y≥400；④x≥y。要求甲产品数量，需要考虑y的取值范围来确定x的范围。当y取最小值时，x取最小值；当y取最大值时，x可能取最大值。通过分析得出甲产品生产数量的合理范围。",
    "analysis": "多约束条件的优化问题需要建立不等式组，并进行综合分析"
  },
  {
    "title": "方案选择问题",
    "problem": "学校要购买两种练习本，A型每本2元，B型每本3元。计划购买总数不少于100本，总费用不超过250元，且A型本数不少于B型本数的2倍。求A型练习本的购买数量范围。",
    "solution": "设A型购买a本，B型购买b本。约束条件：{a+b≥100, 2a+3b≤250, a≥2b, a≥0, b≥0}。由a≥2b和a+b≥100得：2b+b≥100，即b≥33.33，所以b≥34。由2a+3b≤250和a≥2b得：2(2b)+3b≤250，即7b≤250，所以b≤35.7，即b≤35。因此34≤b≤35。当b=34时，a≥68且a+34≥100，所以a≥68；当b=35时，a≥70。所以A型练习本购买数量范围是68≤a≤180。",
    "analysis": "方案选择问题需要建立多个不等式组成的约束系统"
  }
]',
'[
  {
    "concept": "多重约束",
    "explanation": "问题中包含多个限制条件",
    "example": "同时考虑成本、数量、比例等多个条件"
  },
  {
    "concept": "建模策略",
    "explanation": "将实际条件转化为数学不等式",
    "example": "将\"不少于\"转化为≥，\"不超过\"转化为≤"
  },
  {
    "concept": "综合分析",
    "explanation": "统筹考虑所有约束条件",
    "example": "求各个约束条件的公共解"
  }
]',
'[
  "遗漏某些约束条件",
  "不等式方向建立错误",
  "忽视实际问题的隐含条件",
  "不会处理多变量约束关系"
]',
'[
  "全面审题：识别所有约束条件",
  "逐一建模：将每个条件转化为不等式",
  "系统求解：建立完整的不等式组",
  "实际检验：确保解符合所有实际要求"
]',
'{
  "emphasis": ["统筹智慧", "优化之美"],
  "application": ["决策艺术", "管理美学"],
  "connection": ["决策美学", "管理艺术"],
  "cultural_heritage": ["多谋并举", "优化智慧"]
}',
'{
  "emphasis": ["优化理论", "决策科学"],
  "application": ["运筹优化", "决策分析"],
  "connection": ["运筹学", "决策科学"],
  "methodology": ["优化方法", "决策技术"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'); 