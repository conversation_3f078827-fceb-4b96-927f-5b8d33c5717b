/* 导航栏组件样式 */
.nav-bar {
  width: 100%;
  position: relative;
  background-color: #ffffff;
  color: #333333;
  box-sizing: border-box;
}

/* 固定定位 */
.nav-bar-fixed {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
}

/* 阴影效果 */
.nav-bar-shadow {
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

/* 透明背景 */
.nav-bar-transparent {
  background-color: transparent !important;
}

/* 状态栏 */
.nav-bar-status-bar {
  width: 100%;
}

/* 导航栏内容区 */
.nav-bar-content {
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 0 16rpx;
}

/* 左侧区域 */
.nav-bar-left {
  display: flex;
  flex-direction: row;
  align-items: center;
}

/* 中间区域 */
.nav-bar-center {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 30rpx;
}

/* 标题文本 */
.nav-bar-title {
  font-size: 34rpx;
  font-weight: 500;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 右侧区域 */
.nav-bar-right {
  display: flex;
  flex-direction: row;
  align-items: center;
}

/* 按钮样式 */
.nav-bar-button {
  height: 72rpx;
  min-width: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10rpx;
}

/* 按钮文本 */
.nav-bar-button-text {
  font-size: 28rpx;
  margin-left: 4rpx;
}

/* 返回按钮图标 */
.nav-bar-back-icon {
  width: 24rpx;
  height: 40rpx;
  border-left: 4rpx solid currentColor;
  border-bottom: 4rpx solid currentColor;
  transform: rotate(45deg);
  box-sizing: border-box;
  position: relative;
  left: 6rpx;
}

/* 首页按钮图标 */
.nav-bar-home-icon {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid currentColor;
  border-radius: 4rpx;
  box-sizing: border-box;
  position: relative;
}

.nav-bar-home-icon::before {
  content: "";
  width: 20rpx;
  height: 16rpx;
  border-top: 4rpx solid currentColor;
  border-left: 4rpx solid currentColor;
  border-right: 4rpx solid currentColor;
  border-radius: 4rpx 4rpx 0 0;
  box-sizing: border-box;
  position: absolute;
  top: -10rpx;
  left: 6rpx;
} 