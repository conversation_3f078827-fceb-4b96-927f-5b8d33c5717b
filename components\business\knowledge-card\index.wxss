/* 知识点卡片组件样式 */
.knowledge-card {
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
  margin-bottom: 24rpx;
  overflow: hidden;
  transition: all 0.3s ease;
}

.knowledge-card.expanded {
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

/* 卡片头部样式 */
.knowledge-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.header-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.knowledge-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-right: 12rpx;
}

.knowledge-grade {
  font-size: 24rpx;
  color: #666;
  background-color: #f0f2f5;
  padding: 4rpx 12rpx;
  border-radius: 4rpx;
}

.header-right {
  display: flex;
  align-items: center;
}

.collect-icon, .expand-icon {
  padding: 10rpx;
}

.collect-icon .icon, .expand-icon .icon {
  font-size: 36rpx;
  color: #999;
  display: inline-block;
  transition: all 0.3s ease;
}

.collect-icon.collected .icon {
  color: #F5A623;
}

.expand-icon .icon-up {
  transform: rotate(180deg);
}

/* 卡片内容样式 */
.knowledge-content {
  padding: 24rpx 30rpx;
}

.description {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  margin-bottom: 20rpx;
}

/* 标签样式 */
.labels {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 20rpx;
}

.label-item {
  background-color: #f0f8ff;
  color: #3E7BFA;
  font-size: 24rpx;
  padding: 4rpx 16rpx;
  border-radius: 24rpx;
  margin-right: 16rpx;
  margin-bottom: 12rpx;
}

/* 例题样式 */
.section-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 16rpx;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: "";
  display: inline-block;
  width: 6rpx;
  height: 28rpx;
  background-color: #3E7BFA;
  margin-right: 12rpx;
  border-radius: 3rpx;
}

.examples {
  margin-bottom: 40rpx;
}

.example-item {
  background-color: #f7f9fc;
  padding: 16rpx 20rpx;
  border-radius: 8rpx;
  margin-bottom: 16rpx;
}

.example-title {
  font-weight: 500;
  font-size: 26rpx;
  color: #333;
  margin-right: 8rpx;
}

.example-content {
  font-size: 26rpx;
  color: #333;
  line-height: 1.6;
}

/* 相关知识点样式 */
.related-list {
  display: flex;
  flex-wrap: wrap;
}

.related-item {
  background-color: #f0f2f5;
  color: #666;
  font-size: 26rpx;
  padding: 8rpx 20rpx;
  border-radius: 24rpx;
  margin-right: 16rpx;
  margin-bottom: 16rpx;
}

.related-item:active {
  opacity: 0.8;
}

/* 卡片底部样式 */
.knowledge-footer {
  display: flex;
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #f5f5f5;
  justify-content: space-around;
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10rpx 0;
}

.action-btn .icon {
  font-size: 40rpx;
  margin-bottom: 6rpx;
}

.action-btn text:last-child {
  font-size: 24rpx;
  color: #666;
}

.detail-btn .icon {
  color: #3E7BFA;
}

.practice-btn .icon {
  color: #42B983;
}

.share-btn .icon {
  color: #F5A623;
}

/* 动画效果 */
.knowledge-card {
  transition: all 0.3s ease;
}

.knowledge-content {
  transition: max-height 0.3s ease;
}

/* 响应式调整 */
@media (max-width: 375px) {
  .knowledge-title {
    font-size: 28rpx;
  }
  
  .knowledge-content-inner {
    font-size: 26rpx;
  }
} 