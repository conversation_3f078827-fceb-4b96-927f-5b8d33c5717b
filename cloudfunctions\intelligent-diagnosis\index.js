// 智能诊断模块云函数入口 - 优化版
const cloud = require('wx-server-sdk');
const DiagnosisEngine = require('./diagnosis-engine');
const ReportGenerator = require('./report-generator');
const AIEnhancedAnalyzer = require('./ai-enhanced-analyzer');
const LearningOutcomePredictor = require('./learning-outcome-predictor');
const RealtimeBehaviorAnalyzer = require('./real-time-behavior-analyzer');
const AdaptivePathOptimizer = require('./adaptive-path-optimizer');
const LearningPathGenerator = require('./learning-path-generator');
const KnowledgeGraphDiagnosisEngine = require('./knowledge-graph-diagnosis-engine');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

// 优化的全局配置
const CONFIG = {
  maxRetries: 3,
  timeout: 30000, // 30秒超时
  cacheEnabled: true,
  cacheTTL: 5 * 60 * 1000, // 5分钟缓存
  enableMetrics: true,
  enableDetailedLogging: true,
  performanceThreshold: 5000, // 5秒性能阈值
  maxCacheSize: 100,
  rateLimitEnabled: true,
  maxRequestsPerMinute: 60
};

// 增强的性能监控
const metrics = {
  requestCount: 0,
  errorCount: 0,
  avgResponseTime: 0,
  totalResponseTime: 0,
  successCount: 0,
  lastResetTime: Date.now(),
  requestsByAction: {},
  errorsByType: {},
  performanceAlerts: []
};

// 请求限制缓存
const rateLimitCache = new Map();

// 结果缓存
const resultCache = new Map();

/**
 * 智能诊断云函数主入口 - 升级版
 * 基于知识图谱的多维度学习诊断系统
 * @param {Object} event - 请求事件对象
 * @param {Object} context - 云函数上下文
 * @returns {Object} 响应结果
 */
exports.main = async (event, context) => {
  const startTime = Date.now();
  const requestId = generateRequestId();
  
  console.log(`[${requestId}] 智能诊断请求开始`, { action: event?.action, timestamp: new Date().toISOString() });
  
  try {
    // 请求限制检查
    if (CONFIG.rateLimitEnabled && !checkRateLimit(context.requestId || 'unknown')) {
      throw new Error('请求频率过高，请稍后再试');
    }
    
    // 输入验证
    const validationResult = validateInput(event);
    if (!validationResult.valid) {
      throw new Error(`输入验证失败: ${validationResult.error}`);
    }

    const { action, data } = event;
    
    // 记录详细请求日志
    logRequest(requestId, action, data, context);
    
    // 检查缓存
    const cacheKey = generateCacheKey(action, data);
    if (CONFIG.cacheEnabled && resultCache.has(cacheKey)) {
      const cachedResult = resultCache.get(cacheKey);
      if (Date.now() - cachedResult.timestamp < CONFIG.cacheTTL) {
        logResponse(requestId, 'cache_hit', Date.now() - startTime);
        return {
          success: true,
          data: cachedResult.data,
          cached: true,
          timestamp: new Date().toISOString(),
          responseTime: Date.now() - startTime,
          requestId
        };
      }
    }
    
    let result;
    
    // 路由处理 - 升级版
    switch (action) {
      case 'knowledge_graph_diagnosis':
        result = await knowledgeGraphDiagnosis(data, requestId);
        break;
      case 'comprehensive_diagnosis':
        result = await comprehensiveDiagnosis(data, requestId);
        break;
      case 'knowledge_mastery_analysis':
        result = await knowledgeMasteryAnalysis(data, requestId);
        break;
      case 'learning_path_recommendation':
        result = await learningPathRecommendation(data, requestId);
        break;
      case 'weakness_identification':
        result = await weaknessIdentification(data, requestId);
        break;
      case 'ai_enhanced_diagnosis':
        result = await aiEnhancedDiagnosis(data, requestId);
        break;
      case 'personalized_recommendations':
        result = await personalizedRecommendations(data, requestId);
        break;
      case 'progress_assessment':
        result = await progressAssessment(data, requestId);
        break;
      case 'cognitive_analysis':
        result = await cognitiveAnalysis(data, requestId);
        break;
      case 'real_time_behavior_analysis':
        result = await realTimeBehaviorAnalysis(data, requestId);
        break;
      case 'healthCheck':
        result = await healthCheck();
        break;
      case 'getMetrics':
        result = getSystemMetrics();
        break;
      case 'clearCache':
        result = clearSystemCache();
        break;
      default:
        throw new Error(`未知的诊断类型: ${action}`);
    }
    
    // 缓存结果
    if (CONFIG.cacheEnabled && action !== 'healthCheck' && action !== 'getMetrics') {
      resultCache.set(cacheKey, {
        data: result,
        timestamp: Date.now()
      });
      
      // 清理过期缓存
      cleanCache();
    }
    
    // 更新性能指标
    updateMetrics(startTime, true, action);
    
    // 记录响应日志
    logResponse(requestId, 'success', Date.now() - startTime);
    
    // 性能告警检查
    checkPerformanceAlerts(Date.now() - startTime);
    
    console.log(`[${requestId}] 智能诊断请求完成`, { 
      action, 
      success: true,
      processingTime: new Date().toISOString()
    });
    
    return {
      success: true,
      data: result,
      timestamp: new Date().toISOString(),
      responseTime: Date.now() - startTime,
      requestId,
      version: '4.0-knowledge-graph'
    };
    
  } catch (error) {
    // 增强的错误处理
    const errorInfo = handleError(error, requestId, event, startTime);
    updateMetrics(startTime, false, event?.action || 'unknown');
    
    // 记录错误到数据库
    await recordError(requestId, event?.action, error, event?.data);
    
    console.error(`[${requestId}] 智能诊断请求失败:`, error);
    
    return {
      success: false,
      error: errorInfo.message,
      errorCode: errorInfo.code,
      errorType: errorInfo.type,
      timestamp: new Date().toISOString(),
      responseTime: Date.now() - startTime,
      requestId,
      version: '4.0-knowledge-graph',
      fallbackData: generateFallbackResponse(event?.action)
    };
  }
};

/**
 * 增强的输入验证函数
 * @param {Object} event - 输入事件
 * @returns {Object} 验证结果
 */
function validateInput(event) {
  if (!event) {
    return { valid: false, error: '缺少请求数据' };
  }
  
  if (!event.action) {
    return { valid: false, error: '缺少action参数' };
  }
  
  // 验证action类型
  const validActions = [
    'knowledge_graph_diagnosis', 'comprehensive_diagnosis', 'knowledge_mastery_analysis',
    'learning_path_recommendation', 'weakness_identification', 'ai_enhanced_diagnosis',
    'personalized_recommendations', 'progress_assessment', 'cognitive_analysis',
    'real_time_behavior_analysis', 'healthCheck', 'getMetrics', 'clearCache'
  ];
  
  if (!validActions.includes(event.action)) {
    return { valid: false, error: `无效的action类型: ${event.action}` };
  }
  
  if (!event.data && !['healthCheck', 'getMetrics', 'clearCache'].includes(event.action)) {
    return { valid: false, error: '缺少data参数' };
  }
  
  // 验证学生数据
  if (event.data && event.data.studentId) {
    if (typeof event.data.studentId !== 'string' || event.data.studentId.length === 0) {
      return { valid: false, error: '学生ID格式无效' };
    }
  }
  
  // 验证年级数据
  if (event.data && event.data.gradeLevel) {
    if (!Number.isInteger(event.data.gradeLevel) || event.data.gradeLevel < 1 || event.data.gradeLevel > 12) {
      return { valid: false, error: '年级数据无效，应为1-12的整数' };
    }
  }
  
  // 验证数据大小
  const dataSize = JSON.stringify(event).length;
  if (dataSize > 1024 * 1024) { // 1MB限制
    return { valid: false, error: '请求数据过大，请减少数据量' };
  }
  
  return { valid: true };
}

/**
 * 超时包装函数 - 优化版
 * @param {Promise} promise - 要执行的Promise
 * @param {number} timeout - 超时时间（毫秒）
 * @returns {Promise} 带超时的Promise
 */
function withTimeout(promise, timeout) {
  let timeoutId;
  
  const timeoutPromise = new Promise((_, reject) => {
    timeoutId = setTimeout(() => reject(new Error('操作超时')), timeout);
  });
  
  return Promise.race([
    promise.then(result => {
      clearTimeout(timeoutId);
      return result;
    }),
    timeoutPromise
  ]);
}

/**
 * 请求限制检查
 * @param {string} clientId - 客户端ID
 * @returns {boolean} 是否允许请求
 */
function checkRateLimit(clientId) {
  const now = Date.now();
  const windowStart = now - 60000; // 1分钟窗口
  
  if (!rateLimitCache.has(clientId)) {
    rateLimitCache.set(clientId, []);
  }
  
  const requests = rateLimitCache.get(clientId);
  
  // 清理过期请求
  const validRequests = requests.filter(time => time > windowStart);
  
  if (validRequests.length >= CONFIG.maxRequestsPerMinute) {
    return false;
  }
  
  validRequests.push(now);
  rateLimitCache.set(clientId, validRequests);
  
  return true;
}

/**
 * 生成缓存键
 * @param {string} action - 操作类型
 * @param {Object} data - 数据
 * @returns {string} 缓存键
 */
function generateCacheKey(action, data) {
  const dataHash = JSON.stringify(data || {});
  return `${action}:${dataHash.length}:${hashCode(dataHash)}`;
}

/**
 * 简单哈希函数
 * @param {string} str - 字符串
 * @returns {number} 哈希值
 */
function hashCode(str) {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // 转换为32位整数
  }
  return hash;
}

/**
 * 清理过期缓存
 */
function cleanCache() {
  if (resultCache.size > CONFIG.maxCacheSize) {
    const entries = Array.from(resultCache.entries());
    const expiredEntries = entries.filter(([key, value]) => 
      Date.now() - value.timestamp > CONFIG.cacheTTL
    );
    
    // 删除过期条目
    expiredEntries.forEach(([key]) => resultCache.delete(key));
    
    // 如果还是太大，删除最老的条目
    if (resultCache.size > CONFIG.maxCacheSize) {
      const sortedEntries = entries.sort((a, b) => a[1].timestamp - b[1].timestamp);
      const toDelete = sortedEntries.slice(0, resultCache.size - CONFIG.maxCacheSize);
      toDelete.forEach(([key]) => resultCache.delete(key));
    }
  }
}

/**
 * 知识图谱驱动的综合诊断 - 核心新功能
 * 基于知识图谱关系的多维度智能诊断
 * @param {Object} data - 诊断数据
 * @param {string} requestId - 请求ID
 * @returns {Object} 知识图谱诊断结果
 */
async function knowledgeGraphDiagnosis(data, requestId) {
  const { studentId, gradeLevel, learningData, testResults, behaviorData, knowledgePoints } = data;
  
  // 参数验证
  validateRequiredParams({ studentId, gradeLevel }, 'knowledgeGraphDiagnosis');
  
  console.log(`[${requestId}] 开始知识图谱驱动诊断`);
  
  try {
    // 初始化知识图谱诊断引擎
    const kgDiagnosisEngine = new KnowledgeGraphDiagnosisEngine();
    await kgDiagnosisEngine.initialize();
    
    // 执行多维度诊断分析
    const diagnosisResult = await kgDiagnosisEngine.comprehensiveAnalysis({
      studentProfile: {
        studentId,
        gradeLevel,
        learningStyle: data.learningStyle || 'mixed'
      },
      learningData: learningData || {},
      assessmentResults: testResults || {},
      behaviorMetrics: behaviorData || {},
      focusKnowledgePoints: knowledgePoints || []
    });
    
    // 生成知识图谱诊断报告
    const reportGenerator = new ReportGenerator();
    const comprehensiveReport = await reportGenerator.generateKnowledgeGraphReport({
      ...diagnosisResult,
      requestId,
      timestamp: new Date().toISOString()
    });
    
    // 保存诊断结果
    await saveDiagnosisResult(studentId, {
      type: 'knowledge_graph_diagnosis',
      result: diagnosisResult,
      report: comprehensiveReport,
      metadata: {
        version: '4.0',
        engine: 'knowledge-graph',
        processingTime: diagnosisResult.processingTime,
        confidenceScore: diagnosisResult.confidenceMetrics?.overall || 0.75
      }
    });
    
    return {
      diagnosisId: generateDiagnosisId(),
      analysisResults: {
        knowledgeGraphAnalysis: diagnosisResult.knowledgeGraphAnalysis,
        masteryAssessment: diagnosisResult.masteryAssessment,
        learningPathOptimization: diagnosisResult.learningPathOptimization,
        weaknessIdentification: diagnosisResult.weaknessIdentification,
        prerequisiteAnalysis: diagnosisResult.prerequisiteAnalysis,
        cognitiveModelingResults: diagnosisResult.cognitiveModelingResults
      },
      recommendations: {
        immediatePractice: diagnosisResult.recommendations.immediatePractice || [],
        learningSequence: diagnosisResult.recommendations.learningSequence || [],
        difficultyAdjustment: diagnosisResult.recommendations.difficultyAdjustment || {},
        remedialActions: diagnosisResult.recommendations.remedialActions || []
      },
      report: comprehensiveReport,
      metadata: {
        analysisVersion: '4.0-kg',
        confidenceMetrics: diagnosisResult.confidenceMetrics,
        processingTime: diagnosisResult.processingTime,
        knowledgeGraphCoverage: diagnosisResult.graphCoverage || 0.8,
        dataQuality: assessDataQuality(data)
      }
    };
    
  } catch (error) {
    console.error(`[${requestId}] 知识图谱诊断失败:`, error);
    throw new Error(`知识图谱诊断处理失败: ${error.message}`);
  }
}

/**
 * 综合智能诊断 - 升级版（保持向后兼容）
 * @param {Object} data - 学生学习数据
 * @param {string} requestId - 请求ID
 * @returns {Object} 综合诊断报告
 */
async function comprehensiveDiagnosis(data, requestId) {
  const { studentId, gradeLevel, learningData, testResults, behaviorData } = data;
  
  // 参数验证
  validateRequiredParams({ studentId, gradeLevel }, 'comprehensiveDiagnosis');
  
  console.log(`[${requestId}] 开始综合智能诊断 (兼容模式)`);
  
  try {
    // 初始化传统诊断引擎
    const diagnosisEngine = new DiagnosisEngine();
    await diagnosisEngine.initialize();
    
    // 执行综合诊断分析（升级版）
    const diagnosisResult = await diagnosisEngine.comprehensiveDiagnosis(
      { studentId, gradeLevel },
      learningData || {}, 
      testResults || {},
      data.learningProgress || {},
      behaviorData || {}
    );
    
    // 生成诊断报告
    const reportGenerator = new ReportGenerator();
    const diagnosisReport = await reportGenerator.generateComprehensiveReport({
      ...diagnosisResult,
      requestId,
      timestamp: new Date().toISOString()
    });
    
    // 保存诊断结果到数据库
    await saveDiagnosisResult(studentId, {
      type: 'comprehensive_diagnosis',
      result: diagnosisResult,
      report: diagnosisReport
    });
    
    return {
      diagnosisId: generateDiagnosisId(),
      report: diagnosisReport,
      metadata: {
        analysisVersion: '3.0-enhanced',
        processingTime: diagnosisResult.executionTime,
        dataQuality: assessDataQuality(data),
        confidenceScore: diagnosisResult.confidence || 0.75
      }
    };
  } catch (error) {
    console.error(`[${requestId}] 综合诊断失败:`, error);
    throw new Error(`综合诊断处理失败: ${error.message}`);
  }
}

/**
 * 知识掌握分析 - 知识图谱增强版
 * @param {Object} data - 分析数据
 * @param {string} requestId - 请求ID
 * @returns {Object} 知识掌握分析结果
 */
async function knowledgeMasteryAnalysis(data, requestId) {
  const { studentId, learningData, testResults, knowledgePoints } = data;
  
  validateRequiredParams({ studentId }, 'knowledgeMasteryAnalysis');
  
  try {
    const kgEngine = new KnowledgeGraphDiagnosisEngine();
    await kgEngine.initialize();
    
    const masteryAnalysis = await kgEngine.analyzeMasteryWithGraph({
      studentId,
      learningData: learningData || {},
      assessmentResults: testResults || {},
      targetKnowledgePoints: knowledgePoints || []
    });
    
    return {
      analysisId: generateAnalysisId(),
      masteryResults: {
        overallMastery: masteryAnalysis.overallMastery,
        knowledgePointMastery: masteryAnalysis.knowledgePointMastery,
        conceptualConnections: masteryAnalysis.conceptualConnections,
        masteryProgression: masteryAnalysis.masteryProgression,
        gapAnalysis: masteryAnalysis.gapAnalysis
      },
      recommendations: {
        strengthening: masteryAnalysis.recommendations.strengthening || [],
        reinforcement: masteryAnalysis.recommendations.reinforcement || [],
        advancement: masteryAnalysis.recommendations.advancement || []
      },
      visualizations: {
        masteryHeatmap: masteryAnalysis.visualizations.masteryHeatmap,
        progressionGraph: masteryAnalysis.visualizations.progressionGraph,
        knowledgeNetwork: masteryAnalysis.visualizations.knowledgeNetwork
      }
    };
  } catch (error) {
    console.error(`[${requestId}] 知识掌握分析失败:`, error);
    throw new Error(`知识掌握分析失败: ${error.message}`);
  }
}

/**
 * 学习路径推荐 - 知识图谱版
 * @param {Object} data - 推荐数据
 * @param {string} requestId - 请求ID
 * @returns {Object} 学习路径推荐结果
 */
async function learningPathRecommendation(data, requestId) {
  const { studentId, currentKnowledge, targetGoals, learningPreferences } = data;
  
  validateRequiredParams({ studentId }, 'learningPathRecommendation');
  
  try {
    const kgEngine = new KnowledgeGraphDiagnosisEngine();
    await kgEngine.initialize();
    
    const pathRecommendation = await kgEngine.generateOptimalLearningPath({
      studentProfile: {
        studentId,
        currentMastery: currentKnowledge || {},
        learningGoals: targetGoals || [],
        preferences: learningPreferences || {}
      }
    });
    
    return {
      recommendationId: generateRecommendationId(),
      learningPaths: {
        optimal: pathRecommendation.optimalPath,
        alternative: pathRecommendation.alternativePaths || [],
        personalized: pathRecommendation.personalizedPath
      },
      pathMetrics: {
        estimatedDuration: pathRecommendation.estimatedDuration,
        difficultyProgression: pathRecommendation.difficultyProgression,
        prerequisitesCoverage: pathRecommendation.prerequisitesCoverage,
        confidenceScore: pathRecommendation.confidenceScore
      },
      adaptiveFeatures: {
        checkpoints: pathRecommendation.checkpoints || [],
        adaptationRules: pathRecommendation.adaptationRules || [],
        feedbackMechanisms: pathRecommendation.feedbackMechanisms || []
      }
    };
  } catch (error) {
    console.error(`[${requestId}] 学习路径推荐失败:`, error);
    throw new Error(`学习路径推荐失败: ${error.message}`);
  }
}

/**
 * 薄弱点识别与强化建议
 * @param {Object} data - 分析数据
 * @param {string} requestId - 请求ID
 * @returns {Object} 薄弱点分析结果
 */
async function weaknessIdentification(data, requestId) {
  const { studentId, performanceData, learningHistory } = data;
  
  validateRequiredParams({ studentId }, 'weaknessIdentification');
  
  try {
    const kgEngine = new KnowledgeGraphDiagnosisEngine();
    await kgEngine.initialize();
    
    const weaknessAnalysis = await kgEngine.identifyWeaknessesWithContext({
      studentId,
      performanceData: performanceData || {},
      learningHistory: learningHistory || []
    });
    
    return {
      analysisId: generateAnalysisId(),
      weaknessProfile: {
        criticalWeaknesses: weaknessAnalysis.critical || [],
        moderateWeaknesses: weaknessAnalysis.moderate || [],
        minorWeaknesses: weaknessAnalysis.minor || [],
        rootCauseAnalysis: weaknessAnalysis.rootCauses || []
      },
      remediationPlan: {
        immediateActions: weaknessAnalysis.remediation.immediate || [],
        shortTermPlan: weaknessAnalysis.remediation.shortTerm || [],
        longTermStrategy: weaknessAnalysis.remediation.longTerm || []
      },
      progressTracking: {
        metrics: weaknessAnalysis.tracking.metrics || [],
        milestones: weaknessAnalysis.tracking.milestones || [],
        assessmentSchedule: weaknessAnalysis.tracking.schedule || []
      }
    };
  } catch (error) {
    console.error(`[${requestId}] 薄弱点识别失败:`, error);
    throw new Error(`薄弱点识别失败: ${error.message}`);
  }
}

/**
 * AI增强诊断分析 - 升级版
 * @param {Object} data - 分析数据
 * @param {string} requestId - 请求ID
 * @returns {Object} AI增强诊断结果
 */
async function aiEnhancedDiagnosis(data, requestId) {
  const { studentId, learningData, testResults, behaviorData } = data;
  
  validateRequiredParams({ studentId }, 'aiEnhancedDiagnosis');
  
  try {
    // 使用知识图谱增强的AI诊断
    const kgEngine = new KnowledgeGraphDiagnosisEngine();
    await kgEngine.initialize();
    
    const aiAnalysisResult = await kgEngine.performAIEnhancedAnalysis({
      studentProfile: { studentId, gradeLevel: data.gradeLevel },
      learningData: learningData || {},
      assessmentResults: testResults || {},
      behaviorMetrics: behaviorData || {}
    });
    
    return {
      analysisId: generateAnalysisId(),
      aiInsights: {
        learningPatterns: aiAnalysisResult.patterns || [],
        cognitiveProfile: aiAnalysisResult.cognitiveProfile || {},
        predictiveModeling: aiAnalysisResult.predictions || {},
        adaptiveRecommendations: aiAnalysisResult.adaptiveRecommendations || []
      },
      confidenceMetrics: {
        overall: aiAnalysisResult.confidence?.overall || 0.75,
        patternRecognition: aiAnalysisResult.confidence?.patterns || 0.7,
        predictions: aiAnalysisResult.confidence?.predictions || 0.6,
        recommendations: aiAnalysisResult.confidence?.recommendations || 0.8
      },
      interpretations: {
        strengths: aiAnalysisResult.interpretations?.strengths || [],
        opportunities: aiAnalysisResult.interpretations?.opportunities || [],
        insights: aiAnalysisResult.interpretations?.insights || []
      }
    };
  } catch (error) {
    console.error(`[${requestId}] AI增强诊断失败:`, error);
    throw new Error(`AI增强诊断失败: ${error.message}`);
  }
}

/**
 * 个性化学习建议
 * @param {Object} data - 学生数据
 * @param {string} requestId - 请求ID
 * @returns {Object} 个性化建议结果
 */
async function personalizedRecommendations(data, requestId) {
  const { studentId, learningProfile, preferences, goals } = data;
  
  validateRequiredParams({ studentId }, 'personalizedRecommendations');
  
  try {
    const kgEngine = new KnowledgeGraphDiagnosisEngine();
    await kgEngine.initialize();
    
    const recommendations = await kgEngine.generatePersonalizedRecommendations({
      studentId,
      profile: learningProfile || {},
      preferences: preferences || {},
      goals: goals || []
    });
    
    return {
      recommendationId: generateRecommendationId(),
      personalizedStrategies: {
        learningMethods: recommendations.methods || [],
        practiceActivities: recommendations.activities || [],
        difficultyAdjustments: recommendations.adjustments || {},
        timeManagement: recommendations.timeManagement || {}
      },
      adaptiveContent: {
        recommendedTopics: recommendations.topics || [],
        practiceProblems: recommendations.problems || [],
        reviewMaterials: recommendations.materials || []
      },
      motivationalElements: {
        achievements: recommendations.achievements || [],
        challenges: recommendations.challenges || [],
        socialFeatures: recommendations.social || []
      }
    };
  } catch (error) {
    console.error(`[${requestId}] 个性化建议生成失败:`, error);
    throw new Error(`个性化建议生成失败: ${error.message}`);
  }
}

/**
 * 学习进度评估
 * @param {Object} data - 进度数据
 * @param {string} requestId - 请求ID
 * @returns {Object} 进度评估结果
 */
async function progressAssessment(data, requestId) {
  const { studentId, timeRange, benchmarks } = data;
  
  validateRequiredParams({ studentId }, 'progressAssessment');
  
  try {
    const kgEngine = new KnowledgeGraphDiagnosisEngine();
    await kgEngine.initialize();
    
    const progressResults = await kgEngine.assessLearningProgress({
      studentId,
      evaluationPeriod: timeRange || 'last_month',
      benchmarks: benchmarks || []
    });
    
    return {
      assessmentId: generateAssessmentId(),
      progressMetrics: {
        overallProgress: progressResults.overall || 0,
        skillProgression: progressResults.skills || {},
        masteryGrowth: progressResults.mastery || {},
        velocityAnalysis: progressResults.velocity || {}
      },
      comparativeAnalysis: {
        peerComparison: progressResults.comparison?.peers || {},
        gradeStandards: progressResults.comparison?.standards || {},
        historicalTrends: progressResults.comparison?.historical || {}
      },
      futurePredictions: {
        progressForecast: progressResults.predictions?.progress || {},
        goalAchievability: progressResults.predictions?.goals || {},
        riskAssessment: progressResults.predictions?.risks || []
      }
    };
  } catch (error) {
    console.error(`[${requestId}] 学习进度评估失败:`, error);
    throw new Error(`学习进度评估失败: ${error.message}`);
  }
}

/**
 * 认知能力分析
 * @param {Object} data - 认知数据
 * @param {string} requestId - 请求ID
 * @returns {Object} 认知分析结果
 */
async function cognitiveAnalysis(data, requestId) {
  const { studentId, cognitiveAssessments, behaviorMetrics } = data;
  
  validateRequiredParams({ studentId }, 'cognitiveAnalysis');
  
  try {
    const kgEngine = new KnowledgeGraphDiagnosisEngine();
    await kgEngine.initialize();
    
    const cognitiveResults = await kgEngine.analyzeCognitiveCapabilities({
      studentId,
      assessments: cognitiveAssessments || {},
      behaviorData: behaviorMetrics || {}
    });
    
    return {
      analysisId: generateAnalysisId(),
      cognitiveProfile: {
        processingSpeed: cognitiveResults.profile?.speed || 0,
        workingMemory: cognitiveResults.profile?.memory || 0,
        logicalReasoning: cognitiveResults.profile?.reasoning || 0,
        spatialAbility: cognitiveResults.profile?.spatial || 0,
        patternRecognition: cognitiveResults.profile?.patterns || 0
      },
      learningStyles: {
        preferredMethods: cognitiveResults.styles?.preferred || [],
        effectiveMethods: cognitiveResults.styles?.effective || [],
        adaptationSuggestions: cognitiveResults.styles?.adaptations || []
      },
      developmentRecommendations: {
        strengthening: cognitiveResults.development?.strengthen || [],
        compensation: cognitiveResults.development?.compensate || [],
        enhancement: cognitiveResults.development?.enhance || []
      }
    };
  } catch (error) {
    console.error(`[${requestId}] 认知能力分析失败:`, error);
    throw new Error(`认知能力分析失败: ${error.message}`);
  }
}

/**
 * 实时学习行为分析 - 新增功能
 * @param {Object} data - 行为数据
 * @param {string} requestId - 请求ID
 * @returns {Object} 行为分析结果
 */
async function realTimeBehaviorAnalysis(data, requestId) {
  const { studentId, sessionData, interactionLogs } = data;
  
  validateRequiredParams({ studentId, sessionData }, 'realTimeBehaviorAnalysis');
  
  try {
    const kgEngine = new KnowledgeGraphDiagnosisEngine();
    await kgEngine.initialize();
    
    const behaviorResults = await kgEngine.analyzeRealTimeBehavior({
      studentId,
      sessionData: sessionData || {},
      interactions: interactionLogs || []
    });
    
    return {
      analysisId: generateAnalysisId(),
      sessionInsights: {
        engagementLevel: behaviorResults.engagement || 0,
        focusPatterns: behaviorResults.focus || [],
        difficultyResponse: behaviorResults.difficulty || {},
        learningVelocity: behaviorResults.velocity || 0
      },
      adaptiveActions: {
        immediateAdjustments: behaviorResults.adaptations?.immediate || [],
        sessionRecommendations: behaviorResults.adaptations?.session || [],
        futurePersonalization: behaviorResults.adaptations?.future || []
      },
      alertsAndInterventions: {
        strugglingIndicators: behaviorResults.alerts?.struggling || [],
        disengagementWarnings: behaviorResults.alerts?.disengagement || [],
        supportRecommendations: behaviorResults.alerts?.support || []
      }
    };
  } catch (error) {
    console.error(`[${requestId}] 实时行为分析失败:`, error);
    throw new Error(`实时行为分析失败: ${error.message}`);
  }
}

/**
 * 健康检查
 * @returns {Object} 系统状态
 */
async function healthCheck() {
  try {
    const diagnosisEngine = new DiagnosisEngine();
    await diagnosisEngine.initialize();
    
    return {
      status: 'healthy',
      version: '3.0.0',
      timestamp: new Date().toISOString(),
      modules: {
        diagnosisEngine: 'active',
        reportGenerator: 'active',
        database: await checkDatabaseConnection()
      },
      performance: {
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        metrics: getSystemMetrics()
      }
    };
  } catch (error) {
    return {
      status: 'unhealthy',
      error: error.message,
      timestamp: new Date().toISOString()
    };
  }
}

/**
 * 获取系统指标
 * @returns {Object} 系统性能指标
 */
function getSystemMetrics() {
  const now = Date.now();
  const timeDiff = now - metrics.lastResetTime;
  
  return {
    requestCount: metrics.requestCount,
    errorCount: metrics.errorCount,
    errorRate: metrics.requestCount > 0 ? (metrics.errorCount / metrics.requestCount) : 0,
    avgResponseTime: metrics.avgResponseTime,
    uptime: timeDiff,
    memoryUsage: process.memoryUsage(),
    timestamp: new Date().toISOString()
  };
}

// ==================== 辅助函数 ====================

/**
 * 保存诊断结果到数据库
 * @param {string} studentId - 学生ID
 * @param {Object} result - 诊断结果
 */
async function saveDiagnosisResult(studentId, result) {
  try {
    await db.collection('diagnosis_results').add({
      data: {
        studentId,
        ...result,
        createdAt: db.serverDate(),
        updatedAt: db.serverDate()
      }
    });
  } catch (error) {
    console.error('保存诊断结果失败:', error);
    // 不抛出错误，避免影响主流程
  }
}

/**
 * 更新性能指标
 * @param {number} startTime - 开始时间
 * @param {boolean} success - 是否成功
 */
function updateMetrics(startTime, success, action) {
  const responseTime = Date.now() - startTime;
  metrics.avgResponseTime = (metrics.avgResponseTime + responseTime) / 2;
  
  if (!success) {
    metrics.errorCount++;
  }
  
  metrics.totalResponseTime += responseTime;
  metrics.requestsByAction[action] = (metrics.requestsByAction[action] || 0) + 1;
}

/**
 * 获取错误代码
 * @param {Error} error - 错误对象
 * @returns {string} 错误代码
 */
function getErrorCode(error) {
  if (error.message.includes('超时')) return 'TIMEOUT';
  if (error.message.includes('验证失败')) return 'VALIDATION_ERROR';
  if (error.message.includes('缺少')) return 'MISSING_PARAMETER';
  if (error.message.includes('数据库')) return 'DATABASE_ERROR';
  return 'INTERNAL_ERROR';
}

/**
 * 检查数据库连接
 * @returns {Promise<string>} 连接状态
 */
async function checkDatabaseConnection() {
  try {
    await db.collection('diagnosis_results').limit(1).get();
    return 'connected';
  } catch (error) {
    return 'disconnected';
  }
}

// ID生成函数
function generateDiagnosisId() { return `DIAG_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`; }
function generateAnalysisId() { return `ANAL_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`; }
function generateRecommendationId() { return `REC_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`; }
function generateAssessmentId() { return `ASSESS_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`; }

// 数据质量评估
function assessDataQuality(data) {
  let score = 1.0;
  if (!data.learningData || Object.keys(data.learningData).length === 0) score -= 0.3;
  if (!data.testResults || Object.keys(data.testResults).length === 0) score -= 0.3;
  if (!data.behaviorData || Object.keys(data.behaviorData).length === 0) score -= 0.2;
  return Math.max(score, 0.2);
}

/**
 * 参数验证
 * @param {Object} params - 参数对象
 * @param {string} functionName - 函数名
 */
function validateRequiredParams(params, functionName) {
  for (const [key, value] of Object.entries(params)) {
    if (!value) {
      throw new Error(`${functionName}: 缺少必要参数 ${key}`);
    }
  }
}

/**
 * 记录请求日志
 * @param {string} requestId - 请求ID
 * @param {string} action - 操作类型
 * @param {Object} data - 请求数据
 * @param {Object} context - 上下文
 */
function logRequest(requestId, action, data, context) {
  if (CONFIG.enableDetailedLogging) {
    console.log(`[${requestId}] 请求开始: ${action}`, {
      studentId: data?.studentId,
      gradeLevel: data?.gradeLevel,
      dataSize: JSON.stringify(data || {}).length,
      timestamp: new Date().toISOString()
    });
  }
}

/**
 * 记录响应日志
 * @param {string} requestId - 请求ID
 * @param {string} status - 状态
 * @param {number} responseTime - 响应时间
 */
function logResponse(requestId, status, responseTime) {
  if (CONFIG.enableDetailedLogging) {
    console.log(`[${requestId}] 请求完成: ${status}`, {
      responseTime: `${responseTime}ms`,
      timestamp: new Date().toISOString()
    });
  }
}

/**
 * 增强的错误处理
 * @param {Error} error - 错误对象
 * @param {string} requestId - 请求ID
 * @param {Object} event - 事件对象
 * @param {number} startTime - 开始时间
 * @returns {Object} 错误信息
 */
function handleError(error, requestId, event, startTime) {
  const errorType = error.name || 'UnknownError';
  
  // 更新错误统计
  metrics.errorsByType[errorType] = (metrics.errorsByType[errorType] || 0) + 1;
  
  console.error(`[${requestId}] 智能诊断模块错误:`, {
    error: error.message,
    errorType,
    stack: error.stack,
    action: event?.action,
    studentId: event?.data?.studentId,
    responseTime: Date.now() - startTime,
    timestamp: new Date().toISOString()
  });
  
  return {
    message: error.message,
    code: getErrorCode(error),
    type: errorType
  };
}

/**
 * 自适应路径优化
 * @param {Object} data - 优化数据
 * @param {string} requestId - 请求ID
 * @returns {Object} 优化结果
 */
async function optimizeAdaptivePath(data, requestId) {
  console.log(`[${requestId}] 开始自适应路径优化`);
  
  try {
    const optimizer = new AdaptivePathOptimizer();
    await optimizer.initialize();
    
    const optimizationResult = await optimizer.optimizePath(data);
    
    return {
      success: true,
      optimizedPath: optimizationResult.path,
      improvements: optimizationResult.improvements,
      confidence: optimizationResult.confidence,
      estimatedBenefit: optimizationResult.estimatedBenefit
    };
  } catch (error) {
    console.error(`[${requestId}] 自适应路径优化失败:`, error);
    throw error;
  }
}

/**
 * 生成学习路径
 * @param {Object} data - 路径生成数据
 * @param {string} requestId - 请求ID
 * @returns {Object} 学习路径
 */
async function generateLearningPath(data, requestId) {
  console.log(`[${requestId}] 开始生成学习路径`);
  
  try {
    const pathGenerator = new LearningPathGenerator();
    await pathGenerator.initialize();
    
    const pathResult = await pathGenerator.generatePath(data);
    
    return {
      success: true,
      learningPath: pathResult.path,
      milestones: pathResult.milestones,
      estimatedDuration: pathResult.estimatedDuration,
      difficulty: pathResult.difficulty,
      prerequisites: pathResult.prerequisites
    };
  } catch (error) {
    console.error(`[${requestId}] 学习路径生成失败:`, error);
    throw error;
  }
}

/**
 * 清理系统缓存
 * @returns {Object} 清理结果
 */
function clearSystemCache() {
  const cacheSize = resultCache.size;
  const rateLimitSize = rateLimitCache.size;
  
  resultCache.clear();
  rateLimitCache.clear();
  
  console.log('系统缓存已清理', {
    resultCacheSize: cacheSize,
    rateLimitCacheSize: rateLimitSize,
    timestamp: new Date().toISOString()
  });
  
  return {
    success: true,
    clearedCaches: {
      resultCache: cacheSize,
      rateLimitCache: rateLimitSize
    },
    timestamp: new Date().toISOString()
  };
}

/**
 * 性能告警检查
 * @param {number} responseTime - 响应时间
 */
function checkPerformanceAlerts(responseTime) {
  if (responseTime > CONFIG.performanceThreshold) {
    const alert = {
      type: 'PERFORMANCE_WARNING',
      responseTime,
      threshold: CONFIG.performanceThreshold,
      timestamp: new Date().toISOString()
    };
    
    metrics.performanceAlerts.push(alert);
    
    // 保持最近100个告警
    if (metrics.performanceAlerts.length > 100) {
      metrics.performanceAlerts = metrics.performanceAlerts.slice(-100);
    }
    
    console.warn('性能告警:', alert);
  }
}

/**
 * 记录错误信息
 * @param {string} requestId - 请求ID
 * @param {string} action - 操作类型
 * @param {Error} error - 错误对象
 * @param {Object} inputData - 输入数据
 */
async function recordError(requestId, action, error, inputData) {
  try {
    await db.collection('diagnosis_errors').add({
      data: {
        requestId,
        action,
        error: {
          message: error.message,
          stack: error.stack,
          name: error.name
        },
        inputData: JSON.stringify(inputData).substring(0, 1000), // 限制长度
        timestamp: db.serverDate()
      }
    });
  } catch (dbError) {
    console.error('记录错误信息失败:', dbError);
  }
}

/**
 * 生成降级响应
 * @param {string} action - 操作类型
 * @returns {Object} 降级响应数据
 */
function generateFallbackResponse(action) {
  const fallbackData = {
    message: '诊断服务暂时不可用，请稍后重试',
    suggestions: [
      '检查网络连接',
      '稍后重新尝试',
      '联系技术支持'
    ],
    alternativeActions: []
  };
  
  // 根据不同的action提供特定的降级建议
  switch (action) {
    case 'knowledge_graph_diagnosis':
    case 'comprehensive_diagnosis':
      fallbackData.alternativeActions = [
        '使用简化版诊断',
        '查看历史诊断报告',
        '手动评估学习状态'
      ];
      break;
    case 'learning_path_recommendation':
      fallbackData.alternativeActions = [
        '按标准课程顺序学习',
        '咨询老师获取建议',
        '查看同年级推荐内容'
      ];
      break;
    default:
      fallbackData.alternativeActions = [
        '尝试其他功能',
        '查看帮助文档'
      ];
  }
  
  return fallbackData;
} 