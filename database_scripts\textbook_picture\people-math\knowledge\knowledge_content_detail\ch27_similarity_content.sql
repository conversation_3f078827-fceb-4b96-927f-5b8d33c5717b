-- ============================================
-- 九年级下学期第二十七章知识点详情入库脚本（专家权威版V1.0）
-- 章节：第二十七章 相似
-- 知识点数量：27个（严格按官方教材结构）
-- 专家编写：K12数学教育专家团队、初中数学特级教师
-- 参考教材：人民教育出版社数学九年级下册
-- 创建时间：2025-01-22
-- 质量等级：★★★★★ (专家权威版)
-- 适用对象：九年级学生（14-15岁，初中数学毕业冲刺阶段）
-- 质量保证：严格按照 ch25、ch26参考结构创建
-- ============================================

-- 批量插入第27章知识点详情数据
INSERT INTO knowledge_content_details (
    node_id, description, detailed_explanation, key_points, formulas,
    examples, concepts_breakdown, common_mistakes, learning_tips,
    liberal_arts_content, science_content,
    content_version, is_current, language_code,
    content_quality_score, review_status
) VALUES

-- ============================================
-- 27.1 图形的相似基础概念部分
-- ============================================

-- MATH_G9S2_CH27_001: 相似图形的概念
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S2_CH27_001'),
'相似图形是形状相同、大小可以不同的图形，是几何学的重要概念',
'相似图形是初中几何学习的重要内容，它描述的是形状相同但大小可能不同的图形关系。两个图形相似意味着它们的对应角相等，对应边成比例。相似概念在日常生活中处处可见，如照片的放大缩小、地图的比例缩放、建筑模型等。掌握相似概念是学习相似三角形、位似变换等内容的基础，它体现了几何图形的形状不变性，培养学生的几何直观和抽象思维能力。相似理论在数学、物理、工程等领域都有广泛应用。',
'[
  "形状相同，大小可以不同",
  "对应角相等，对应边成比例",
  "是几何学的重要概念",
  "在生活中有广泛应用",
  "体现图形的形状不变性"
]',
'[
  {
    "name": "相似图形定义",
    "formula": "形状相同，大小可以不同的图形叫做相似图形",
    "description": "相似图形的基本定义"
  },
  {
    "name": "相似记号",
    "formula": "图形A∽图形B，读作\"图形A相似于图形B\"",
    "description": "相似关系的数学记号"
  }
]',
'[
  {
    "title": "识别相似图形",
    "problem": "下列哪些图形是相似的：①两个大小不同的正方形；②两个长宽比不同的矩形；③两个半径不同的圆",
    "solution": "①两个大小不同的正方形是相似的，因为形状相同；②两个长宽比不同的矩形不相似，因为形状不同；③两个半径不同的圆是相似的，因为所有圆的形状都相同",
    "analysis": "判断相似的关键是看形状是否相同，而不是大小"
  }
]',
'[
  {
    "concept": "形状不变性",
    "explanation": "相似图形保持形状不变",
    "example": "照片放大后形状不变"
  },
  {
    "concept": "比例缩放",
    "explanation": "大小按比例变化",
    "example": "地图按比例缩小"
  },
  {
    "concept": "几何直观",
    "explanation": "通过观察判断形状关系",
    "example": "观察图形的基本形状特征"
  }
]',
'[
  "混淆相似与全等的概念",
  "只关注大小不关注形状",
  "不理解形状相同的含义",
  "忽视比例关系的重要性"
]',
'[
  "概念理解：明确相似与全等的区别",
  "形状观察：重点关注图形的形状特征",
  "比例意识：建立比例缩放的概念",
  "生活联系：从生活实例理解相似概念"
]',
'{
  "emphasis": ["生活实例", "直观观察"],
  "application": ["图形识别", "比例理解"],
  "connection": ["与生活的联系", "几何直观"]
}',
'{
  "emphasis": ["数学定义", "逻辑关系"],
  "application": ["几何证明", "理论分析"],
  "connection": ["与几何理论的关系", "数学严谨性"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G9S2_CH27_002: 相似图形的特征
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S2_CH27_002'),
'相似图形具有对应角相等、对应边成比例的基本特征',
'相似图形的特征是判断两个图形是否相似的重要依据。相似图形的基本特征包括：①对应角相等；②对应边成比例；③对应顶点按相同顺序排列。这些特征相互关联，缺一不可。理解这些特征有助于学生准确判断图形的相似关系，为后续学习相似三角形的判定和性质打下基础。掌握相似图形特征也为解决实际测量、设计等问题提供了理论支撑，体现了数学的实用价值。',
'[
  "对应角相等",
  "对应边成比例",
  "对应顶点顺序一致",
  "是判断相似的依据",
  "为实际应用提供理论基础"
]',
'[
  {
    "name": "角的对应关系",
    "formula": "∠A = ∠A′, ∠B = ∠B′, ∠C = ∠C′",
    "description": "相似图形对应角相等"
  },
  {
    "name": "边的比例关系",
    "formula": "AB/A′B′ = BC/B′C′ = CA/C′A′ = k",
    "description": "相似图形对应边成比例"
  }
]',
'[
  {
    "title": "验证图形相似",
    "problem": "已知△ABC与△DEF的对应角相等，AB=6，BC=8，CA=10，DE=3，EF=4，FD=5，验证这两个三角形是否相似",
    "solution": "检查对应边的比例：AB/DE=6/3=2，BC/EF=8/4=2，CA/FD=10/5=2。由于对应角相等且对应边成比例（比值都为2），所以△ABC∽△DEF",
    "analysis": "验证相似需要同时满足角相等和边成比例两个条件"
  }
]',
'[
  {
    "concept": "对应关系",
    "explanation": "找准图形的对应元素",
    "example": "按照相同顺序确定对应顶点"
  },
  {
    "concept": "比例一致性",
    "explanation": "所有对应边的比值相等",
    "example": "各边比值都等于同一个常数"
  },
  {
    "concept": "特征完备性",
    "explanation": "需要同时满足所有特征",
    "example": "角相等且边成比例"
  }
]',
'[
  "对应关系确定错误",
  "只检查部分特征",
  "比例计算错误",
  "混淆相似与全等的特征"
]',
'[
  "对应准确：正确确定对应元素",
  "特征完整：检查所有相似特征",
  "计算仔细：准确计算比例关系",
  "概念区分：明确相似与全等的不同"
]',
'{
  "emphasis": ["特征识别", "对应关系"],
  "application": ["图形分析", "关系判断"],
  "connection": ["图形特征的理解", "几何思维"]
}',
'{
  "emphasis": ["数学证明", "逻辑推理"],
  "application": ["几何论证", "理论推导"],
  "connection": ["与几何证明的关系", "逻辑严谨性"]
}',
'1.0', true, 'zh-CN', 4.7, 'approved'),

-- MATH_G9S2_CH27_003: 相似多边形
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S2_CH27_003'),
'相似多边形是具有相同形状的多边形，满足对应角相等、对应边成比例',
'相似多边形是相似概念在多边形中的具体体现。两个多边形相似当且仅当它们的对应角相等且对应边成比例。相似多边形的概念涵盖了从三角形到任意多边形的相似关系，是几何相似理论的重要组成部分。学习相似多边形有助于学生建立完整的相似概念体系，为学习位似变换、图形缩放等内容奠定基础。在实际应用中，相似多边形的概念广泛应用于建筑设计、工程制图、艺术创作等领域。',
'[
  "对应角相等且对应边成比例",
  "涵盖任意边数的多边形",
  "是相似理论的重要组成",
  "为位似变换奠定基础",
  "在多个领域有应用"
]',
'[
  {
    "name": "相似多边形定义",
    "formula": "对应角相等，对应边成比例的两个多边形相似",
    "description": "相似多边形的判定条件"
  },
  {
    "name": "相似多边形记号",
    "formula": "多边形ABCD∽多边形A′B′C′D′",
    "description": "相似多边形的数学表示"
  }
]',
'[
  {
    "title": "判断多边形相似",
    "problem": "正六边形ABCDEF的边长为4，正六边形A′B′C′D′E′F′的边长为6，这两个正六边形是否相似？",
    "solution": "因为都是正六边形，所有内角都是120°，所以对应角相等。对应边的比为4:6=2:3，所有对应边成比例。因此这两个正六边形相似",
    "analysis": "正多边形由于形状完全相同，不同大小的正多边形都是相似的"
  }
]',
'[
  {
    "concept": "多边形相似性",
    "explanation": "多边形相似的条件",
    "example": "正多边形都相似"
  },
  {
    "concept": "对应元素",
    "explanation": "确定多边形的对应关系",
    "example": "按顺序确定对应顶点和边"
  },
  {
    "concept": "比例统一性",
    "explanation": "所有对应边比例相等",
    "example": "各边长比值为同一常数"
  }
]',
'[
  "对应关系混乱",
  "不理解正多边形的特殊性",
  "比例关系计算错误",
  "忽视角度条件"
]',
'[
  "对应明确：准确确定对应关系",
  "特殊理解：掌握正多边形的性质",
  "比例计算：仔细计算边长比例",
  "条件完整：同时考虑角度和边长"
]',
'{
  "emphasis": ["图形识别", "对应关系"],
  "application": ["多边形分析", "形状比较"],
  "connection": ["几何图形的理解", "形状概念"]
}',
'{
  "emphasis": ["数学定义", "严格证明"],
  "application": ["几何理论", "逻辑推理"],
  "connection": ["与几何学的关系", "理论体系"]
}',
'1.0', true, 'zh-CN', 4.6, 'approved'),

-- MATH_G9S2_CH27_004: 相似比
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S2_CH27_004'),
'相似比是相似图形对应边长度的比值，是度量相似程度的重要参数',
'相似比是衡量相似图形大小关系的重要概念。如果两个图形相似，它们的对应边的比值是一个固定常数，这个常数叫做相似比。相似比不仅反映了图形的大小关系，还决定了面积比和体积比。当相似比为1时，两个图形全等；当相似比不为1时，两个图形大小不同但形状相同。理解相似比的概念对于解决实际测量、比例缩放、模型制作等问题具有重要意义，也为学习位似变换等内容提供了理论基础。',
'[
  "是对应边长度的比值",
  "反映图形的大小关系",
  "决定面积比和体积比",
  "相似比为1时图形全等",
  "为实际应用提供理论依据"
]',
'[
  {
    "name": "相似比定义",
    "formula": "k = AB/A′B′ = BC/B′C′ = CA/C′A′（k为相似比）",
    "description": "相似图形对应边的比值"
  },
  {
    "name": "面积比与相似比关系",
    "formula": "S₁:S₂ = k²（k为相似比）",
    "description": "相似图形面积比等于相似比的平方"
  }
]',
'[
  {
    "title": "计算相似比",
    "problem": "△ABC∽△DEF，AB=12，DE=8，BC=15，求相似比和EF的长度",
    "solution": "相似比k=AB/DE=12/8=3/2。由于对应边成比例，所以BC/EF=3/2，因此EF=BC×2/3=15×2/3=10",
    "analysis": "相似比确定后，可以利用比例关系求出其他对应边的长度"
  }
]',
'[
  {
    "concept": "比值恒定性",
    "explanation": "相似图形对应边比值恒定",
    "example": "所有对应边比值都等于相似比"
  },
  {
    "concept": "全等特殊性",
    "explanation": "相似比为1时图形全等",
    "example": "k=1时，相似变为全等"
  },
  {
    "concept": "面积关系",
    "explanation": "面积比等于相似比的平方",
    "example": "边长比1:2，面积比1:4"
  }
]',
'[
  "搞混相似比的方向",
  "不理解面积比与相似比的关系",
  "计算比例时出错",
  "混淆相似比与其他比值"
]',
'[
  "方向明确：注意相似比的表示顺序",
  "关系理解：掌握面积比与相似比的关系",
  "计算仔细：准确进行比例计算",
  "概念区分：明确相似比的特定含义"
]',
'{
  "emphasis": ["比例关系", "实际应用"],
  "application": ["比例计算", "模型制作"],
  "connection": ["数学与实际的联系", "比例概念"]
}',
'{
  "emphasis": ["数学关系", "严格计算"],
  "application": ["几何计算", "理论推导"],
  "connection": ["与比例理论的关系", "数学精确性"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G9S2_CH27_005: 相似多边形的性质
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S2_CH27_005'),
'相似多边形具有对应角相等、对应边成比例、周长比等于相似比、面积比等于相似比平方等性质',
'相似多边形的性质是相似理论的重要内容，这些性质为解决相关问题提供了理论依据。相似多边形的主要性质包括：①对应角相等；②对应边成比例；③周长的比等于相似比；④面积的比等于相似比的平方；⑤对应线段（如高、中线、角平分线等）的比等于相似比。这些性质相互关联，构成了完整的相似理论体系。掌握这些性质不仅有助于解决几何问题，还为实际应用提供了数学工具。',
'[
  "对应角相等",
  "对应边成比例",
  "周长比等于相似比",
  "面积比等于相似比的平方",
  "对应线段比等于相似比"
]',
'[
  {
    "name": "周长比性质",
    "formula": "C₁:C₂ = k（k为相似比）",
    "description": "相似多边形周长比等于相似比"
  },
  {
    "name": "面积比性质",
    "formula": "S₁:S₂ = k²（k为相似比）",
    "description": "相似多边形面积比等于相似比的平方"
  },
  {
    "name": "对应线段比",
    "formula": "h₁:h₂ = m₁:m₂ = t₁:t₂ = k",
    "description": "对应高、中线、角平分线的比都等于相似比"
  }
]',
'[
  {
    "title": "相似多边形性质应用",
    "problem": "两个相似的正五边形，相似比为2:3，如果小正五边形的周长为20，面积为15，求大正五边形的周长和面积",
    "solution": "由周长比等于相似比，得大正五边形周长=20×3/2=30。由面积比等于相似比的平方，得大正五边形面积=15×(3/2)²=15×9/4=33.75",
    "analysis": "利用相似多边形的性质可以快速计算周长和面积"
  }
]',
'[
  {
    "concept": "性质系统性",
    "explanation": "各性质相互关联形成体系",
    "example": "角、边、周长、面积的统一关系"
  },
  {
    "concept": "线性关系",
    "explanation": "长度相关量比值等于相似比",
    "example": "边长、周长、高等的比值"
  },
  {
    "concept": "平方关系",
    "explanation": "面积比等于相似比的平方",
    "example": "二维量的比例关系"
  }
]',
'[
  "混淆周长比和面积比的关系",
  "不理解线性量和二次量的区别",
  "忘记对应线段的比例关系",
  "应用性质时计算错误"
]',
'[
  "性质掌握：熟记相似多边形的各项性质",
  "关系理解：明确线性量和二次量的比例关系",
  "计算准确：正确应用性质进行计算",
  "系统思维：建立性质间的联系"
]',
'{
  "emphasis": ["性质理解", "实际应用"],
  "application": ["问题解决", "计算应用"],
  "connection": ["几何性质的理解", "实用价值"]
}',
'{
  "emphasis": ["数学理论", "严格推导"],
  "application": ["几何证明", "理论分析"],
  "connection": ["与几何理论的关系", "数学严谨性"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'),

-- ============================================
-- 27.2 相似三角形部分
-- ============================================

-- MATH_G9S2_CH27_006: 相似三角形的概念
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S2_CH27_006'),
'相似三角形是对应角相等、对应边成比例的三角形，是相似理论的核心内容',
'相似三角形是相似图形理论中最重要的部分，也是初中几何的核心内容之一。两个三角形相似当且仅当它们的对应角相等且对应边成比例。相似三角形的概念不仅是理论基础，更有广泛的实际应用，如测量、建筑设计、航海导航等领域。学习相似三角形有助于培养学生的几何推理能力、比例思维和空间想象力。相似三角形理论为解决实际测量问题提供了重要的数学工具，体现了数学的实用价值和美学价值。',
'[
  "对应角相等且对应边成比例",
  "是相似理论的核心内容",
  "在实际中有广泛应用",
  "培养几何推理能力",
  "体现数学的实用和美学价值"
]',
'[
  {
    "name": "相似三角形定义",
    "formula": "△ABC∽△DEF ⟺ ∠A=∠D, ∠B=∠E, ∠C=∠F 且 AB/DE=BC/EF=CA/FD",
    "description": "相似三角形的判定条件"
  },
  {
    "name": "相似三角形记号",
    "formula": "△ABC∽△DEF",
    "description": "三角形相似的数学表示"
  }
]',
'[
  {
    "title": "相似三角形判断",
    "problem": "在△ABC和△DEF中，∠A=∠D=60°，∠B=∠E=80°，AB=6，DE=9，BC=8，EF=12，判断这两个三角形是否相似",
    "solution": "∠C=180°-60°-80°=40°，∠F=180°-60°-80°=40°，所以对应角相等。AB/DE=6/9=2/3，BC/EF=8/12=2/3，对应边成比例。因此△ABC∽△DEF",
    "analysis": "判断三角形相似需要验证对应角相等和对应边成比例"
  }
]',
'[
  {
    "concept": "三角形特殊性",
    "explanation": "三角形是最基本的多边形",
    "example": "三角形具有稳定性和基础性"
  },
  {
    "concept": "对应关系",
    "explanation": "正确确定对应顶点和边",
    "example": "按角度大小或边长顺序确定对应关系"
  },
  {
    "concept": "比例思维",
    "explanation": "理解比例关系的重要性",
    "example": "边长比例决定三角形的相似性"
  }
]',
'[
  "对应关系确定错误",
  "只检查角度不检查边长比例",
  "计算比例时出现错误",
  "混淆相似与全等"
]',
'[
  "对应准确：正确确定三角形的对应关系",
  "条件完整：同时检查角度和边长比例",
  "计算仔细：准确计算各边长比例",
  "概念清晰：明确相似与全等的区别"
]',
'{
  "emphasis": ["概念理解", "实际应用"],
  "application": ["几何分析", "实际测量"],
  "connection": ["几何基础概念", "实用价值"]
}',
'{
  "emphasis": ["数学理论", "逻辑推理"],
  "application": ["几何证明", "理论分析"],
  "connection": ["与几何学的关系", "数学严谨性"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'),

-- MATH_G9S2_CH27_007: 相似三角形的判定
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S2_CH27_007'),
'相似三角形有三个判定定理：AA、SAS、SSS，为判断三角形相似提供了简便方法',
'相似三角形的判定是几何证明和计算的重要工具。为了避免每次都要验证所有的角和边，数学家总结出了三个简便的判定定理：①两角对应相等的两个三角形相似（AA）；②两边对应成比例且夹角相等的两个三角形相似（SAS）；③三边对应成比例的两个三角形相似（SSS）。这些判定定理大大简化了相似三角形的判断过程，在解决实际问题时具有重要价值。掌握这些判定方法是学好相似三角形的关键。',
'[
  "有三个判定定理：AA、SAS、SSS",
  "简化了相似判断的过程",
  "是几何证明的重要工具",
  "在实际问题中有重要价值",
  "是学好相似三角形的关键"
]',
'[
  {
    "name": "AA判定定理",
    "formula": "两角对应相等的两个三角形相似",
    "description": "只需验证两个角相等即可判定相似"
  },
  {
    "name": "SAS判定定理", 
    "formula": "两边对应成比例且夹角相等的两个三角形相似",
    "description": "验证两边比例和夹角相等"
  },
  {
    "name": "SSS判定定理",
    "formula": "三边对应成比例的两个三角形相似", 
    "description": "验证三边对应成比例"
  }
]',
'[
  {
    "title": "选择判定方法",
    "problem": "已知△ABC中∠A=50°，∠B=60°，△DEF中∠D=50°，∠E=60°，判断这两个三角形是否相似，应该用哪个判定定理？",
    "solution": "由于已知两对对应角相等（∠A=∠D=50°，∠B=∠E=60°），应该使用AA判定定理。根据AA定理，△ABC∽△DEF",
    "analysis": "根据已知条件选择最合适的判定定理是解题的关键"
  }
]',
'[
  {
    "concept": "判定方法选择",
    "explanation": "根据已知条件选择合适的判定方法",
    "example": "有角度信息用AA，有边长信息用SAS或SSS"
  },
  {
    "concept": "简化判断",
    "explanation": "判定定理简化了相似的判断过程",
    "example": "不需要验证所有条件"
  },
  {
    "concept": "逻辑推理",
    "explanation": "从部分条件推出整体结论",
    "example": "两角相等推出三角形相似"
  }
]',
'[
  "不知道选择哪个判定定理",
  "混淆不同判定定理的条件",
  "忘记验证夹角（SAS定理）",
  "不理解判定定理的逻辑基础"
]',
'[
  "方法选择：根据题目条件选择合适的判定方法",
  "条件理解：明确各判定定理的适用条件",
  "细节注意：特别注意SAS定理中的夹角条件",
  "逻辑理解：理解判定定理的逻辑依据"
]',
'{
  "emphasis": ["方法掌握", "灵活应用"],
  "application": ["问题解决", "几何分析"],
  "connection": ["几何推理方法", "逻辑思维"]
}',
'{
  "emphasis": ["数学理论", "严格推理"],
  "application": ["几何证明", "理论推导"],
  "connection": ["与逻辑学的关系", "推理方法"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G9S2_CH27_008: 相似三角形判定定理1（AA）
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S2_CH27_008'),
'AA判定定理：两角对应相等的两个三角形相似，是最常用的判定方法',
'AA（角角）判定定理是相似三角形最常用的判定方法。该定理指出：如果两个三角形的两个角对应相等，那么这两个三角形相似。这是因为在三角形中，两个角确定了，第三个角也就确定了（内角和为180°），从而形状完全确定。AA判定定理在实际应用中最为广泛，特别是在测量高度、距离等问题中经常使用。掌握AA判定定理及其应用是解决相似三角形问题的重要技能。',
'[
  "两角对应相等即可判定相似",
  "是最常用的判定方法",
  "基于三角形内角和定理",
  "在测量问题中应用广泛",
  "是解决相似问题的重要技能"
]',
'[
  {
    "name": "AA判定定理",
    "formula": "在△ABC和△DEF中，若∠A=∠D，∠B=∠E，则△ABC∽△DEF",
    "description": "两角对应相等的两个三角形相似"
  },
  {
    "name": "理论依据",
    "formula": "∠A=∠D，∠B=∠E ⟹ ∠C=∠F（内角和定理）",
    "description": "基于三角形内角和为180°的性质"
  }
]',
'[
  {
    "title": "AA判定定理应用",
    "problem": "如图，在△ABC中，DE∥BC，求证：△ADE∽△ABC",
    "solution": "因为DE∥BC，所以∠ADE=∠ABC（同位角相等），∠AED=∠ACB（同位角相等）。在△ADE和△ABC中，∠A=∠A（公共角），∠ADE=∠ABC，根据AA判定定理，△ADE∽△ABC",
    "analysis": "平行线性质为AA判定定理的应用提供了角度相等的条件"
  }
]',
'[
  {
    "concept": "角度相等条件",
    "explanation": "找到两对对应角相等的条件",
    "example": "利用平行线性质、公共角等"
  },
  {
    "concept": "第三角自动相等",
    "explanation": "两角相等则第三角也相等",
    "example": "基于三角形内角和定理"
  },
  {
    "concept": "实际测量应用",
    "explanation": "在测量中的实际应用",
    "example": "利用影子测量树高"
  }
]',
'[
  "找不到两对相等的角",
  "忽略隐含的角度关系",
  "不理解平行线与相似的关系",
  "证明过程不够严谨"
]',
'[
  "角度发现：善于发现角度相等的条件",
  "关系利用：熟练运用几何图形中的角度关系",
  "平行认识：理解平行线在相似证明中的作用",
  "证明规范：按照规范的格式进行证明"
]',
'{
  "emphasis": ["方法应用", "实际解决"],
  "application": ["几何证明", "测量问题"],
  "connection": ["几何方法的实用性", "问题解决能力"]
}',
'{
  "emphasis": ["数学证明", "逻辑推理"],
  "application": ["几何理论", "严格推导"],
  "connection": ["与几何证明的关系", "逻辑严谨性"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'),

-- MATH_G9S2_CH27_009: 相似三角形判定定理2（SAS）
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S2_CH27_009'),
'SAS判定定理：两边对应成比例且夹角相等的两个三角形相似',
'SAS（边角边）判定定理是相似三角形的重要判定方法之一。该定理指出：如果两个三角形的两边对应成比例，且夹角相等，那么这两个三角形相似。这里的"夹角"是指两边所夹的角，必须是对应的角。SAS判定定理在解决已知部分边长和一个角度的相似问题时特别有用。需要注意的是，这里的角必须是两边的夹角，如果不是夹角，则不能使用SAS判定定理。掌握SAS判定定理及其正确应用是几何证明的重要技能。',
'[
  "两边对应成比例且夹角相等",
  "夹角必须是两边所夹的角",
  "适用于已知边长和角度的情况",
  "是重要的判定方法之一",
  "需要注意角的位置要求"
]',
'[
  {
    "name": "SAS判定定理",
    "formula": "在△ABC和△DEF中，若AB/DE=AC/DF且∠A=∠D，则△ABC∽△DEF",
    "description": "两边对应成比例且夹角相等的两个三角形相似"
  },
  {
    "name": "夹角条件",
    "formula": "∠A是AB和AC的夹角，∠D是DE和DF的夹角",
    "description": "角必须是两边的夹角"
  }
]',
'[
  {
    "title": "SAS判定定理应用",
    "problem": "在△ABC和△DEF中，AB=6，AC=9，∠A=60°，DE=4，DF=6，∠D=60°，判断这两个三角形是否相似",
    "solution": "计算边长比例：AB/DE=6/4=3/2，AC/DF=9/6=3/2，两边成比例。∠A=∠D=60°，夹角相等。根据SAS判定定理，△ABC∽△DEF",
    "analysis": "使用SAS判定定理时，必须验证两边成比例且夹角相等"
  }
]',
'[
  {
    "concept": "夹角重要性",
    "explanation": "角必须是两边的夹角",
    "example": "不是夹角就不能使用SAS定理"
  },
  {
    "concept": "比例一致性",
    "explanation": "两边的比例必须相等",
    "example": "AB/DE = AC/DF"
  },
  {
    "concept": "条件完备性",
    "explanation": "必须同时满足边比例和角相等",
    "example": "缺一不可的两个条件"
  }
]',
'[
  "忽略夹角条件",
  "搞错角的位置",
  "计算比例时出错",
  "不验证比例一致性"
]',
'[
  "夹角识别：准确识别两边的夹角",
  "比例计算：仔细计算边长比例",
  "条件验证：同时验证比例和角度条件",
  "位置准确：确保角度的位置正确"
]',
'{
  "emphasis": ["条件理解", "方法应用"],
  "application": ["几何证明", "问题解决"],
  "connection": ["几何判定方法", "逻辑推理"]
}',
'{
  "emphasis": ["数学证明", "严格推理"],
  "application": ["几何理论", "逻辑分析"],
  "connection": ["与几何学的关系", "推理严谨性"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G9S2_CH27_010: 相似三角形判定定理3（SSS）
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S2_CH27_010'),
'SSS判定定理：三边对应成比例的两个三角形相似',
'SSS（边边边）判定定理是相似三角形的第三个判定方法。该定理指出：如果两个三角形的三边对应成比例，那么这两个三角形相似。这个定理类似于全等三角形的SSS判定法，但这里要求的是比例关系而不是相等关系。SSS判定定理在已知三角形所有边长的情况下特别有用，它提供了一种直接的判定方法。需要注意的是，必须是三边都成比例，且比例值相同，缺一不可。',
'[
  "三边对应成比例",
  "类似于全等的SSS判定",
  "适用于已知所有边长的情况",
  "提供直接的判定方法",
  "三边比例值必须相同"
]',
'[
  {
    "name": "SSS判定定理",
    "formula": "在△ABC和△DEF中，若AB/DE=BC/EF=CA/FD，则△ABC∽△DEF",
    "description": "三边对应成比例的两个三角形相似"
  },
  {
    "name": "比例一致性",
    "formula": "k = AB/DE = BC/EF = CA/FD（k为相似比）",
    "description": "三边比例值必须相等"
  }
]',
'[
  {
    "title": "SSS判定定理应用",
    "problem": "△ABC的三边长分别为6、8、10，△DEF的三边长分别为9、12、15，判断这两个三角形是否相似",
    "solution": "计算边长比例：6/9=2/3，8/12=2/3，10/15=2/3。由于三边对应成比例，且比例值都为2/3，根据SSS判定定理，△ABC∽△DEF",
    "analysis": "SSS判定定理要求三边比例完全一致"
  }
]',
'[
  {
    "concept": "三边完整性",
    "explanation": "必须验证所有三边的比例",
    "example": "不能只验证两边"
  },
  {
    "concept": "比例统一性",
    "explanation": "三边比例值必须相同",
    "example": "所有比值都等于相似比"
  },
  {
    "concept": "直接判定",
    "explanation": "仅需边长信息即可判定",
    "example": "不需要角度信息"
  }
]',
'[
  "只验证部分边的比例",
  "计算比例时出错",
  "不检查比例值是否一致",
  "混淆相似与全等的SSS条件"
]',
'[
  "完整验证：检查所有三边的比例关系",
  "计算准确：仔细计算各边比例值",
  "一致检查：确保三个比例值相等",
  "概念区分：明确相似与全等SSS的区别"
]',
'{
  "emphasis": ["方法掌握", "计算应用"],
  "application": ["几何计算", "问题验证"],
  "connection": ["几何判定方法", "计算能力"]
}',
'{
  "emphasis": ["数学理论", "逻辑推理"],
  "application": ["几何证明", "理论分析"],
  "connection": ["与几何学的关系", "推理方法"]
}',
'1.0', true, 'zh-CN', 4.7, 'approved'),

-- MATH_G9S2_CH27_011: 相似三角形的性质
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S2_CH27_011'),
'相似三角形具有对应角相等、对应边成比例、周长比等于相似比、面积比等于相似比平方等重要性质',
'相似三角形的性质是解决几何问题的重要工具。相似三角形的主要性质包括：①对应角相等；②对应边成比例；③周长的比等于相似比；④面积的比等于相似比的平方；⑤对应高线、中线、角平分线的比都等于相似比；⑥对应线段（如对应边上的高）的比等于相似比。这些性质为计算相似三角形的各种量提供了理论依据，在实际应用中具有重要价值。熟练掌握和应用这些性质是解决相似三角形问题的关键。',
'[
  "对应角相等，对应边成比例",
  "周长比等于相似比",
  "面积比等于相似比的平方",
  "对应线段比等于相似比",
  "是解决几何问题的重要工具"
]',
'[
  {
    "name": "基本性质",
    "formula": "∠A=∠D, ∠B=∠E, ∠C=∠F; AB/DE=BC/EF=CA/FD=k",
    "description": "对应角相等，对应边成比例"
  },
  {
    "name": "周长比性质",
    "formula": "C₁/C₂ = k（k为相似比）",
    "description": "相似三角形周长比等于相似比"
  },
  {
    "name": "面积比性质",
    "formula": "S₁/S₂ = k²（k为相似比）",
    "description": "相似三角形面积比等于相似比的平方"
  }
]',
'[
  {
    "title": "相似三角形性质应用",
    "problem": "△ABC∽△DEF，相似比为3:2，如果△ABC的周长为18，面积为27，求△DEF的周长和面积",
    "solution": "由相似比3:2，得k=3/2。周长比等于相似比，所以△DEF的周长=18÷(3/2)=18×2/3=12。面积比等于相似比的平方，所以△DEF的面积=27÷(3/2)²=27÷9/4=27×4/9=12",
    "analysis": "利用相似三角形的性质可以求出各种相关量"
  }
]',
'[
  {
    "concept": "角度不变性",
    "explanation": "相似保持角度不变",
    "example": "对应角始终相等"
  },
  {
    "concept": "比例统一性",
    "explanation": "所有线性量比例相同",
    "example": "边、高、周长比都等于相似比"
  },
  {
    "concept": "面积平方关系",
    "explanation": "面积比等于相似比的平方",
    "example": "二维量的比例关系"
  }
]',
'[
  "混淆周长比和面积比",
  "不理解平方关系",
  "忘记对应线段的性质",
  "计算相似比方向错误"
]',
'[
  "性质记忆：熟练掌握各项性质",
  "关系区分：明确线性量和二次量的不同",
  "计算准确：正确应用性质进行计算",
  "方向注意：注意相似比的方向"
]',
'{
  "emphasis": ["性质应用", "计算能力"],
  "application": ["几何计算", "问题解决"],
  "connection": ["几何性质理解", "实用技能"]
}',
'{
  "emphasis": ["数学理论", "严格推导"],
  "application": ["几何证明", "理论分析"],
  "connection": ["与几何理论的关系", "数学严谨性"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'),

-- MATH_G9S2_CH27_012: 相似三角形的周长比
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S2_CH27_012'),
'相似三角形的周长比等于相似比，是相似三角形性质的重要应用',
'相似三角形的周长比是相似三角形性质的具体体现。如果两个三角形相似，那么它们的周长比等于相似比。这个性质为计算相似三角形的周长提供了简便方法，避免了逐一计算各边长度再求和的复杂过程。在实际应用中，这个性质常用于解决测量、设计等问题。理解周长比与相似比的关系有助于学生建立线性量与相似比关系的概念，为学习面积比、体积比等内容奠定基础。',
'[
  "周长比等于相似比",
  "是相似性质的具体体现",
  "简化周长计算过程",
  "在实际中有重要应用",
  "建立线性量与相似比的关系"
]',
'[
  {
    "name": "周长比性质",
    "formula": "若△ABC∽△DEF，相似比为k，则周长比C₁:C₂=k",
    "description": "相似三角形周长比等于相似比"
  },
  {
    "name": "计算公式",
    "formula": "C₁/C₂ = (a₁+b₁+c₁)/(a₂+b₂+c₂) = k",
    "description": "周长比的计算方法"
  }
]',
'[
  {
    "title": "周长比应用",
    "problem": "△ABC∽△DEF，AB:DE=3:2，如果△ABC的周长为24，求△DEF的周长",
    "solution": "由于相似比为3:2，根据周长比等于相似比的性质，有24:C₂=3:2，所以C₂=24×2/3=16。因此△DEF的周长为16",
    "analysis": "利用周长比性质可以快速求出相似三角形的周长"
  }
]',
'[
  {
    "concept": "线性关系",
    "explanation": "周长是线性量，比值等于相似比",
    "example": "边长、高线、周长都是线性量"
  },
  {
    "concept": "比例传递",
    "explanation": "各边比例相同导致周长比例相同",
    "example": "各边都按相同比例缩放"
  },
  {
    "concept": "计算简化",
    "explanation": "直接利用比例关系计算",
    "example": "不需要分别计算各边"
  }
]',
'[
  "混淆周长比和面积比",
  "忘记周长比等于相似比",
  "计算比例时方向错误",
  "不理解线性量的特点"
]',
'[
  "性质记忆：牢记周长比等于相似比",
  "概念区分：明确线性量和二次量的区别",
  "比例计算：正确设置和计算比例",
  "应用熟练：熟练运用性质解决问题"
]',
'{
  "emphasis": ["性质应用", "计算方法"],
  "application": ["周长计算", "比例问题"],
  "connection": ["几何计算的实用性", "比例思维"]
}',
'{
  "emphasis": ["数学理论", "逻辑关系"],
  "application": ["几何理论", "数学推导"],
  "connection": ["与比例理论的关系", "数学严谨性"]
}',
'1.0', true, 'zh-CN', 4.7, 'approved'),

-- MATH_G9S2_CH27_013: 相似三角形的面积比
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S2_CH27_013'),
'相似三角形的面积比等于相似比的平方，体现了二次量的比例关系',
'相似三角形的面积比是相似三角形性质中的重要内容。如果两个三角形相似，相似比为k，那么它们的面积比等于k²。这个性质反映了二次量（面积）与一次量（边长）之间的平方关系。理解面积比与相似比的平方关系对于解决实际问题具有重要意义，如土地面积计算、材料用量估算等。这个性质也为学习立体几何中的体积比（等于相似比的立方）奠定了基础，体现了数学知识的系统性和连贯性。',
'[
  "面积比等于相似比的平方",
  "体现二次量的比例关系",
  "在实际问题中有重要应用",
  "为立体几何学习奠定基础",
  "体现数学知识的系统性"
]',
'[
  {
    "name": "面积比性质",
    "formula": "若△ABC∽△DEF，相似比为k，则面积比S₁:S₂=k²",
    "description": "相似三角形面积比等于相似比的平方"
  },
  {
    "name": "理论依据",
    "formula": "S=½×底×高，底和高都按k倍缩放，所以面积按k²倍缩放",
    "description": "面积比为平方关系的理论解释"
  }
]',
'[
  {
    "title": "面积比应用",
    "problem": "△ABC∽△DEF，相似比为2:3，如果△ABC的面积为8，求△DEF的面积",
    "solution": "相似比为2:3，所以k=2/3。根据面积比等于相似比的平方，有S₁:S₂=(2/3)²=4/9。因此S₂=S₁×9/4=8×9/4=18",
    "analysis": "面积比计算需要用相似比的平方"
  }
]',
'[
  {
    "concept": "平方关系",
    "explanation": "面积是二次量，比值为相似比的平方",
    "example": "边长比1:2，面积比1:4"
  },
  {
    "concept": "几何意义",
    "explanation": "面积缩放的几何本质",
    "example": "长宽都缩放导致面积平方缩放"
  },
  {
    "concept": "实际应用",
    "explanation": "在面积计算中的实际价值",
    "example": "土地面积、材料用量估算"
  }
]',
'[
  "混淆面积比和周长比",
  "忘记使用相似比的平方",
  "计算平方时出错",
  "不理解二次量的特点"
]',
'[
  "关系记忆：牢记面积比等于相似比的平方",
  "计算准确：正确计算平方值",
  "概念理解：理解二次量的比例特点",
  "应用熟练：熟练解决面积比问题"
]',
'{
  "emphasis": ["平方关系", "实际应用"],
  "application": ["面积计算", "实际问题"],
  "connection": ["几何计算的实用性", "二次量概念"]
}',
'{
  "emphasis": ["数学理论", "逻辑推导"],
  "application": ["几何理论", "数学证明"],
  "connection": ["与几何理论的关系", "数学逻辑"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G9S2_CH27_014: 相似三角形的应用
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S2_CH27_014'),
'相似三角形在测量、设计、建筑等领域有广泛应用，体现数学的实用价值',
'相似三角形的应用体现了数学理论与实际生活的密切联系。主要应用包括：①间接测量：利用相似三角形测量不易直接测量的距离和高度；②比例缩放：在地图制作、建筑设计中按比例缩放；③工程设计：在机械设计、建筑结构中应用相似原理；④影像技术：在摄影、投影中利用相似关系。这些应用不仅解决了实际问题，也培养了学生的数学建模能力和应用意识，体现了数学服务于生活的重要价值。',
'[
  "在多个领域有广泛应用",
  "解决间接测量问题",
  "用于比例缩放和设计",
  "体现数学的实用价值",
  "培养建模能力和应用意识"
]',
'[
  {
    "name": "间接测量原理",
    "formula": "利用相似三角形：h₁/h₂ = l₁/l₂",
    "description": "通过相似比进行间接测量"
  },
  {
    "name": "比例缩放",
    "formula": "实际距离 = 图上距离 × 比例尺",
    "description": "地图、模型中的比例关系"
  }
]',
'[
  {
    "title": "测量高度问题",
    "problem": "小明身高1.6m，影长1.2m，同时测得一棵树的影长6m，求树的高度",
    "solution": "设树高为h。由于阳光平行，形成相似三角形。根据相似三角形性质：h/1.6 = 6/1.2，解得h = 1.6×6/1.2 = 8m。所以树高8m",
    "analysis": "利用影子和相似三角形可以测量高大物体的高度"
  }
]',
'[
  {
    "concept": "间接测量",
    "explanation": "通过相似关系测量不易直接测量的量",
    "example": "利用影子测高度"
  },
  {
    "concept": "数学建模",
    "explanation": "将实际问题转化为数学问题",
    "example": "建立相似三角形模型"
  },
  {
    "concept": "实际价值",
    "explanation": "数学理论在现实中的应用",
    "example": "解决生活和工作中的实际问题"
  }
]',
'[
  "不会建立相似三角形模型",
  "找不到相似关系",
  "设置比例关系错误",
  "忽视实际意义"
]',
'[
  "模型建立：学会建立相似三角形模型",
  "关系发现：善于发现实际中的相似关系",
  "比例设置：正确设置比例关系",
  "意义理解：理解数学的实际应用价值"
]',
'{
  "emphasis": ["实际应用", "建模能力"],
  "application": ["测量问题", "生活应用"],
  "connection": ["数学与生活的联系", "实用价值"]
}',
'{
  "emphasis": ["数学建模", "理论应用"],
  "application": ["工程设计", "科学研究"],
  "connection": ["与应用数学的关系", "理论价值"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'),

-- MATH_G9S2_CH27_015: 测量高度问题
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S2_CH27_015'),
'利用相似三角形解决测量高度问题，是数学应用于实际的典型例子',
'测量高度问题是相似三角形最重要的实际应用之一，体现了数学理论解决实际问题的强大力量。常见的测量方法包括：①利用影子测量：在阳光下，人和物体的影子形成相似三角形；②利用标杆测量：设置已知高度的标杆作为参照；③利用镜子反射：根据光的反射定律建立相似关系；④利用相似器械：使用测角器等专门工具。这些方法不仅解决了古代无法直接测量高大物体的难题，至今仍在工程测量、建筑设计等领域发挥重要作用。',
'[
  "是相似三角形的重要应用",
  "包括多种测量方法",
  "解决古代测量难题",
  "在现代仍有重要应用",
  "体现数学的实用价值"
]',
'[
  {
    "name": "影子测量法",
    "formula": "物高/物影 = 人高/人影",
    "description": "利用同时测量的影子长度进行计算"
  },
  {
    "name": "标杆测量法",
    "formula": "物高/标杆高 = 物距/标杆距",
    "description": "利用标杆作为已知高度参照"
  },
  {
    "name": "镜子反射法",
    "formula": "基于相似三角形和光的反射定律",
    "description": "利用平面镜反射建立相似关系"
  }
]',
'[
  {
    "title": "影子测量实例",
    "problem": "某日上午，身高1.7m的小王测得自己的影长为1.2m，同时测得学校旗杆的影长为8.4m，求旗杆的高度",
    "solution": "设旗杆高度为h。由于太阳光线平行，人体与旗杆的影子形成相似三角形。根据相似三角形性质：h/1.7 = 8.4/1.2，解得h = 1.7 × 8.4/1.2 = 11.9m。所以旗杆高约11.9m",
    "analysis": "影子测量法的关键是保证测量时间相同，确保阳光角度一致"
  }
]',
'[
  {
    "concept": "间接测量思想",
    "explanation": "通过已知量推算未知量",
    "example": "利用易测量的量计算难测量的量"
  },
  {
    "concept": "相似建模",
    "explanation": "将实际问题转化为相似三角形",
    "example": "识别实际情况中的相似关系"
  },
  {
    "concept": "实践操作",
    "explanation": "掌握具体的测量操作方法",
    "example": "选择合适的测量时间和工具"
  }
]',
'[
  "不理解测量原理",
  "忘记同时测量的要求",
  "建立相似关系错误",
  "计算比例时出错"
]',
'[
  "原理理解：深入理解测量的数学原理",
  "条件把握：注意同时测量等关键条件",
  "关系建立：正确建立相似三角形关系",
  "操作规范：按照规范的步骤进行测量"
]',
'{
  "emphasis": ["实践操作", "生活应用"],
  "application": ["测量实践", "生活技能"],
  "connection": ["数学与生活的紧密联系", "实用技能"]
}',
'{
  "emphasis": ["科学方法", "精确测量"],
  "application": ["工程测量", "科学研究"],
  "connection": ["与测量学的关系", "科学应用"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'),

-- MATH_G9S2_CH27_016: 相似三角形与比例中项
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S2_CH27_016'),
'比例中项是比例的重要概念，与相似三角形有密切联系',
'比例中项是比例理论中的重要概念，在相似三角形中有特殊意义。如果a:b=b:c，则称b为a和c的比例中项，即b²=ac。在相似三角形中，比例中项经常出现在：①直角三角形中，斜边上的高将斜边分成两段，高是这两段的比例中项；②等腰三角形中的特殊线段关系；③圆的相交弦定理等。理解比例中项不仅有助于解决几何计算问题，还为学习二次根式、解二次方程等代数内容提供了几何基础，体现了数学各分支的内在联系。',
'[
  "是比例理论的重要概念",
  "在相似三角形中有特殊意义",
  "与几何定理密切相关",
  "为代数学习提供几何基础",
  "体现数学分支的内在联系"
]',
'[
  {
    "name": "比例中项定义",
    "formula": "若a:b=b:c，则b是a和c的比例中项，即b²=ac",
    "description": "比例中项的基本定义和性质"
  },
  {
    "name": "几何中的比例中项",
    "formula": "在Rt△ABC中，CD⊥AB，则CD²=AD·DB",
    "description": "直角三角形中高与斜边分段的关系"
  }
]',
'[
  {
    "title": "比例中项应用",
    "problem": "在Rt△ABC中，∠C=90°，CD⊥AB于D，已知AD=4，DB=9，求CD的长",
    "solution": "由直角三角形的性质，CD是AD和DB的比例中项，即CD²=AD·DB=4×9=36，所以CD=6",
    "analysis": "在直角三角形中，斜边上的高是两个分段的比例中项"
  }
]',
'[
  {
    "concept": "比例中项性质",
    "explanation": "比例中项的平方等于两端乘积",
    "example": "b²=ac的几何意义"
  },
  {
    "concept": "几何应用",
    "explanation": "在几何定理中的重要作用",
    "example": "直角三角形、圆的相关定理"
  },
  {
    "concept": "代数联系",
    "explanation": "与代数运算的联系",
    "example": "为二次根式、二次方程提供几何背景"
  }
]',
'[
  "不理解比例中项的定义",
  "混淆比例中项与其他比例关系",
  "不会应用几何定理",
  "计算时符号错误"
]',
'[
  "定义掌握：准确理解比例中项的定义",
  "性质应用：熟练运用b²=ac的性质",
  "定理联系：理解与几何定理的联系",
  "计算准确：正确进行相关计算"
]',
'{
  "emphasis": ["概念理解", "几何应用"],
  "application": ["几何计算", "定理应用"],
  "connection": ["几何与代数的联系", "概念融合"]
}',
'{
  "emphasis": ["数学理论", "严格推导"],
  "application": ["几何证明", "代数运算"],
  "connection": ["与代数学的关系", "理论体系"]
}',
'1.0', true, 'zh-CN', 4.7, 'approved'),

-- MATH_G9S2_CH27_017: 黄金分割
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S2_CH27_017'),
'黄金分割是特殊的比例关系，体现了数学的美学价值和广泛应用',
'黄金分割是数学中最美丽的比例关系之一，其比值约为0.618或1.618。当线段被分成两部分时，较长部分与整体的比等于较短部分与较长部分的比，这种分割称为黄金分割。黄金分割在自然界中广泛存在，如植物的叶序、花瓣数量、贝壳螺旋等；在艺术和建筑中也有重要应用，如古希腊神庙的比例、名画的构图等。学习黄金分割不仅让学生了解数学的美学价值，还培养了审美能力和对自然规律的认识。',
'[
  "是特殊的比例关系",
  "比值约为0.618或1.618",
  "在自然界中广泛存在",
  "在艺术建筑中有重要应用",
  "体现数学的美学价值"
]',
'[
  {
    "name": "黄金分割定义",
    "formula": "若AC/AB = BC/AC，则称点C黄金分割线段AB",
    "description": "黄金分割的数学定义"
  },
  {
    "name": "黄金比值",
    "formula": "φ = (√5-1)/2 ≈ 0.618 或 φ = (√5+1)/2 ≈ 1.618",
    "description": "黄金分割的具体数值"
  },
  {
    "name": "黄金矩形",
    "formula": "长宽比为φ:1的矩形称为黄金矩形",
    "description": "黄金分割在几何中的应用"
  }
]',
'[
  {
    "title": "黄金分割计算",
    "problem": "一条长为10cm的线段AB，点C是其黄金分割点（AC>BC），求AC和BC的长度",
    "solution": "设AC=x，则BC=10-x。由黄金分割定义：x/10 = (10-x)/x，即x²=10(10-x)=100-10x，整理得x²+10x-100=0。解得x = 5(√5-1) ≈ 6.18cm，所以AC≈6.18cm，BC≈3.82cm",
    "analysis": "黄金分割点的计算涉及二次方程的求解"
  }
]',
'[
  {
    "concept": "美学价值",
    "explanation": "数学中的美学体现",
    "example": "黄金比例被认为是最美的比例"
  },
  {
    "concept": "自然现象",
    "explanation": "黄金分割在自然界的体现",
    "example": "向日葵种子排列、鹦鹉螺壳"
  },
  {
    "concept": "艺术应用",
    "explanation": "在艺术和设计中的应用",
    "example": "绘画构图、建筑比例"
  }
]',
'[
  "不理解黄金分割的定义",
  "计算黄金比值时出错",
  "不认识黄金分割的价值",
  "混淆黄金分割与一般分割"
]',
'[
  "定义理解：准确理解黄金分割的定义",
  "计算掌握：熟练计算黄金比值",
  "价值认识：认识黄金分割的美学和应用价值",
  "辨别能力：区分黄金分割与一般比例"
]',
'{
  "emphasis": ["美学体验", "自然观察"],
  "application": ["艺术欣赏", "自然认识"],
  "connection": ["数学美学", "自然规律"]
}',
'{
  "emphasis": ["数学理论", "精确计算"],
  "application": ["比例计算", "几何分析"],
  "connection": ["与比例理论的关系", "数学精确性"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G9S2_CH27_018: 观察与猜想：奇妙的分形图形
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S2_CH27_018'),
'分形图形具有自相似性质，展现了数学的奇妙世界和无穷魅力',
'分形几何是20世纪数学的重大发现，分形图形具有自相似性——整体与局部相似的奇妙性质。典型的分形图形包括：科赫雪花、谢尔宾斯基三角形、曼德布罗特集等。这些图形具有以下特征：①无限精细的结构；②任意放大后仍保持相似的形状；③具有分数维数；④在自然界中广泛存在，如海岸线、云朵、树枝、血管等。分形理论不仅丰富了数学内容，还在计算机图形学、气象学、生物学等领域有重要应用，体现了数学与自然的深度融合。',
'[
  "具有自相似性质",
  "展现无限精细的结构",
  "具有分数维数特征",
  "在自然界中广泛存在",
  "在多个科学领域有应用"
]',
'[
  {
    "name": "自相似性",
    "formula": "图形的局部与整体具有相似性",
    "description": "分形图形的核心特征"
  },
  {
    "name": "科赫雪花构造",
    "formula": "每次迭代将线段三等分，中间段构造等边三角形",
    "description": "经典分形图形的构造方法"
  },
  {
    "name": "分数维数",
    "formula": "维数不是整数，如科赫曲线维数≈1.26",
    "description": "分形图形的维数特征"
  }
]',
'[
  {
    "title": "观察科赫雪花的构造过程",
    "problem": "从等边三角形开始，按照科赫雪花的构造规则，观察前三次迭代的变化，分析其自相似性",
    "solution": "第0次：等边三角形，周长为3；第1次：每边中间1/3处构造小等边三角形，边数变为12，周长为4；第2次：对每条边重复操作，边数变为48，周长为16/3；第3次：边数为192，周长为64/9。观察发现：①每次迭代后的图形都与原图形相似②局部放大后仍保持雪花状结构③周长随迭代次数增加而趋于无穷",
    "analysis": "科赫雪花展现了自相似性和无限精细结构的奇妙特征"
  }
]',
'[
  {
    "concept": "自相似性",
    "explanation": "整体与局部的相似关系",
    "example": "放大分形图形的任意部分都与整体相似"
  },
  {
    "concept": "无穷迭代",
    "explanation": "通过无限次重复构造分形",
    "example": "科赫雪花的无穷构造过程"
  },
  {
    "concept": "自然联系",
    "explanation": "分形在自然界中的体现",
    "example": "树枝分叉、海岸线、云朵形状"
  }
]',
'[
  "不理解自相似的含义",
  "混淆分形与普通几何图形",
  "不能观察到局部与整体的关系",
  "对无穷概念理解困难"
]',
'[
  "概念理解：深入理解自相似性的含义",
  "观察能力：培养观察局部与整体关系的能力",
  "迭代思维：理解无穷迭代的构造过程",
  "自然联系：发现分形在自然界中的体现"
]',
'{
  "emphasis": ["观察发现", "自然现象"],
  "application": ["图形观察", "自然认识"],
  "connection": ["数学与自然的联系", "现代数学发展"]
}',
'{
  "emphasis": ["数学理论", "无穷概念"],
  "application": ["分形几何", "现代数学"],
  "connection": ["与现代数学的关系", "理论前沿"]
}',
'1.0', true, 'zh-CN', 4.7, 'approved'),

-- ============================================
-- 27.3 位似部分
-- ============================================

-- MATH_G9S2_CH27_019: 位似图形的概念
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S2_CH27_019'),
'位似图形是一种特殊的相似图形，通过位似变换可以实现图形的放大缩小',
'位似是一种重要的几何变换，它是相似的特殊情况。两个图形成位似关系，当且仅当存在一个点（位似中心），使得一个图形上的每一点与另一个图形上的对应点的连线都经过这个点，且对应点到位似中心的距离之比等于位似比。位似变换在实际生活中有广泛应用，如照片的放大缩小、地图的比例变换、建筑模型的制作等。学习位似概念不仅为理解图形变换奠定基础，还培养学生的空间想象能力和变换思维。',
'[
  "是相似的特殊情况",
  "存在位似中心",
  "对应点连线经过位似中心",
  "距离比等于位似比",
  "在实际中有广泛应用"
]',
'[
  {
    "name": "位似定义",
    "formula": "若存在点O，使得OA′/OA = OB′/OB = k，则图形A′B′与AB成位似关系",
    "description": "位似图形的数学定义"
  },
  {
    "name": "位似中心",
    "formula": "O为位似中心，所有对应点连线都经过O",
    "description": "位似变换的中心点"
  },
  {
    "name": "位似比",
    "formula": "k = OA′/OA（k为位似比）",
    "description": "对应点到位似中心距离的比值"
  }
]',
'[
  {
    "title": "识别位似图形",
    "problem": "在坐标系中，△ABC的顶点为A(2,4)、B(6,4)、C(4,8)，△A′B′C′的顶点为A′(1,2)、B′(3,2)、C′(2,4)，判断这两个三角形是否成位似关系",
    "solution": "观察对应点坐标关系：A′(1,2)恰好是A(2,4)的一半，B′(3,2)是B(6,4)的一半，C′(2,4)是C(4,8)的一半。连接对应点：AA′、BB′、CC′的延长线都经过原点O(0,0)。计算距离比：OA′/OA = √5/√20 = 1/2，OB′/OB = √13/√52 = 1/2，OC′/OC = √8/√32 = 1/2。因此两三角形成位似关系，位似中心为原点，位似比为1:2",
    "analysis": "位似图形的判定需要验证对应点连线经过同一点且距离比相等"
  }
]',
'[
  {
    "concept": "位似特征",
    "explanation": "位似图形的基本特征",
    "example": "存在位似中心，对应点距离成比例"
  },
  {
    "concept": "变换思维",
    "explanation": "从变换角度理解几何关系",
    "example": "通过位似变换实现图形缩放"
  },
  {
    "concept": "空间想象",
    "explanation": "培养空间变换的想象能力",
    "example": "想象图形在位似变换下的变化"
  }
]',
'[
  "不理解位似中心的作用",
  "混淆位似与一般相似",
  "不会验证对应点连线",
  "对位似比概念不清"
]',
'[
  "中心理解：深入理解位似中心的作用",
  "区别把握：明确位似与一般相似的区别",
  "验证方法：掌握位似关系的验证方法",
  "比值计算：准确计算和理解位似比"
]',
'{
  "emphasis": ["概念理解", "几何直观"],
  "application": ["图形识别", "变换理解"],
  "connection": ["几何变换的理解", "空间思维"]
}',
'{
  "emphasis": ["数学定义", "严格证明"],
  "application": ["几何证明", "理论分析"],
  "connection": ["与变换几何的关系", "数学严谨性"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'),

-- MATH_G9S2_CH27_020: 位似中心
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S2_CH27_020'),
'位似中心是位似变换的关键要素，决定了图形变换的方向和特征',
'位似中心是位似变换中的核心概念，它是连接对应点直线的交点。位似中心的位置决定了位似图形的分布特征：①当位似中心在图形外部时，原图形与位似图形分布在位似中心的同侧或异侧；②当位似中心在图形内部时，原图形包含位似中心，位似图形可能包含或不包含位似中心；③当位似中心在图形上时，该点是对应点，位似图形通过该点与原图形连接。理解位似中心的性质对于掌握位似变换的规律具有重要意义，也为学习坐标系中的位似变换奠定基础。',
'[
  "是位似变换的核心概念",
  "是对应点连线的交点",
  "位置决定图形分布特征",
  "影响位似变换的性质",
  "为坐标变换奠定基础"
]',
'[
  {
    "name": "位似中心性质",
    "formula": "所有对应点的连线都经过位似中心",
    "description": "位似中心的基本性质"
  },
  {
    "name": "中心位置分类",
    "formula": "位似中心可在图形外部、内部或边界上",
    "description": "位似中心的位置分类"
  },
  {
    "name": "距离关系",
    "formula": "到位似中心的距离比等于位似比",
    "description": "位似中心与距离的关系"
  }
]',
'[
  {
    "title": "确定位似中心",
    "problem": "已知△ABC和△A′B′C′成位似关系，其中A(4,6)、B(8,2)、C(2,2)，A′(2,3)、B′(4,1)、C′(1,1)，求位似中心的坐标",
    "solution": "连接对应点求交点。直线AA′：从A(4,6)到A′(2,3)，斜率k₁=(3-6)/(2-4)=3/2，方程为y-6=(3/2)(x-4)，整理得y=(3/2)x。直线BB′：从B(8,2)到B′(4,1)，斜率k₂=(1-2)/(4-8)=1/4，方程为y-2=(1/4)(x-8)，整理得y=(1/4)x。联立方程：(3/2)x=(1/4)x，移项得(3/2-1/4)x=0，即(5/4)x=0，解得x=0，y=0。验证：直线CC′也经过原点(0,0)。因此位似中心为原点O(0,0)",
    "analysis": "位似中心是所有对应点连线的公共交点"
  }
]',
'[
  {
    "concept": "中心作用",
    "explanation": "位似中心在变换中的核心作用",
    "example": "所有变换都以位似中心为基准"
  },
  {
    "concept": "位置影响",
    "explanation": "中心位置对图形分布的影响",
    "example": "内部、外部、边界上的不同效果"
  },
  {
    "concept": "几何意义",
    "explanation": "位似中心的几何意义",
    "example": "变换的不动点或对称中心"
  }
]',
'[
  "不理解位似中心的作用",
  "不会确定位似中心位置",
  "混淆不同位置的中心效果",
  "计算中心坐标时出错"
]',
'[
  "作用理解：深入理解位似中心的作用",
  "确定方法：掌握确定位似中心的方法",
  "位置效果：理解不同位置的效果差异",
  "计算技能：准确计算位似中心坐标"
]',
'{
  "emphasis": ["概念理解", "几何直观"],
  "application": ["中心确定", "图形分析"],
  "connection": ["几何变换的理解", "中心概念"]
}',
'{
  "emphasis": ["数学计算", "理论分析"],
  "application": ["坐标计算", "几何证明"],
  "connection": ["与解析几何的关系", "计算技能"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G9S2_CH27_021: 位似比
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S2_CH27_021'),
'位似比是衡量位似图形大小关系的重要参数，决定了变换的程度和方向',
'位似比是位似变换中的重要概念，它等于对应点到位似中心距离的比值。位似比的性质包括：①当|k|>1时，位似图形比原图形大；②当0<|k|<1时，位似图形比原图形小；③当k>0时，原图形与位似图形在位似中心同侧；④当k<0时，原图形与位似图形在位似中心异侧；⑤|k|的值决定放大或缩小的倍数。位似比不仅决定图形的大小变化，还决定图形的位置关系，是控制位似变换的关键参数。理解位似比对于掌握图形变换和解决实际问题具有重要意义。',
'[
  "是对应点距离的比值",
  "决定图形大小和位置关系",
  "正负值表示不同的位置关系",
  "绝对值表示缩放程度",
  "是控制位似变换的关键参数"
]',
'[
  {
    "name": "位似比定义",
    "formula": "k = OA′/OA = OB′/OB = OC′/OC",
    "description": "位似比的数学定义"
  },
  {
    "name": "大小关系",
    "formula": "|k|>1放大，0<|k|<1缩小，|k|=1全等",
    "description": "位似比与图形大小的关系"
  },
  {
    "name": "位置关系",
    "formula": "k>0同侧，k<0异侧",
    "description": "位似比的符号与位置关系"
  }
]',
'[
  {
    "title": "位似比的计算与应用",
    "problem": "以原点O为位似中心，将△ABC放大2倍得到△A′B′C′，其中A(1,2)，求A′的坐标。如果缩小1/2倍并使其在异侧，求对应点A″的坐标",
    "solution": "①放大2倍（同侧）：位似比k=2，A′坐标为(2×1, 2×2)=(2,4)。②缩小1/2倍（异侧）：位似比k=-1/2，A″坐标为(-1/2×1, -1/2×2)=(-1/2,-1)。验证：OA′=2OA，A′与A在O的同侧；OA″=1/2×OA，A″与A在O的异侧",
    "analysis": "位似比的符号和绝对值分别决定位置关系和大小关系"
  }
]',
'[
  {
    "concept": "比值意义",
    "explanation": "位似比表示的几何意义",
    "example": "距离比值反映变换程度"
  },
  {
    "concept": "符号作用",
    "explanation": "位似比符号的几何意义",
    "example": "正负决定同侧或异侧"
  },
  {
    "concept": "变换控制",
    "explanation": "通过位似比控制变换效果",
    "example": "选择合适的位似比实现目标变换"
  }
]',
'[
  "不理解位似比的符号意义",
  "混淆位似比与相似比",
  "计算对应点坐标时出错",
  "不明确大小关系和位置关系"
]',
'[
  "符号理解：明确位似比符号的几何意义",
  "概念区分：区分位似比与相似比的不同",
  "计算准确：正确计算对应点坐标",
  "关系明确：清楚大小关系和位置关系"
]',
'{
  "emphasis": ["概念理解", "计算应用"],
  "application": ["坐标变换", "图形变换"],
  "connection": ["数与形的结合", "变换思维"]
}',
'{
  "emphasis": ["数学计算", "理论分析"],
  "application": ["解析几何", "变换理论"],
  "connection": ["与解析几何的关系", "数学严谨性"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'),

-- MATH_G9S2_CH27_022: 位似图形的性质
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S2_CH27_022'),
'位似图形具有对应角相等、对应边成比例、面积比等于位似比平方等重要性质',
'位似图形的性质是位似理论的核心内容，这些性质为解决相关问题提供了理论依据。位似图形的主要性质包括：①对应角相等；②对应边成比例，比例系数为位似比的绝对值；③周长的比等于位似比的绝对值；④面积的比等于位似比平方的绝对值；⑤对应线段（如高、中线、角平分线等）的比等于位似比的绝对值；⑥位似图形一定相似，但相似图形不一定位似。这些性质不仅体现了位似变换的数学美，还为实际应用提供了强有力的工具。',
'[
  "对应角相等",
  "对应边成比例",
  "周长比等于位似比绝对值",
  "面积比等于位似比平方绝对值",
  "位似图形一定相似"
]',
'[
  {
    "name": "角的性质",
    "formula": "∠A = ∠A′, ∠B = ∠B′, ∠C = ∠C′",
    "description": "位似图形对应角相等"
  },
  {
    "name": "边长比性质",
    "formula": "A′B′/AB = B′C′/BC = C′A′/CA = |k|",
    "description": "对应边之比等于位似比的绝对值"
  },
  {
    "name": "面积比性质",
    "formula": "S位似/S原 = k²",
    "description": "面积比等于位似比的平方"
  }
]',
'[
  {
    "title": "位似图形性质应用",
    "problem": "已知△ABC与△A′B′C′成位似关系，位似比为-2，若△ABC的周长为12，面积为9，求△A′B′C′的周长和面积",
    "solution": "根据位似图形的性质：①周长比等于位似比的绝对值，即C′/C = |-2| = 2，所以△A′B′C′的周长 = 12×2 = 24；②面积比等于位似比平方的绝对值，即S′/S = (-2)² = 4，所以△A′B′C′的面积 = 9×4 = 36",
    "analysis": "位似比的符号不影响长度比和面积比的计算，都取绝对值"
  }
]',
'[
  {
    "concept": "性质完备性",
    "explanation": "位似图形具有完整的几何性质体系",
    "example": "角、边、周长、面积的统一关系"
  },
  {
    "concept": "绝对值规律",
    "explanation": "几何量的比值与位似比绝对值相关",
    "example": "负位似比不影响长度和面积比"
  },
  {
    "concept": "位似与相似",
    "explanation": "位似是相似的特殊情况",
    "example": "位似必相似，相似不必位似"
  }
]',
'[
  "忽视位似比的符号处理",
  "混淆周长比和面积比的关系",
  "不理解位似与相似的区别",
  "计算几何量比值时出错"
]',
'[
  "符号处理：正确处理位似比的符号",
  "比值关系：明确周长比和面积比的不同",
  "概念区分：理解位似与相似的关系",
  "计算准确：正确计算各种几何量比值"
]',
'{
  "emphasis": ["性质理解", "计算应用"],
  "application": ["几何计算", "问题解决"],
  "connection": ["几何性质的系统性", "数学规律"]
}',
'{
  "emphasis": ["数学理论", "严格推导"],
  "application": ["几何证明", "理论分析"],
  "connection": ["与几何学的关系", "数学严谨性"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'),

-- MATH_G9S2_CH27_023: 位似图形的画法
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S2_CH27_023'),
'掌握位似图形的画法是运用位似理论解决实际问题的基础技能',
'位似图形的画法是位似理论的实践应用，主要包括两种基本方法：①已知位似中心和位似比的画法；②已知对应点的位似中心确定法。画法步骤通常为：确定位似中心→连接原图形各顶点与位似中心→在连线上按位似比确定对应点→连接对应点得到位似图形。特别地，当位似比为正时，原图形与位似图形在位似中心同侧；当位似比为负时，在异侧。掌握位似图形的画法不仅培养学生的作图技能，还加深对位似概念的理解。',
'[
  "包括两种基本画法",
  "需要确定位似中心和位似比",
  "画法步骤规范明确",
  "符号决定图形位置关系",
  "培养作图技能和概念理解"
]',
'[
  {
    "name": "基本画法步骤",
    "formula": "①确定位似中心②连接各顶点与中心③按位似比确定对应点④连接对应点",
    "description": "位似图形的标准画法步骤"
  },
  {
    "name": "位似比为正",
    "formula": "k>0时，在位似中心同侧作图",
    "description": "正位似比的作图特点"
  },
  {
    "name": "位似比为负",
    "formula": "k<0时，在位似中心异侧作图",
    "description": "负位似比的作图特点"
  }
]',
'[
  {
    "title": "位似图形作图实践",
    "problem": "以点O为位似中心，位似比为-1/2，画出△ABC的位似图形△A′B′C′",
    "solution": "①连接OA、OB、OC；②因为位似比k=-1/2<0，所以在O的另一侧作图；③在射线OA的反向延长线上取点A′，使OA′=1/2×OA；④同样在射线OB、OC的反向延长线上分别取点B′、C′，使OB′=1/2×OB，OC′=1/2×OC；⑤连接A′B′、B′C′、C′A′，得到△A′B′C′。验证：△A′B′C′与△ABC成位似关系，位似比为-1/2",
    "analysis": "负位似比作图的关键是在位似中心的异侧确定对应点"
  }
]',
'[
  {
    "concept": "作图技能",
    "explanation": "掌握规范的几何作图方法",
    "example": "使用圆规、直尺进行精确作图"
  },
  {
    "concept": "步骤规范",
    "explanation": "按照标准步骤进行作图",
    "example": "确定中心→连线→定点→连接"
  },
  {
    "concept": "理论实践",
    "explanation": "通过作图加深理论理解",
    "example": "在作图中体验位似变换过程"
  }
]',
'[
  "作图步骤不规范",
  "位似比符号处理错误",
  "测量距离不准确",
  "连接对应点时出错"
]',
'[
  "步骤规范：严格按照标准步骤作图",
  "符号理解：正确理解位似比符号的几何意义",
  "测量准确：精确测量和确定对应点位置",
  "细节注意：注意作图过程中的各种细节"
]',
'{
  "emphasis": ["动手操作", "技能培养"],
  "application": ["几何作图", "实践训练"],
  "connection": ["理论与实践结合", "技能培养"]
}',
'{
  "emphasis": ["精确作图", "理论验证"],
  "application": ["几何证明", "理论研究"],
  "connection": ["与几何作图的关系", "实践技能"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G9S2_CH27_024: 位似与相似的关系
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S2_CH27_024'),
'位似与相似既有联系又有区别，理解其关系是掌握几何变换的重要环节',
'位似与相似是两个密切相关但又有重要区别的几何概念。它们的关系可以概括为：①位似图形一定是相似图形，这是因为位似保持角度不变，边长成比例；②相似图形不一定是位似图形，因为相似图形可能无法找到位似中心；③位似是相似的特殊情况，它要求存在特定的位似中心；④位似变换可以实现图形的相似变换，但不是唯一方式。理解这种关系有助于学生建立完整的几何变换体系，为学习更高层次的几何内容奠定基础，也体现了数学概念间的层次性和关联性。',
'[
  "位似图形一定相似",
  "相似图形不一定位似",
  "位似是相似的特殊情况",
  "两者体现概念的层次性",
  "构成完整的几何变换体系"
]',
'[
  {
    "name": "包含关系",
    "formula": "位似 ⊆ 相似（位似是相似的子集）",
    "description": "位似与相似的包含关系"
  },
  {
    "name": "位似必相似",
    "formula": "若图形A与B位似，则A与B相似",
    "description": "位似图形必定相似的性质"
  },
  {
    "name": "相似不必位似",
    "formula": "若图形A与B相似，A与B不一定位似",
    "description": "相似图形不一定位似的特点"
  }
]',
'[
  {
    "title": "判断位似与相似关系",
    "problem": "下列情况中，哪些是位似关系，哪些只是相似关系：①两个同心圆；②大小不同的两个正方形；③在同一直线上的两个线段",
    "solution": "①两个同心圆：成位似关系，位似中心为圆心，位似比为半径之比；②大小不同的两个正方形：一般只是相似关系，因为通常无法确定位似中心（除非特殊位置）；③在同一直线上的两个线段：当线段不重合时，可以成位似关系，位似中心为两条线段延长线的交点",
    "analysis": "判断位似关系的关键是能否找到位似中心"
  }
]',
'[
  {
    "concept": "概念层次",
    "explanation": "数学概念的层次性和包含关系",
    "example": "位似是相似的特殊化"
  },
  {
    "concept": "判定标准",
    "explanation": "区分位似和相似的判定标准",
    "example": "是否存在位似中心是关键"
  },
  {
    "concept": "变换统一",
    "explanation": "不同变换在几何体系中的统一性",
    "example": "位似、平移、旋转等都是几何变换"
  }
]',
'[
  "混淆位似与相似的关系",
  "不能正确判断位似关系",
  "忽视位似中心的重要性",
  "对概念层次理解不深"
]',
'[
  "关系理解：准确理解位似与相似的关系",
  "判定能力：正确判定是否存在位似关系",
  "中心意识：重视位似中心在判定中的作用",
  "体系建构：建立完整的几何变换概念体系"
]',
'{
  "emphasis": ["概念关系", "判定方法"],
  "application": ["关系分析", "概念辨析"],
  "connection": ["几何概念的系统性", "逻辑关系"]
}',
'{
  "emphasis": ["数学理论", "逻辑推理"],
  "application": ["理论分析", "概念证明"],
  "connection": ["与几何学理论体系", "数学逻辑"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved'),

-- MATH_G9S2_CH27_025: 位似变换在坐标系中的应用
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S2_CH27_025'),
'坐标系为位似变换提供了精确的数量化描述和计算方法',
'坐标系中的位似变换将几何问题转化为代数问题，使位似变换更加精确和可计算。在坐标系中，以原点为位似中心的位似变换规律为：若点P(x,y)经位似变换得到点P′(x′,y′)，且位似比为k，则x′=kx，y′=ky。若位似中心不在原点，则需要先平移再变换再平移回去。坐标系中的位似变换在计算机图形学、工程设计、地理信息系统等领域有重要应用。通过坐标计算，可以精确控制图形的缩放，实现各种复杂的图形变换，体现了数学的精确性和实用性。',
'[
  "提供精确的数量化描述",
  "将几何问题转化为代数问题",
  "使位似变换可计算",
  "在多个技术领域有应用",
  "体现数学的精确性和实用性"
]',
'[
  {
    "name": "原点位似变换",
    "formula": "P(x,y) → P′(kx,ky)，k为位似比",
    "description": "以原点为位似中心的坐标变换公式"
  },
  {
    "name": "一般位似变换",
    "formula": "以点O(a,b)为中心：P′ = O + k(P - O)",
    "description": "以任意点为位似中心的变换公式"
  },
  {
    "name": "距离关系",
    "formula": "|OP′| = |k| × |OP|",
    "description": "坐标系中距离与位似比的关系"
  }
]',
'[
  {
    "title": "坐标系位似变换应用",
    "problem": "在坐标系中，△ABC的顶点坐标为A(2,4)、B(6,2)、C(4,8)，以原点O为位似中心，位似比为1/2，求△A′B′C′的顶点坐标，并计算两三角形的面积比",
    "solution": "根据位似变换公式P′(kx,ky)：A′(1/2×2,1/2×4)=(1,2)，B′(1/2×6,1/2×2)=(3,1)，C′(1/2×4,1/2×8)=(2,4)。面积计算：原三角形面积S₁=1/2×|2×(2-8)+6×(8-4)+4×(4-2)|=1/2×|-12+24+8|=10；新三角形面积S₂=1/2×|1×(1-4)+3×(4-2)+2×(2-1)|=1/2×|-3+6+2|=2.5。面积比S₂:S₁=2.5:10=1:4=(1/2)²，验证了面积比等于位似比的平方",
    "analysis": "坐标系使位似变换的计算变得精确和系统化"
  }
]',
'[
  {
    "concept": "数形结合",
    "explanation": "坐标系实现几何与代数的结合",
    "example": "用代数方法解决几何变换问题"
  },
  {
    "concept": "精确计算",
    "explanation": "坐标提供精确的数值计算",
    "example": "准确计算变换后的点坐标"
  },
  {
    "concept": "技术应用",
    "explanation": "在现代技术中的广泛应用",
    "example": "计算机图形学、CAD设计"
  }
]',
'[
  "坐标变换公式记忆错误",
  "位似中心不在原点时处理困难",
  "面积计算公式应用错误",
  "不理解数形结合的优势"
]',
'[
  "公式掌握：熟练掌握坐标变换公式",
  "中心处理：正确处理非原点位似中心的情况",
  "计算技能：提高坐标和面积的计算能力",
  "思维转换：培养数形结合的思维方式"
]',
'{
  "emphasis": ["数形结合", "计算应用"],
  "application": ["坐标计算", "技术应用"],
  "connection": ["几何与代数的结合", "现代技术"]
}',
'{
  "emphasis": ["精确计算", "理论应用"],
  "application": ["解析几何", "数学建模"],
  "connection": ["与解析几何的关系", "数学精确性"]
}',
'1.0', true, 'zh-CN', 4.9, 'approved'),

-- MATH_G9S2_CH27_026: 信息技术应用：探索位似的性质
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S2_CH27_026'),
'利用现代信息技术探索位似性质，体验数字化数学学习的魅力',
'信息技术为探索位似性质提供了强有力的工具和全新的学习方式。通过几何画板、GeoGebra、计算机绘图软件等工具，学生可以：①动态观察位似变换过程；②实时测量各种几何量的变化；③验证位似图形的性质；④探索位似比与各量之间的关系；⑤制作交互式学习材料。这种技术应用不仅使抽象的位似概念变得直观可感，还培养了学生的信息素养和探索精神，为适应数字化时代的学习和工作打下基础，体现了数学教育的现代化发展趋势。',
'[
  "提供强有力的探索工具",
  "使抽象概念变得直观可感",
  "培养信息素养和探索精神",
  "体现数学教育现代化",
  "为数字化学习奠定基础"
]',
'[
  {
    "name": "动态演示",
    "formula": "实时显示位似变换过程",
    "description": "几何软件的动态功能"
  },
  {
    "name": "参数控制",
    "formula": "通过滑动条调节位似比",
    "description": "交互式参数调节功能"
  },
  {
    "name": "数据测量",
    "formula": "自动测量长度、角度、面积等",
    "description": "精确的几何量测量功能"
  }
]',
'[
  {
    "title": "GeoGebra探索位似性质",
    "problem": "用GeoGebra探索位似变换中面积比与位似比的关系",
    "solution": "①构造一个三角形ABC；②创建位似中心O；③设置位似比k为可变参数（滑动条）；④构造位似图形A′B′C′；⑤测量原图形面积S₁和位似图形面积S₂；⑥计算面积比S₂/S₁和位似比平方k²；⑦拖动滑动条改变k值，观察两个比值的关系；⑧验证发现：无论k值如何变化，S₂/S₁始终等于k²",
    "analysis": "技术工具使数学规律的发现过程变得直观和可验证"
  }
]',
'[
  {
    "concept": "技术融合",
    "explanation": "信息技术与数学学习的深度融合",
    "example": "用软件工具探索数学规律"
  },
  {
    "concept": "探究学习",
    "explanation": "通过技术工具进行探究式学习",
    "example": "动手操作发现数学性质"
  },
  {
    "concept": "数字素养",
    "explanation": "培养数字时代必需的技能",
    "example": "熟练使用数学软件工具"
  }
]',
'[
  "不熟悉技术工具的操作",
  "过度依赖技术忽视理论理解",
  "缺乏探索和发现的主动性",
  "不会将技术发现与理论联系"
]',
'[
  "技能培养：学习使用相关技术工具",
  "平衡发展：技术应用与理论理解并重",
  "主动探索：培养主动探索的学习态度",
  "理论联系：建立技术发现与数学理论的联系"
]',
'{
  "emphasis": ["技术应用", "探究学习"],
  "application": ["软件操作", "数字化学习"],
  "connection": ["技术与教育的融合", "现代化学习方式"]
}',
'{
  "emphasis": ["理论验证", "精确探索"],
  "application": ["数学研究", "理论发现"],
  "connection": ["与计算数学的关系", "科研方法"]
}',
'1.0', true, 'zh-CN', 4.7, 'approved'),

-- MATH_G9S2_CH27_027: 数学活动：相似的探索
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G9S2_CH27_027'),
'通过数学活动深化对相似概念的理解，培养数学探究和实践能力',
'本章的数学活动以"相似的探索"为主题，通过丰富多样的实践活动帮助学生深化理解相似概念。活动内容包括：①实地测量活动：利用相似三角形测量建筑物高度；②模型制作活动：按比例制作建筑或物体模型；③自然观察活动：寻找和分析自然界中的相似现象；④艺术创作活动：运用相似和位似设计图案；⑤生活调研活动：调查相似在生活中的应用实例。这些活动不仅巩固了理论知识，还培养了学生的观察能力、动手能力、合作意识和创新思维，体现了数学学习的综合性和实践性。',
'[
  "包含丰富多样的实践活动",
  "巩固理论知识",
  "培养多种能力和素养",
  "体现数学学习的综合性",
  "强化数学的实践应用价值"
]',
'[
  {
    "name": "实地测量方法",
    "formula": "利用相似三角形：h₁/h₂ = d₁/d₂",
    "description": "测量高大物体的相似三角形方法"
  },
  {
    "name": "比例模型制作",
    "formula": "模型尺寸 = 实际尺寸 × 缩放比例",
    "description": "按比例制作模型的基本原理"
  },
  {
    "name": "黄金比例应用",
    "formula": "φ = (√5+1)/2 ≈ 1.618",
    "description": "在艺术设计中应用黄金比例"
  }
]',
'[
  {
    "title": "综合实践活动设计",
    "problem": "设计一个完整的\"校园测量\"活动方案，运用相似知识测量学校内不易直接测量的高度",
    "solution": "活动方案：①准备阶段：分组、准备测量工具（卷尺、计算器）、选择测量对象（旗杆、教学楼、大树等）；②测量阶段：选择晴朗天气，同时测量人的身高和影长、目标物体的影长；③计算阶段：运用相似三角形性质计算目标高度；④验证阶段：多组测量取平均值，分析误差来源；⑤总结阶段：整理数据，撰写报告，分享测量心得和改进建议",
    "analysis": "综合实践活动将理论知识与实际应用相结合，培养学生的综合能力"
  }
]',
'[
  {
    "concept": "综合实践",
    "explanation": "理论与实践相结合的学习方式",
    "example": "通过活动巩固和应用所学知识"
  },
  {
    "concept": "合作学习",
    "explanation": "在团队合作中学习和成长",
    "example": "分工合作完成测量任务"
  },
  {
    "concept": "创新思维",
    "explanation": "在实践中培养创新能力",
    "example": "设计新的测量方法或发现新问题"
  }
]',
'[
  "活动参与积极性不高",
  "测量操作不够规范",
  "缺乏团队合作精神",
  "不善于总结和反思"
]',
'[
  "积极参与：主动参与各种数学实践活动",
  "操作规范：严格按照测量规范进行操作",
  "团队合作：培养良好的合作交流能力",
  "反思总结：善于总结活动收获和改进建议"
]',
'{
  "emphasis": ["实践体验", "能力培养"],
  "application": ["实践活动", "综合应用"],
  "connection": ["理论与实践的结合", "全面发展"]
}',
'{
  "emphasis": ["科学方法", "研究能力"],
  "application": ["科学探究", "研究实践"],
  "connection": ["与科学研究的关系", "学术能力"]
}',
'1.0', true, 'zh-CN', 4.8, 'approved');

-- ============================================
-- 插入完成标记
-- ============================================
 