// 知识图谱数据查询云函数
const cloud = require('wx-server-sdk')

// 初始化云环境
cloud.init({
  env: process.env.CLOUD_ENV || cloud.DYNAMIC_CURRENT_ENV
})

// 获取数据库引用
const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  console.log('数据库查询参数:', event)
  
  // 获取当前用户OpenID
  const wxContext = cloud.getWXContext()
  const openId = wxContext.OPENID
  
  // 从event中提取查询类型和参数
  let { type, grade, id, limit = 100, offset = 0 } = event
  
  // 如果参数在event.data中，也兼容处理
  if (!type && event.data) {
    const data = event.data
    type = data.type
    grade = data.grade
    id = data.id
    limit = data.limit || limit
    offset = data.offset || offset
  }
  
  try {
    // 根据不同的查询类型执行不同的查询操作
    switch (type) {
      case 'knowledge-graph-nodes':
        return await queryKnowledgeGraphNodes(grade)
      case 'knowledge-graph-edges':
        return await queryKnowledgeGraphEdges(grade)
      case 'knowledge-graph-cross-links':
        return await queryKnowledgeGraphCrossLinks(grade)
      case 'knowledge-graph-node-detail':
        return await queryKnowledgeGraphNodeDetail(id)
      case 'knowledge-graph-node-by-name':
        return await queryKnowledgeGraphNodeByName(event.name)
      case 'knowledge-graph-node-relationships':
        return await queryKnowledgeGraphNodeRelationships(event.nodeId)
      case 'knowledge-graph-nodes-batch':
        return await queryKnowledgeGraphNodesBatch(event.nodeIds)
      default:
        return {
          success: false,
          message: `不支持的查询类型: ${type}`,
          supportedTypes: [
            'knowledge-graph-nodes',
            'knowledge-graph-edges',
            'knowledge-graph-cross-links',
            'knowledge-graph-node-detail',
            'knowledge-graph-node-by-name',
            'knowledge-graph-node-relationships',
            'knowledge-graph-nodes-batch'
          ]
        }
    }
  } catch (error) {
    console.error('数据库查询失败:', error)
    return {
      success: false,
      message: '数据库查询失败: ' + error.message,
      error
    }
  }
}

/**
 * 查询知识图谱节点
 * @param {number|string} grade - 年级数字(1-12)或年级名称
 * @returns {Promise<Object>} - 查询结果
 */
async function queryKnowledgeGraphNodes(grade) {
  // 参数验证
  if (grade === undefined || grade === null) {
    return {
      success: false,
      message: '缺少必要参数: grade'
    }
  }
  
  try {
    // 构建查询条件 - 支持level数字查询
    let query;
    
    if (typeof grade === 'number' || !isNaN(parseInt(grade))) {
      // 使用level字段进行数字查询
      const level = parseInt(grade);
      console.log('按level查询知识点:', level);
      query = db.collection('knowledge_graph_nodes')
                .where({ level: level })
                .orderBy('sequence', 'asc');  // 按序列号排序
    } else {
      // 兼容旧版本，使用grade字段进行文本查询
      console.log('按grade查询知识点:', grade);
      query = db.collection('knowledge_graph_nodes')
                .where({ grade: grade })
                .orderBy('sequence', 'asc');  // 按序列号排序
    }
    
    // 执行查询
    const count = await query.count()
    const total = count.total || 0
    
    // 使用分页查询处理大量数据
    const MAX_LIMIT = 100
    const batchTimes = Math.ceil(total / MAX_LIMIT)
    const tasks = []
    
    for (let i = 0; i < batchTimes; i++) {
      const promise = query.skip(i * MAX_LIMIT).limit(MAX_LIMIT).get()
      tasks.push(promise)
    }
    
    // 等待所有查询完成并合并结果
    const results = await Promise.all(tasks)
    const data = []
    
    results.forEach(result => {
      data.push(...result.data)
    })
    
    return {
      success: true,
      data,
      total,
      message: `成功查询 level=${grade} 的 ${data.length} 个知识点节点数据`
    }
  } catch (error) {
    console.error('查询知识图谱节点失败:', error)
    return {
      success: false,
      message: '查询知识图谱节点失败: ' + error.message,
      error
    }
  }
}

/**
 * 查询知识图谱边
 * @param {number|string} grade - 年级数字(1-12)或年级名称
 * @returns {Promise<Object>} - 查询结果
 */
async function queryKnowledgeGraphEdges(grade) {
  // 参数验证
  if (grade === undefined || grade === null) {
    return {
      success: false,
      message: '缺少必要参数: grade'
    }
  }
  
  try {
    // 构建查询条件 - 支持level数字查询
    let query;
    
    if (typeof grade === 'number' || !isNaN(parseInt(grade))) {
      // 使用level字段进行数字查询
      const level = parseInt(grade);
      console.log('按level查询边:', level);
      query = db.collection('knowledge_graph_edges')
                .where({ level: level });
    } else {
      // 兼容旧版本，使用grade字段进行文本查询
      console.log('按grade查询边:', grade);
      query = db.collection('knowledge_graph_edges')
                .where({ grade: grade });
    }
    
    // 执行查询
    const count = await query.count()
    const total = count.total || 0
    
    // 使用分页查询处理大量数据
    const MAX_LIMIT = 100
    const batchTimes = Math.ceil(total / MAX_LIMIT)
    const tasks = []
    
    for (let i = 0; i < batchTimes; i++) {
      const promise = query.skip(i * MAX_LIMIT).limit(MAX_LIMIT).get()
      tasks.push(promise)
    }
    
    // 等待所有查询完成并合并结果
    const results = await Promise.all(tasks)
    const data = []
    
    results.forEach(result => {
      data.push(...result.data)
    })
    
    return {
      success: true,
      data,
      total,
      message: `成功查询 level=${grade} 的 ${data.length} 个连接数据`
    }
  } catch (error) {
    console.error('查询知识图谱边失败:', error)
    return {
      success: false,
      message: '查询知识图谱边失败: ' + error.message,
      error
    }
  }
}

/**
 * 查询知识图谱跨年级连接
 * @param {number|string} grade - 年级数字(1-12)或年级名称
 * @returns {Promise<Object>} - 查询结果
 */
async function queryKnowledgeGraphCrossLinks(grade) {
  // 参数验证
  if (grade === undefined || grade === null) {
    return {
      success: false,
      message: '缺少必要参数: grade'
    }
  }
  
  try {
    // 构建查询条件 - 支持level数字查询
    let query;
    
    if (typeof grade === 'number' || !isNaN(parseInt(grade))) {
      // 使用level字段进行数字查询
      const level = parseInt(grade);
      console.log('按level查询跨年级连接:', level);
      query = db.collection('knowledge_graph_cross_links')
              .where(db.command.or([
                { sourceLevel: level },
                { targetLevel: level }
              ]));
    } else {
      // 兼容旧版本，使用grade字段进行文本查询
      console.log('按grade查询跨年级连接:', grade);
      query = db.collection('knowledge_graph_cross_links')
              .where(db.command.or([
                { sourceGrade: grade },
                { targetGrade: grade }
              ]));
    }
    
    // 执行查询
    const count = await query.count()
    const total = count.total || 0
    
    // 使用分页查询处理大量数据
    const MAX_LIMIT = 100
    const batchTimes = Math.ceil(total / MAX_LIMIT)
    const tasks = []
    
    for (let i = 0; i < batchTimes; i++) {
      const promise = query.skip(i * MAX_LIMIT).limit(MAX_LIMIT).get()
      tasks.push(promise)
    }
    
    // 等待所有查询完成并合并结果
    const results = await Promise.all(tasks)
    const data = []
    
    results.forEach(result => {
      data.push(...result.data)
    })
    
    return {
      success: true,
      data,
      total,
      message: `成功查询与 level=${grade} 相关的 ${data.length} 个跨年级连接数据`
    }
  } catch (error) {
    console.error('查询知识图谱跨年级连接失败:', error)
    return {
      success: false,
      message: '查询知识图谱跨年级连接失败: ' + error.message,
      error
    }
  }
}

/**
 * 查询知识图谱节点详情
 * @param {string} id - 节点ID
 * @returns {Promise<Object>} - 查询结果
 */
async function queryKnowledgeGraphNodeDetail(id) {
  // 参数验证
  if (!id) {
    return {
      success: false,
      message: '缺少必要参数: id'
    }
  }
  
  try {
    // 查询节点详情
    let nodeResult;
    try {
      // 尝试直接查询节点文档
      nodeResult = await db.collection('knowledge_graph_nodes').doc(id).get();
    } catch (e) {
      console.warn(`直接查询节点ID ${id} 失败: ${e.message}`);
      
      // 尝试通过查询集合获取节点
      try {
        const collResult = await db.collection('knowledge_graph_nodes')
          .where({
            id: id
          })
          .limit(1)
          .get();
          
        if (collResult && collResult.data && collResult.data.length > 0) {
          console.log(`通过ID字段成功找到节点 ${id}`);
          nodeResult = {
            data: collResult.data[0]
          };
        } else {
          // 两种方式都无法查到节点
          console.warn(`通过ID字段也未找到节点 ${id}`);
          
          // 尝试从节点ID解析年级信息
          const grade = getGradeFromNodeId(id);
          
          // 创建一个虚拟节点
          return {
            success: false,
            data: {
              node: {
                _id: id,
                id: id,
                name: `知识点 ${id}`,
                grade: grade || '未知年级',
                status: 'not-started',
                difficulty: 'medium',
                description: '该知识点信息尚未完全加载',
                virtual: true
              },
              links: []
            },
            message: `未找到ID为 ${id} 的知识点节点`,
            virtual: true // 标记为虚拟节点
          }
        }
      } catch (collErr) {
        console.error(`集合查询节点 ${id} 失败:`, collErr);
        
        // 两种方式都查询失败，返回虚拟节点
        const grade = getGradeFromNodeId(id);
        return {
          success: false,
          data: {
            node: {
              _id: id,
              id: id,
              name: `知识点 ${id}`,
              grade: grade || '未知年级',
              status: 'not-started',
              difficulty: 'medium',
              description: '该知识点信息尚未完全加载',
              virtual: true
            },
            links: []
          },
          message: `查询知识点 ${id} 失败: ${collErr.message}`,
          virtual: true
        }
      }
    }
    
    if (!nodeResult || !nodeResult.data) {
      return {
        success: false,
        message: `未找到ID为 ${id} 的知识点节点`
      }
    }
    
    const node = nodeResult.data
    const grade = node.grade
    
    // 查询与该节点相关的所有连接（该节点作为源或目标）
    const linksResult = await db.collection('knowledge_graph_edges')
                              .where(db.command.or([
                                { source: id },
                                { target: id }
                              ]))
                              .get()
    
    // 查询与该节点相关的跨年级连接
    const crossLinksResult = await db.collection('knowledge_graph_cross_links')
                                  .where(db.command.or([
                                    { source: id },
                                    { target: id }
                                  ]))
                                  .get()
    
    // 合并连接数据
    const links = [
      ...(linksResult.data || []),
      ...(crossLinksResult.data || [])
    ]
    
    return {
      success: true,
      data: {
        node,
        links
      },
      message: `成功查询ID为 ${id} 的知识点详情`
    }
  } catch (error) {
    console.error('查询知识图谱节点详情失败:', error)
    // 创建一个虚拟节点作为兜底方案
    const grade = getGradeFromNodeId(id);
    return {
      success: false,
      data: {
        node: {
          _id: id,
          id: id,
          name: `知识点 ${id}`,
          grade: grade || '未知年级',
          status: 'not-started',
          difficulty: 'medium',
          description: '该知识点信息加载失败，请联系管理员',
          virtual: true
        },
        links: []
      },
      message: '查询知识图谱节点详情失败: ' + error.message,
      error: error.message,
      virtual: true
    }
  }
}

/**
 * 从节点ID解析年级信息
 * @param {string} nodeId - 节点ID
 * @returns {string} - 年级名称
 */
function getGradeFromNodeId(nodeId) {
  if (!nodeId || typeof nodeId !== 'string' || nodeId.length < 2) return '';
  
  const prefix = nodeId.substring(0, 2);
  const gradeMap = {
    'e1': '小学一年级',
    'e2': '小学二年级',
    'e3': '小学三年级',
    'e4': '小学四年级',
    'e5': '小学五年级',
    'e6': '小学六年级',
    'j1': '初中一年级',
    'j2': '初中二年级',
    'j3': '初中三年级',
    's1': '高中一年级',
    's2': '高中二年级',
    's3': '高中三年级'
  };
  
  return gradeMap[prefix] || '';
}

/**
 * 查询知识图谱节点按名称
 * @param {string} name - 节点名称
 * @returns {Promise<Object>} - 查询结果
 */
async function queryKnowledgeGraphNodeByName(name) {
  // 参数验证
  if (!name) {
    return {
      success: false,
      message: '缺少必要参数: name'
    }
  }
  
  try {
    // 构建查询条件 - 使用正则表达式实现模糊匹配
    const query = db.collection('knowledge_graph_nodes')
                    .where({
                      name: db.RegExp({
                        regexp: name,
                        options: 'i' // 不区分大小写
                      })
                    })
                    .limit(10) // 限制返回数量
    
    // 执行查询
    const nodeResult = await query.get()
    
    if (!nodeResult || !nodeResult.data || nodeResult.data.length === 0) {
      // 如果精确匹配没有结果，尝试分词搜索
      const query2 = db.collection('knowledge_graph_nodes')
                      .where({
                        keywords: db.RegExp({
                          regexp: name.split('').join('|'), // 将每个字符作为关键词搜索
                          options: 'i'
                        })
                      })
                      .limit(10)
      
      const fuzzyResult = await query2.get()
      
      if (!fuzzyResult || !fuzzyResult.data || fuzzyResult.data.length === 0) {
        return {
          success: false,
          message: `未找到名称包含 ${name} 的知识点节点`
        }
      }
      
      // 找到最佳匹配
      const bestMatch = fuzzyResult.data[0]
      
      return {
        success: true,
        data: {
          id: bestMatch._id, // 确保返回_id作为节点ID
          name: bestMatch.name,
          grade: bestMatch.grade,
          description: bestMatch.description
        },
        message: `成功查询与 ${name} 相关的知识点`
      }
    }
    
    // 处理查询结果，确保返回的是最匹配的节点
    // 查找名称完全匹配的节点，如果没有则使用第一个结果
    const exactMatch = nodeResult.data.find(node => node.name === name) || nodeResult.data[0]
    
    return {
      success: true,
      data: {
        id: exactMatch._id, // 确保返回_id作为节点ID
        name: exactMatch.name,
        grade: exactMatch.grade,
        description: exactMatch.description
      },
      message: `成功查询名称为 ${name} 的知识点详情`
    }
  } catch (error) {
    console.error('查询知识图谱节点按名称失败:', error)
    return {
      success: false,
      message: '查询知识图谱节点按名称失败: ' + error.message,
      error
    }
  }
}

/**
 * 查询知识图谱节点关系
 * @param {string} nodeId - 节点ID
 * @returns {Promise<Object>} - 查询结果
 */
async function queryKnowledgeGraphNodeRelationships(nodeId) {
  // 参数验证
  if (!nodeId) {
    return {
      success: false,
      message: '缺少必要参数: nodeId'
    }
  }
  
  try {
    // 查询与该节点相关的所有连接（该节点作为源或目标）
    const linksResult = await db.collection('knowledge_graph_edges')
                              .where(db.command.or([
                                { source: nodeId },
                                { target: nodeId }
                              ]))
                              .get()
    
    // 查询与该节点相关的跨年级连接
    const crossLinksResult = await db.collection('knowledge_graph_cross_links')
                                  .where(db.command.or([
                                    { source: nodeId },
                                    { target: nodeId }
                                  ]))
                                  .get()
    
    // 合并连接数据
    const links = [
      ...(linksResult.data || []),
      ...(crossLinksResult.data || [])
    ]
    
    return {
      success: true,
      data: {
        nodeId,
        links
      },
      message: `成功查询ID为 ${nodeId} 的知识点关系`
    }
  } catch (error) {
    console.error('查询知识图谱节点关系失败:', error)
    return {
      success: false,
      message: '查询知识图谱节点关系失败: ' + error.message,
      error
    }
  }
}

/**
 * 批量查询知识图谱节点详情
 * @param {Array<string>} nodeIds - 节点ID数组
 * @returns {Promise<Object>} - 查询结果
 */
async function queryKnowledgeGraphNodesBatch(nodeIds) {
  // 参数验证
  if (!nodeIds || !Array.isArray(nodeIds) || nodeIds.length === 0) {
    return {
      success: false,
      message: '缺少必要参数: nodeIds 或格式不正确'
    }
  }
  
  try {
    // 构建批量查询条件
    const query = db.collection('knowledge_graph_nodes')
                    .where({
                      id: db.command.in(nodeIds)
                    })
    
    // 执行查询
    const count = await query.count()
    const total = count.total || 0
    
    // 使用分页查询处理大量数据
    const MAX_LIMIT = 100
    const batchTimes = Math.ceil(total / MAX_LIMIT)
    const tasks = []
    
    for (let i = 0; i < batchTimes; i++) {
      const promise = query.skip(i * MAX_LIMIT).limit(MAX_LIMIT).get()
      tasks.push(promise)
    }
    
    // 等待所有查询完成并合并结果
    const results = await Promise.all(tasks)
    let data = []
    
    results.forEach(result => {
      data.push(...result.data)
    })
    
    // 处理未找到的节点，创建虚拟节点
    const foundNodeIds = new Set(data.map(node => node.id))
    const notFoundNodeIds = nodeIds.filter(id => !foundNodeIds.has(id))
    
    // 为未找到的节点创建虚拟节点
    notFoundNodeIds.forEach(id => {
      const grade = getGradeFromNodeId(id)
      data.push({
        _id: id,
        id: id,
        name: `知识点 ${id}`,
        grade: grade || '未知年级',
        status: 'not-started',
        difficulty: 'medium',
        description: '该知识点信息尚未完全加载',
        virtual: true
      })
    })
    
    return {
      success: true,
      data,
      total: data.length,
      message: `成功批量查询 ${data.length} 个知识点节点数据`
    }
  } catch (error) {
    console.error('批量查询知识图谱节点失败:', error)
    return {
      success: false,
      message: '批量查询知识图谱节点失败: ' + error.message,
      error
    }
  }
} 