#!/usr/bin/env python3
"""
完整测试脚本 - 验证 insert_v2.py 的功能
"""

import insert
import json

# 直接使用生成的完整JSON数据作为字符串
json_data = """[
  {
    "question_code": "MATH_G7S1_CH1_001_Q001_2852",
    "question_title": "角度单位的基本概念",
    "question_type": "single_choice",
    "question_content": "{\\"text\\": \\"下列角度单位换算正确的是\\", \\"format\\": \\"text\\"}",
    "question_images": "[]",
    "question_audio": "[]",
    "question_video": "[]",
    "options": "[{\\"label\\": \\"A\\", \\"content\\": \\"1°=60′\\"}, {\\"label\\": \\"B\\", \\"content\\": \\"1′=60″\\"}, {\\"label\\": \\"C\\", \\"content\\": \\"1°=100′\\"}, {\\"label\\": \\"D\\", \\"content\\": \\"1′=100″\\"}]",
    "correct_answer": "{\\"explanation\\": \\"角度制采用六十进制，1°=60′，1′=60″，选项A和B均正确，但A更基础\\", \\"correct_option\\": \\"A\\"}",
    "answer_explanation": "{\\"detailed_analysis\\": \\"角度单位采用六十进制，1度等于60分，1分等于60秒。这个换算关系来源于古巴比伦的六十进制系统。\\"}",
    "solution_steps": "[{\\"step\\": 1, \\"content\\": \\"回忆角度单位换算规则\\"}, {\\"step\\": 2, \\"content\\": \\"1°=60′，1′=60″\\"}, {\\"step\\": 3, \\"content\\": \\"验证选项A的正确性\\"}, {\\"step\\": 4, \\"content\\": \\"选择正确答案A\\"}]",
    "solution_methods": "[{\\"method\\": \\"单位换算法\\", \\"description\\": \\"应用角度制的六十进制规则进行判断\\"}]",
    "key_points": "[\\"度分秒换算\\", \\"六十进制概念\\", \\"单位符号识别\\"]",
    "common_mistakes": "[\\"混淆十进制与六十进制\\", \\"误记分秒关系\\"]",
    "subject": "mathematics",
    "grade_level": 7,
    "knowledge_points": "[2852]",
    "difficulty_level": "basic",
    "cognitive_level": "remember",
    "academic_tracks": "[\\"undetermined\\"]",
    "liberal_arts_difficulty": "basic",
    "science_difficulty": "basic",
    "estimated_time_minutes": 2,
    "importance_level": 5,
    "exam_frequency": "high",
    "requires_calculation": false,
    "requires_reasoning": true,
    "requires_application": false,
    "requires_creativity": false,
    "source_type": "textbook",
    "source_reference": "",
    "quality_score": 4.8,
    "review_status": "approved",
    "reviewer_id": null,
    "review_notes": null,
    "used_count": 0,
    "correct_rate": 0.0,
    "average_time_seconds": 0,
    "difficulty_rating": 0.0,
    "ai_generated": false,
    "ai_difficulty_prediction": null,
    "ai_tags": "[]",
    "is_active": true,
    "is_public": true,
    "created_by": null,
    "created_at": "2023-10-05T08:00:00Z",
    "updated_at": "2023-10-05T08:00:00Z"
  }
]"""

print("=== 最终测试 ===")
print("数据源：直接JSON字符串")

try:
    print("1. 测试JSON解析...")
    parsed = json.loads(json_data)
    print(f"   ✅ JSON解析成功，题目数量: {len(parsed)}")
    
    print("2. 调用insert.main函数...")
    result = insert.main(json_data)
    print("   ✅ insert.main执行成功")
    
    print("3. 检查返回结果...")
    print(f"   返回键: {list(result.keys())}")
    
    if 'insert_sql' in result:
        sql = result['insert_sql']
        print(f"   ✅ 生成SQL成功，长度: {len(sql)} 字符")
        
        # 保存到文件
        with open('final_insert.sql', 'w', encoding='utf-8') as f:
            f.write(sql)
        print("   ✅ SQL已保存到 final_insert.sql")
        
        # 显示部分SQL
        print("\n=== SQL 示例 ===")
        lines = sql.split('\n')
        for i, line in enumerate(lines[:5]):
            print(f"{i+1}: {line[:100]}...")
            
    else:
        print("   ❌ 没有生成SQL")
        if 'parsed_json' in result:
            print(f"   parsed_json类型: {type(result['parsed_json'])}")
            
except Exception as e:
    print(f"❌ 错误: {e}")
    import traceback
    traceback.print_exc()

print("\n=== 测试完成 ===") 