/**
 * 通用分享模态框组件
 */
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 是否显示分享弹窗
    visible: {
      type: Boolean,
      value: false
    },
    // 分享标题
    title: {
      type: String,
      value: '分享到'
    },
    // 分享内容信息
    shareData: {
      type: Object,
      value: null
    },
    // 是否显示朋友圈选项
    showMoment: {
      type: Boolean,
      value: true
    },
    // 是否显示生成海报选项
    showPoster: {
      type: Boolean,
      value: true
    },
    // 是否显示复制链接选项
    showLink: {
      type: Boolean,
      value: true
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 阻止事件冒泡
     */
    stopPropagation() {
      return;
    },
    
    /**
     * 关闭分享弹窗
     */
    close() {
      this.triggerEvent('close');
    },
    
    /**
     * 分享给微信好友
     */
    shareToFriend() {
      wx.showShareMenu({
        withShareTicket: true,
        menus: ['shareAppMessage']
      });
      
      wx.showToast({
        title: '请点击右上角分享',
        icon: 'none'
      });
      
      // 触发分享微信好友事件
      this.triggerEvent('shareFriend', {
        shareData: this.properties.shareData
      });
    },
    
    /**
     * 分享到朋友圈
     */
    shareToMoment() {
      wx.showShareMenu({
        withShareTicket: true,
        menus: ['shareTimeline']
      });
      
      wx.showToast({
        title: '请点击右上角分享到朋友圈',
        icon: 'none'
      });
      
      // 触发分享朋友圈事件
      this.triggerEvent('shareMoment', {
        shareData: this.properties.shareData
      });
    },
    
    /**
     * 生成分享海报
     */
    generatePoster() {
      // 触发生成海报事件
      this.triggerEvent('generatePoster', {
        shareData: this.properties.shareData
      });
    },
    
    /**
     * 复制分享链接
     */
    copyShareLink() {
      // 触发复制链接事件
      this.triggerEvent('copyLink', {
        shareData: this.properties.shareData
      });
    }
  }
}) 