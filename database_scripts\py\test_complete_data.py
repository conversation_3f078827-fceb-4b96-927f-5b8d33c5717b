import insert
import json

# 完整的9题数据
complete_data = """{
  "text": "```json\\n[\\n  {\\n    \\"question_code\\": \\"MATH_G7S1_CH1_001_Q001_2852\\",\\n    \\"question_title\\": \\"角度单位的基本概念\\",\\n    \\"question_type\\": \\"single_choice\\",\\n    \\"question_content\\": \\"{\\\\\\\"text\\\\\\\": \\\\\\\"下列角度单位换算正确的是\\\\\\\", \\\\\\\"format\\\\\\\": \\\\\\\"text\\\\\\"}\\",\\n    \\"question_images\\": \\"[]\\",\\n    \\"question_audio\\": \\"[]\\",\\n    \\"question_video\\": \\"[]\\",\\n    \\"options\\": \\"[{\\\\\\\"label\\\\\\\": \\\\\\\"A\\\\\\\", \\\\\\\"content\\\\\\\": \\\\\\\"1°=60′\\\\\\\"}, {\\\\\\\"label\\\\\\\": \\\\\\\"B\\\\\\\", \\\\\\\"content\\\\\\\": \\\\\\\"1′=60″\\\\\\\"}, {\\\\\\\"label\\\\\\\": \\\\\\\"C\\\\\\\", \\\\\\\"content\\\\\\\": \\\\\\\"1°=100′\\\\\\\"}, {\\\\\\\"label\\\\\\\": \\\\\\\"D\\\\\\\", \\\\\\\"content\\\\\\\": \\\\\\\"1′=100″\\\\\\\"}]\\",\\n    \\"correct_answer\\": \\"{\\\\\\\"explanation\\\\\\\": \\\\\\\"角度制采用六十进制，1°=60′，1′=60″，选项A和B均正确，但A更基础\\\\\\\", \\\\\\\"correct_option\\\\\\\": \\\\\\\"A\\\\\\\"}\\",\\n    \\"answer_explanation\\": \\"{\\\\\\\"detailed_analysis\\\\\\\": \\\\\\\"角度单位采用六十进制，1度等于60分，1分等于60秒。这个换算关系来源于古巴比伦的六十进制系统。\\\\\\\"}\\",\\n    \\"solution_steps\\": \\"[{\\\\\\\"step\\\\\\\": 1, \\\\\\\"content\\\\\\\": \\\\\\\"回忆角度单位换算规则\\\\\\\"}, {\\\\\\\"step\\\\\\\": 2, \\\\\\\"content\\\\\\\": \\\\\\\"1°=60′，1′=60″\\\\\\\"}, {\\\\\\\"step\\\\\\\": 3, \\\\\\\"content\\\\\\\": \\\\\\\"验证选项A的正确性\\\\\\\"}, {\\\\\\\"step\\\\\\\": 4, \\\\\\\"content\\\\\\\": \\\\\\\"选择正确答案A\\\\\\\"}]\\",\\n    \\"solution_methods\\": \\"[{\\\\\\\"method\\\\\\\": \\\\\\\"单位换算法\\\\\\\", \\\\\\\"description\\\\\\\": \\\\\\\"应用角度制的六十进制规则进行判断\\\\\\\"}]\\",\\n    \\"key_points\\": \\"[\\\\\\\"度分秒换算\\\\\\\", \\\\\\\"六十进制概念\\\\\\\", \\\\\\\"单位符号识别\\\\\\\"]\\",\\n    \\"common_mistakes\\": \\"[\\\\\\\"混淆十进制与六十进制\\\\\\\", \\\\\\\"误记分秒关系\\\\\\\"]\\",\\n    \\"subject\\": \\"mathematics\\",\\n    \\"grade_level\\": 7,\\n    \\"knowledge_points\\": \\"[2852]\\",\\n    \\"difficulty_level\\": \\"basic\\",\\n    \\"cognitive_level\\": \\"remember\\",\\n    \\"academic_tracks\\": \\"[\\\\\\"undetermined\\\\\\"]\\",\\n    \\"liberal_arts_difficulty\\": \\"basic\\",\\n    \\"science_difficulty\\": \\"basic\\",\\n    \\"estimated_time_minutes\\": 2,\\n    \\"importance_level\\": 5,\\n    \\"exam_frequency\\": \\"high\\",\\n    \\"requires_calculation\\": false,\\n    \\"requires_reasoning\\": true,\\n    \\"requires_application\\": false,\\n    \\"requires_creativity\\": false,\\n    \\"source_type\\": \\"textbook\\",\\n    \\"source_reference\\": \\"\\",\\n    \\"quality_score\\": 4.8,\\n    \\"review_status\\": \\"approved\\",\\n    \\"reviewer_id\\": null,\\n    \\"review_notes\\": null,\\n    \\"used_count\\": 0,\\n    \\"correct_rate\\": 0.0,\\n    \\"average_time_seconds\\": 0,\\n    \\"difficulty_rating\\": 0.0,\\n    \\"ai_generated\\": false,\\n    \\"ai_difficulty_prediction\\": null,\\n    \\"ai_tags\\": \\"[]\\",\\n    \\"is_active\\": true,\\n    \\"is_public\\": true,\\n    \\"created_by\\": null,\\n    \\"created_at\\": \\"2023-10-05T08:00:00Z\\",\\n    \\"updated_at\\": \\"2023-10-05T08:00:00Z\\"\\n  }\\n]\\n```"
}"""

try:
    print("=== 测试完整数据处理 ===")
    result = insert.main(complete_data)
    print("✅ 函数执行成功")
    print(f"返回键: {list(result.keys())}")
    
    if 'parsed_json' in result:
        questions = result['parsed_json']
        print(f"✅ 解析题目数量: {len(questions) if isinstance(questions, list) else 'N/A'}")
    
    if 'insert_sql' in result:
        sql = result['insert_sql']
        print("✅ 生成了INSERT SQL语句")
        print(f"SQL长度: {len(sql)} 字符")
        
        # 保存SQL到文件
        with open('generated_insert.sql', 'w', encoding='utf-8') as f:
            f.write(sql)
        print("✅ SQL已保存到 generated_insert.sql")
        
        # 显示SQL的前1000字符
        print("\n=== SQL预览 ===")
        print(sql[:1000] + '...' if len(sql) > 1000 else sql)
    else:
        print("❌ 没有生成INSERT SQL语句")
        
except Exception as e:
    print(f"❌ 错误: {e}")
    import traceback
    traceback.print_exc()

print("\n=== 测试完成 ===") 