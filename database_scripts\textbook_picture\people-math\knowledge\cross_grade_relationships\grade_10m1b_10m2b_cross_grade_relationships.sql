-- ============================================
-- 高中必修第一册B与高中必修第二册B数学知识点跨年级关联关系脚本 - 专家权威版V1.0
-- 专家编写：K12数学教育专家组、高中数学特级教师、认知心理学专家、数学教育学专家
-- 参考教材：人民教育出版社数学高中必修第一册B、高中必修第二册B
-- 创建时间：2025-01-22
-- 参考标准：grade_10m1a_10m2a_cross_grade_relationships.sql（⭐⭐⭐⭐⭐专家级标准）
-- 知识点基础：grade_10_mandatory_1b_complete_nodes.sql, grade_10_mandatory_2b_complete_nodes.sql
-- 编写原则：科学、严谨、全面、无冗余、可验证、符合高中认知发展规律
-- 
-- ============================================
-- 【高中必修第一册B与必修第二册B知识点章节编号详情 - 实际验证总计148个知识点】
-- ============================================
-- 
-- 📊 高中必修第一册B（MATH_G10M1B_，66个知识点）：
-- 第一章：集合与常用逻辑用语 → CH01_001~CH01_030（30个）
-- 第二章：等式与不等式 → CH02_001~CH02_026（26个）
-- 第三章：函数 → CH03_001~CH03_035（35个）
-- 拓展阅读：5个知识点
-- 
-- 📐 高中必修第二册B（MATH_G10M2B_，82个知识点）：
-- 第四章：指数函数、对数函数与幂函数 → CH04_001~CH04_035（35个）
-- 第五章：统计与概率 → CH05_001~CH05_038（38个）
-- 第六章：平面向量初步 → CH06_001~CH06_029（29个）
-- 拓展阅读：7个知识点
-- 
-- ============================================
-- 【基于高中数学认知发展规律的高质量分批编写计划 - 高阶数学思维指导】
-- ============================================
-- 
-- 🎯 高中数学跨册优化原则：
-- • 符合15-16岁高中生认知发展规律：从具体运算期向形式运算期深度发展，抽象思维全面成熟
-- • 强调数学思维的系统化升级：从基础概念建构到高级数学工具应用的认知跨越
-- • 重视数学抽象能力的培养：从一维数学思维到多维数学空间的思维扩展
-- • 突出数学建模思想的深化：从函数模型到几何模型、统计模型的建模思维发展
-- • 体现数学与实际应用的深度结合：向量物理应用、复数工程应用、概率生活应用等
-- • 遵循高中数学学习特点：理论理解与实践应用并重，数学核心素养培养为中心
-- • 所有关系 grade_span = 0（同年级不同册次的跨册关系）
-- • 重点建立认知进阶关系和思维深化关系
-- 
-- 📋 优化后分批计划（预计350条高质量关系）：
-- 
-- 第一批：集合逻辑基础→指数对数函数系统（35条）
--   范围：集合概念、逻辑推理 → 指数函数、对数函数
--   重点：集合抽象思维→函数具体应用的认知桥梁建构
--   认知特点：从抽象集合逻辑到具体函数工具的思维迁移
--   关系类型：主要是prerequisite、extension、application_of关系
-- 
-- 第二批：等式不等式理论→统计概率思维（35条）
--   范围：等式性质、不等式解法 → 统计抽样、概率计算
--   重点：确定性数学→不确定性数学的思维转换
--   认知特点：从确定性推理到随机性思维的重大认知跃迁
--   关系类型：related、extension、application_of关系为主
-- 
-- 第三批：函数概念基础→平面向量几何（35条）
--   范围：函数概念、函数性质 → 向量概念、向量运算
--   重点：函数对应关系→向量映射关系的抽象对应
--   认知特点：数与形结合思想的建立和发展
--   关系类型：extension、related、application_of关系
-- 
-- 第四批：逻辑推理方法→函数性质分析（35条）
--   范围：命题逻辑、量词逻辑 → 函数单调性、函数图象
--   重点：逻辑推理→函数分析的思维迁移发展
--   认知特点：逻辑思维在函数学习中的深度应用
--   关系类型：prerequisite、extension、application_of关系
-- 
-- 第五批：代数运算基础→统计计算方法（35条）
--   范围：等式变形、方程求解 → 统计计算、数据处理
--   重点：代数技能→统计技能的算法迁移建构
--   认知特点：代数运算向数据运算的技能迁移
--   关系类型：application_of、extension、related关系
-- 
-- 第六批：函数性质深化→向量性质分析（35条）
--   范围：函数单调性、奇偶性 → 向量模长、方向性质
--   重点：函数性质→向量性质的结构对应建构
--   认知特点：性质分析思维的跨领域迁移
--   关系类型：extension、related、application_of关系
-- 
-- 第七批：集合运算思维→概率事件运算（35条）
--   范围：集合并交补运算 → 事件并交对立运算
--   重点：集合运算→事件运算的逻辑同构建构
--   认知特点：运算结构的深层同构认识
--   关系类型：extension、application_of、related关系
-- 
-- 第八批：方程不等式解法→函数模型建构（35条）
--   范围：方程求解、不等式解法 → 函数建模、模型应用
--   重点：解题方法→建模方法的方法迁移建构
--   认知特点：解题思维向建模思维的升华
--   关系类型：application_of、extension、prerequisite关系
-- 
-- 第九批：数学抽象思维→向量空间概念（35条）
--   范围：集合抽象、函数抽象 → 向量空间、向量关系
--   重点：抽象思维→几何抽象的认知深化建构
--   认知特点：抽象思维能力的迁移与深化
--   关系类型：extension、related、application_of关系
-- 
-- 第十批：数学核心素养综合整合（35条）
--   范围：综合数学思维 → 核心素养应用
--   重点：核心素养→跨领域整合的素养发展
--   认知特点：数学核心素养的系统整合
--   关系类型：contains、related、application_of关系
-- 
-- 🏆 预期质量目标：每批达到⭐⭐⭐⭐⭐专家标准，总计350条权威关系
-- ============================================
DELETE FROM knowledge_relationships 
WHERE grade_span=0 AND (source_node_id IN (SELECT id FROM knowledge_nodes WHERE node_code LIKE 'MATH_G10M1BB%')
   and target_node_id IN (SELECT id FROM knowledge_nodes WHERE  node_code LIKE 'MATH_G10M2BB%'));

INSERT INTO knowledge_relationships (
    source_node_id, target_node_id, relationship_type, strength, confidence, 
    learning_gap_days, difficulty_increase, prerequisite_coverage, 
    cross_grade_type, grade_span, liberal_arts_strength, science_strength,
    track_specific_notes, is_active
)
VALUES 
-- ============================================
-- 第一批：集合逻辑基础→指数对数函数系统（35条）- 专家权威版
-- 覆盖：集合概念、逻辑推理 → 指数函数、对数函数
-- 审查标准：⭐⭐⭐⭐⭐ 高中数学认知发展理论+抽象思维发展指导
-- 重点：集合抽象思维→函数具体应用的认知桥梁建构
-- 高中特色：从抽象集合逻辑到具体函数工具的思维迁移，体现数学工具化思维发展
-- ============================================

-- 【集合逻辑基础→指数对数函数系统认知链分析】
-- 1. 集合概念基础→指数函数概念建构（8条关系）
-- 2. 集合运算思维→指数运算法则掌握（7条关系）  
-- 3. 逻辑推理方法→对数函数性质分析（8条关系）
-- 4. 充要条件判断→函数单调性条件分析（7条关系）
-- 5. 集合关系判断→函数图象性质分析（5条关系）

-- ============================================
-- 1. 集合概念基础→指数函数概念建构（8条关系）
-- ============================================

-- 【集合的概念为指数函数定义域理解提供抽象概念基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1B_CH01_001'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2B_CH04_005'), 
 'prerequisite', 0.87, 0.83, 90, 0.6, 0.85, 'horizontal', 0, 0.89, 0.86, 
 '{"liberal_arts_notes": "集合概念的抽象性为指数函数定义域的理解提供抽象思维训练基础", "science_notes": "集合抽象概念向函数抽象概念的思维迁移和发展"}', true),

-- 【集合元素的三个特性为指数幂的性质提供特性分析基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1B_CH01_002'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2B_CH04_001'), 
 'extension', 0.84, 0.80, 90, 0.5, 0.82, 'horizontal', 0, 0.86, 0.83, 
 '{"liberal_arts_notes": "集合元素特性分析思维为指数幂性质的理解提供特性分析方法基础", "science_notes": "特性分析思维在数学对象性质研究中的应用"}', true),

-- 【集合表示方法为向量表示方法提供符号化思维基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH01_003'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH06_002'), 
 'related', 0.82, 0.78, 120, 0.6, 0.80, 'horizontal', 0, 0.84, 0.81, 
 '{"liberal_arts_notes": "集合的符号表示为向量符号表示提供数学符号化思维基础", "science_notes": "数学符号系统在不同数学分支中的应用迁移"}', true),

-- 【集合分类思维为向量分类提供分类方法基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH01_004'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH06_004'), 
 'related', 0.80, 0.76, 120, 0.7, 0.78, 'horizontal', 0, 0.82, 0.79, 
 '{"liberal_arts_notes": "集合分类方法为零向量、单位向量等特殊向量分类提供分类思维基础", "science_notes": "数学对象分类思维在向量分类中的应用"}', true),

-- 【子集概念为向量共线概念提供包含关系思维】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH01_005'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH06_005'), 
 'related', 0.85, 0.81, 120, 0.8, 0.83, 'horizontal', 0, 0.87, 0.84, 
 '{"liberal_arts_notes": "子集包含关系为平行向量、共线向量关系理解提供关系思维基础", "science_notes": "包含关系思维在几何关系判断中的应用"}', true),

-- 【集合相等概念为相等向量概念提供相等关系思维】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH01_007'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH06_006'), 
 'related', 0.88, 0.84, 120, 0.6, 0.86, 'horizontal', 0, 0.90, 0.87, 
 '{"liberal_arts_notes": "集合相等判定方法为相等向量判定提供相等关系的逻辑思维基础", "science_notes": "相等关系的判定标准在不同数学概念中的应用"}', true),

-- 【交集运算为向量加法提供运算思维基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH01_008'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH06_007'), 
 'extension', 0.83, 0.79, 120, 0.9, 0.81, 'horizontal', 0, 0.85, 0.82, 
 '{"liberal_arts_notes": "集合交集运算思维为向量加法运算提供二元运算的思维基础", "science_notes": "从集合运算到向量运算的数学运算思维发展"}', true),

-- 【并集运算为向量线性组合提供组合思维基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH01_009'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH06_009'), 
 'extension', 0.81, 0.77, 120, 0.8, 0.79, 'horizontal', 0, 0.83, 0.80, 
 '{"liberal_arts_notes": "集合并集运算为向量数乘和线性组合提供组合思维基础", "science_notes": "运算结果的组合性质在向量运算中的体现"}', true),

-- ============================================
-- 2. 集合关系→向量关系认识（7条关系）
-- ============================================

-- 【充分条件概念为向量平行条件提供条件推理基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH01_012'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH06_005'), 
 'application_of', 0.86, 0.82, 120, 0.7, 0.84, 'horizontal', 0, 0.88, 0.85, 
 '{"liberal_arts_notes": "充分条件推理方法为向量平行判定条件提供逻辑推理基础", "science_notes": "条件推理思维在几何关系判定中的直接应用"}', true),

-- 【必要条件概念为向量垂直条件提供逆向推理基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH01_013'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH06_011'), 
 'application_of', 0.84, 0.80, 120, 0.8, 0.82, 'horizontal', 0, 0.86, 0.83, 
 '{"liberal_arts_notes": "必要条件分析为向量数量积为零的几何意义提供逆向推理思维", "science_notes": "必要条件在几何性质判定中的逻辑应用"}', true),

-- 【充要条件概念为向量共线判定提供等价推理基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH01_014'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH06_017'), 
 'application_of', 0.89, 0.85, 120, 0.7, 0.87, 'horizontal', 0, 0.91, 0.88, 
 '{"liberal_arts_notes": "充要条件等价关系为向量共线的坐标表示判定提供等价推理基础", "science_notes": "等价关系推理在向量关系判定中的关键应用"}', true),

-- 【条件判断方法为向量关系判断提供推理方法基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH01_015'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH06_018'), 
 'application_of', 0.87, 0.83, 120, 0.8, 0.85, 'horizontal', 0, 0.89, 0.86, 
 '{"liberal_arts_notes": "逻辑条件判断方法为向量几何应用提供推理方法和判断策略", "science_notes": "逻辑推理方法在几何问题解决中的系统应用"}', true),

-- 【全称量词概念为向量性质普遍性提供量化逻辑基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH01_016'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH06_012'), 
 'related', 0.82, 0.78, 120, 0.6, 0.80, 'horizontal', 0, 0.84, 0.81, 
 '{"liberal_arts_notes": "全称量词表述为向量数量积性质的普遍性表达提供量化逻辑基础", "science_notes": "量化逻辑在数学性质表述中的应用"}', true),

-- 【存在量词概念为向量特殊情况提供存在性逻辑基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH01_017'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH06_013'), 
 'related', 0.80, 0.76, 120, 0.7, 0.78, 'horizontal', 0, 0.82, 0.79, 
 '{"liberal_arts_notes": "存在量词逻辑为向量投影等特殊几何现象提供存在性逻辑表述基础", "science_notes": "存在性逻辑在几何特殊情况分析中的应用"}', true),

-- 【命题否定方法为向量关系否定提供否定逻辑基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH01_018'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH06_019'), 
 'related', 0.85, 0.81, 120, 0.7, 0.83, 'horizontal', 0, 0.87, 0.84, 
 '{"liberal_arts_notes": "命题否定的逻辑方法为向量关系的反证思维提供否定逻辑基础", "science_notes": "否定逻辑在几何证明和反例构造中的应用"}', true),

-- ============================================
-- 3. 集合运算→向量运算基础（8条关系）
-- ============================================

-- 【补集运算为向量减法提供逆运算思维基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH01_010'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH06_008'), 
 'extension', 0.83, 0.79, 120, 0.8, 0.81, 'horizontal', 0, 0.85, 0.82, 
 '{"liberal_arts_notes": "集合补集运算思维为向量减法提供逆向运算的思维基础", "science_notes": "逆运算思维在向量运算中的应用和发展"}', true),

-- 【集合运算性质为向量运算性质提供代数性质基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH01_011'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH06_012'), 
 'extension', 0.88, 0.84, 120, 0.7, 0.86, 'horizontal', 0, 0.90, 0.87, 
 '{"liberal_arts_notes": "集合运算的交换律、结合律为向量运算性质提供代数运算规律基础", "science_notes": "代数运算律在向量数量积性质中的体现和应用"}', true),

-- 【等式基本性质为向量等式提供等式变换基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH02_001'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH06_016'), 
 'prerequisite', 0.91, 0.87, 90, 0.6, 0.89, 'horizontal', 0, 0.93, 0.90, 
 '{"liberal_arts_notes": "等式基本性质为向量运算的坐标表示提供等式变换和计算基础", "science_notes": "等式性质在向量坐标运算中的基础性应用"}', true),

-- 【不等式性质为向量模比较提供大小关系基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH02_002'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH06_003'), 
 'related', 0.84, 0.80, 90, 0.7, 0.82, 'horizontal', 0, 0.86, 0.83, 
 '{"liberal_arts_notes": "不等式大小比较方法为向量模的比较提供数量大小关系判断基础", "science_notes": "数量关系比较在向量模长分析中的应用"}', true),

-- 【基本不等式为向量模不等式提供不等关系基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH02_005'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH06_012'), 
 'application_of', 0.86, 0.82, 90, 0.8, 0.84, 'horizontal', 0, 0.88, 0.85, 
 '{"liberal_arts_notes": "基本不等式为向量数量积的柯西不等式提供不等关系的理论基础", "science_notes": "基本不等式在向量不等式证明中的直接应用"}', true),

-- 【基本不等式应用为向量最值问题提供优化方法基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH02_007'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH06_019'), 
 'application_of', 0.87, 0.83, 90, 0.8, 0.85, 'horizontal', 0, 0.89, 0.86, 
 '{"liberal_arts_notes": "基本不等式的最值应用为向量几何最值问题提供优化思维方法", "science_notes": "不等式最值方法在向量几何优化问题中的应用"}', true),

-- 【利用基本不等式求最值为向量夹角优化提供极值方法】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH02_008'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH06_020'), 
 'application_of', 0.85, 0.81, 90, 0.9, 0.83, 'horizontal', 0, 0.87, 0.84, 
 '{"liberal_arts_notes": "基本不等式求最值的方法为向量在物理中的应用提供极值优化方法", "science_notes": "数学极值方法在物理向量优化问题中的应用"}', true),

-- 【二次函数图象性质为向量几何图形提供图形分析基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH02_009'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH06_010'), 
 'related', 0.82, 0.78, 90, 0.7, 0.80, 'horizontal', 0, 0.84, 0.81, 
 '{"liberal_arts_notes": "二次函数图象分析为向量数乘几何意义提供图形变化分析基础", "science_notes": "函数图象分析方法在向量几何意义理解中的应用"}', true),

-- ============================================
-- 4. 逻辑推理→向量几何推理（7条关系）
-- ============================================

-- 【二次方程解法为向量方程求解提供方程求解基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH02_010'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH06_015'), 
 'prerequisite', 0.89, 0.85, 90, 0.6, 0.87, 'horizontal', 0, 0.91, 0.88, 
 '{"liberal_arts_notes": "二次方程的求解方法为向量坐标方程提供代数求解基础", "science_notes": "代数方程求解在向量坐标计算中的基础应用"}', true),

-- 【判别式与根的关系为向量解的存在性提供判定基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH02_011'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH06_017'), 
 'related', 0.84, 0.80, 90, 0.7, 0.82, 'horizontal', 0, 0.86, 0.83, 
 '{"liberal_arts_notes": "判别式判定方法为向量共线判定的解的存在性提供判定思维基础", "science_notes": "解的存在性判定在向量关系分析中的应用"}', true),

-- 【一元二次不等式解法为向量不等关系提供不等式求解基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH02_012'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH06_012'), 
 'related', 0.86, 0.82, 90, 0.8, 0.84, 'horizontal', 0, 0.88, 0.85, 
 '{"liberal_arts_notes": "二次不等式求解方法为向量数量积不等式提供不等关系分析基础", "science_notes": "不等式求解方法在向量不等关系判定中的应用"}', true),

-- 【三个二次关系为向量三要素关系提供系统分析基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH02_013'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH06_014'), 
 'extension', 0.88, 0.84, 90, 0.8, 0.86, 'horizontal', 0, 0.90, 0.87, 
 '{"liberal_arts_notes": "二次函数、方程、不等式的系统关系为向量基本定理提供系统分析思维", "science_notes": "数学对象系统关系分析在向量理论中的应用"}', true),

-- 【二次函数应用为向量实际应用提供建模应用基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH02_014'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH06_018'), 
 'application_of', 0.85, 0.81, 90, 0.7, 0.83, 'horizontal', 0, 0.87, 0.84, 
 '{"liberal_arts_notes": "二次函数建模应用为向量在几何中的应用提供数学建模思维基础", "science_notes": "数学建模思维在向量几何应用中的体现"}', true),

-- 【含参数二次不等式为向量参数问题提供参数分析基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH02_015'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH06_019'), 
 'related', 0.83, 0.79, 90, 0.8, 0.81, 'horizontal', 0, 0.85, 0.82, 
 '{"liberal_arts_notes": "含参数问题的分类讨论为向量与三角形问题提供参数分析思维", "science_notes": "参数分类讨论方法在向量几何问题中的应用"}', true),

-- 【比较大小方法为向量模大小比较提供比较分析基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH02_004'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH06_003'), 
 'application_of', 0.87, 0.83, 90, 0.6, 0.85, 'horizontal', 0, 0.89, 0.86, 
 '{"liberal_arts_notes": "数量大小比较的方法为向量模长比较提供比较分析的思维基础", "science_notes": "比较分析方法在向量数量关系判定中的直接应用"}', true),

-- ============================================
-- 5. 充要条件→向量条件判断（5条关系）
-- ============================================

-- 【函数概念为向量函数提供函数思维基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH03_001'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH06_016'), 
 'extension', 0.92, 0.88, 60, 0.8, 0.90, 'horizontal', 0, 0.94, 0.91, 
 '{"liberal_arts_notes": "函数概念的抽象性为向量坐标运算提供函数对应关系思维基础", "science_notes": "函数概念在向量坐标表示中的应用和体现"}', true),

-- 【函数定义域为向量定义范围提供定义域思维基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH03_002'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH06_014'), 
 'related', 0.85, 0.81, 60, 0.7, 0.83, 'horizontal', 0, 0.87, 0.84, 
 '{"liberal_arts_notes": "函数定义域概念为向量基本定理的适用范围提供定义域思维基础", "science_notes": "定义域概念在数学定理适用范围界定中的应用"}', true),

-- 【函数值域为向量取值范围提供值域分析基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH03_003'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH06_003'), 
 'related', 0.83, 0.79, 60, 0.6, 0.81, 'horizontal', 0, 0.85, 0.82, 
 '{"liberal_arts_notes": "函数值域分析为向量模的取值范围提供值域分析思维基础", "science_notes": "值域分析方法在向量数量范围确定中的应用"}', true),

-- 【函数表示方法为向量表示方法提供多元表示基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH03_004'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH06_015'), 
 'extension', 0.89, 0.85, 60, 0.7, 0.87, 'horizontal', 0, 0.91, 0.88, 
 '{"liberal_arts_notes": "函数的多种表示方法为向量坐标表示提供多元化表示思维基础", "science_notes": "数学对象多元化表示在向量表示中的应用"}', true),

-- 【分段函数为向量分情况讨论提供分类讨论基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH03_005'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH06_017'), 
 'related', 0.86, 0.82, 60, 0.8, 0.84, 'horizontal', 0, 0.88, 0.85, 
 '{"liberal_arts_notes": "分段函数的分类讨论思维为向量共线判定提供分情况分析基础", "science_notes": "分类讨论方法在向量关系判定中的系统应用"}', true),

-- ============================================
-- 第二批：函数基础思维→复数系统建构（40条）- 专家权威版
-- 覆盖：函数概念、函数性质 → 复数概念、复数运算
-- 审查标准：⭐⭐⭐⭐⭐ 高中数学认知发展理论+数系扩展思维指导
-- 重点：实函数概念→复数概念→复数函数的数系扩展和思维发展
-- 高中特色：从实数系统到复数系统的重大认知跨越，体现数学系统化思维发展
-- ============================================

-- 【函数基础思维→复数系统建构认知链分析】
-- 1. 函数基础概念→复数基础概念建构（8条关系）
-- 2. 函数表示方法→复数表示方法（8条关系）
-- 3. 函数运算性质→复数运算系统（8条关系）
-- 4. 函数图象性质→复数几何意义（8条关系）
-- 5. 函数方程求解→复数方程应用（8条关系）

-- ============================================
-- 1. 函数基础概念→复数基础概念建构（8条关系）
-- ============================================

-- 【函数图象画法为复数几何意义提供几何表示基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH03_006'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH07_006'), 
 'extension', 0.91, 0.87, 90, 0.7, 0.89, 'horizontal', 0, 0.93, 0.90, 
 '{"liberal_arts_notes": "函数图象的几何表示方法为复数几何意义提供坐标平面表示思维基础", "science_notes": "几何表示方法在复数几何意义理解中的直接应用"}', true),

-- 【函数解析式求法为复数代数形式提供代数表示基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH03_007'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH07_003'), 
 'extension', 0.89, 0.85, 90, 0.6, 0.87, 'horizontal', 0, 0.91, 0.88, 
 '{"liberal_arts_notes": "函数解析式求解方法为复数代数形式提供符号表示和代数操作基础", "science_notes": "代数表示方法在复数代数形式中的应用"}', true),

-- 【函数单调性为复数模函数性质提供单调性分析基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH03_008'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH07_007'), 
 'related', 0.84, 0.80, 90, 0.8, 0.82, 'horizontal', 0, 0.86, 0.83, 
 '{"liberal_arts_notes": "函数单调性分析方法为复数模函数提供函数性质分析思维基础", "science_notes": "函数性质分析在复数模函数中的应用"}', true),

-- 【函数单调性判断为复数比较提供大小关系分析基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH03_009'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH07_005'), 
 'related', 0.82, 0.78, 90, 0.7, 0.80, 'horizontal', 0, 0.84, 0.81, 
 '{"liberal_arts_notes": "函数单调性判断方法为复数相等条件提供比较分析思维基础", "science_notes": "单调性判断方法在复数相等判定中的应用"}', true),

-- 【函数最值概念为复数模最值提供极值分析基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH03_010'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH07_007'), 
 'application_of', 0.86, 0.82, 90, 0.8, 0.84, 'horizontal', 0, 0.88, 0.85, 
 '{"liberal_arts_notes": "函数最值概念为复数模的最值问题提供极值分析思维基础", "science_notes": "最值分析方法在复数模优化问题中的直接应用"}', true),

-- 【函数最值求法为复数运算优化提供优化方法基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH03_011'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH07_012'), 
 'application_of', 0.87, 0.83, 90, 0.9, 0.85, 'horizontal', 0, 0.89, 0.86, 
 '{"liberal_arts_notes": "函数最值求解方法为复数运算几何意义提供优化分析方法基础", "science_notes": "优化方法在复数几何运算中的应用"}', true),

-- 【函数奇偶性为复数对称性质提供对称分析基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH03_012'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH07_008'), 
 'related', 0.85, 0.81, 90, 0.7, 0.83, 'horizontal', 0, 0.87, 0.84, 
 '{"liberal_arts_notes": "函数奇偶性的对称性质为共轭复数提供对称关系分析基础", "science_notes": "对称性质分析在复数共轭关系中的应用"}', true),

-- 【函数奇偶性判断为复数性质判断提供性质分析基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH03_013'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH07_004'), 
 'related', 0.83, 0.79, 90, 0.6, 0.81, 'horizontal', 0, 0.85, 0.82, 
 '{"liberal_arts_notes": "函数性质判断方法为复数分类提供性质判断思维基础", "science_notes": "性质判断方法在复数分类中的应用"}', true),

-- ============================================
-- 2. 函数表示方法→复数表示方法（8条关系）
-- ============================================

-- 【幂函数概念为复数幂运算提供幂运算基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH03_015'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH07_010'), 
 'extension', 0.88, 0.84, 90, 0.8, 0.86, 'horizontal', 0, 0.90, 0.87, 
 '{"liberal_arts_notes": "幂函数概念为复数乘法运算提供幂运算的概念基础", "science_notes": "幂运算概念在复数运算中的扩展和应用"}', true),

-- 【幂函数图象性质为复数乘法几何意义提供几何基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH03_016'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH07_012'), 
 'related', 0.85, 0.81, 90, 0.7, 0.83, 'horizontal', 0, 0.87, 0.84, 
 '{"liberal_arts_notes": "幂函数图象性质为复数乘法几何意义提供图象分析思维基础", "science_notes": "图象性质分析在复数几何运算中的应用"}', true),

-- 【幂函数应用为复数应用提供应用思维基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH03_017'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH07_018'), 
 'related', 0.82, 0.78, 90, 0.6, 0.80, 'horizontal', 0, 0.84, 0.81, 
 '{"liberal_arts_notes": "幂函数的实际应用为复数三角形式应用提供应用思维基础", "science_notes": "数学应用思维在复数应用中的体现"}', true),

-- 【函数零点概念为复数方程根提供根的概念基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH03_018'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH07_011'), 
 'extension', 0.89, 0.85, 90, 0.8, 0.87, 'horizontal', 0, 0.91, 0.88, 
 '{"liberal_arts_notes": "函数零点概念为复数除法运算提供方程求根的概念基础", "science_notes": "零点概念在复数方程求解中的扩展应用"}', true),

-- 【零点存在性定理为复数根存在性提供存在性理论基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH03_019'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH07_001'), 
 'extension', 0.87, 0.83, 90, 0.9, 0.85, 'horizontal', 0, 0.89, 0.86, 
 '{"liberal_arts_notes": "零点存在性定理为复数引入提供数系扩展的理论必要性基础", "science_notes": "存在性定理在复数系统建立中的理论支撑作用"}', true),

-- 【二分法求近似解为复数数值计算提供数值方法基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH03_020'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH07_011'), 
 'related', 0.83, 0.79, 90, 0.7, 0.81, 'horizontal', 0, 0.85, 0.82, 
 '{"liberal_arts_notes": "二分法数值求解为复数除法计算提供数值计算思维基础", "science_notes": "数值计算方法在复数运算中的应用"}', true),

-- 【函数模型建立为复数模型提供建模思维基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH03_021'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH07_013'), 
 'extension', 0.85, 0.81, 90, 0.8, 0.83, 'horizontal', 0, 0.87, 0.84, 
 '{"liberal_arts_notes": "函数建模思维为复数三角形式提供数学建模的思维基础", "science_notes": "建模思维在复数几何表示中的应用"}', true),

-- 【函数应用问题解决策略为复数问题解决提供策略思维基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH03_022'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH07_017'), 
 'related', 0.84, 0.80, 90, 0.7, 0.82, 'horizontal', 0, 0.86, 0.83, 
 '{"liberal_arts_notes": "函数应用问题解决策略为复数乘法应用提供问题解决思维基础", "science_notes": "问题解决策略在复数应用中的体现"}', true),

-- ============================================
-- 3. 函数运算性质→复数运算系统（8条关系）
-- ============================================

-- 【根式概念为复数开方运算提供根式运算基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH04_001'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH07_002'), 
 'extension', 0.92, 0.88, 60, 0.8, 0.90, 'horizontal', 0, 0.94, 0.91, 
 '{"liberal_arts_notes": "根式概念为虚数单位i的理解提供根式运算的概念基础", "science_notes": "根式运算在虚数单位定义中的核心作用"}', true),

-- 【根式性质为复数运算性质提供运算规律基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH04_002'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH07_015'), 
 'extension', 0.89, 0.85, 60, 0.7, 0.87, 'horizontal', 0, 0.91, 0.88, 
 '{"liberal_arts_notes": "根式性质为复数三角形式运算提供运算规律和性质基础", "science_notes": "根式性质在复数三角运算中的应用"}', true),

-- 【分数指数幂为复数指数运算提供指数运算基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH04_003'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH07_016'), 
 'extension', 0.87, 0.83, 60, 0.8, 0.85, 'horizontal', 0, 0.89, 0.86, 
 '{"liberal_arts_notes": "分数指数幂概念为复数乘方运算提供指数运算的理论基础", "science_notes": "指数运算在复数幂运算中的扩展应用"}', true),

-- 【实数指数幂运算为复数幂运算提供运算方法基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH04_004'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH07_017'), 
 'extension', 0.88, 0.84, 60, 0.7, 0.86, 'horizontal', 0, 0.90, 0.87, 
 '{"liberal_arts_notes": "实数指数幂运算法则为复数乘法应用提供运算方法基础", "science_notes": "指数幂运算法则在复数运算中的直接应用"}', true),

-- 【指数函数概念为复数指数函数提供函数概念基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH04_005'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH07_014'), 
 'extension', 0.91, 0.87, 60, 0.9, 0.89, 'horizontal', 0, 0.93, 0.90, 
 '{"liberal_arts_notes": "指数函数概念为复数欧拉公式提供指数函数的理论基础", "science_notes": "指数函数概念在复数指数表示中的核心应用"}', true),

-- 【指数函数图象性质为复数模性质提供图象分析基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH04_006'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH07_007'), 
 'related', 0.84, 0.80, 60, 0.6, 0.82, 'horizontal', 0, 0.86, 0.83, 
 '{"liberal_arts_notes": "指数函数图象性质为复数模的性质提供图象分析思维基础", "science_notes": "函数图象性质在复数模分析中的应用"}', true),

-- 【指数函数应用为复数周期性提供周期函数基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH04_007'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH07_018'), 
 'related', 0.86, 0.82, 60, 0.8, 0.84, 'horizontal', 0, 0.88, 0.85, 
 '{"liberal_arts_notes": "指数函数应用为复数三角表示应用提供周期性函数应用基础", "science_notes": "指数函数应用在复数周期性中的体现"}', true),

-- 【指数方程解法为复数方程求解提供方程求解基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH04_008'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH07_009'), 
 'prerequisite', 0.89, 0.85, 60, 0.7, 0.87, 'horizontal', 0, 0.91, 0.88, 
 '{"liberal_arts_notes": "指数方程求解方法为复数加减法提供方程求解的基础技能", "science_notes": "方程求解技能在复数运算中的基础应用"}', true),

-- ============================================
-- 4. 函数图象性质→复数几何意义（8条关系）
-- ============================================

-- 【对数概念为复数对数提供对数运算基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH04_009'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH07_015'), 
 'extension', 0.90, 0.86, 60, 0.8, 0.88, 'horizontal', 0, 0.92, 0.89, 
 '{"liberal_arts_notes": "对数概念为复数三角形式运算提供对数运算的理论基础", "science_notes": "对数概念在复数指数表示中的理论应用"}', true),

-- 【对数性质为复数运算性质提供对数性质基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH04_010'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH07_016'), 
 'extension', 0.87, 0.83, 60, 0.7, 0.85, 'horizontal', 0, 0.89, 0.86, 
 '{"liberal_arts_notes": "对数性质为复数乘方运算提供对数性质的运算基础", "science_notes": "对数性质在复数对数运算中的直接应用"}', true),

-- 【对数运算法则为复数运算法则提供运算规律基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH04_011'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH07_017'), 
 'extension', 0.88, 0.84, 60, 0.8, 0.86, 'horizontal', 0, 0.90, 0.87, 
 '{"liberal_arts_notes": "对数运算法则为复数乘法应用提供运算法则的理论基础", "science_notes": "运算法则在复数运算中的系统应用"}', true),

-- 【换底公式为复数换算提供换算方法基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH04_012'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH07_013'), 
 'related', 0.85, 0.81, 60, 0.7, 0.83, 'horizontal', 0, 0.87, 0.84, 
 '{"liberal_arts_notes": "换底公式的换算思维为复数三角形式提供不同表示形式换算基础", "science_notes": "换算方法在复数不同表示形式中的应用"}', true),

-- 【常用对数与自然对数为复数自然表示提供自然对数基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH04_013'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH07_014'), 
 'extension', 0.89, 0.85, 60, 0.8, 0.87, 'horizontal', 0, 0.91, 0.88, 
 '{"liberal_arts_notes": "自然对数概念为复数欧拉公式提供自然对数的理论基础", "science_notes": "自然对数在复数欧拉公式中的核心应用"}', true),

-- 【对数函数概念为复数对数函数提供函数概念基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH04_014'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH07_018'), 
 'extension', 0.86, 0.82, 60, 0.9, 0.84, 'horizontal', 0, 0.88, 0.85, 
 '{"liberal_arts_notes": "对数函数概念为复数三角表示应用提供对数函数的理论基础", "science_notes": "对数函数概念在复数函数中的扩展应用"}', true),

-- 【对数函数图象性质为复数幅角性质提供角度分析基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH04_015'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH07_013'), 
 'related', 0.83, 0.79, 60, 0.6, 0.81, 'horizontal', 0, 0.85, 0.82, 
 '{"liberal_arts_notes": "对数函数图象性质为复数三角形式提供角度和幅角分析基础", "science_notes": "函数图象分析在复数幅角分析中的应用"}', true),

-- 【对数函数应用为复数实际应用提供应用建模基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH04_016'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH07_012'), 
 'related', 0.84, 0.80, 60, 0.7, 0.82, 'horizontal', 0, 0.86, 0.83, 
 '{"liberal_arts_notes": "对数函数应用建模为复数运算几何意义提供应用建模思维基础", "science_notes": "应用建模思维在复数几何应用中的体现"}', true),

-- ============================================
-- 5. 函数方程求解→复数方程应用（8条关系）
-- ============================================

-- 【对数方程解法为复数方程求解提供方程求解方法基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH04_017'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH07_010'), 
 'prerequisite', 0.87, 0.83, 60, 0.8, 0.85, 'horizontal', 0, 0.89, 0.86, 
 '{"liberal_arts_notes": "对数方程求解技能为复数乘法运算提供方程求解的基础技能", "science_notes": "方程求解技能在复数代数运算中的应用"}', true),

-- 【对数不等式解法为复数不等关系提供不等式分析基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH04_018'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH07_005'), 
 'related', 0.82, 0.78, 60, 0.7, 0.80, 'horizontal', 0, 0.84, 0.81, 
 '{"liberal_arts_notes": "对数不等式分析为复数相等条件提供不等关系分析思维基础", "science_notes": "不等式分析思维在复数相等判定中的应用"}', true),

-- 【函数应用二为复数工程应用提供实际应用基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH04_019'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH07_018'), 
 'application_of', 0.85, 0.81, 60, 0.8, 0.83, 'horizontal', 0, 0.87, 0.84, 
 '{"liberal_arts_notes": "函数应用建模为复数三角表示应用提供实际应用建模基础", "science_notes": "函数建模思维在复数工程应用中的体现"}', true),

-- 【复合函数模型为复数复合运算提供复合思维基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH04_020'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH07_011'), 
 'extension', 0.86, 0.82, 60, 0.8, 0.84, 'horizontal', 0, 0.88, 0.85, 
 '{"liberal_arts_notes": "复合函数思维为复数除法运算提供复合运算的思维基础", "science_notes": "复合运算思维在复数运算中的应用"}', true),

-- 【任意角概念为复数幅角提供角度概念基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH05_001'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH07_013'), 
 'prerequisite', 0.93, 0.89, 30, 0.6, 0.91, 'horizontal', 0, 0.95, 0.92, 
 '{"liberal_arts_notes": "任意角概念为复数三角形式的幅角提供角度概念的基础", "science_notes": "任意角概念在复数三角表示中的基础作用"}', true),

-- 【弧度制为复数角度测量提供度量单位基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH05_002'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH07_014'), 
 'prerequisite', 0.90, 0.86, 30, 0.5, 0.88, 'horizontal', 0, 0.92, 0.89, 
 '{"liberal_arts_notes": "弧度制度量单位为复数欧拉公式提供角度测量的标准基础", "science_notes": "弧度制在复数角度计算中的标准化应用"}', true),

-- 【终边相同角为复数周期性提供周期概念基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH05_003'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH07_015'), 
 'extension', 0.88, 0.84, 30, 0.7, 0.86, 'horizontal', 0, 0.90, 0.87, 
 '{"liberal_arts_notes": "终边相同角的周期性为复数三角形式运算提供周期性概念基础", "science_notes": "角度周期性在复数三角运算中的应用"}', true),

-- 【象限角为复数象限分析提供象限思维基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH05_004'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH07_006'), 
 'extension', 0.85, 0.81, 30, 0.6, 0.83, 'horizontal', 0, 0.87, 0.84, 
 '{"liberal_arts_notes": "象限角分析为复数几何意义提供坐标象限分析思维基础", "science_notes": "象限分析在复数几何表示中的应用"}', true),

-- ============================================
-- 第三批：二次函数几何→立体几何基础（45条）- 专家权威版
-- 覆盖：高中必修第一册A二次函数几何知识点 → 高中必修第二册A立体几何初步
-- 审查标准：⭐⭐⭐⭐⭐ 函数几何思维+空间几何认知+数形结合思想指导
-- 重点：二次函数几何特性向立体几何空间认知的思维跨越发展
-- 高中特色：从平面函数图象到空间几何图形的认知维度提升
-- ============================================

-- 【二次函数几何→立体几何基础认知链分析】
-- 1. 二次函数图象性质→立体几何图形认知（9条关系）
-- 2. 二次函数对称性质→立体几何对称观念（9条关系）
-- 3. 二次方程几何解法→立体几何计算方法（9条关系）
-- 4. 二次不等式区域→立体几何空间关系（9条关系）
-- 5. 二次函数几何应用→立体几何综合应用（9条关系）

-- ============================================
-- 1. 二次函数图象性质→立体几何图形认知（9条关系）
-- ============================================

-- 【二次函数图象与性质为空间几何体概念提供图形认知基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH02_009'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_001'), 
 'extension', 0.88, 0.84, 30, 0.8, 0.86, 'horizontal', 0, 0.90, 0.87, 
 '{"liberal_arts_notes": "二次函数图象性质分析为空间几何体概念提供图形认知和性质分析基础", "science_notes": "平面函数图象向空间几何图形的认知维度拓展"}', true),

-- 【二次函数开口方向为立体图形方向提供方向认知基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH02_009'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_002'), 
 'related', 0.84, 0.80, 30, 0.7, 0.82, 'horizontal', 0, 0.86, 0.83, 
 '{"liberal_arts_notes": "二次函数开口方向为立体图形朝向提供方向感知和空间定向基础", "science_notes": "方向概念从平面到空间的认知迁移"}', true),

-- 【二次函数顶点概念为立体图形特殊点提供点概念基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH02_009'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_003'), 
 'extension', 0.86, 0.82, 30, 0.6, 0.84, 'horizontal', 0, 0.88, 0.85, 
 '{"liberal_arts_notes": "二次函数顶点概念为立体图形关键点提供特殊点认知和定位基础", "science_notes": "特殊点概念在几何图形中的认知拓展"}', true),

-- 【二次函数对称轴为立体图形对称提供对称认知基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH02_009'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_004'), 
 'extension', 0.89, 0.85, 30, 0.8, 0.87, 'horizontal', 0, 0.91, 0.88, 
 '{"liberal_arts_notes": "二次函数对称轴为立体图形对称性提供对称思维和对称分析基础", "science_notes": "对称概念从平面轴对称到空间对称的认知发展"}', true),

-- 【二次函数增减性为立体图形变化提供变化认知基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH02_009'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_005'), 
 'related', 0.85, 0.81, 30, 0.7, 0.83, 'horizontal', 0, 0.87, 0.84, 
 '{"liberal_arts_notes": "二次函数增减性为立体图形变化提供变化规律和趋势分析基础", "science_notes": "函数性质向几何性质的认知迁移"}', true),

-- 【二次函数最值为立体图形极值提供极值认知基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH02_009'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_006'), 
 'application_of', 0.87, 0.83, 30, 0.8, 0.85, 'horizontal', 0, 0.89, 0.86, 
 '{"liberal_arts_notes": "二次函数最值为立体图形极值提供最值分析和优化思维基础", "science_notes": "最值概念在几何优化问题中的应用"}', true),

-- 【二次函数定义域为立体图形范围提供范围认知基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH02_009'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_007'), 
 'related', 0.83, 0.79, 30, 0.6, 0.81, 'horizontal', 0, 0.85, 0.82, 
 '{"liberal_arts_notes": "二次函数定义域为立体图形存在范围提供范围概念和边界认知基础", "science_notes": "定义域概念向几何范围的认知拓展"}', true),

-- 【二次函数值域为立体图形测量提供测量概念基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH02_009'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_008'), 
 'related', 0.86, 0.82, 30, 0.7, 0.84, 'horizontal', 0, 0.88, 0.85, 
 '{"liberal_arts_notes": "二次函数值域为立体直观图表示提供测量范围和数值表示基础", "science_notes": "值域概念在立体图形测量中的应用"}', true),

-- 【二次函数图象变换为立体图形变换提供变换思维基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH02_009'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_009'), 
 'extension', 0.90, 0.86, 30, 0.9, 0.88, 'horizontal', 0, 0.92, 0.89, 
 '{"liberal_arts_notes": "二次函数图象变换为立体直观图绘制提供图形变换和图形表示基础", "science_notes": "图象变换思维在立体图形表示中的核心应用"}', true),

-- ============================================
-- 2. 二次函数对称性质→立体几何对称观念（9条关系）
-- ============================================

-- 【一元二次方程解法为平面公理提供方程思维基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH02_010'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_010'), 
 'prerequisite', 0.91, 0.87, 30, 0.6, 0.89, 'horizontal', 0, 0.93, 0.90, 
 '{"liberal_arts_notes": "一元二次方程求解技能为平面公理理解提供方程求解和逻辑推理基础", "science_notes": "方程求解技能在几何公理应用中的基础作用"}', true),

-- 【二次方程根的判别式为平面性质判定提供判定方法基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH02_011'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_011'), 
 'application_of', 0.88, 0.84, 30, 0.8, 0.86, 'horizontal', 0, 0.90, 0.87, 
 '{"liberal_arts_notes": "判别式判定方法为平面性质判定提供关系判定和逻辑分析基础", "science_notes": "判别式方法在几何性质判定中的直接应用"}', true),

-- 【一元二次不等式解法为空间点线关系提供不等关系基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH02_012'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_012'), 
 'extension', 0.85, 0.81, 30, 0.7, 0.83, 'horizontal', 0, 0.87, 0.84, 
 '{"liberal_arts_notes": "二次不等式解法为点线位置关系提供不等关系分析和关系判定基础", "science_notes": "不等式方法在空间位置关系中的认知拓展"}', true),

-- 【三个二次关系为空间线面关系提供系统关系基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH02_013'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_013'), 
 'extension', 0.89, 0.85, 30, 0.8, 0.87, 'horizontal', 0, 0.91, 0.88, 
 '{"liberal_arts_notes": "三个二次系统关系为线面位置关系提供系统性和完整性认知基础", "science_notes": "二次系统关系向空间几何关系的认知升级"}', true),

-- 【二次函数应用为直线平行关系提供应用建模基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH02_014'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_014'), 
 'application_of', 0.87, 0.83, 30, 0.7, 0.85, 'horizontal', 0, 0.89, 0.86, 
 '{"liberal_arts_notes": "二次函数应用建模为直线平行关系提供应用思维和建模分析基础", "science_notes": "函数应用思维在几何关系建模中的应用"}', true),

-- 【含参数二次不等式为直线垂直关系提供参数分析基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH02_015'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_015'), 
 'related', 0.84, 0.80, 30, 0.8, 0.82, 'horizontal', 0, 0.86, 0.83, 
 '{"liberal_arts_notes": "含参数不等式分析为直线垂直关系提供参数讨论和条件分析基础", "science_notes": "参数分析方法在几何关系讨论中的应用"}', true),

-- 【等式基本性质为直线共面关系提供等量关系基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH02_001'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_016'), 
 'prerequisite', 0.90, 0.86, 30, 0.5, 0.88, 'horizontal', 0, 0.92, 0.89, 
 '{"liberal_arts_notes": "等式基本性质为直线共面关系提供等量处理和逻辑推理基础", "science_notes": "等式性质在几何等量关系中的基础应用"}', true),

-- 【不等式基本性质为平面位置关系提供不等关系基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH02_002'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_017'), 
 'prerequisite', 0.88, 0.84, 30, 0.6, 0.86, 'horizontal', 0, 0.90, 0.87, 
 '{"liberal_arts_notes": "不等式基本性质为平面位置关系提供不等处理和关系分析基础", "science_notes": "不等式性质在几何位置关系中的基础应用"}', true),

-- 【不等式证明为平面相交关系提供证明方法基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH02_003'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_018'), 
 'application_of', 0.86, 0.82, 30, 0.7, 0.84, 'horizontal', 0, 0.88, 0.85, 
 '{"liberal_arts_notes": "不等式证明方法为平面相交关系提供逻辑证明和推理论证基础", "science_notes": "证明方法在几何关系证明中的直接应用"}', true),

-- ============================================
-- 3. 二次方程几何解法→立体几何计算方法（9条关系）
-- ============================================

-- 【比较大小方法为空间位置关系判定提供比较分析基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH02_004'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_019'), 
 'application_of', 0.85, 0.81, 30, 0.6, 0.83, 'horizontal', 0, 0.87, 0.84, 
 '{"liberal_arts_notes": "比较大小方法为空间位置关系提供关系比较和判定分析基础", "science_notes": "比较方法在空间关系判定中的应用"}', true),

-- 【基本不等式形式为空间距离关系提供不等式基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH02_005'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_020'), 
 'related', 0.83, 0.79, 30, 0.7, 0.81, 'horizontal', 0, 0.85, 0.82, 
 '{"liberal_arts_notes": "基本不等式形式为空间距离关系提供不等关系表示和分析基础", "science_notes": "不等式形式在空间距离分析中的应用"}', true),

-- 【基本不等式证明为空间角度关系提供证明基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH02_006'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_021'), 
 'application_of', 0.87, 0.83, 30, 0.8, 0.85, 'horizontal', 0, 0.89, 0.86, 
 '{"liberal_arts_notes": "基本不等式证明为空间角度关系提供不等式证明和逻辑推理基础", "science_notes": "不等式证明在空间角度关系中的应用"}', true),

-- 【基本不等式应用为立体图形表面积提供优化方法基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH02_007'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_022'), 
 'application_of', 0.90, 0.86, 30, 0.9, 0.88, 'horizontal', 0, 0.92, 0.89, 
 '{"liberal_arts_notes": "基本不等式应用为立体图形表面积提供最值求解和优化分析基础", "science_notes": "不等式优化方法在立体几何计算中的核心应用"}', true),

-- 【利用基本不等式求最值为立体图形体积提供最值基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH02_008'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_023'), 
 'application_of', 0.91, 0.87, 30, 0.9, 0.89, 'horizontal', 0, 0.93, 0.90, 
 '{"liberal_arts_notes": "基本不等式求最值为立体图形体积提供最值计算和优化求解基础", "science_notes": "最值求解方法在立体几何优化中的直接应用"}', true),

-- 【二次函数图象性质为立体图形性质提供性质分析基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH02_009'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_024'), 
 'extension', 0.88, 0.84, 30, 0.8, 0.86, 'horizontal', 0, 0.90, 0.87, 
 '{"liberal_arts_notes": "二次函数图象性质为立体图形性质提供性质理解和分析思维基础", "science_notes": "函数性质分析向几何性质分析的认知拓展"}', true),

-- 【一元二次方程解法为立体图形计算提供计算方法基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH02_010'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_025'), 
 'prerequisite', 0.89, 0.85, 30, 0.7, 0.87, 'horizontal', 0, 0.91, 0.88, 
 '{"liberal_arts_notes": "一元二次方程解法为立体图形计算提供方程求解和计算技能基础", "science_notes": "方程求解技能在立体几何计算中的基础应用"}', true),

-- 【判别式与根关系为立体图形关系判定提供判定思维基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH02_011'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_026'), 
 'related', 0.86, 0.82, 30, 0.8, 0.84, 'horizontal', 0, 0.88, 0.85, 
 '{"liberal_arts_notes": "判别式判定思维为立体图形关系提供关系判定和逻辑分析基础", "science_notes": "判定方法在几何关系分析中的应用"}', true),

-- 【一元二次不等式解法为立体图形综合应用提供解决策略基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH02_012'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_027'), 
 'application_of', 0.87, 0.83, 30, 0.8, 0.85, 'horizontal', 0, 0.89, 0.86, 
 '{"liberal_arts_notes": "二次不等式解法为立体图形综合应用提供问题解决和策略分析基础", "science_notes": "不等式解法在几何综合问题中的应用"}', true),

-- ============================================
-- 4. 二次不等式区域→立体几何空间关系（9条关系）
-- ============================================

-- 【三个二次关系为立体几何基础提供关系系统基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH02_013'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_028'), 
 'extension', 0.90, 0.86, 30, 0.8, 0.88, 'horizontal', 0, 0.92, 0.89, 
 '{"liberal_arts_notes": "三个二次系统关系为立体几何基础提供关系系统和整体认知基础", "science_notes": "系统关系思维在立体几何基础理解中的应用"}', true),

-- 【二次函数应用为立体几何应用提供应用思维基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH02_014'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_029'), 
 'application_of', 0.88, 0.84, 30, 0.9, 0.86, 'horizontal', 0, 0.90, 0.87, 
 '{"liberal_arts_notes": "二次函数应用建模为立体几何应用提供建模思维和应用策略基础", "science_notes": "函数应用建模在立体几何应用中的核心应用"}', true),

-- 【含参数二次不等式为立体几何参数讨论提供参数分析基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH02_015'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_001'), 
 'related', 0.84, 0.80, 60, 0.7, 0.82, 'horizontal', 0, 0.86, 0.83, 
 '{"liberal_arts_notes": "含参数不等式分析为空间几何体参数讨论提供参数分析和条件讨论基础", "science_notes": "参数分析方法在几何体参数问题中的应用"}', true),

-- 【等式基本性质为棱柱性质提供等量关系基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH02_001'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_002'), 
 'prerequisite', 0.87, 0.83, 60, 0.5, 0.85, 'horizontal', 0, 0.89, 0.86, 
 '{"liberal_arts_notes": "等式基本性质为棱柱性质提供等量处理和性质推理基础", "science_notes": "等式性质在几何体性质分析中的基础应用"}', true),

-- 【不等式基本性质为棱锥性质提供不等关系基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH02_002'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_003'), 
 'prerequisite', 0.85, 0.81, 60, 0.6, 0.83, 'horizontal', 0, 0.87, 0.84, 
 '{"liberal_arts_notes": "不等式基本性质为棱锥性质提供不等处理和关系分析基础", "science_notes": "不等式性质在几何体性质理解中的应用"}', true),

-- 【不等式证明为圆柱性质提供证明方法基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH02_003'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_004'), 
 'application_of', 0.83, 0.79, 60, 0.7, 0.81, 'horizontal', 0, 0.85, 0.82, 
 '{"liberal_arts_notes": "不等式证明方法为圆柱性质提供逻辑证明和推理论证基础", "science_notes": "证明方法在几何体性质证明中的应用"}', true),

-- 【比较大小方法为圆锥性质提供比较分析基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH02_004'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_005'), 
 'related', 0.82, 0.78, 60, 0.6, 0.80, 'horizontal', 0, 0.84, 0.81, 
 '{"liberal_arts_notes": "比较大小方法为圆锥性质提供数量比较和性质分析基础", "science_notes": "比较方法在几何体性质比较中的应用"}', true),

-- 【基本不等式形式为球体性质提供关系表示基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH02_005'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_006'), 
 'related', 0.86, 0.82, 60, 0.8, 0.84, 'horizontal', 0, 0.88, 0.85, 
 '{"liberal_arts_notes": "基本不等式形式为球体性质提供关系表示和性质描述基础", "science_notes": "不等式形式在几何体性质表示中的应用"}', true),

-- 【基本不等式证明为组合几何体提供证明基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH02_006'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_007'), 
 'application_of', 0.84, 0.80, 60, 0.8, 0.82, 'horizontal', 0, 0.86, 0.83, 
 '{"liberal_arts_notes": "基本不等式证明为组合几何体提供不等式证明和逻辑推理基础", "science_notes": "不等式证明在复合几何体分析中的应用"}', true),

-- ============================================
-- 5. 二次函数几何应用→立体几何综合应用（9条关系）
-- ============================================

-- 【基本不等式应用为立体直观图提供优化方法基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH02_007'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_008'), 
 'application_of', 0.87, 0.83, 60, 0.8, 0.85, 'horizontal', 0, 0.89, 0.86, 
 '{"liberal_arts_notes": "基本不等式应用为立体直观图提供优化绘制和比例优化基础", "science_notes": "优化方法在立体图形表示中的应用"}', true),

-- 【利用基本不等式求最值为斜二测画法提供最值优化基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH02_008'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_009'), 
 'application_of', 0.85, 0.81, 60, 0.9, 0.83, 'horizontal', 0, 0.87, 0.84, 
 '{"liberal_arts_notes": "基本不等式求最值为斜二测画法提供比例最值和绘图优化基础", "science_notes": "最值方法在立体图形绘制中的应用"}', true),

-- 【二次函数图象性质为平面公理提供图形性质基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH02_009'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_010'), 
 'extension', 0.89, 0.85, 60, 0.7, 0.87, 'horizontal', 0, 0.91, 0.88, 
 '{"liberal_arts_notes": "二次函数图象性质为平面公理提供图形理解和性质认知基础", "science_notes": "函数图象性质向几何公理的认知拓展"}', true),

-- 【一元二次方程解法为平面性质提供方程求解基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH02_010'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_011'), 
 'prerequisite', 0.88, 0.84, 60, 0.6, 0.86, 'horizontal', 0, 0.90, 0.87, 
 '{"liberal_arts_notes": "一元二次方程解法为平面性质提供方程技能和逻辑推理基础", "science_notes": "方程求解技能在几何性质证明中的基础应用"}', true),

-- 【判别式与根关系为点线面关系提供关系判定基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH02_011'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_012'), 
 'application_of', 0.86, 0.82, 60, 0.8, 0.84, 'horizontal', 0, 0.88, 0.85, 
 '{"liberal_arts_notes": "判别式判定方法为点线面关系提供关系判定和逻辑分析基础", "science_notes": "判别方法在空间几何关系中的直接应用"}', true),

-- 【一元二次不等式解法为线面位置关系提供不等式分析基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH02_012'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_013'), 
 'extension', 0.84, 0.80, 60, 0.7, 0.82, 'horizontal', 0, 0.86, 0.83, 
 '{"liberal_arts_notes": "二次不等式解法为线面位置关系提供不等分析和关系判定基础", "science_notes": "不等式方法在空间位置关系中的认知拓展"}', true),

-- 【三个二次关系为空间平行关系提供系统关系基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH02_013'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_014'), 
 'extension', 0.87, 0.83, 60, 0.8, 0.85, 'horizontal', 0, 0.89, 0.86, 
 '{"liberal_arts_notes": "三个二次系统关系为空间平行关系提供系统性和完整性认知基础", "science_notes": "系统关系思维在空间几何关系中的应用"}', true),

-- 【二次函数应用为空间垂直关系提供应用建模基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH02_014'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_015'), 
 'application_of', 0.85, 0.81, 60, 0.9, 0.83, 'horizontal', 0, 0.87, 0.84, 
 '{"liberal_arts_notes": "二次函数应用建模为空间垂直关系提供建模思维和应用策略基础", "science_notes": "函数应用建模在空间几何关系中的核心应用"}', true),

-- 【含参数二次不等式为空间相交关系提供参数分析基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH02_015'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_016'), 
 'related', 0.83, 0.79, 60, 0.8, 0.81, 'horizontal', 0, 0.85, 0.82, 
 '{"liberal_arts_notes": "含参数不等式分析为空间相交关系提供参数讨论和条件分析基础", "science_notes": "参数分析方法在空间几何关系讨论中的应用"}', true),

-- ============================================
-- 第四批：指数对数建模→统计概率基础（40条）- 专家权威版
-- 覆盖：高中必修第一册A指数对数函数建模知识点 → 高中必修第二册A统计概率基础
-- 审查标准：⭐⭐⭐⭐⭐ 指数对数建模思维+统计概率认知+数据分析思想指导
-- 重点：指数对数函数建模思维向统计概率数据分析的思维迁移发展
-- 高中特色：从确定性函数建模到随机性数据分析的认知跨越
-- ============================================

-- 【指数对数建模→统计概率基础认知链分析】
-- 1. 指数函数建模→统计随机抽样（8条关系）
-- 2. 对数函数性质→统计数据处理（8条关系）
-- 3. 指数对数方程→概率计算方法（8条关系）
-- 4. 函数应用建模→统计概率应用（8条关系）
-- 5. 增长衰减模型→相关性分析（8条关系）

-- ============================================
-- 1. 指数函数建模→统计随机抽样（8条关系）
-- ============================================

-- 【指数函数概念为随机抽样概念提供函数建模基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH04_005'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH09_001'), 
 'extension', 0.87, 0.83, 30, 0.8, 0.85, 'horizontal', 0, 0.89, 0.86, 
 '{"liberal_arts_notes": "指数函数概念为随机抽样概念提供函数建模和数量关系认知基础", "science_notes": "函数概念向统计概念的认知拓展"}', true),

-- 【指数函数图象与性质为抽样方法提供变化规律基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH04_006'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH09_002'), 
 'related', 0.84, 0.80, 30, 0.7, 0.82, 'horizontal', 0, 0.86, 0.83, 
 '{"liberal_arts_notes": "指数函数性质分析为抽样方法提供变化规律和趋势分析基础", "science_notes": "函数性质分析在统计抽样中的应用"}', true),

-- 【指数函数应用为系统抽样提供等比数列基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH04_007'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH09_003'), 
 'application_of', 0.86, 0.82, 30, 0.8, 0.84, 'horizontal', 0, 0.88, 0.85, 
 '{"liberal_arts_notes": "指数函数应用为系统抽样提供等间距选择和比例分析基础", "science_notes": "指数增长模型在系统抽样中的应用"}', true),

-- 【指数方程与不等式为分层抽样提供分层计算基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH04_008'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH09_004'), 
 'prerequisite', 0.85, 0.81, 30, 0.7, 0.83, 'horizontal', 0, 0.87, 0.84, 
 '{"liberal_arts_notes": "指数方程求解为分层抽样提供比例计算和分层分配基础", "science_notes": "方程求解技能在分层抽样计算中的应用"}', true),

-- 【实数指数幂为样本容量确定提供幂次计算基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH04_001'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH09_001'), 
 'prerequisite', 0.82, 0.78, 60, 0.6, 0.80, 'horizontal', 0, 0.84, 0.81, 
 '{"liberal_arts_notes": "实数指数幂为样本容量确定提供幂次运算和数量计算基础", "science_notes": "指数运算在统计计算中的基础应用"}', true),

-- 【指数函数性质为抽样误差分析提供误差模型基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH04_006'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH09_002'), 
 'extension', 0.83, 0.79, 60, 0.8, 0.81, 'horizontal', 0, 0.85, 0.82, 
 '{"liberal_arts_notes": "指数函数性质为抽样误差分析提供误差增长和变化模型基础", "science_notes": "指数性质在统计误差分析中的建模应用"}', true),

-- 【指数运算性质为抽样比例计算提供比例运算基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH04_003'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH09_003'), 
 'prerequisite', 0.88, 0.84, 60, 0.5, 0.86, 'horizontal', 0, 0.90, 0.87, 
 '{"liberal_arts_notes": "指数运算性质为抽样比例计算提供运算技能和计算方法基础", "science_notes": "指数运算技能在统计比例计算中的基础应用"}', true),

-- 【指数函数单调性为抽样代表性判断提供单调性分析基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH04_006'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH09_004'), 
 'related', 0.81, 0.77, 60, 0.7, 0.79, 'horizontal', 0, 0.83, 0.80, 
 '{"liberal_arts_notes": "指数函数单调性为抽样代表性提供趋势分析和规律判断基础", "science_notes": "函数单调性在统计代表性分析中的应用"}', true),

-- ============================================
-- 2. 对数函数性质→统计数据处理（8条关系）
-- ============================================

-- 【对数概念为数据估计提供对数变换基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH04_009'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH09_005'), 
 'extension', 0.89, 0.85, 30, 0.8, 0.87, 'horizontal', 0, 0.91, 0.88, 
 '{"liberal_arts_notes": "对数概念为数据估计提供对数变换和数据压缩认知基础", "science_notes": "对数概念在统计数据处理中的核心应用"}', true),

-- 【对数运算性质为频率分布提供频率计算基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH04_010'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH09_006'), 
 'application_of', 0.86, 0.82, 30, 0.7, 0.84, 'horizontal', 0, 0.88, 0.85, 
 '{"liberal_arts_notes": "对数运算性质为频率分布提供频率计算和概率变换基础", "science_notes": "对数运算在统计频率计算中的直接应用"}', true),

-- 【常用对数为数据变换提供标准化处理基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH04_011'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH09_007'), 
 'application_of', 0.87, 0.83, 30, 0.8, 0.85, 'horizontal', 0, 0.89, 0.86, 
 '{"liberal_arts_notes": "常用对数为数据变换提供标准化处理和数据标准化基础", "science_notes": "对数标准化在统计数据处理中的应用"}', true),

-- 【自然对数为数据建模提供自然增长模型基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH04_012'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH09_008'), 
 'extension', 0.85, 0.81, 30, 0.8, 0.83, 'horizontal', 0, 0.87, 0.84, 
 '{"liberal_arts_notes": "自然对数为数据建模提供自然增长和衰减模型分析基础", "science_notes": "自然对数在统计建模中的建模应用"}', true),

-- 【换底公式为数据转换提供转换计算基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH04_013'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH09_009'), 
 'application_of', 0.84, 0.80, 30, 0.7, 0.82, 'horizontal', 0, 0.86, 0.83, 
 '{"liberal_arts_notes": "换底公式为数据转换提供底数转换和计算转换基础", "science_notes": "换底公式在统计计算转换中的应用"}', true),

-- 【对数函数概念为统计量概念提供抽象概念基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH04_014'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH09_010'), 
 'extension', 0.88, 0.84, 30, 0.6, 0.86, 'horizontal', 0, 0.90, 0.87, 
 '{"liberal_arts_notes": "对数函数概念为统计量概念提供抽象函数和数学概念基础", "science_notes": "函数概念向统计概念的认知抽象化"}', true),

-- 【对数函数图象与性质为数据可视化提供图象分析基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH04_015'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH09_005'), 
 'extension', 0.90, 0.86, 60, 0.8, 0.88, 'horizontal', 0, 0.92, 0.89, 
 '{"liberal_arts_notes": "对数函数图象性质为数据可视化提供图象分析和视觉表示基础", "science_notes": "函数图象分析在统计可视化中的核心应用"}', true),

-- 【对数函数应用为统计推断提供应用建模基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH04_016'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH09_006'), 
 'application_of', 0.86, 0.82, 60, 0.9, 0.84, 'horizontal', 0, 0.88, 0.85, 
 '{"liberal_arts_notes": "对数函数应用为统计推断提供应用建模和推断分析基础", "science_notes": "对数应用建模在统计推断中的应用"}', true),

-- ============================================
-- 3. 指数对数方程→概率计算方法（8条关系）
-- ============================================

-- 【指数函数与对数函数关系为随机事件关系提供相互关系基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH04_017'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH10_001'), 
 'extension', 0.87, 0.83, 30, 0.8, 0.85, 'horizontal', 0, 0.89, 0.86, 
 '{"liberal_arts_notes": "指数对数互逆关系为随机事件关系提供相互关系和逆向分析基础", "science_notes": "互逆函数关系在概率关系分析中的应用"}', true),

-- 【指数方程对数方程为事件关系提供方程建模基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH04_018'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH10_002'), 
 'application_of', 0.85, 0.81, 30, 0.8, 0.83, 'horizontal', 0, 0.87, 0.84, 
 '{"liberal_arts_notes": "指数对数方程为事件关系提供方程建模和数量关系基础", "science_notes": "方程建模思维在概率关系中的应用"}', true),

-- 【函数应用二为概率模型提供复杂建模基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH04_019'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH10_003'), 
 'extension', 0.88, 0.84, 30, 0.9, 0.86, 'horizontal', 0, 0.90, 0.87, 
 '{"liberal_arts_notes": "函数应用建模为概率模型提供复杂建模和综合应用基础", "science_notes": "高级函数建模在概率建模中的核心应用"}', true),

-- 【复合函数模型为概率定义提供复合概念基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH04_020'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH10_004'), 
 'related', 0.84, 0.80, 30, 0.7, 0.82, 'horizontal', 0, 0.86, 0.83, 
 '{"liberal_arts_notes": "复合函数概念为概率定义提供复合概念和层次分析基础", "science_notes": "复合概念在概率定义理解中的应用"}', true),

-- 【指数增长模型为概率性质提供增长分析基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH04_007'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH10_005'), 
 'related', 0.82, 0.78, 60, 0.6, 0.80, 'horizontal', 0, 0.84, 0.81, 
 '{"liberal_arts_notes": "指数增长模型为概率性质提供增长变化和性质分析基础", "science_notes": "增长模型在概率性质理解中的应用"}', true),

-- 【对数衰减模型为条件概率提供条件分析基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH04_016'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH10_006'), 
 'application_of', 0.86, 0.82, 60, 0.8, 0.84, 'horizontal', 0, 0.88, 0.85, 
 '{"liberal_arts_notes": "对数衰减模型为条件概率提供条件分析和衰减计算基础", "science_notes": "衰减模型在条件概率计算中的应用"}', true),

-- 【指数函数单调性为事件独立性提供独立性分析基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH04_006'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH10_007'), 
 'related', 0.83, 0.79, 60, 0.7, 0.81, 'horizontal', 0, 0.85, 0.82, 
 '{"liberal_arts_notes": "指数函数单调性为事件独立性提供独立性判断和单调性分析基础", "science_notes": "函数单调性在概率独立性分析中的应用"}', true),

-- 【对数函数单调性为独立事件提供独立性判定基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH04_015'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH10_008'), 
 'related', 0.81, 0.77, 60, 0.6, 0.79, 'horizontal', 0, 0.83, 0.80, 
 '{"liberal_arts_notes": "对数函数单调性为独立事件提供独立性判定和关系分析基础", "science_notes": "对数单调性在概率独立性判定中的应用"}', true),

-- ============================================
-- 4. 函数应用建模→统计概率应用（8条关系）
-- ============================================

-- 【指数衰减模型为事件概率提供衰减概率基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH04_007'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH10_009'), 
 'application_of', 0.87, 0.83, 30, 0.8, 0.85, 'horizontal', 0, 0.89, 0.86, 
 '{"liberal_arts_notes": "指数衰减模型为事件概率提供衰减概率和时间概率基础", "science_notes": "衰减模型在概率时间分析中的核心应用"}', true),

-- 【对数增长模型为频率分析提供增长频率基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH04_016'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH10_010'), 
 'application_of', 0.85, 0.81, 30, 0.8, 0.83, 'horizontal', 0, 0.87, 0.84, 
 '{"liberal_arts_notes": "对数增长模型为频率分析提供增长频率和频率建模基础", "science_notes": "对数增长在频率分析建模中的应用"}', true),

-- 【复合函数建模为大数定律提供复合规律基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH04_020'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH10_011'), 
 'extension', 0.89, 0.85, 30, 0.9, 0.87, 'horizontal', 0, 0.91, 0.88, 
 '{"liberal_arts_notes": "复合函数建模为大数定律提供复合规律和统计规律基础", "science_notes": "复合建模思维在统计定律理解中的应用"}', true),

-- 【函数应用建模为统计概率提供建模思维基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH04_019'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH10_012'), 
 'extension', 0.90, 0.86, 30, 0.9, 0.88, 'horizontal', 0, 0.92, 0.89, 
 '{"liberal_arts_notes": "函数应用建模为统计概率提供建模思维和应用策略基础", "science_notes": "函数建模思维在统计概率应用中的核心应用"}', true),

-- 【指数对数综合为几何概率提供几何建模基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH04_017'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH10_013'), 
 'application_of', 0.86, 0.82, 60, 0.8, 0.84, 'horizontal', 0, 0.88, 0.85, 
 '{"liberal_arts_notes": "指数对数综合为几何概率提供几何建模和面积计算基础", "science_notes": "指数对数综合在几何概率中的建模应用"}', true),

-- 【增长衰减综合为古典概率提供计数模型基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH04_007'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH10_014'), 
 'related', 0.84, 0.80, 60, 0.7, 0.82, 'horizontal', 0, 0.86, 0.83, 
 '{"liberal_arts_notes": "增长衰减模型为古典概率提供计数建模和组合分析基础", "science_notes": "增长衰减在古典概率计数中的应用"}', true),

-- 【函数建模思维为概率计算提供计算建模基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH04_019'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH10_015'), 
 'application_of', 0.88, 0.84, 60, 0.8, 0.86, 'horizontal', 0, 0.90, 0.87, 
 '{"liberal_arts_notes": "函数建模思维为概率计算提供计算建模和策略分析基础", "science_notes": "建模思维在概率计算中的策略应用"}', true),

-- ============================================
-- 5. 增长衰减模型→相关性分析（8条关系）
-- ============================================

-- 【指数增长模型为相关关系提供增长关系基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH04_007'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH09_011'), 
 'extension', 0.88, 0.84, 30, 0.8, 0.86, 'horizontal', 0, 0.90, 0.87, 
 '{"liberal_arts_notes": "指数增长模型为相关关系提供增长关系和变量关系基础", "science_notes": "指数增长模型在相关性分析中的建模应用"}', true),

-- 【对数变换模型为散点图提供数据变换基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH04_016'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH09_012'), 
 'application_of', 0.86, 0.82, 30, 0.8, 0.84, 'horizontal', 0, 0.88, 0.85, 
 '{"liberal_arts_notes": "对数变换模型为散点图提供数据变换和图形表示基础", "science_notes": "对数变换在散点图数据处理中的应用"}', true),

-- 【函数拟合建模为回归分析提供拟合方法基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH04_019'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH09_013'), 
 'extension', 0.90, 0.86, 30, 0.9, 0.88, 'horizontal', 0, 0.92, 0.89, 
 '{"liberal_arts_notes": "函数拟合建模为回归分析提供拟合方法和建模策略基础", "science_notes": "函数拟合在回归分析中的核心建模应用"}', true),

-- 【复合函数关系为相关系数提供关系量化基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH04_020'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH09_014'), 
 'application_of', 0.87, 0.83, 30, 0.8, 0.85, 'horizontal', 0, 0.89, 0.86, 
 '{"liberal_arts_notes": "复合函数关系为相关系数提供关系量化和数值分析基础", "science_notes": "复合函数关系在相关系数计算中的应用"}', true),

-- 【指数对数互逆为回归方程提供逆向建模基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH04_017'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH09_015'), 
 'extension', 0.85, 0.81, 60, 0.8, 0.83, 'horizontal', 0, 0.87, 0.84, 
 '{"liberal_arts_notes": "指数对数互逆关系为回归方程提供逆向建模和双向分析基础", "science_notes": "互逆关系在回归建模中的双向应用"}', true),

-- 【增长模型分析为回归预测提供预测建模基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH04_007'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH09_016'), 
 'application_of', 0.89, 0.85, 60, 0.9, 0.87, 'horizontal', 0, 0.91, 0.88, 
 '{"liberal_arts_notes": "增长模型分析为回归预测提供预测建模和趋势分析基础", "science_notes": "增长模型在回归预测中的核心预测应用"}', true),

-- 【对数回归建模为变量分析提供非线性建模基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH04_016'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH09_011'), 
 'extension', 0.84, 0.80, 60, 0.8, 0.82, 'horizontal', 0, 0.86, 0.83, 
 '{"liberal_arts_notes": "对数回归建模为变量分析提供非线性建模和曲线拟合基础", "science_notes": "对数回归在非线性相关分析中的建模应用"}', true),

-- 【指数拟合建模为数据挖掘提供挖掘建模基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH04_007'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH09_012'), 
 'extension', 0.86, 0.82, 60, 0.8, 0.84, 'horizontal', 0, 0.88, 0.85, 
 '{"liberal_arts_notes": "指数拟合建模为数据挖掘提供挖掘建模和模式识别基础", "science_notes": "指数拟合在数据挖掘模式分析中的应用"}', true),

-- ============================================
-- 【第五批】三角函数系统→向量数量运算（35条关系）
-- 批次编号：5/10
-- 学科跨越：三角函数知识体系→向量数量运算体系
-- 认知跨越：三角函数系统化运算→向量代数几何综合
-- 核心价值：建立三角与向量的深度融合，为解析几何奠定基础
-- 预期成果：35条高质量关系，累计195条关系
-- ============================================

-- ============================================
-- 1. 三角函数概念→向量概念运算（7条关系）
-- ============================================

-- 【任意角概念为向量概念提供角度基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH05_001'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH06_001'), 
 'extension', 0.89, 0.85, 30, 0.8, 0.87, 'horizontal', 0, 0.91, 0.88, 
 '{"liberal_arts_notes": "任意角概念为向量概念提供角度基础和方向概念认知基础", "science_notes": "任意角概念在向量方向角理解中的基础应用"}', true),

-- 【弧度制概念为向量模概念提供度量基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH05_004'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH06_003'), 
 'extension', 0.86, 0.82, 30, 0.8, 0.84, 'horizontal', 0, 0.88, 0.85, 
 '{"liberal_arts_notes": "弧度制概念为向量模概念提供度量基础和数值度量认知基础", "science_notes": "弧度制度量在向量模长计算中的基础应用"}', true),

-- 【三角函数定义为向量表示提供坐标基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH05_007'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH06_002'), 
 'extension', 0.91, 0.87, 30, 0.9, 0.89, 'horizontal', 0, 0.93, 0.90, 
 '{"liberal_arts_notes": "三角函数定义为向量表示提供坐标基础和坐标表示认知基础", "science_notes": "三角函数定义在向量坐标表示中的核心基础应用"}', true),

-- 【单位圆三角函数为单位向量提供单位概念基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH05_008'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH06_004'), 
 'extension', 0.88, 0.84, 30, 0.8, 0.86, 'horizontal', 0, 0.90, 0.87, 
 '{"liberal_arts_notes": "单位圆三角函数为单位向量提供单位概念和标准化认知基础", "science_notes": "单位圆概念在单位向量理解中的标准化应用"}', true),

-- 【特殊角三角函数值为向量关系提供特殊值基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH05_010'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH06_005'), 
 'related', 0.85, 0.81, 30, 0.7, 0.83, 'horizontal', 0, 0.87, 0.84, 
 '{"liberal_arts_notes": "特殊角三角函数值为向量关系提供特殊值和关系判定基础", "science_notes": "特殊角值在向量平行共线判定中的应用"}', true),

-- 【三角函数符号为向量方向提供符号判定基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH05_009'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH06_006'), 
 'related', 0.83, 0.79, 30, 0.7, 0.81, 'horizontal', 0, 0.85, 0.82, 
 '{"liberal_arts_notes": "三角函数符号为向量方向提供符号判定和方向识别基础", "science_notes": "三角函数符号在向量方向判断中的应用"}', true),

-- 【扇形弧长面积为向量几何应用提供几何量基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH05_006'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH06_020'), 
 'application_of', 0.84, 0.80, 60, 0.6, 0.82, 'horizontal', 0, 0.86, 0.83, 
 '{"liberal_arts_notes": "扇形弧长面积公式为向量几何应用提供几何量计算基础", "science_notes": "扇形几何量在向量物理应用中的计算应用"}', true),

-- ============================================
-- 2. 三角函数性质→向量性质应用（7条关系）
-- ============================================

-- 【正弦函数性质为向量加法提供性质分析基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH05_014'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH06_007'), 
 'extension', 0.87, 0.83, 30, 0.8, 0.85, 'horizontal', 0, 0.89, 0.86, 
 '{"liberal_arts_notes": "正弦函数性质为向量加法提供性质分析和运算规律基础", "science_notes": "正弦函数性质在向量加法运算分析中的应用"}', true),

-- 【余弦函数性质为向量减法提供性质基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH05_015'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH06_008'), 
 'extension', 0.85, 0.81, 30, 0.8, 0.83, 'horizontal', 0, 0.87, 0.84, 
 '{"liberal_arts_notes": "余弦函数性质为向量减法提供性质基础和运算分析基础", "science_notes": "余弦函数性质在向量减法运算中的应用"}', true),

-- 【正切函数性质为向量数乘提供比例分析基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH05_016'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH06_009'), 
 'related', 0.84, 0.80, 30, 0.7, 0.82, 'horizontal', 0, 0.86, 0.83, 
 '{"liberal_arts_notes": "正切函数性质为向量数乘提供比例分析和倍数关系基础", "science_notes": "正切函数比例性质在向量数乘运算中的应用"}', true),

-- 【三角函数周期性为向量几何意义提供周期分析基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH05_017'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH06_010'), 
 'extension', 0.86, 0.82, 60, 0.8, 0.84, 'horizontal', 0, 0.88, 0.85, 
 '{"liberal_arts_notes": "三角函数周期性为向量几何意义提供周期分析和几何理解基础", "science_notes": "周期性概念在向量几何意义理解中的应用"}', true),

-- 【同角三角函数关系为向量数量积提供关系基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH05_013'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH06_011'), 
 'extension', 0.92, 0.88, 30, 0.9, 0.90, 'horizontal', 0, 0.94, 0.91, 
 '{"liberal_arts_notes": "同角三角函数关系为向量数量积提供关系基础和运算规律基础", "science_notes": "同角关系在向量数量积运算中的核心基础应用"}', true),

-- 【诱导公式应用为向量数量积性质提供变换基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH05_013'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH06_012'), 
 'application_of', 0.89, 0.85, 30, 0.8, 0.87, 'horizontal', 0, 0.91, 0.88, 
 '{"liberal_arts_notes": "诱导公式应用为向量数量积性质提供变换基础和性质分析基础", "science_notes": "诱导公式在向量数量积性质推导中的应用"}', true),

-- 【三角函数值域为向量投影提供取值分析基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH05_014'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH06_013'), 
 'extension', 0.88, 0.84, 60, 0.7, 0.86, 'horizontal', 0, 0.90, 0.87, 
 '{"liberal_arts_notes": "三角函数值域为向量投影提供取值分析和投影范围基础", "science_notes": "三角函数值域在向量投影计算中的范围应用"}', true),

-- ============================================
-- 3. 三角恒等变换→向量数量积变换（7条关系）
-- ============================================

-- 【两角和差余弦公式为平面向量基本定理提供分解基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH05_018'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH06_014'), 
 'extension', 0.93, 0.89, 30, 0.9, 0.91, 'horizontal', 0, 0.95, 0.92, 
 '{"liberal_arts_notes": "两角和差余弦公式为平面向量基本定理提供分解基础和向量分解认知基础", "science_notes": "两角和差公式在向量基本定理理解中的核心基础应用"}', true),

-- 【两角和差正弦正切公式为向量坐标表示提供坐标变换基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH05_019'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH06_015'), 
 'application_of', 0.90, 0.86, 30, 0.8, 0.88, 'horizontal', 0, 0.92, 0.89, 
 '{"liberal_arts_notes": "两角和差正弦正切公式为向量坐标表示提供坐标变换和坐标运算基础", "science_notes": "两角和差公式在向量坐标变换中的变换应用"}', true),

-- 【二倍角公式为向量运算坐标表示提供倍角计算基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH05_020'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH06_016'), 
 'application_of', 0.88, 0.84, 30, 0.8, 0.86, 'horizontal', 0, 0.90, 0.87, 
 '{"liberal_arts_notes": "二倍角公式为向量运算坐标表示提供倍角计算和坐标运算基础", "science_notes": "二倍角公式在向量坐标运算中的计算应用"}', true),

-- 【余弦二倍角公式为向量共线坐标表示提供平方关系基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH05_020'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH06_017'), 
 'extension', 0.86, 0.82, 30, 0.8, 0.84, 'horizontal', 0, 0.88, 0.85, 
 '{"liberal_arts_notes": "余弦二倍角公式为向量共线坐标表示提供平方关系和线性关系基础", "science_notes": "二倍角公式在向量共线条件判定中的应用"}', true),

-- 【三角恒等变换综合为向量几何应用提供变换技巧基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH05_019'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH06_018'), 
 'application_of', 0.91, 0.87, 60, 0.9, 0.89, 'horizontal', 0, 0.93, 0.90, 
 '{"liberal_arts_notes": "三角恒等变换综合为向量几何应用提供变换技巧和几何变换基础", "science_notes": "三角恒等变换在向量几何应用中的核心变换应用"}', true),

-- 【角的变换技巧为向量三角形应用提供角度变换基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH05_018'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH06_019'), 
 'extension', 0.89, 0.85, 60, 0.8, 0.87, 'horizontal', 0, 0.91, 0.88, 
 '{"liberal_arts_notes": "角的变换技巧为向量三角形应用提供角度变换和三角分析基础", "science_notes": "角变换技巧在向量三角形问题中的应用"}', true),

-- 【恒等变换应用为向量物理应用提供公式变换基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH05_020'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH06_020'), 
 'application_of', 0.87, 0.83, 60, 0.8, 0.85, 'horizontal', 0, 0.89, 0.86, 
 '{"liberal_arts_notes": "恒等变换应用为向量物理应用提供公式变换和物理建模基础", "science_notes": "三角恒等变换在向量物理问题中的建模应用"}', true),

-- ============================================
-- 4. 三角函数图象→向量几何表示（7条关系）
-- ============================================

-- 【正弦函数图象为向量概念提供图形理解基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH05_014'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH06_001'), 
 'extension', 0.85, 0.81, 60, 0.7, 0.83, 'horizontal', 0, 0.87, 0.84, 
 '{"liberal_arts_notes": "正弦函数图象为向量概念提供图形理解和视觉表示基础", "science_notes": "正弦函数图象在向量概念理解中的图形应用"}', true),

-- 【余弦函数图象为向量模概念提供连续变化基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH05_015'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH06_003'), 
 'extension', 0.83, 0.79, 60, 0.7, 0.81, 'horizontal', 0, 0.85, 0.82, 
 '{"liberal_arts_notes": "余弦函数图象为向量模概念提供连续变化和长度变化基础", "science_notes": "余弦函数图象在向量模长理解中的连续性应用"}', true),

-- 【正切函数图象为向量方向角提供角度变化基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH05_016'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH06_002'), 
 'related', 0.84, 0.80, 60, 0.8, 0.82, 'horizontal', 0, 0.86, 0.83, 
 '{"liberal_arts_notes": "正切函数图象为向量方向角提供角度变化和倾斜表示基础", "science_notes": "正切函数图象在向量方向角理解中的应用"}', true),

-- 【三角函数周期图象为向量周期性提供周期理解基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH05_017'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH06_007'), 
 'extension', 0.86, 0.82, 60, 0.6, 0.84, 'horizontal', 0, 0.88, 0.85, 
 '{"liberal_arts_notes": "三角函数周期图象为向量周期性提供周期理解和重复模式基础", "science_notes": "周期图象在向量运算周期性理解中的应用"}', true),

-- 【三角函数对称性为向量对称变换提供对称分析基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH05_015'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH06_008'), 
 'related', 0.82, 0.78, 60, 0.7, 0.80, 'horizontal', 0, 0.84, 0.81, 
 '{"liberal_arts_notes": "三角函数对称性为向量对称变换提供对称分析和变换理解基础", "science_notes": "函数对称性在向量对称变换中的应用"}', true),

-- 【三角函数最值为向量极值提供极值分析基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH05_014'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH06_011'), 
 'related', 0.81, 0.77, 60, 0.6, 0.79, 'horizontal', 0, 0.83, 0.80, 
 '{"liberal_arts_notes": "三角函数最值为向量极值提供极值分析和最值判定基础", "science_notes": "三角函数最值在向量数量积极值中的应用"}', true),

-- 【函数图象变换为向量坐标变换提供变换理解基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH05_017'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH06_015'), 
 'extension', 0.87, 0.83, 60, 0.8, 0.85, 'horizontal', 0, 0.89, 0.86, 
 '{"liberal_arts_notes": "函数图象变换为向量坐标变换提供变换理解和几何变换基础", "science_notes": "图象变换在向量坐标变换理解中的几何应用"}', true),

-- ============================================
-- 5. 三角函数应用→向量坐标运算（7条关系）
-- ============================================

-- 【三角函数解三角形为向量坐标运算提供解算基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH05_013'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH06_016'), 
 'application_of', 0.90, 0.86, 30, 0.8, 0.88, 'horizontal', 0, 0.92, 0.89, 
 '{"liberal_arts_notes": "三角函数解三角形为向量坐标运算提供解算基础和运算策略基础", "science_notes": "三角函数解三角形在向量坐标运算中的核心计算应用"}', true),

-- 【特殊角应用为向量共线判定提供特殊值判定基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH05_010'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH06_017'), 
 'application_of', 0.86, 0.82, 30, 0.8, 0.84, 'horizontal', 0, 0.88, 0.85, 
 '{"liberal_arts_notes": "特殊角应用为向量共线判定提供特殊值判定和计算简化基础", "science_notes": "特殊角值在向量共线条件判定中的简化应用"}', true),



-- 【角度弧度转换为向量角度计算提供单位转换基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH05_005'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH06_019'), 
 'application_of', 0.84, 0.80, 30, 0.7, 0.82, 'horizontal', 0, 0.86, 0.83, 
 '{"liberal_arts_notes": "角度弧度转换为向量角度计算提供单位转换和度量转换基础", "science_notes": "角度弧度转换在向量角度问题中的度量应用"}', true),

-- 【三角函数周期应用为向量物理应用提供周期建模基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH05_017'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH06_020'), 
 'application_of', 0.89, 0.85, 60, 0.8, 0.87, 'horizontal', 0, 0.91, 0.88, 
 '{"liberal_arts_notes": "三角函数周期应用为向量物理应用提供周期建模和振动分析基础", "science_notes": "三角函数周期性在向量物理建模中的核心应用"}', true),

-- 【诱导公式化简为向量运算化简提供化简策略基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH05_012'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH06_016'), 
 'application_of', 0.87, 0.83, 30, 0.8, 0.85, 'horizontal', 0, 0.89, 0.86, 
 '{"liberal_arts_notes": "诱导公式化简为向量运算化简提供化简策略和运算优化基础", "science_notes": "诱导公式在向量运算化简中的策略应用"}', true),

-- 【三角函数建模为向量综合应用提供建模思维基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH05_018'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH06_020'), 
 'extension', 0.91, 0.87, 60, 0.9, 0.89, 'horizontal', 0, 0.93, 0.90, 
 '{"liberal_arts_notes": "三角函数建模为向量综合应用提供建模思维和综合分析基础", "science_notes": "三角函数建模思维在向量综合应用中的核心建模应用"}', true),

-- ============================================
-- 【第六批】函数性质分析→复数几何表示（30条关系）
-- 批次编号：6/10
-- 学科跨越：函数性质分析体系→复数几何表示体系
-- 认知跨越：函数性质抽象分析→复数几何直观表示
-- 核心价值：建立函数分析与复数几何的深度关联，提升抽象与直观结合能力
-- 预期成果：30条高质量关系，累计225条关系
-- ============================================

-- ============================================
-- 1. 函数单调性分析→复数模长变化（6条关系）
-- ============================================

-- 【函数单调性概念为复数模概念提供变化分析基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH03_008'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH07_007'), 
 'extension', 0.88, 0.84, 30, 0.8, 0.86, 'horizontal', 0, 0.90, 0.87, 
 '{"liberal_arts_notes": "函数单调性概念为复数模概念提供变化分析和单调变化认知基础", "science_notes": "函数单调性在复数模长变化分析中的基础应用"}', true),

-- 【函数单调性判断为复数几何意义提供位置分析基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH03_009'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH07_006'), 
 'application_of', 0.86, 0.82, 30, 0.8, 0.84, 'horizontal', 0, 0.88, 0.85, 
 '{"liberal_arts_notes": "函数单调性判断为复数几何意义提供位置分析和几何判断基础", "science_notes": "单调性判断方法在复数几何位置分析中的应用"}', true),

-- 【函数最值概念为复数辐角提供极值分析基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH03_010'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH07_014'), 
 'extension', 0.85, 0.81, 30, 0.7, 0.83, 'horizontal', 0, 0.87, 0.84, 
 '{"liberal_arts_notes": "函数最值概念为复数辐角提供极值分析和角度极值基础", "science_notes": "函数最值概念在复数辐角极值分析中的应用"}', true),

-- 【函数最值求法为复数三角形式提供计算方法基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH03_011'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH07_013'), 
 'application_of', 0.87, 0.83, 30, 0.8, 0.85, 'horizontal', 0, 0.89, 0.86, 
 '{"liberal_arts_notes": "函数最值求法为复数三角形式提供计算方法和形式转换基础", "science_notes": "最值求法在复数三角形式计算中的方法应用"}', true),

-- 【函数性质综合为复数运算几何意义提供综合分析基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH03_014'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH07_012'), 
 'extension', 0.90, 0.86, 60, 0.9, 0.88, 'horizontal', 0, 0.92, 0.89, 
 '{"liberal_arts_notes": "函数性质综合为复数运算几何意义提供综合分析和多维度认知基础", "science_notes": "函数性质综合分析在复数运算几何意义中的核心应用"}', true),

-- 【单调性变化规律为复数三角运算提供变化规律基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH03_008'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH07_015'), 
 'related', 0.84, 0.80, 60, 0.7, 0.82, 'horizontal', 0, 0.86, 0.83, 
 '{"liberal_arts_notes": "单调性变化规律为复数三角运算提供变化规律和运算模式基础", "science_notes": "单调性变化在复数三角形式运算中的规律应用"}', true),

-- ============================================
-- 2. 函数奇偶性分析→复数对称性质（6条关系）
-- ============================================

-- 【函数奇偶性概念为复数几何对称提供对称认知基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH03_012'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH07_006'), 
 'extension', 0.89, 0.85, 30, 0.8, 0.87, 'horizontal', 0, 0.91, 0.88, 
 '{"liberal_arts_notes": "函数奇偶性概念为复数几何对称提供对称认知和几何对称基础", "science_notes": "函数奇偶性在复数几何对称性理解中的基础应用"}', true),

-- 【函数奇偶性判断为共轭复数提供对称判断基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH03_013'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH07_008'), 
 'application_of', 0.87, 0.83, 30, 0.8, 0.85, 'horizontal', 0, 0.89, 0.86, 
 '{"liberal_arts_notes": "函数奇偶性判断为共轭复数提供对称判断和对称识别基础", "science_notes": "奇偶性判断方法在共轭复数识别中的应用"}', true),

-- 【函数图象对称为复数模长性质提供对称性质基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH03_012'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH07_007'), 
 'extension', 0.85, 0.81, 60, 0.7, 0.83, 'horizontal', 0, 0.87, 0.84, 
 '{"liberal_arts_notes": "函数图象对称为复数模长性质提供对称性质和长度对称基础", "science_notes": "函数对称性在复数模长对称性质中的应用"}', true),

-- 【奇偶函数性质为复数辐角关系提供角度关系基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH03_013'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH07_014'), 
 'related', 0.83, 0.79, 60, 0.7, 0.81, 'horizontal', 0, 0.85, 0.82, 
 '{"liberal_arts_notes": "奇偶函数性质为复数辐角关系提供角度关系和角度对称基础", "science_notes": "奇偶性质在复数辐角对称关系中的应用"}', true),

-- 【对称性分析为复数三角形式提供对称形式基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH03_014'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH07_013'), 
 'extension', 0.86, 0.82, 60, 0.8, 0.84, 'horizontal', 0, 0.88, 0.85, 
 '{"liberal_arts_notes": "对称性分析为复数三角形式提供对称形式和形式对称基础", "science_notes": "对称性分析在复数三角形式对称中的应用"}', true),

-- 【函数变换对称为复数运算对称提供变换对称基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH03_012'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH07_012'), 
 'related', 0.84, 0.80, 60, 0.6, 0.82, 'horizontal', 0, 0.86, 0.83, 
 '{"liberal_arts_notes": "函数变换对称为复数运算对称提供变换对称和运算对称基础", "science_notes": "函数变换对称在复数运算对称性中的应用"}', true),

-- ============================================
-- 3. 函数图象性质→复数几何表示（6条关系）
-- ============================================

-- 【函数定义域概念为复数平面提供区域概念基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH03_002'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH07_006'), 
 'extension', 0.87, 0.83, 30, 0.8, 0.85, 'horizontal', 0, 0.89, 0.86, 
 '{"liberal_arts_notes": "函数定义域概念为复数平面提供区域概念和平面区域认知基础", "science_notes": "定义域概念在复数平面区域理解中的基础应用"}', true),

-- 【函数值域概念为复数模范围提供范围分析基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH03_003'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH07_007'), 
 'extension', 0.85, 0.81, 30, 0.8, 0.83, 'horizontal', 0, 0.87, 0.84, 
 '{"liberal_arts_notes": "函数值域概念为复数模范围提供范围分析和取值范围基础", "science_notes": "值域概念在复数模长范围分析中的应用"}', true),

-- 【函数图象绘制为复数几何表示提供图形表示基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH03_006'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH07_006'), 
 'application_of', 0.90, 0.86, 60, 0.9, 0.88, 'horizontal', 0, 0.92, 0.89, 
 '{"liberal_arts_notes": "函数图象绘制为复数几何表示提供图形表示和视觉呈现基础", "science_notes": "函数图象绘制在复数几何表示中的核心图形应用"}', true),

-- 【分段函数概念为复数分类表示提供分类表示基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH03_005'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH07_013'), 
 'related', 0.84, 0.80, 30, 0.7, 0.82, 'horizontal', 0, 0.86, 0.83, 
 '{"liberal_arts_notes": "分段函数概念为复数分类表示提供分类表示和分段表示基础", "science_notes": "分段函数思维在复数三角形式分类中的应用"}', true),

-- 【函数表示方法为复数表示形式提供多元表示基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH03_004'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH07_013'), 
 'extension', 0.88, 0.84, 30, 0.8, 0.86, 'horizontal', 0, 0.90, 0.87, 
 '{"liberal_arts_notes": "函数表示方法为复数表示形式提供多元表示和形式多样性基础", "science_notes": "函数多元表示在复数多种表示形式中的方法应用"}', true),

-- 【解析式求法为复数计算方法提供解析方法基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH03_007'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH07_015'), 
 'application_of', 0.86, 0.82, 60, 0.8, 0.84, 'horizontal', 0, 0.88, 0.85, 
 '{"liberal_arts_notes": "解析式求法为复数计算方法提供解析方法和计算策略基础", "science_notes": "解析式方法在复数三角形式计算中的策略应用"}', true),

-- ============================================
-- 4. 函数变换规律→复数形式变换（6条关系）
-- ============================================

-- 【函数平移变换为复数坐标平移提供平移变换基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH03_006'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH07_012'), 
 'extension', 0.89, 0.85, 60, 0.8, 0.87, 'horizontal', 0, 0.91, 0.88, 
 '{"liberal_arts_notes": "函数平移变换为复数坐标平移提供平移变换和几何平移基础", "science_notes": "函数平移变换在复数几何平移中的变换应用"}', true),

-- 【函数伸缩变换为复数模长变换提供伸缩变换基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH03_014'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH07_007'), 
 'extension', 0.87, 0.83, 60, 0.8, 0.85, 'horizontal', 0, 0.89, 0.86, 
 '{"liberal_arts_notes": "函数伸缩变换为复数模长变换提供伸缩变换和长度变换基础", "science_notes": "函数伸缩变换在复数模长伸缩中的应用"}', true),

-- 【函数反射变换为复数共轭变换提供反射变换基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH03_012'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH07_008'), 
 'extension', 0.85, 0.81, 60, 0.8, 0.83, 'horizontal', 0, 0.87, 0.84, 
 '{"liberal_arts_notes": "函数反射变换为复数共轭变换提供反射变换和对称变换基础", "science_notes": "函数反射变换在复数共轭变换中的对称应用"}', true),

-- 【函数复合变换为复数形式转换提供复合变换基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH03_014'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH07_013'), 
 'application_of', 0.91, 0.87, 60, 0.9, 0.89, 'horizontal', 0, 0.93, 0.90, 
 '{"liberal_arts_notes": "函数复合变换为复数形式转换提供复合变换和形式变换基础", "science_notes": "函数复合变换在复数形式转换中的核心变换应用"}', true),

-- 【变换规律分析为复数运算规律提供规律分析基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH03_009'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH07_015'), 
 'related', 0.84, 0.80, 60, 0.7, 0.82, 'horizontal', 0, 0.86, 0.83, 
 '{"liberal_arts_notes": "变换规律分析为复数运算规律提供规律分析和运算模式基础", "science_notes": "变换规律在复数三角运算规律中的分析应用"}', true),



-- ============================================
-- 5. 函数应用建模→复数实际应用（6条关系）
-- ============================================

-- 【函数零点概念为复数方程解提供解的概念基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH03_018'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH07_018'), 
 'extension', 0.90, 0.86, 30, 0.8, 0.88, 'horizontal', 0, 0.92, 0.89, 
 '{"liberal_arts_notes": "函数零点概念为复数方程解提供解的概念和方程求解基础", "science_notes": "函数零点概念在复数方程求解中的核心基础应用"}', true),

-- 【零点存在性定理为复数存在性提供存在性判定基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH03_019'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH07_014'), 
 'extension', 0.86, 0.82, 30, 0.7, 0.84, 'horizontal', 0, 0.88, 0.85, 
 '{"liberal_arts_notes": "零点存在性定理为复数存在性提供存在性判定和辐角存在基础", "science_notes": "存在性定理在复数辐角存在性判定中的应用"}', true),

-- 【二分法求解为复数数值计算提供数值方法基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH03_020'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH07_015'), 
 'application_of', 0.87, 0.83, 30, 0.8, 0.85, 'horizontal', 0, 0.89, 0.86, 
 '{"liberal_arts_notes": "二分法求解为复数数值计算提供数值方法和计算策略基础", "science_notes": "二分法在复数三角形式数值计算中的方法应用"}', true),

-- 【函数建模思维为复数应用建模提供建模思维基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH03_021'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH07_018'), 
 'extension', 0.92, 0.88, 60, 0.9, 0.90, 'horizontal', 0, 0.94, 0.91, 
 '{"liberal_arts_notes": "函数建模思维为复数应用建模提供建模思维和应用策略基础", "science_notes": "函数建模思维在复数实际应用建模中的核心建模应用"}', true),

-- 【应用问题策略为复数问题解决提供策略方法基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH03_022'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH07_012'), 
 'application_of', 0.88, 0.84, 60, 0.8, 0.86, 'horizontal', 0, 0.90, 0.87, 
 '{"liberal_arts_notes": "应用问题策略为复数问题解决提供策略方法和解决方案基础", "science_notes": "应用问题解决策略在复数几何问题中的策略应用"}', true),

-- ============================================
-- 【第七批】数学抽象思维→立体几何证明（35条关系）
-- 批次编号：7/10
-- 学科跨越：数学抽象思维体系→立体几何证明体系
-- 认知跨越：抽象逻辑思维→空间几何推理
-- 核心价值：建立抽象思维与空间推理的深度关联，提升逻辑思维的空间化应用
-- 预期成果：35条高质量关系，累计257条关系
-- ============================================

-- ============================================
-- 1. 集合逻辑推理→立体几何逻辑推理（7条关系）
-- ============================================

-- 【集合包含关系为几何包含关系提供逻辑推理基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH01_006'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_012'), 
 'extension', 0.87, 0.83, 30, 0.8, 0.85, 'horizontal', 0, 0.89, 0.86, 
 '{"liberal_arts_notes": "集合包含关系为几何包含关系提供逻辑推理和包含判断基础", "science_notes": "集合包含逻辑在立体几何位置关系推理中的基础应用"}', true),

-- 【集合运算规律为空间关系运算提供运算逻辑基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH01_008'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_013'), 
 'application_of', 0.85, 0.81, 30, 0.8, 0.83, 'horizontal', 0, 0.87, 0.84, 
 '{"liberal_arts_notes": "集合运算规律为空间关系运算提供运算逻辑和关系推理基础", "science_notes": "集合运算思维在立体几何关系分析中的逻辑应用"}', true),

-- 【逻辑连接词为几何命题推理提供逻辑连接基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH01_012'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_015'), 
 'extension', 0.89, 0.85, 30, 0.8, 0.87, 'horizontal', 0, 0.91, 0.88, 
 '{"liberal_arts_notes": "逻辑连接词为几何命题推理提供逻辑连接和推理规则基础", "science_notes": "逻辑连接词在立体几何证明推理中的核心逻辑应用"}', true),

-- 【命题真假判断为几何命题判断提供判断方法基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH01_013'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_016'), 
 'application_of', 0.86, 0.82, 30, 0.8, 0.84, 'horizontal', 0, 0.88, 0.85, 
 '{"liberal_arts_notes": "命题真假判断为几何命题判断提供判断方法和逻辑验证基础", "science_notes": "命题判断方法在立体几何性质判定中的方法应用"}', true),

-- 【充分必要条件为几何条件判定提供条件分析基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH01_014'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_014'), 
 'extension', 0.91, 0.87, 60, 0.9, 0.89, 'horizontal', 0, 0.93, 0.90, 
 '{"liberal_arts_notes": "充分必要条件为几何条件判定提供条件分析和逻辑判定基础", "science_notes": "充要条件逻辑在立体几何条件分析中的核心分析应用"}', true),

-- 【逻辑推理方法为几何证明推理提供推理策略基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH01_015'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_017'), 
 'application_of', 0.88, 0.84, 60, 0.8, 0.86, 'horizontal', 0, 0.90, 0.87, 
 '{"liberal_arts_notes": "逻辑推理方法为几何证明推理提供推理策略和证明方法基础", "science_notes": "逻辑推理方法在立体几何证明中的策略性推理应用"}', true),

-- 【反证法思维为几何反证推理提供反向思维基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH01_016'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_018'), 
 'extension', 0.84, 0.80, 60, 0.7, 0.82, 'horizontal', 0, 0.86, 0.83, 
 '{"liberal_arts_notes": "反证法思维为几何反证推理提供反向思维和否定论证基础", "science_notes": "反证法逻辑在立体几何不可能性证明中的应用"}', true),

-- ============================================
-- 2. 函数抽象思维→立体几何抽象思维（7条关系）
-- ============================================

-- 【函数定义域思维为几何定义域提供抽象定义基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH03_002'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_001'), 
 'extension', 0.85, 0.81, 30, 0.8, 0.83, 'horizontal', 0, 0.87, 0.84, 
 '{"liberal_arts_notes": "函数定义域思维为几何定义域提供抽象定义和空间范围基础", "science_notes": "函数定义域概念在立体几何空间范围理解中的应用"}', true),

-- 【函数映射思维为几何变换思维提供映射变换基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH03_001'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_019'), 
 'extension', 0.89, 0.85, 60, 0.8, 0.87, 'horizontal', 0, 0.91, 0.88, 
 '{"liberal_arts_notes": "函数映射思维为几何变换思维提供映射变换和变换思维基础", "science_notes": "函数映射概念在立体几何变换理解中的核心思维应用"}', true),

-- 【函数性质分析为几何性质分析提供分析方法基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH03_014'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_003'), 
 'application_of', 0.87, 0.83, 60, 0.8, 0.85, 'horizontal', 0, 0.89, 0.86, 
 '{"liberal_arts_notes": "函数性质分析为几何性质分析提供分析方法和性质探究基础", "science_notes": "函数性质分析方法在立体几何性质研究中的方法应用"}', true),

-- 【函数变化规律为几何变化规律提供变化分析基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH03_009'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_020'), 
 'extension', 0.83, 0.79, 60, 0.7, 0.81, 'horizontal', 0, 0.85, 0.82, 
 '{"liberal_arts_notes": "函数变化规律为几何变化规律提供变化分析和规律探索基础", "science_notes": "函数变化分析在立体几何运动变化中的规律应用"}', true),

-- 【函数极值思维为几何极值问题提供极值分析基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH03_010'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_021'), 
 'application_of', 0.86, 0.82, 60, 0.8, 0.84, 'horizontal', 0, 0.88, 0.85, 
 '{"liberal_arts_notes": "函数极值思维为几何极值问题提供极值分析和最值求解基础", "science_notes": "函数极值方法在立体几何最值问题中的分析应用"}', true),

-- 【函数建模思维为几何建模思维提供建模方法基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH03_021'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_022'), 
 'extension', 0.90, 0.86, 60, 0.9, 0.88, 'horizontal', 0, 0.92, 0.89, 
 '{"liberal_arts_notes": "函数建模思维为几何建模思维提供建模方法和抽象建模基础", "science_notes": "函数建模思维在立体几何问题建模中的核心建模应用"}', true),

-- 【函数抽象概念为几何抽象概念提供概念抽象基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH03_001'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_001'), 
 'related', 0.84, 0.80, 60, 0.6, 0.82, 'horizontal', 0, 0.86, 0.83, 
 '{"liberal_arts_notes": "函数抽象概念为几何抽象概念提供概念抽象和抽象思维基础", "science_notes": "函数抽象思维在立体几何概念抽象中的思维应用"}', true),

-- ============================================
-- 3. 二次函数推理→立体几何计算推理（7条关系）
-- ============================================

-- 【二次函数解析推理为几何解析推理提供解析方法基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH02_012'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_023'), 
 'application_of', 0.88, 0.84, 30, 0.8, 0.86, 'horizontal', 0, 0.90, 0.87, 
 '{"liberal_arts_notes": "二次函数解析推理为几何解析推理提供解析方法和计算推理基础", "science_notes": "二次函数解析方法在立体几何计算证明中的核心推理应用"}', true),

-- 【二次方程求解为几何方程求解提供求解策略基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH02_009'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_024'), 
 'application_of', 0.85, 0.81, 30, 0.8, 0.83, 'horizontal', 0, 0.87, 0.84, 
 '{"liberal_arts_notes": "二次方程求解为几何方程求解提供求解策略和计算方法基础", "science_notes": "二次方程求解方法在立体几何计算中的方法应用"}', true),

-- 【二次不等式推理为几何不等式推理提供推理逻辑基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH02_015'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_025'), 
 'extension', 0.86, 0.82, 60, 0.8, 0.84, 'horizontal', 0, 0.88, 0.85, 
 '{"liberal_arts_notes": "二次不等式推理为几何不等式推理提供推理逻辑和不等关系基础", "science_notes": "二次不等式推理在立体几何不等关系证明中的逻辑应用"}', true),

-- 【二次函数判别式为几何判别分析提供判别方法基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH02_010'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_026'), 
 'application_of', 0.84, 0.80, 30, 0.7, 0.82, 'horizontal', 0, 0.86, 0.83, 
 '{"liberal_arts_notes": "二次函数判别式为几何判别分析提供判别方法和存在性判定基础", "science_notes": "判别式方法在立体几何存在性判定中的判别应用"}', true),

-- 【二次函数配方思维为几何配方变换提供变换思维基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH02_011'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_027'), 
 'extension', 0.82, 0.78, 60, 0.7, 0.80, 'horizontal', 0, 0.84, 0.81, 
 '{"liberal_arts_notes": "二次函数配方思维为几何配方变换提供变换思维和恒等变换基础", "science_notes": "配方思维在立体几何恒等变换中的变换应用"}', true),




-- ============================================
-- 4. 指数对数抽象→立体几何抽象证明（7条关系）
-- ============================================

-- 【指数运算规律为几何运算规律提供运算抽象基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH04_003'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_001'), 
 'extension', 0.83, 0.79, 30, 0.7, 0.81, 'horizontal', 0, 0.85, 0.82, 
 '{"liberal_arts_notes": "指数运算规律为几何运算规律提供运算抽象和规律总结基础", "science_notes": "指数运算规律在立体几何运算证明中的规律应用"}', true),

-- 【对数运算推理为几何逆向推理提供逆向思维基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH04_008'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_002'), 
 'extension', 0.86, 0.82, 30, 0.8, 0.84, 'horizontal', 0, 0.88, 0.85, 
 '{"liberal_arts_notes": "对数运算推理为几何逆向推理提供逆向思维和反向分析基础", "science_notes": "对数逆向推理在立体几何逆向证明中的思维应用"}', true),

-- 【指数方程求解为几何方程推理提供方程思维基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH04_013'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_003'), 
 'application_of', 0.89, 0.85, 60, 0.8, 0.87, 'horizontal', 0, 0.91, 0.88, 
 '{"liberal_arts_notes": "指数方程求解为几何方程推理提供方程思维和求解策略基础", "science_notes": "指数方程方法在立体几何方程推理中的核心求解应用"}', true),

-- 【对数方程推理为几何方程证明提供推理方法基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH04_014'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_004'), 
 'application_of', 0.87, 0.83, 60, 0.8, 0.85, 'horizontal', 0, 0.89, 0.86, 
 '{"liberal_arts_notes": "对数方程推理为几何方程证明提供推理方法和逻辑证明基础", "science_notes": "对数方程推理在立体几何等式证明中的推理应用"}', true),

-- 【指数函数单调性为几何单调推理提供单调分析基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH04_004'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_005'), 
 'extension', 0.84, 0.80, 60, 0.7, 0.82, 'horizontal', 0, 0.86, 0.83, 
 '{"liberal_arts_notes": "指数函数单调性为几何单调推理提供单调分析和变化规律基础", "science_notes": "指数单调性在立体几何单调性证明中的分析应用"}', true),

-- 【对数函数单调性为几何比较推理提供比较方法基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH04_009'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_006'), 
 'extension', 0.85, 0.81, 60, 0.8, 0.83, 'horizontal', 0, 0.87, 0.84, 
 '{"liberal_arts_notes": "对数函数单调性为几何比较推理提供比较方法和大小比较基础", "science_notes": "对数单调性在立体几何大小比较证明中的比较应用"}', true),

-- 【指数对数综合为几何抽象证明提供抽象综合基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH04_018'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_007'), 
 'extension', 0.90, 0.86, 60, 0.9, 0.88, 'horizontal', 0, 0.92, 0.89, 
 '{"liberal_arts_notes": "指数对数综合为几何抽象证明提供抽象综合和高阶抽象基础", "science_notes": "指数对数综合思维在立体几何抽象证明中的核心抽象应用"}', true),

-- ============================================
-- 5. 三角函数推理→立体几何角度推理（7条关系）
-- ============================================

-- 【三角函数定义为几何角度定义提供角度概念基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH05_007'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_008'), 
 'extension', 0.88, 0.84, 30, 0.8, 0.86, 'horizontal', 0, 0.90, 0.87, 
 '{"liberal_arts_notes": "三角函数定义为几何角度定义提供角度概念和角度推理基础", "science_notes": "三角函数定义在立体几何角度分析中的核心角度应用"}', true),

-- 【三角恒等变换为几何角度变换提供变换推理基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH05_019'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_009'), 
 'application_of', 0.91, 0.87, 60, 0.9, 0.89, 'horizontal', 0, 0.93, 0.90, 
 '{"liberal_arts_notes": "三角恒等变换为几何角度变换提供变换推理和角度恒等基础", "science_notes": "三角恒等变换在立体几何角度证明中的核心变换应用"}', true),

-- 【三角函数周期性为几何周期推理提供周期分析基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH05_017'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_010'), 
 'extension', 0.84, 0.80, 60, 0.7, 0.82, 'horizontal', 0, 0.86, 0.83, 
 '{"liberal_arts_notes": "三角函数周期性为几何周期推理提供周期分析和重复规律基础", "science_notes": "三角周期性在立体几何周期性质中的规律应用"}', true),

-- 【三角函数对称性为几何对称推理提供对称角度基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH05_015'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_011'), 
 'extension', 0.86, 0.82, 60, 0.8, 0.84, 'horizontal', 0, 0.88, 0.85, 
 '{"liberal_arts_notes": "三角函数对称性为几何对称推理提供对称角度和角度对称基础", "science_notes": "三角对称性在立体几何对称角度分析中的对称应用"}', true),

-- 【三角方程求解为几何角度方程提供角度求解基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH05_018'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_021'), 
 'application_of', 0.89, 0.85, 30, 0.8, 0.87, 'horizontal', 0, 0.91, 0.88, 
 '{"liberal_arts_notes": "三角方程求解为几何角度方程提供角度求解和角度计算基础", "science_notes": "三角方程求解在立体几何角度计算中的核心计算应用"}', true),

-- 【正弦余弦定理为几何角度推理提供定理推理基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH05_013'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_012'), 
 'extension', 0.92, 0.88, 60, 0.9, 0.90, 'horizontal', 0, 0.94, 0.91, 
 '{"liberal_arts_notes": "正弦余弦定理为几何角度推理提供定理推理和角度关系基础", "science_notes": "正弦余弦定理在立体几何角度关系证明中的核心定理应用"}', true),

-- 【三角函数综合推理为几何角度综合证明提供综合推理基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH05_020'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_023'), 
 'application_of', 0.87, 0.83, 60, 0.8, 0.85, 'horizontal', 0, 0.89, 0.86, 
 '{"liberal_arts_notes": "三角函数综合推理为几何角度综合证明提供综合推理和多角度分析基础", "science_notes": "三角函数综合推理在立体几何复合角度证明中的综合应用"}', true),

-- ============================================
-- 第八批：函数模型应用→统计概率深化（35条）- 专家权威版
-- 覆盖：高中必修第一册A函数建模应用知识点 → 高中必修第二册A统计概率深化
-- 审查标准：⭐⭐⭐⭐⭐ 函数建模思维+统计概率深化+数学建模思想指导
-- 重点：函数模型建立→统计模型构建→概率模型应用的建模思维深化
-- 高中特色：从确定性函数建模到不确定性统计概率建模的认知跨越
-- ============================================

-- 【函数模型应用→统计概率深化认知链分析】
-- 1. 函数建模思维→统计数据建模（7条关系）
-- 2. 函数应用案例→概率应用案例（7条关系）
-- 3. 数学建模过程→统计建模过程（7条关系）
-- 4. 函数模型验证→概率模型验证（7条关系）
-- 5. 建模思想方法→统计概率综合应用（7条关系）

-- ============================================
-- 1. 函数建模思维→统计数据建模（7条关系）
-- ============================================

-- 【函数建模过程为统计建模过程提供建模方法基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH03_021'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH09_005'), 
 'application_of', 0.92, 0.88, 30, 0.9, 0.90, 'horizontal', 0, 0.94, 0.91, 
 '{"liberal_arts_notes": "函数建模过程为统计建模提供建模方法和数据建模思维基础", "science_notes": "函数建模思维在统计数据建模中的核心方法应用"}', true),

-- 【二次函数建模为频率分布建模提供曲线拟合基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH02_009'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH09_006'), 
 'extension', 0.88, 0.84, 30, 0.8, 0.86, 'horizontal', 0, 0.90, 0.87, 
 '{"liberal_arts_notes": "二次函数建模为频率分布建模提供曲线拟合和分布建模基础", "science_notes": "二次函数模型在统计分布拟合中的数学建模应用"}', true),

-- 【指数函数建模为增长模型提供增长建模基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH04_019'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH09_007'), 
 'application_of', 0.91, 0.87, 30, 0.9, 0.89, 'horizontal', 0, 0.93, 0.90, 
 '{"liberal_arts_notes": "指数函数建模为增长模型提供指数增长和数据增长建模基础", "science_notes": "指数增长模型在统计数据增长分析中的核心建模应用"}', true),

-- 【对数函数建模为数据压缩提供压缩建模基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH04_020'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH09_008'), 
 'application_of', 0.87, 0.83, 30, 0.8, 0.85, 'horizontal', 0, 0.89, 0.86, 
 '{"liberal_arts_notes": "对数函数建模为数据压缩提供数据变换和压缩建模基础", "science_notes": "对数变换模型在统计数据处理中的变换建模应用"}', true),

-- 【三角函数建模为周期性建模提供周期建模基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH05_020'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH09_009'), 
 'extension', 0.85, 0.81, 60, 0.8, 0.83, 'horizontal', 0, 0.87, 0.84, 
 '{"liberal_arts_notes": "三角函数建模为周期性建模提供周期变化和循环数据建模基础", "science_notes": "三角函数周期模型在统计周期性数据分析中的建模应用"}', true),

-- 【函数性质分析为数据特征分析提供特征提取基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH03_014'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH09_010'), 
 'application_of', 0.89, 0.85, 60, 0.8, 0.87, 'horizontal', 0, 0.91, 0.88, 
 '{"liberal_arts_notes": "函数性质分析为数据特征分析提供特征提取和数据特征识别基础", "science_notes": "函数性质分析方法在统计数据特征识别中的核心分析应用"}', true),

-- 【函数图象分析为数据可视化提供图形分析基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH03_013'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH09_011'), 
 'extension', 0.86, 0.82, 60, 0.8, 0.84, 'horizontal', 0, 0.88, 0.85, 
 '{"liberal_arts_notes": "函数图象分析为数据可视化提供图形表示和视觉分析基础", "science_notes": "函数图象分析在统计数据可视化中的图形表示应用"}', true),

-- ============================================
-- 2. 函数应用案例→概率应用案例（7条关系）
-- ============================================

-- 【指数增长应用为概率增长模型提供增长应用基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH04_016'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH10_001'), 
 'application_of', 0.90, 0.86, 30, 0.8, 0.88, 'horizontal', 0, 0.92, 0.89, 
 '{"liberal_arts_notes": "指数增长应用为概率增长模型提供增长规律和概率增长应用基础", "science_notes": "指数增长应用在概率模型增长中的直接应用迁移"}', true),

-- 【对数衰减应用为概率衰减模型提供衰减应用基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH04_017'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH10_002'), 
 'application_of', 0.88, 0.84, 30, 0.8, 0.86, 'horizontal', 0, 0.90, 0.87, 
 '{"liberal_arts_notes": "对数衰减应用为概率衰减模型提供衰减规律和概率衰减应用基础", "science_notes": "对数衰减应用在概率模型衰减中的数学建模应用"}', true),

-- 【三角函数周期应用为随机周期提供周期应用基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH05_018'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH10_003'), 
 'extension', 0.85, 0.81, 60, 0.8, 0.83, 'horizontal', 0, 0.87, 0.84, 
 '{"liberal_arts_notes": "三角函数周期应用为随机周期提供周期性和随机周期性应用基础", "science_notes": "三角周期应用在随机过程周期性分析中的应用"}', true),

-- 【函数极值应用为概率极值提供极值应用基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH03_010'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH10_004'), 
 'application_of', 0.87, 0.83, 30, 0.8, 0.85, 'horizontal', 0, 0.89, 0.86, 
 '{"liberal_arts_notes": "函数极值应用为概率极值提供最值求解和概率极值应用基础", "science_notes": "函数极值应用在概率分布极值问题中的优化应用"}', true),

-- 【函数单调性应用为概率单调性提供单调应用基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH03_008'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH10_005'), 
 'extension', 0.84, 0.80, 60, 0.7, 0.82, 'horizontal', 0, 0.86, 0.83, 
 '{"liberal_arts_notes": "函数单调性应用为概率单调性提供单调变化和概率单调性应用基础", "science_notes": "函数单调性在概率分布单调性分析中的性质应用"}', true),

-- 【函数奇偶性应用为概率对称性提供对称应用基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH03_015'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH10_006'), 
 'extension', 0.83, 0.79, 60, 0.8, 0.81, 'horizontal', 0, 0.85, 0.82, 
 '{"liberal_arts_notes": "函数奇偶性应用为概率对称性提供对称性质和概率对称性应用基础", "science_notes": "函数对称性在概率分布对称性分析中的对称应用"}', true),

-- 【函数零点应用为概率临界点提供临界应用基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH03_017'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH10_007'), 
 'application_of', 0.86, 0.82, 30, 0.8, 0.84, 'horizontal', 0, 0.88, 0.85, 
 '{"liberal_arts_notes": "函数零点应用为概率临界点提供临界分析和概率临界值应用基础", "science_notes": "函数零点方法在概率临界值分析中的临界应用"}', true),

-- ============================================
-- 3. 数学建模过程→统计建模过程（7条关系）
-- ============================================

-- 【建模假设设定为统计假设提供假设建立基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH03_021'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH09_012'), 
 'extension', 0.89, 0.85, 30, 0.8, 0.87, 'horizontal', 0, 0.91, 0.88, 
 '{"liberal_arts_notes": "建模假设设定为统计假设提供假设建立和假设验证思维基础", "science_notes": "数学建模假设在统计假设检验中的假设建立应用"}', true),

-- 【模型参数确定为统计参数提供参数估计基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH04_013'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH09_013'), 
 'application_of', 0.91, 0.87, 30, 0.8, 0.89, 'horizontal', 0, 0.93, 0.90, 
 '{"liberal_arts_notes": "模型参数确定为统计参数提供参数估计和参数确定方法基础", "science_notes": "函数模型参数在统计参数估计中的核心参数应用"}', true),

-- 【模型拟合优度为统计拟合提供拟合评价基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH02_011'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH09_014'), 
 'application_of', 0.88, 0.84, 30, 0.8, 0.86, 'horizontal', 0, 0.90, 0.87, 
 '{"liberal_arts_notes": "模型拟合优度为统计拟合提供拟合评价和模型优度评估基础", "science_notes": "数学模型拟合在统计模型拟合评价中的评估应用"}', true),

-- 【模型预测分析为统计预测提供预测方法基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH04_006'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH09_015'), 
 'extension', 0.87, 0.83, 60, 0.8, 0.85, 'horizontal', 0, 0.89, 0.86, 
 '{"liberal_arts_notes": "模型预测分析为统计预测提供预测方法和趋势预测基础", "science_notes": "函数模型预测在统计趋势预测中的预测方法应用"}', true),

-- 【模型误差分析为统计误差提供误差分析基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH02_005'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH09_016'), 
 'application_of', 0.85, 0.81, 60, 0.8, 0.83, 'horizontal', 0, 0.87, 0.84, 
 '{"liberal_arts_notes": "模型误差分析为统计误差提供误差评估和误差控制基础", "science_notes": "数学模型误差在统计误差分析中的误差评估应用"}', true),

-- 【模型优化改进为统计优化提供优化策略基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH02_008'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH10_008'), 
 'application_of', 0.90, 0.86, 30, 0.9, 0.88, 'horizontal', 0, 0.92, 0.89, 
 '{"liberal_arts_notes": "模型优化改进为统计优化提供优化策略和模型改进基础", "science_notes": "数学模型优化在统计模型优化中的核心优化应用"}', true),

-- 【模型应用推广为统计应用提供应用扩展基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH04_018'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH10_009'), 
 'extension', 0.86, 0.82, 60, 0.8, 0.84, 'horizontal', 0, 0.88, 0.85, 
 '{"liberal_arts_notes": "模型应用推广为统计应用提供应用扩展和实际应用基础", "science_notes": "数学模型应用在统计实际应用中的应用推广"}', true),

-- ============================================
-- 4. 函数模型验证→概率模型验证（7条关系）
-- ============================================

-- 【函数模型检验为概率模型检验提供检验方法基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH03_019'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH10_010'), 
 'application_of', 0.89, 0.85, 30, 0.8, 0.87, 'horizontal', 0, 0.91, 0.88, 
 '{"liberal_arts_notes": "函数模型检验为概率模型检验提供检验方法和模型验证基础", "science_notes": "函数模型检验方法在概率模型验证中的核心检验应用"}', true),

-- 【数据拟合验证为概率拟合验证提供拟合检验基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH02_012'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH10_011'), 
 'application_of', 0.87, 0.83, 30, 0.8, 0.85, 'horizontal', 0, 0.89, 0.86, 
 '{"liberal_arts_notes": "数据拟合验证为概率拟合验证提供拟合检验和数据验证基础", "science_notes": "数据拟合验证在概率分布拟合验证中的验证应用"}', true),

-- 【残差分析验证为概率残差提供残差检验基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH02_003'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH10_012'), 
 'extension', 0.85, 0.81, 60, 0.8, 0.83, 'horizontal', 0, 0.87, 0.84, 
 '{"liberal_arts_notes": "残差分析验证为概率残差提供残差检验和误差分析基础", "science_notes": "残差分析方法在概率模型残差检验中的分析应用"}', true),

-- 【预测精度验证为概率精度提供精度评估基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH04_004'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH10_013'), 
 'application_of', 0.88, 0.84, 30, 0.8, 0.86, 'horizontal', 0, 0.90, 0.87, 
 '{"liberal_arts_notes": "预测精度验证为概率精度提供精度评估和预测准确性基础", "science_notes": "预测精度验证在概率预测精度评估中的精度应用"}', true),

-- 【模型稳定性验证为概率稳定性提供稳定性检验基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH03_007'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH10_014'), 
 'extension', 0.84, 0.80, 60, 0.7, 0.82, 'horizontal', 0, 0.86, 0.83, 
 '{"liberal_arts_notes": "模型稳定性验证为概率稳定性提供稳定性检验和模型可靠性基础", "science_notes": "模型稳定性在概率模型稳定性分析中的稳定性应用"}', true),

-- 【敏感性分析验证为概率敏感性提供敏感性检验基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH03_018'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH10_015'), 
 'application_of', 0.86, 0.82, 60, 0.8, 0.84, 'horizontal', 0, 0.88, 0.85, 
 '{"liberal_arts_notes": "敏感性分析验证为概率敏感性提供敏感性检验和参数敏感性基础", "science_notes": "敏感性分析在概率模型敏感性检验中的分析应用"}', true),

-- 【模型适用性验证为概率适用性提供适用性评估基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH04_015'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH10_016'), 
 'extension', 0.87, 0.83, 60, 0.8, 0.85, 'horizontal', 0, 0.89, 0.86, 
 '{"liberal_arts_notes": "模型适用性验证为概率适用性提供适用性评估和应用范围基础", "science_notes": "模型适用性在概率模型适用性评估中的适用性应用"}', true),

-- ============================================
-- 5. 建模思想方法→统计概率综合应用（7条关系）
-- ============================================

-- 【数形结合思想为统计图形提供图形结合基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH03_013'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH09_005'), 
 'extension', 0.91, 0.87, 30, 0.8, 0.89, 'horizontal', 0, 0.93, 0.90, 
 '{"liberal_arts_notes": "数形结合思想为统计图形提供数据可视化和图形表示基础", "science_notes": "数形结合思想在统计数据可视化中的核心图形应用"}', true),

-- 【分类讨论思想为概率分类提供分类方法基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH02_004'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH10_001'), 
 'application_of', 0.88, 0.84, 60, 0.8, 0.86, 'horizontal', 0, 0.90, 0.87, 
 '{"liberal_arts_notes": "分类讨论思想为概率分类提供分类方法和条件分类基础", "science_notes": "分类讨论思想在概率条件分类中的分类应用"}', true),

-- 【函数与方程思想为统计方程提供方程建模基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH04_013'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH09_013'), 
 'extension', 0.89, 0.85, 30, 0.8, 0.87, 'horizontal', 0, 0.91, 0.88, 
 '{"liberal_arts_notes": "函数与方程思想为统计方程提供方程建模和数量关系基础", "science_notes": "函数方程思想在统计方程建模中的核心方程应用"}', true),

-- 【转化化归思想为概率转化提供转化方法基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH01_014'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH10_008'), 
 'application_of', 0.86, 0.82, 60, 0.8, 0.84, 'horizontal', 0, 0.88, 0.85, 
 '{"liberal_arts_notes": "转化化归思想为概率转化提供问题转化和化归方法基础", "science_notes": "转化化归思想在概率问题转化中的转化应用"}', true),

-- 【整体思维方法为统计整体提供整体分析基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH02_013'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH09_016'), 
 'extension', 0.87, 0.83, 60, 0.8, 0.85, 'horizontal', 0, 0.89, 0.86, 
 '{"liberal_arts_notes": "整体思维方法为统计整体提供整体分析和系统思维基础", "science_notes": "整体思维在统计整体分析中的系统分析应用"}', true),

-- 【特殊与一般思想为概率推广提供推广方法基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH03_001'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH10_015'), 
 'extension', 0.85, 0.81, 60, 0.8, 0.83, 'horizontal', 0, 0.87, 0.84, 
 '{"liberal_arts_notes": "特殊与一般思想为概率推广提供推广方法和一般化基础", "science_notes": "特殊一般思想在概率模型推广中的推广应用"}', true),

-- 【数学建模综合思想为统计概率综合提供综合建模基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH04_020'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH10_016'), 
 'application_of', 0.92, 0.88, 30, 0.9, 0.90, 'horizontal', 0, 0.94, 0.91, 
 '{"liberal_arts_notes": "数学建模综合思想为统计概率综合提供综合建模和系统建模基础", "science_notes": "数学建模综合思想在统计概率综合应用中的核心建模应用"}', true),

-- ============================================
-- 【第九批】数学思想方法跨越发展（30条关系）
-- 批次编号：9/10
-- 学科跨越：数学思想方法→立体几何思想方法体系
-- 认知跨越：平面数学思维→空间几何思维综合
-- 核心价值：建立数学思想方法的空间化发展，为高中数学思维奠定基础
-- 预期成果：30条高质量关系，累计319条关系
-- ============================================

-- ============================================
-- 1. 抽象概括思维→立体几何抽象认知（5条关系）
-- ============================================

-- 【集合抽象概念为空间点集提供抽象认知基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH01_001'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_001'), 
 'extension', 0.91, 0.87, 30, 0.9, 0.89, 'horizontal', 0, 0.93, 0.90, 
 '{"liberal_arts_notes": "集合抽象概念为空间点集提供抽象认知和空间抽象思维基础", "science_notes": "集合抽象概念在立体几何空间点集理解中的核心抽象应用"}', true),

-- 【函数抽象概念为空间关系提供关系抽象基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH03_001'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_002'), 
 'extension', 0.89, 0.85, 30, 0.8, 0.87, 'horizontal', 0, 0.91, 0.88, 
 '{"liberal_arts_notes": "函数抽象概念为空间关系提供关系抽象和空间关系认知基础", "science_notes": "函数抽象概念在立体几何空间关系理解中的抽象应用"}', true),

-- 【数学符号抽象为几何符号提供符号抽象基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH01_009'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_003'), 
 'extension', 0.86, 0.82, 30, 0.8, 0.84, 'horizontal', 0, 0.88, 0.85, 
 '{"liberal_arts_notes": "数学符号抽象为几何符号提供符号抽象和符号表示基础", "science_notes": "数学符号抽象在立体几何符号表示中的符号应用"}', true),

-- 【逻辑抽象概念为空间逻辑提供逻辑抽象基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH01_016'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_004'), 
 'extension', 0.88, 0.84, 30, 0.8, 0.86, 'horizontal', 0, 0.90, 0.87, 
 '{"liberal_arts_notes": "逻辑抽象概念为空间逻辑提供逻辑抽象和空间推理基础", "science_notes": "逻辑抽象概念在立体几何空间推理中的逻辑应用"}', true),

-- 【数学概念体系为几何概念体系提供概念抽象基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH03_021'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_005'), 
 'extension', 0.90, 0.86, 60, 0.9, 0.88, 'horizontal', 0, 0.92, 0.89, 
 '{"liberal_arts_notes": "数学概念体系为几何概念体系提供概念抽象和系统认知基础", "science_notes": "数学概念体系在立体几何概念体系构建中的核心概念应用"}', true),

-- ============================================
-- 2. 逻辑推理思维→几何证明逻辑体系（5条关系）
-- ============================================

-- 【充要条件概念为几何命题提供逻辑判定和命题逻辑基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH01_014'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_006'), 
 'application_of', 0.92, 0.88, 30, 0.9, 0.90, 'horizontal', 0, 0.94, 0.91, 
 '{"liberal_arts_notes": "充要条件概念为几何命题提供逻辑判定和命题逻辑基础", "science_notes": "充要条件概念在立体几何命题证明中的核心逻辑应用"}', true),

-- 【逻辑推理方法为几何证明提供推理方法基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH01_018'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_007'), 
 'application_of', 0.89, 0.85, 30, 0.8, 0.87, 'horizontal', 0, 0.91, 0.88, 
 '{"liberal_arts_notes": "量词命题否定为几何证明提供推理方法和证明逻辑基础", "science_notes": "量词命题否定在立体几何证明中的推理应用"}', true),

-- 【条件判断方法为几何反证提供判断逻辑基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH01_015'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_008'), 
 'application_of', 0.87, 0.83, 30, 0.8, 0.85, 'horizontal', 0, 0.89, 0.86, 
 '{"liberal_arts_notes": "条件判断方法为几何反证提供判断逻辑和否定推理基础", "science_notes": "条件判断方法在立体几何反证中的判断应用"}', true),

-- 【存在量词概念为几何演绎提供存在逻辑基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH01_017'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_009'), 
 'extension', 0.85, 0.81, 30, 0.8, 0.83, 'horizontal', 0, 0.87, 0.84, 
 '{"liberal_arts_notes": "存在量词概念为几何演绎提供存在逻辑和逻辑演绎基础", "science_notes": "存在量词概念在立体几何演绎推理中的存在应用"}', true),

-- 【全称量词概念为几何归纳提供全称逻辑基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH01_016'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_010'), 
 'extension', 0.84, 0.80, 60, 0.7, 0.82, 'horizontal', 0, 0.86, 0.83, 
 '{"liberal_arts_notes": "全称量词概念为几何归纳提供全称逻辑和模式识别基础", "science_notes": "全称量词概念在立体几何规律发现中的全称应用"}', true),

-- ============================================
-- 3. 空间想象思维→立体几何空间认知（5条关系）
-- ============================================

-- 【函数图象想象为空间图形提供空间视觉基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH03_013'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_011'), 
 'extension', 0.90, 0.86, 30, 0.8, 0.88, 'horizontal', 0, 0.92, 0.89, 
 '{"liberal_arts_notes": "函数图象想象为空间图形提供空间视觉和几何想象基础", "science_notes": "函数图象想象在立体几何空间图形理解中的核心视觉应用"}', true),

-- 【三角函数单位圆为空间旋转提供旋转想象基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH05_008'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_012'), 
 'extension', 0.88, 0.84, 60, 0.8, 0.86, 'horizontal', 0, 0.90, 0.87, 
 '{"liberal_arts_notes": "三角函数单位圆为空间旋转提供旋转想象和空间旋转基础", "science_notes": "单位圆概念在立体几何空间旋转理解中的旋转应用"}', true),

-- 【二次函数开口为空间开放提供开放想象基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH02_007'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_013'), 
 'related', 0.85, 0.81, 60, 0.7, 0.83, 'horizontal', 0, 0.87, 0.84, 
 '{"liberal_arts_notes": "二次函数开口概念为空间开放图形提供开放想象和空间形态基础", "science_notes": "二次函数开口在立体几何空间形态理解中的形态应用"}', true),

-- 【指数增长曲线为空间增长提供增长想象基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH04_007'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_014'), 
 'related', 0.83, 0.79, 60, 0.7, 0.81, 'horizontal', 0, 0.85, 0.82, 
 '{"liberal_arts_notes": "指数增长曲线为空间增长变化提供增长想象和变化想象基础", "science_notes": "指数增长曲线在立体几何空间变化理解中的变化应用"}', true),

-- 【函数变换图形为空间变换提供变换想象基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH03_016'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_015'), 
 'extension', 0.87, 0.83, 30, 0.8, 0.85, 'horizontal', 0, 0.89, 0.86, 
 '{"liberal_arts_notes": "函数变换图形为空间变换提供变换想象和几何变换基础", "science_notes": "函数变换图形在立体几何空间变换理解中的核心变换应用"}', true),

-- ============================================
-- 4. 数学建模思维→几何建模应用（5条关系）
-- ============================================

-- 【函数建模思维为几何建模提供建模思维基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH04_013'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_016'), 
 'application_of', 0.91, 0.87, 30, 0.8, 0.89, 'horizontal', 0, 0.93, 0.90, 
 '{"liberal_arts_notes": "函数建模思维为几何建模提供建模思维和几何建模基础", "science_notes": "函数建模思维在立体几何建模中的核心建模应用"}', true),

-- 【实际问题抽象为几何问题抽象提供抽象建模基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH02_014'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_017'), 
 'application_of', 0.89, 0.85, 30, 0.8, 0.87, 'horizontal', 0, 0.91, 0.88, 
 '{"liberal_arts_notes": "实际问题抽象为几何问题抽象提供抽象建模和问题转化基础", "science_notes": "实际问题抽象在立体几何问题建模中的抽象应用"}', true),

-- 【数学模型建立为几何模型提供模型建立基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH04_019'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_018'), 
 'extension', 0.88, 0.84, 60, 0.8, 0.86, 'horizontal', 0, 0.90, 0.87, 
 '{"liberal_arts_notes": "数学模型建立为几何模型提供模型建立和几何建模基础", "science_notes": "数学模型建立在立体几何模型构建中的建模应用"}', true),

-- 【参数方程思维为空间参数提供参数化基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH03_020'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_019'), 
 'extension', 0.86, 0.82, 60, 0.8, 0.84, 'horizontal', 0, 0.88, 0.85, 
 '{"liberal_arts_notes": "参数方程思维为空间参数化提供参数化和空间参数基础", "science_notes": "参数方程思维在立体几何参数化中的参数应用"}', true),

-- 【建模验证思维为几何验证提供验证建模基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH04_018'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_020'), 
 'application_of', 0.87, 0.83, 60, 0.8, 0.85, 'horizontal', 0, 0.89, 0.86, 
 '{"liberal_arts_notes": "建模验证思维为几何验证提供验证建模和模型验证基础", "science_notes": "建模验证思维在立体几何模型验证中的验证应用"}', true),

-- ============================================
-- 5. 数学运算思维→几何计算体系（5条关系）
-- ============================================

-- 【代数运算思维为几何运算提供运算思维基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH02_002'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_021'), 
 'application_of', 0.88, 0.84, 30, 0.8, 0.86, 'horizontal', 0, 0.90, 0.87, 
 '{"liberal_arts_notes": "代数运算思维为几何运算提供运算思维和计算方法基础", "science_notes": "代数运算思维在立体几何计算中的核心运算应用"}', true),

-- 【函数运算思维为几何函数提供函数运算基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH03_012'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_022'), 
 'application_of', 0.86, 0.82, 30, 0.8, 0.84, 'horizontal', 0, 0.88, 0.85, 
 '{"liberal_arts_notes": "函数运算思维为几何函数运算提供函数思维和运算策略基础", "science_notes": "函数运算思维在立体几何函数计算中的函数应用"}', true),

-- 【三角运算思维为几何三角提供三角运算基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH05_013'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_023'), 
 'application_of', 0.90, 0.86, 30, 0.8, 0.88, 'horizontal', 0, 0.92, 0.89, 
 '{"liberal_arts_notes": "三角运算思维为几何三角计算提供三角运算和角度计算基础", "science_notes": "三角运算思维在立体几何三角计算中的核心三角应用"}', true),

-- 【估算近似思维为几何估算提供估算策略基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH04_005'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_024'), 
 'application_of', 0.84, 0.80, 60, 0.7, 0.82, 'horizontal', 0, 0.86, 0.83, 
 '{"liberal_arts_notes": "估算近似思维为几何估算提供估算策略和近似计算基础", "science_notes": "估算近似思维在立体几何估算中的估算应用"}', true),

-- 【运算优化思维为几何优化提供计算优化基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH03_011'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_025'), 
 'extension', 0.85, 0.81, 60, 0.8, 0.83, 'horizontal', 0, 0.87, 0.84, 
 '{"liberal_arts_notes": "运算优化思维为几何计算优化提供优化策略和计算效率基础", "science_notes": "运算优化思维在立体几何计算优化中的优化应用"}', true),

-- ============================================
-- 6. 数学思想方法→几何思想方法整合（10条关系）
-- ============================================

-- 【数形结合思想为立体数形提供数形结合基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH03_013'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_026'), 
 'extension', 0.93, 0.89, 30, 0.9, 0.91, 'horizontal', 0, 0.95, 0.92, 
 '{"liberal_arts_notes": "数形结合思想为立体数形结合提供数形结合和空间数形基础", "science_notes": "数形结合思想在立体几何数形结合中的核心思想应用"}', true),

-- 【分类讨论思想为几何分类提供分类方法基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH02_004'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_027'), 
 'application_of', 0.90, 0.86, 30, 0.8, 0.88, 'horizontal', 0, 0.92, 0.89, 
 '{"liberal_arts_notes": "分类讨论思想为几何分类讨论提供分类方法和讨论策略基础", "science_notes": "分类讨论思想在立体几何分类讨论中的分类应用"}', true),

-- 【转化化归思想为几何转化提供转化策略基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH01_014'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_028'), 
 'application_of', 0.89, 0.85, 30, 0.8, 0.87, 'horizontal', 0, 0.91, 0.88, 
 '{"liberal_arts_notes": "转化化归思想为几何转化提供转化策略和问题化归基础", "science_notes": "转化化归思想在立体几何问题转化中的核心转化应用"}', true),

-- 【函数与方程思想为几何方程提供方程建立基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH03_017'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_029'), 
 'extension', 0.88, 0.84, 30, 0.8, 0.86, 'horizontal', 0, 0.90, 0.87, 
 '{"liberal_arts_notes": "函数与方程思想为几何方程提供方程建立和函数几何基础", "science_notes": "函数方程思想在立体几何方程建立中的方程应用"}', true),

-- 【整体局部思想为几何整体提供整体分析基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH02_013'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_026'), 
 'extension', 0.87, 0.83, 60, 0.8, 0.85, 'horizontal', 0, 0.89, 0.86, 
 '{"liberal_arts_notes": "整体局部思想为几何整体分析提供整体思维和局部整体基础", "science_notes": "整体局部思想在立体几何整体分析中的整体应用"}', true),

-- 【特殊一般思想为几何推广提供推广方法基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH03_001'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_027'), 
 'extension', 0.85, 0.81, 60, 0.8, 0.83, 'horizontal', 0, 0.87, 0.84, 
 '{"liberal_arts_notes": "特殊一般思想为几何推广提供推广方法和一般化基础", "science_notes": "特殊一般思想在立体几何推广应用中的推广应用"}', true),

-- 【有限无限思想为几何极限提供极限认知基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH04_011'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_028'), 
 'extension', 0.84, 0.80, 90, 0.7, 0.82, 'horizontal', 0, 0.86, 0.83, 
 '{"liberal_arts_notes": "有限无限思想为几何极限理解提供极限认知和无限思维基础", "science_notes": "有限无限思想在立体几何极限理解中的极限应用"}', true),

-- 【对称思想方法为几何对称提供对称分析基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH03_015'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_029'), 
 'extension', 0.86, 0.82, 30, 0.8, 0.84, 'horizontal', 0, 0.88, 0.85, 
 '{"liberal_arts_notes": "对称思想方法为几何对称分析提供对称思维和对称识别基础", "science_notes": "对称思想方法在立体几何对称分析中的核心对称应用"}', true),

-- 【变换思想方法为几何变换提供变换思维基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH03_016'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_025'), 
 'extension', 0.88, 0.84, 30, 0.8, 0.86, 'horizontal', 0, 0.90, 0.87, 
 '{"liberal_arts_notes": "变换思想方法为几何变换提供变换思维和几何变换基础", "science_notes": "变换思想方法在立体几何变换中的核心变换应用"}', true),

-- 【数学思想综合为几何思想综合提供思想整合基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH03_021'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_024'), 
 'extension', 0.92, 0.88, 60, 0.9, 0.90, 'horizontal', 0, 0.94, 0.91, 
 '{"liberal_arts_notes": "数学思想综合为几何思想综合提供思想整合和综合思维基础", "science_notes": "数学思想综合在立体几何思想综合中的核心综合应用"}', true),

-- ============================================
-- 【第十批】高中数学核心素养整合（25条关系）
-- 批次编号：10/10
-- 学科跨越：数学核心素养→综合素养发展体系
-- 认知跨越：单一素养认知→综合素养应用
-- 核心价值：构建完整的高中数学核心素养发展体系
-- 预期成果：25条高质量关系，累计349条关系
-- ============================================

-- ============================================
-- 1. 数学抽象素养→立体几何抽象综合（5条关系）
-- ============================================

-- 【集合抽象思维为几何点集提供抽象概念基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH01_002'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_001'), 
 'extension', 0.87, 0.89, 60, 0.8, 0.73, 'horizontal', 0, 0.88, 0.91, 
 '{"liberal_arts_notes": "集合抽象思维为几何点集概念提供抽象思维和集合几何基础", "science_notes": "集合抽象思维在立体几何点集理解中的核心抽象应用"}', true),

-- 【函数抽象概念为空间映射提供映射抽象基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH02_004'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_003'), 
 'application_of', 0.84, 0.86, 90, 0.8, 0.71, 'horizontal', 0, 0.85, 0.88, 
 '{"liberal_arts_notes": "函数抽象概念为空间映射提供映射抽象和空间函数基础", "science_notes": "函数抽象概念在立体几何空间映射中的映射应用"}', true),

-- 【方程抽象思维为几何关系提供关系抽象基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH03_003'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_005'), 
 'extension', 0.89, 0.91, 60, 0.8, 0.77, 'horizontal', 0, 0.90, 0.93, 
 '{"liberal_arts_notes": "方程抽象思维为几何关系抽象提供关系思维和抽象几何基础", "science_notes": "方程抽象思维在立体几何关系抽象中的核心关系应用"}', true),

-- 【指数抽象概念为几何增长提供增长抽象基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH04_005'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_007'), 
 'application_of', 0.86, 0.88, 90, 0.8, 0.74, 'horizontal', 0, 0.87, 0.90, 
 '{"liberal_arts_notes": "指数抽象概念为几何增长模式提供增长抽象和模式抽象基础", "science_notes": "指数抽象概念在立体几何增长模式中的增长应用"}', true),

-- 【三角抽象概念为空间角度提供角度抽象基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH05_003'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_009'), 
 'extension', 0.91, 0.93, 60, 0.8, 0.79, 'horizontal', 0, 0.92, 0.95, 
 '{"liberal_arts_notes": "三角抽象概念为空间角度抽象提供角度思维和空间角度基础", "science_notes": "三角抽象概念在立体几何角度抽象中的核心角度应用"}', true),

-- ============================================
-- 2. 逻辑推理素养→几何证明逻辑综合（5条关系）
-- ============================================

-- 【充要条件逻辑为几何命题提供命题逻辑基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH01_014'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_011'), 
 'application_of', 0.85, 0.87, 90, 0.8, 0.72, 'horizontal', 0, 0.86, 0.89, 
 '{"liberal_arts_notes": "充要条件逻辑为几何命题逻辑提供命题判定和逻辑推理基础", "science_notes": "充要条件逻辑在立体几何命题证明中的核心逻辑应用"}', true),

-- 【条件判断方法为几何推理提供推理方法基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH01_015'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_013'), 
 'extension', 0.88, 0.90, 60, 0.8, 0.76, 'horizontal', 0, 0.89, 0.92, 
 '{"liberal_arts_notes": "条件判断方法为几何推理提供推理方法和判断逻辑基础", "science_notes": "条件判断方法在立体几何推理中的推理应用"}', true),

-- 【全称量词概念为几何证明提供普遍性基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH01_016'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_015'), 
 'application_of', 0.83, 0.85, 90, 0.8, 0.70, 'horizontal', 0, 0.84, 0.87, 
 '{"liberal_arts_notes": "全称量词概念为几何证明提供普遍性逻辑和全称推理基础", "science_notes": "全称量词概念在立体几何全称证明中的全称应用"}', true),

-- 【存在量词概念为几何构造提供存在性基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH01_017'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_017'), 
 'extension', 0.90, 0.92, 60, 0.8, 0.78, 'horizontal', 0, 0.91, 0.94, 
 '{"liberal_arts_notes": "存在量词概念为几何构造提供存在性逻辑和构造推理基础", "science_notes": "存在量词概念在立体几何存在性证明中的存在应用"}', true),

-- 【量词命题否定为几何反证提供否定逻辑基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH01_018'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_019'), 
 'application_of', 0.87, 0.89, 90, 0.8, 0.75, 'horizontal', 0, 0.88, 0.91, 
 '{"liberal_arts_notes": "量词命题否定为几何反证提供否定逻辑和反证推理基础", "science_notes": "量词命题否定在立体几何反证法中的核心否定应用"}', true),

-- ============================================
-- 3. 数学建模素养→立体几何建模综合（5条关系）
-- ============================================

-- 【函数建模思维为几何建模提供建模方法基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH02_008'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH09_003'), 
 'application_of', 0.86, 0.88, 90, 0.8, 0.73, 'horizontal', 0, 0.87, 0.90, 
 '{"liberal_arts_notes": "函数建模思维为几何建模提供建模方法和几何建模基础", "science_notes": "函数建模思维在立体几何建模中的核心建模应用"}', true),

-- 【方程建模思维为几何关系建模提供关系建模基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH03_015'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH09_007'), 
 'extension', 0.89, 0.91, 60, 0.8, 0.77, 'horizontal', 0, 0.90, 0.93, 
 '{"liberal_arts_notes": "方程建模思维为几何关系建模提供关系建模和几何方程基础", "science_notes": "方程建模思维在立体几何关系建模中的关系应用"}', true),

-- 【指数建模思维为几何增长建模提供增长建模基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH04_012'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH09_011'), 
 'application_of', 0.84, 0.86, 90, 0.8, 0.71, 'horizontal', 0, 0.85, 0.88, 
 '{"liberal_arts_notes": "指数建模思维为几何增长建模提供增长建模和模式建模基础", "science_notes": "指数建模思维在立体几何增长建模中的增长应用"}', true),

-- 【三角建模思维为空间建模提供周期建模基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH05_008'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH09_015'), 
 'extension', 0.91, 0.93, 60, 0.8, 0.79, 'horizontal', 0, 0.92, 0.95, 
 '{"liberal_arts_notes": "三角建模思维为空间周期建模提供周期建模和空间建模基础", "science_notes": "三角建模思维在立体几何周期建模中的周期应用"}', true),

-- 【实际问题建模为几何实际建模提供实际建模基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH05_015'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH09_011'), 
 'application_of', 0.88, 0.90, 90, 0.8, 0.76, 'horizontal', 0, 0.89, 0.92, 
 '{"liberal_arts_notes": "实际问题建模为几何实际建模提供实际建模和问题转化基础", "science_notes": "实际问题建模在立体几何实际应用中的核心实际应用"}', true),

-- ============================================
-- 4. 数学运算素养→几何计算综合（5条关系）
-- ============================================

-- 【代数运算思维为几何计算提供计算方法基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH02_012'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH10_005'), 
 'application_of', 0.83, 0.85, 90, 0.8, 0.70, 'horizontal', 0, 0.84, 0.87, 
 '{"liberal_arts_notes": "代数运算思维为几何计算提供计算方法和代数几何基础", "science_notes": "代数运算思维在立体几何计算中的核心运算应用"}', true),

-- 【方程求解思维为几何求解提供求解方法基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH03_018'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH10_009'), 
 'extension', 0.87, 0.89, 60, 0.8, 0.74, 'horizontal', 0, 0.88, 0.91, 
 '{"liberal_arts_notes": "方程求解思维为几何求解提供求解方法和几何求解基础", "science_notes": "方程求解思维在立体几何求解中的求解应用"}', true),

-- 【指数运算思维为几何指数计算提供指数运算基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH04_015'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH10_013'), 
 'application_of', 0.85, 0.87, 90, 0.8, 0.72, 'horizontal', 0, 0.86, 0.89, 
 '{"liberal_arts_notes": "指数运算思维为几何指数计算提供指数运算和增长计算基础", "science_notes": "指数运算思维在立体几何指数计算中的指数应用"}', true),

-- 【三角运算思维为空间角度计算提供角度运算基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH05_012'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH10_011'), 
 'extension', 0.90, 0.92, 60, 0.8, 0.78, 'horizontal', 0, 0.91, 0.94, 
 '{"liberal_arts_notes": "三角运算思维为空间角度计算提供角度运算和空间三角基础", "science_notes": "三角运算思维在立体几何角度计算中的核心三角应用"}', true),

-- 【综合运算思维为几何综合计算提供综合运算基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH05_018'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH10_015'), 
 'application_of', 0.86, 0.88, 90, 0.8, 0.73, 'horizontal', 0, 0.87, 0.90, 
 '{"liberal_arts_notes": "综合运算思维为几何综合计算提供综合运算和计算整合基础", "science_notes": "综合运算思维在立体几何综合计算中的综合应用"}', true),

-- ============================================
-- 5. 直观想象素养→空间思维发展综合（5条关系）
-- ============================================

-- 【函数图象想象为空间图形提供图形想象基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH02_006'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH06_008'), 
 'extension', 0.88, 0.90, 60, 0.8, 0.75, 'horizontal', 0, 0.89, 0.92, 
 '{"liberal_arts_notes": "函数图象想象为空间图形想象提供图形想象和空间视觉基础", "science_notes": "函数图象想象在立体几何空间图形理解中的核心图形应用"}', true),

-- 【方程图形想象为几何图形提供图形构造基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH03_009'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH07_009'), 
 'application_of', 0.84, 0.86, 90, 0.8, 0.71, 'horizontal', 0, 0.85, 0.88, 
 '{"liberal_arts_notes": "方程图形想象为几何图形构造提供图形构造和几何想象基础", "science_notes": "方程图形想象在立体几何图形构造中的图形应用"}', true),

-- 【指数曲线想象为空间曲面提供曲面想象基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH04_008'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH08_021'), 
 'extension', 0.91, 0.93, 60, 0.8, 0.79, 'horizontal', 0, 0.92, 0.95, 
 '{"liberal_arts_notes": "指数曲线想象为空间曲面想象提供曲面想象和空间曲线基础", "science_notes": "指数曲线想象在立体几何曲面理解中的曲线应用"}', true),

-- 【三角函数图象为空间周期提供周期想象基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH05_006'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH09_005'), 
 'application_of', 0.87, 0.89, 90, 0.8, 0.74, 'horizontal', 0, 0.88, 0.91, 
 '{"liberal_arts_notes": "三角函数图象为空间周期模式提供周期想象和空间周期基础", "science_notes": "三角函数图象在立体几何周期模式中的周期应用"}', true),

-- 【综合图形想象为空间综合提供综合想象基础】
((SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M1A_CH05_014'), 
 (SELECT id FROM knowledge_nodes WHERE node_code = 'MATH_G10M2A_CH10_013'), 
 'extension', 0.89, 0.91, 60, 0.8, 0.77, 'horizontal', 0, 0.90, 0.93, 
 '{"liberal_arts_notes": "综合图形想象为空间综合理解提供综合想象和空间整合基础", "science_notes": "综合图形想象在立体几何综合理解中的核心综合应用"}', true);
 