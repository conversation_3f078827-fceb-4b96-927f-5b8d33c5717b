# K12数学知识点数据导入系统

本系统提供了完整的K12数学知识图谱数据导入解决方案，将PostgreSQL数据库中的531个知识点和2426条关系转换为微信小程序可用的JavaScript格式。

## 📊 数据概览

### 知识点统计
- **总计**: 531个知识点
- **小学(1-6年级)**: 198个知识点
- **初中(7-9年级)**: 87个知识点  
- **高中(10-12年级)**: 186个知识点

### 关系统计
- **总计**: 2426条关系
- **同年级内部关系**: 1305条
- **跨年级关系**: 1121条

### 难度分布
- **简单(easy)**: 87个
- **中等(medium)**: 245个
- **困难(hard)**: 156个
- **专家级(expert)**: 43个

## 📁 文件结构

```
data/
├── knowledge-nodes.js          # 示例知识点数据(2个完整样例)
├── knowledge-relationships.js  # 示例关系数据和查询工具
├── data-import-script.js      # 数据导入脚本
├── full-data-config.js        # 完整数据配置文件
└── README.md                   # 使用说明文档
```

## 🚀 快速开始

### 1. 运行数据导入脚本

```bash
# 进入data目录
cd data

# 运行导入脚本
node data-import-script.js
```

### 2. 生成的文件

脚本执行后会生成两个文件：
- `knowledge-nodes-full.js` - 完整的531个知识点数据
- `knowledge-relationships-full.js` - 完整的2426条关系数据

### 3. 在小程序中使用

```javascript
// 引入知识点数据
const { knowledgeNodes, knowledgeStatistics } = require('./data/knowledge-nodes-full.js');

// 引入关系数据
const { knowledgeRelationships, relationshipStatistics } = require('./data/knowledge-relationships-full.js');

// 使用数据
console.log(`总知识点数: ${knowledgeStatistics.total_count}`);
console.log(`总关系数: ${relationshipStatistics.total_count}`);
```

## 🏗️ 数据结构说明

### 知识点数据结构

每个知识点包含17个核心字段：

```javascript
{
  id: "e1n001",                    // 知识点唯一标识
  name: "数一数",                  // 知识点名称
  code: "e1n001",                  // 知识点编码
  
  educational_metadata: {          // 教育元数据
    grade: 1,                      // 年级
    semester: "first",             // 学期
    subject: "数学",               // 学科
    textbook: "一年级上册",        // 教材
    chapter: "准备课",             // 章节
    section: "数一数",             // 小节
    chapter_number: 0,             // 章节序号
    section_number: "0.1",         // 小节序号
    estimated_time: 45             // 预计学习时间(分钟)
  },
  
  difficulty_info: {               // 难度信息
    level: "easy",                 // 难度等级
    cognitive_load: "low",         // 认知负荷
    prerequisite_count: 0,         // 前置知识点数量
    complexity_score: 0.1          // 复杂度评分
  },
  
  knowledge_classification: {      // 知识分类
    domain: "数与代数",            // 数学领域
    subdomain: "数的认识",         // 子领域
    concept_type: "基础技能",      // 概念类型
    learning_objectives: [],       // 学习目标
    key_concepts: [],              // 核心概念
    skills: []                     // 技能要求
  },
  
  learning_content: {              // 学习内容
    concept_definition: "",        // 概念定义
    key_points: [],                // 要点
    examples: [],                  // 示例
    common_mistakes: [],           // 常见错误
    teaching_tips: []              // 教学建议
  },
  
  practice_assessment: {           // 练习与评估
    practice_types: [],            // 练习类型
    difficulty_levels: [],         // 难度等级
    assessment_criteria: [],       // 评估标准
    sample_problems: [],           // 样题
    solution_strategies: []        // 解题策略
  },
  
  learning_guidance: {             // 学习指导
    study_methods: [],             // 学习方法
    time_allocation: {},           // 时间分配
    review_schedule: [],           // 复习计划
    extension_activities: [],      // 拓展活动
    remedial_strategies: []        // 补救策略
  },
  
  cognitive_analysis: {            // 认知分析
    mental_models: [],             // 思维模型
    reasoning_patterns: [],        // 推理模式
    transfer_potential: 0.9,       // 迁移潜力
    abstraction_level: "具体操作", // 抽象水平
    cognitive_bridges: []          // 认知桥梁
  },
  
  knowledge_network: {             // 知识网络
    prerequisite_links: [],        // 前置链接
    successor_links: [],           // 后继链接
    parallel_links: [],            // 平行链接
    application_links: [],         // 应用链接
    cross_domain_links: []         // 跨域链接
  },
  
  learning_path: {                 // 学习路径
    entry_points: [],              // 入口点
    milestone_checkpoints: [],     // 里程碑检查点
    alternative_routes: [],        // 替代路径
    acceleration_options: [],      // 加速选项
    support_resources: []          // 支持资源
  },
  
  personalization_data: {          // 个性化数据
    learning_style_adaptations: {},// 学习风格适应
    ability_level_adjustments: {}, // 能力水平调整
    interest_connections: [],      // 兴趣关联
    cultural_contexts: [],         // 文化背景
    accessibility_features: []     // 无障碍特性
  },
  
  ai_analysis: {                   // AI分析数据
    learning_analytics: {},        // 学习分析
    predictive_indicators: [],     // 预测指标
    recommendation_weights: {},    // 推荐权重
    adaptive_parameters: {},       // 自适应参数
    performance_benchmarks: []     // 性能基准
  },
  
  technical_metadata: {            // 技术元数据
    data_version: "1.0",           // 数据版本
    last_updated: Date,            // 最后更新时间
    quality_score: 0.95,           // 质量评分
    validation_status: "validated",// 验证状态
    usage_statistics: {}           // 使用统计
  }
}
```

### 关系数据结构

每条关系包含以下字段：

```javascript
{
  id: "e1l001",                    // 关系唯一标识
  source_id: "e1n001",             // 源知识点ID
  target_id: "e1n002",             // 目标知识点ID
  relationship_type: "foundation", // 关系类型
  strength: 0.95,                  // 关系强度(0-1)
  description: "描述",             // 关系描述
  
  cognitive_analysis: {            // 认知分析
    cognitive_load: "low",         // 认知负荷
    difficulty_progression: 0.1,   // 难度递进
    mental_model_connection: ""    // 思维模型连接
  },
  
  learning_guidance: {             // 学习指导
    prerequisite_skills: [],       // 前置技能
    learning_objectives: [],       // 学习目标
    teaching_strategies: [],       // 教学策略
    common_difficulties: [],       // 常见困难
    assessment_points: []          // 评估要点
  },
  
  grade_span: "1-2"               // 年级跨度(仅跨年级关系)
}
```

## 🔧 关系类型说明

系统支持14种关系类型：

| 类型 | 名称 | 说明 | 强度范围 |
|------|------|------|----------|
| foundation | 基础支撑 | 一个知识点为另一个提供基础认知支撑 | 0.7-0.95 |
| prerequisite | 前置依赖 | 学习某知识点的必要前置条件 | 0.8-0.95 |
| generalization | 泛化扩展 | 从特殊概念推广到一般概念 | 0.6-0.95 |
| specialization | 特化具体 | 从一般概念特化到具体情况 | 0.6-0.9 |
| derivation | 推导衍生 | 从已知知识点推导出新知识点 | 0.7-0.95 |
| abstraction | 抽象概括 | 从具体现象抽象出一般规律 | 0.6-0.8 |
| reverse | 互逆运算 | 两个知识点互为逆运算关系 | 0.85-0.95 |
| analogy | 类比联系 | 在认知结构上具有相似性 | 0.2-0.7 |
| parallel | 平行发展 | 可以同步学习，互相促进 | 0.6-0.9 |
| application | 应用实践 | 一个知识点在另一个中的应用 | 0.5-0.8 |
| integration | 整合综合 | 多个知识点整合形成新认知结构 | 0.7-0.9 |
| reinforcement | 强化巩固 | 一个知识点强化另一个的理解 | 0.6-0.8 |
| progression | 自然延伸 | 知识点间的自然发展和延续 | 0.8-0.95 |
| contrast | 对比辨析 | 通过对比加深理解 | 0.6-0.8 |

## 🛠️ 工具函数

### 知识点查询

```javascript
const { knowledgeQueries } = require('./knowledge-nodes.js');

// 根据年级获取知识点
const grade1Nodes = knowledgeQueries.getByGrade(1);

// 根据难度获取知识点
const easyNodes = knowledgeQueries.getByDifficulty('easy');

// 根据领域获取知识点
const algebraNodes = knowledgeQueries.getByDomain('数与代数');

// 搜索知识点
const searchResults = knowledgeQueries.search('加法');

// 获取知识点详情
const nodeDetail = knowledgeQueries.getById('e1n001');
```

### 关系查询

```javascript
const { relationshipQuery } = require('./knowledge-relationships.js');

// 获取知识点的所有关系
const relationships = relationshipQuery.getKnowledgeRelationships('e1n001');

// 查找最短学习路径
const path = relationshipQuery.findShortestPath('e1n001', 'e1n010');
```

### AI学习分析

```javascript
const { aiLearningAnalysis } = require('./knowledge-relationships.js');

// 分析学习难度
const difficulty = aiLearningAnalysis.analyzeLearningDifficulty('e1n001');

// 生成个性化练习推荐
const recommendation = aiLearningAnalysis.generatePracticeRecommendation(
  'e1n001', 
  studentData
);
```

### 学习路径推荐

```javascript
const { learningPathRecommendation } = require('./knowledge-relationships.js');

// 生成个性化学习路径
const learningPath = learningPathRecommendation.generateLearningPath(
  'e1n010',  // 目标知识点
  studentProfile // 学生画像
);
```

## 📝 数据完整性检查

### 验证数据完整性

```javascript
const { DataValidator } = require('./full-data-config.js');

// 验证知识点数据
const nodesValid = DataValidator.validateKnowledgeNodes();

// 验证关系数据
const relsValid = DataValidator.validateRelationships();

// 验证数据一致性
const consistent = DataValidator.validateConsistency();
```

## 🎯 使用场景

### 1. AI学习路径规划
利用知识图谱为学生生成个性化学习路径，基于前置依赖关系确保学习顺序的合理性。

### 2. 智能练习推荐
根据学生掌握情况和知识点关系，推荐合适的练习内容和难度。

### 3. 知识点可视化
在小程序中展示知识点之间的关系图，帮助学生理解知识结构。

### 4. 学习进度追踪
基于知识图谱跟踪学生的学习进度，识别薄弱环节。

### 5. 个性化复习计划
根据遗忘曲线和知识点关系，生成个性化的复习计划。

## ⚠️ 注意事项

1. **数据完整性**: 当前示例只包含部分数据，完整使用需要填入所有531个知识点和2426条关系。

2. **性能优化**: 大量数据加载时建议使用分包加载或懒加载策略。

3. **数据更新**: 定期检查数据版本，确保与教学大纲保持一致。

4. **存储空间**: 完整数据集较大，需要合理管理小程序存储空间。

5. **查询优化**: 对于频繁查询，建议添加索引和缓存机制。

## 📞 技术支持

如有疑问或需要技术支持，请参考：
- 项目规范文档
- 微信小程序开发文档
- K12教学大纲标准

## 🔄 更新日志

- **v1.0** (2024-01-15): 初始版本，包含完整数据结构设计
- 支持531个知识点和2426条关系
- 提供完整的查询和分析工具
- 适配微信小程序环境 